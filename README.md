[![Linter](https://github.com/carawayhome/caraway-meridian/actions/workflows/lint.yml/badge.svg)](https://github.com/carawayhome/caraway-meridian/actions/workflows/lint.yml) [![release-please](https://github.com/carawayhome/caraway-meridian/actions/workflows/release-please.yml/badge.svg)](https://github.com/carawayhome/caraway-meridian/actions/workflows/release-please.yml)


![Meridian Logo](./.github/images/meridian-logo.png)

<p align="center">Meridian is <a href="https://carawayhome.com/">Caraway Home's</a> headless commerce store. It comes fully equipped with Shopify's Storefront and Hydrogen stack. It also  includes tools, utilities, and a best-in-class approach for building dynamic and performant commerce applications with Next.js 14.</p>

# 🛍 Meridian

A Next.js 14 and App Router-ready ecommerce standalone featuring:
- [x] ⚡ [Next.js 14](https://nextjs.org) with App Router support
- [x] ⚛️ [React 18](https://react.dev) for React Server Components (RSCs) and Suspense
- [x] 💻 [Node.js 20](https://nodejs.org/en/download) for enhanced performance and security
- [x] 🛍️ [Shopify Hydrogen React](https://shopify.dev/docs/api/hydrogen-react)
  - [ ] ℹ️ Shopify Analytics
  - [x] 🏗️ Shopify Hydrogen Components
- [x] 🛍️ [Shopify Storefront GraphQL API](https://shopify.dev/docs/api/storefront)
- [x] ⛑ [TypeScript](https://www.typescriptlang.org/) to Safely Create React Hooks, and Components and ensure Type Checking
- [x] 💖 Styling with [StyleX](https://stylexjs.com/) to build a scalable design system with tokens
- [x] 📏 Using [ESLint](https://eslint.org/) to Follow Next.js Best Practices
- [ ] 🏃 Edge Runtime
- [x] 🛒 Checkout and payments with Shopify
- [ ] 🌓 Automatic light/dark mode based on system settings
- [ ] 🌎 Multi-language (i18n)
- [x] ⏲️ Real-time inventory checks
- [ ] 👀 Live Preview for realtime collaboration
- [ ] 🗺️  Sitemap.xml and robots.txt
- [x] 🏪 Redux Toolkit for streamlined state management
- [ ] 🏪 RTK Query for streamlined data management
  - [ ] 👮 ZOD or Valibot for data Type Safety
- [ ] 🤖 Optimized SEO metadata, JSON-LD and Open Graph tags
- [ ] ♿ A11y first design
- [ ] 🦺 Unit Testing
- [ ] 🚨 Error Monitoring with [Sentry](https://sentry.io)
- [x] 💯 Track and monitor lighthouse scores with [Vercel Speed Insights](https://github.com/vercel/speed-insights)
- [x] 📊 [Vercel Analytics](https://vercel.com/analytics)
- [x] ☕ Minify HTML & CSS
- [x] 🧼 ESLint Github Action to sanitize code. Using Airbnb TypeScript Config.
- [x] 🐾 Husky to enable comment linting automation
- [x] 🧽 Automated commit linting with [Commit Lint](https://github.com/conventional-changelog/commitlint) & pre-commit messages with [Commitizen](https://github.com/commitizen/cz-cli)
  - [x] Adopted [conventional commits](https://www.conventionalcommits.org/en/v1.0.0/)
- [x] ⏫ Update dependencies with Dependabot
- [x] 🆕 Automated releases with [Release Please](https://github.com/googleapis/release-please) or [Semantic Release](https://semantic-release.gitbook.io/semantic-release/)
- [x] 💨 Live reload
- [ ] ✅ Cache busting

## Hydrogen
📚 [Docs](https://shopify.dev/custom-storefronts/hydrogen) | 🗣 [Discord](https://discord.gg/shopifydevs) | 💬 [Discussions](https://github.com/Shopify/hydrogen/discussions) | 📝 [Changelog](./packages/hydrogen/CHANGELOG.md)

## Core Pages
- [ ] Homepage
- [ ] Product Collections
- [ ] Product Details Page
- [ ] Search
- [ ] Mini Cart

## Integrations
Additional functionality for Project Meridian
- [ ] Okendo
- [ ] MeiliSearch
- [ ] Gladly
- [ ] Klaviyo
- [ ] PostScript
- [ ] Impact
- [ ] Facebook
- [ ] TikTok
- [ ] Google Analytics 4
- [ ] Shopify Checkout

## Getting Started

**Requirements:**

- Node.js version 20 or higher
- `npm` (or your package manager of choice, such as `yarn` or `pnpm`)*

### Running locally
You will need to use the environment variables defined in .env.example to run Project Meridian.
> Note: You should not commit your .env file or it will expose secrets that will allow others to control your Shopify store.

Clone the source code into your computer.

```bash
git clone https://github.com/carawayhome/meridian.git
```

### Usage

First, you need to set the below environment variables in the `.env` file or your deployment platforms.

- `NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN`
- `NEXT_PUBLIC_SHOPIFY_PUBLIC_STOREFRONT_TOKEN`
- `NEXT_PUBLIC_SHOPIFY_STOREFRONT_API_VERSION`
- `SHOPIFY_PRIVATE_STOREFRONT_TOKEN`


You can follow the [Shopify Storefront GraphQL API](https://shopify.dev/api/storefront/getting-started) documentation to get Storefront API information.

### 🧞 Installation and Commands

| Command                | Action                                              |
| :--------------------- | :-------------------------------------------------- |
| `npm install`          | Installs dependencies                               |
| `npm run dev`          | Starts local dev server at [localhost:3000](http://localhost:3000/) |
| `npm run build`        | Builds your production site to `./dist/`            |
| `npm run start`        | Starts the project in production mode.              |
| `npm run lint`         | Analyzes the code to find problems with `eslint`    |
| `npm run clean`        | Removes the `.next` directory                       |
| `npm run clean-dev`    | Cleans the `.next` directory and starts the dev server |
| `npm run clean-build`  | Cleans the `.next` directory and builds the project |
| `npm run prepare`      | Sets up Husky for commit hooks                      |
| `npm run cz`           | Runs Commitizen for structured commit messages      |


#### Visual Studio Code Extensions

To speed up your productivity, you can install these extensions:

- [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)

* if you use a different package manager locally do not commit changes related to the package manager.

