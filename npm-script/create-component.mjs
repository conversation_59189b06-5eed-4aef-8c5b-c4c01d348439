import inquirer from "inquirer";
import { pascalCase } from "change-case";
import { fileURLToPath } from "url";

import fs from "fs";
import path from "path";

import createIndex from "./create-index.mjs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

inquirer
  .prompt([
    {
      type: "list",
      message: "What type of component do you want to create?",
      name: "component-type",
      choices: [
        {
          name: "Cart",
          value: "Cart",
        },
        {
          name: "Content",
          value: "Content",
        },
        {
          name: "Generic",
          value: "Generic",
        },
        {
          name: "Layout",
          value: "layout",
        },
        {
          name: "Product",
          value: "Product",
        },
      ],
    },
    {
      type: "input",
      message: "What is the name of the component?",
      name: "component-name",
    },
  ])
  .then((answers) => {
    const type = answers["component-type"];
    const name = pascalCase(answers["component-name"]);

    const componentPath = path.join(
      __dirname,
      "..",
      "src",
      "components",
      type,
      name,
    );

    const indexFilePath = path.join(componentPath, "index.tsx");

    const reactFunctionalComponentName = pascalCase(answers["component-name"]);

    if (!fs.existsSync(componentPath)) {
      fs.mkdirSync(componentPath);

      fs.writeFileSync(
        indexFilePath,
        createIndex(reactFunctionalComponentName),
      );

    } else {
      console.error("Component already exists");
    }
  });
