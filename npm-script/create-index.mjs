

export function createIndex(componentName) {
  const indexFileContent = `import {
  fontSizes,
  globalTokens,
  spacing,
  defaultTheme as $T
} from '@/app/themeTokens.stylex';
import { ThemeColors } from '@/app/themeThemes.stylex';
import Container from '@components/layout/Container';

import * as stylex from '@stylexjs/stylex';

export type ${componentName}Props = {
  theme?: ThemeColors
}

const styles = stylex.create({
  container: {}
})
  
const ${componentName} = ({ theme = 'cream' }: ${componentName}Props)=>{

    return (
        <Container as="div" theme={theme} styleProp={styles.container}>
          { null }
        </Container>
    );
}

export default ${componentName};
`;

  return indexFileContent;
}
export default createIndex;
