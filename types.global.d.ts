export {}

declare global {
  interface Window {
    ttq: {
      push: () => void;
      track: (event: string, data: any, opts: {}) => void
    };
    okeWidgetApi: {
      initWidget?: (options: Record<string, any> | null) => void;
    };
    tolstoyAppKey?: string;
    tolstoyWidget: {
      subscribe: (event: string, callback: (payload: any) => void, options?: any) => void;
      postMessage: (message: any) => void;
    };
  }
}
