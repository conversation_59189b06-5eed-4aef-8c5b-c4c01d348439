  name: Linter

  on:  # yamllint disable-line rule:truthy
    push: null
    pull_request_target: null

  permissions: {}

  jobs:
    build:
      name: Running ESLint
      runs-on: ubuntu-latest

      permissions:
        contents: read
        packages: read
        # To report GitHub Actions status checks
        statuses: write

      steps:
        - name: Checkout code
          uses: actions/checkout@v4
          with:
            fetch-depth: 0
        - run: |
            npm ci
            npm run build
            npm run lint
          env:
            NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN: ${{ secrets.PUBLICSTOREDOMAIN }}
            NEXT_PUBLIC_SHOPIFY_PUBLIC_STOREFRONT_TOKEN: ${{ secrets.PUBLICSTOREFRONTTOKEN }}
            SHOPIFY_PRIVATE_STOREFRONT_TOKEN: ${{ secrets.PRIVATESTOREFRONTTOKEN }}
            NEXT_PUBLIC_SHOPIFY_STOREFRONT_API_VERSION: ${{ secrets.STOREFRONTAPIVERSION }}
            CONTENTFUL_SPACE_ID: ${{ secrets.CONTENTFULSPACEID }}
            CONTENTFUL_ACCESS_TOKEN: ${{ secrets.CONTENTFULACCESSTOKEN }}
            CONTENTFUL_PREVIEW_ACCESS_TOKEN: ${{ secrets.CONTENTFULPREVIEWACCESSTOKEN }}
            NEXT_PUBLIC_OKENDO_SUBSCRIBER_ID: ${{ secrets.PUBLICOKENDOSUBSCRIBERID }}