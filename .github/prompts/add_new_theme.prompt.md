#file:../../src/app/themeTokens.stylex.ts
#file:../../src/app/themeThemes.stylex.ts

For best output, paste the image from the Figma with the theme variables.

1. First, check to see if this color is defined as a color variable in `src\app\themeTokens.stylex.ts`. If it doesn't exist, add it as a variable to the color tokens in camelCase. Ensure the new theme is added in alphabetical order too.
2. Then, add the following theme to the `src\app\themeThemes.stylex.ts` file. Ensure:
   - The types are generated.
   - The new theme color is added to the list of colors in alphabetical order.
   - The theme is exported in the function.
   Example variables to include
    primarySurface
    primaryText
    primaryCTASurface
    primaryCTAText
    primaryCTASurfaceHover

Don't forget to leave a message in the chat letting the user know to update the content model in Contentful. They need to update Token Settings to include the theme.