{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@providers/*": ["./src/providers/*"], "@utils/*": ["./src/utils/*"], "@lib/*": ["./src/lib/*"], "@redux/*": ["./src/redux/*"], "@hooks/*": ["./src/hooks/*"], "@scripts/*": ["./src/scripts/*"], "@nav/*": ["./src/components/layout/Header/Navigation/*"], "@footer/*": ["./src/components/layout/Footer/*"], "@assets/*": ["./public/*"]}}, "include": ["next-env.d.ts", "next.config.js", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "./types.global.d.ts"], "exclude": ["node_modules"]}