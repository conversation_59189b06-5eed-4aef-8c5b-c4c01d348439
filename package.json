{"name": "meridian", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build  && node ./scripts/generateJson.js", "start": "next start", "lint": "next lint", "lint-staged": "lint-staged", "clean": "rm -rf .next", "clean-dev": "npm run clean && npm run dev", "clean-build": "npm run clean && npm run build", "prepare": "husky", "cz": "cz", "cc": "node ./npm-script/create-component.mjs"}, "dependencies": {"@babel/runtime": "^7.27.1", "@chordcommerce/analytics": "^1.8.0", "@contentful/rich-text-from-markdown": "^16.0.0", "@contentful/rich-text-html-renderer": "^16.6.10", "@contentful/rich-text-plain-text-renderer": "^17.0.0", "@contentful/rich-text-react-renderer": "^16.0.1", "@contentful/rich-text-types": "^17.0.0", "@formspree/react": "^2.5.1", "@growthbook/growthbook-react": "^1.3.1", "@img-comparison-slider/react": "^8.0.2", "@radix-ui/react-popover": "^1.1.13", "@reduxjs/toolkit": "^2.8.1", "@rtk-query/graphql-request-base-query": "^2.3.1", "@segment/analytics-next": "^1.79.0", "@shopify/hydrogen-react": "^2024.10.1", "@stylexjs/stylex": "^0.8.0", "@vercel/analytics": "^1.4.1", "@vercel/speed-insights": "^1.2.0", "airtable": "^0.12.2", "embla-carousel": "^8.5.1", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-react": "^8.5.1", "embla-carousel-wheel-gestures": "^8.0.2", "eslint-plugin-newline-destructuring": "^1.2.2", "framer-motion": "^11.11.9", "gqlmin": "^0.2.2", "graphql-request": "^6.1.0", "meilisearch": "^0.48.2", "next": "^14.2.28", "react": "^18.3.1", "react-confetti": "^6.4.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-redux": "^9.1.2", "sharp": "^0.33.5"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@stylexjs/babel-plugin": "^0.8.0", "@stylexjs/eslint-plugin": "^0.8.0", "@stylexjs/nextjs-plugin": "^0.8.0", "@types/node": "^22.15.3", "@types/react": "^18", "@types/react-dom": "^18", "change-case": "^5.4.4", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-next": "^14.2.14", "husky": "^9.1.7", "inquirer": "^10.1.8", "lint-staged": "^15.5.0", "typescript": "^5.7.2"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "husky": {"hooks": {"prepare-commit-msg": "console.log('a')"}}}}