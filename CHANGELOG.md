# Changelog

## [0.71.0](https://github.com/carawayhome/caraway-meridian/compare/v0.70.0...v0.71.0) (2025-05-22)


### Features

* adding product link to recommended product image ([#1382](https://github.com/carawayhome/caraway-meridian/issues/1382)) ([074f63d](https://github.com/carawayhome/caraway-meridian/commit/074f63d67a96a791b76c529522750d9f741b6693))
* implement single line swatches ([#1353](https://github.com/carawayhome/caraway-meridian/issues/1353)) ([3bfdc1a](https://github.com/carawayhome/caraway-meridian/commit/3bfdc1a663c9d334d56be2dbd999a36021a298c5))
* Legal compliance - show aggregated reviews where store reviews are shown ([#1347](https://github.com/carawayhome/caraway-meridian/issues/1347)) ([179d10d](https://github.com/carawayhome/caraway-meridian/commit/179d10deaa4c3e954008a61f3b6e98f8037a265c))
* notify me button on product cards ([#1357](https://github.com/carawayhome/caraway-meridian/issues/1357)) ([6e43e7d](https://github.com/carawayhome/caraway-meridian/commit/6e43e7dbcc5b4c7ce7a2aea0e9b9203458ec7c4c))
* Pricing updates in PDP and PLPs ([#1379](https://github.com/carawayhome/caraway-meridian/issues/1379)) ([525860b](https://github.com/carawayhome/caraway-meridian/commit/525860ba5447b68646f0c05807235a47fabcfd8e))


### Bug Fixes

* non toxic iteams showing empty popover tooltip ([#1374](https://github.com/carawayhome/caraway-meridian/issues/1374)) ([dd1befb](https://github.com/carawayhome/caraway-meridian/commit/dd1befb1578b67cf649b7c41c055b960ca7e6d8f))
* quantity selector missing padding above ([#1386](https://github.com/carawayhome/caraway-meridian/issues/1386)) ([fa0d544](https://github.com/carawayhome/caraway-meridian/commit/fa0d5442102cff280455cb80ff8269c5895ea545))
* **theme:** add React.Fragment for key prop in multiple components ([#1370](https://github.com/carawayhome/caraway-meridian/issues/1370)) ([afa3fa5](https://github.com/carawayhome/caraway-meridian/commit/afa3fa5139513beb0eb7120c1b955a0f62335faf))

## [0.70.0](https://github.com/carawayhome/caraway-meridian/compare/v0.69.0...v0.70.0) (2025-05-13)


### Features

* Create landing page for DTC okendo review form ([#1274](https://github.com/carawayhome/caraway-meridian/issues/1274)) ([d11211e](https://github.com/carawayhome/caraway-meridian/commit/d11211e2eab7abbc19c36a3784ae9282c711f13f))
* **lineitem:** display selected options value conditionally ([#1351](https://github.com/carawayhome/caraway-meridian/issues/1351)) ([19d1b3b](https://github.com/carawayhome/caraway-meridian/commit/19d1b3b7322a13827f6c2ea2e02f2ead5ddd848e))
* **producttoggles:** add row gap to grid styles for improved layout ([#1348](https://github.com/carawayhome/caraway-meridian/issues/1348)) ([77e31e9](https://github.com/carawayhome/caraway-meridian/commit/77e31e9c25e5b7ded448577072638ea444355b2d))
* **scripts:** add KeepCart component for extension detection ([#1349](https://github.com/carawayhome/caraway-meridian/issues/1349)) ([56356b8](https://github.com/carawayhome/caraway-meridian/commit/56356b80e851c44312b0981c0cffbea8d8d9c855))


### Bug Fixes

* **keys:** add unique keys for mapped components in ProductAccordions, ProductGrid, and CustomSlider ([#1345](https://github.com/carawayhome/caraway-meridian/issues/1345)) ([f305e84](https://github.com/carawayhome/caraway-meridian/commit/f305e84957f698043e704e900a479be5a77c737c))

## [0.69.0](https://github.com/carawayhome/caraway-meridian/compare/v0.68.0...v0.69.0) (2025-05-12)


### Features

* corso ab test delist keeping corso opt in ([#1334](https://github.com/carawayhome/caraway-meridian/issues/1334)) ([7495106](https://github.com/carawayhome/caraway-meridian/commit/74951062fca74ab69a2a880bee934b651fc75200))
* delisting control for typography hierarchy test ([#1331](https://github.com/carawayhome/caraway-meridian/issues/1331)) ([3716c64](https://github.com/carawayhome/caraway-meridian/commit/3716c64a83eb961dadc5f92803178808fc4a2f50))
* delisting double feature card ab test ([#1330](https://github.com/carawayhome/caraway-meridian/issues/1330)) ([d066f3d](https://github.com/carawayhome/caraway-meridian/commit/d066f3d7fc88d93875c8816dc95349a65bac0d42))
* **productcardswatches:** add navy color to product card swatches style ([#1337](https://github.com/carawayhome/caraway-meridian/issues/1337)) ([ccb0076](https://github.com/carawayhome/caraway-meridian/commit/ccb007656e10b1a7f6b0f2e7cba35424c634a327))

## [0.68.0](https://github.com/carawayhome/caraway-meridian/compare/v0.67.0...v0.68.0) (2025-05-08)


### Features

* exclude products with block search indexing [sc-19450] ([#1289](https://github.com/carawayhome/caraway-meridian/issues/1289)) ([5e22eb2](https://github.com/carawayhome/caraway-meridian/commit/5e22eb2f9fa41f1be6f5b53312b2b6c48efe99d9))
* small text in select size dialog ([#1311](https://github.com/carawayhome/caraway-meridian/issues/1311)) ([c48c94f](https://github.com/carawayhome/caraway-meridian/commit/c48c94f80ebb0404c8e1cde5c168980b9cb59682))


### Bug Fixes

* swatch slider on product cards for mobile ([#1321](https://github.com/carawayhome/caraway-meridian/issues/1321)) ([2bbb28f](https://github.com/carawayhome/caraway-meridian/commit/2bbb28f944ccf38abcd02298b2028080aaa3aaff))
* update image loader configuration to unoptimized ([#1313](https://github.com/carawayhome/caraway-meridian/issues/1313)) ([6811244](https://github.com/carawayhome/caraway-meridian/commit/68112442c0b4f275e090669593d2ef0556f9f8aa))

## [0.67.0](https://github.com/carawayhome/caraway-meridian/compare/v0.66.0...v0.67.0) (2025-04-30)


### Features

* add Alia script component to enhance tracking capabilities ([#1306](https://github.com/carawayhome/caraway-meridian/issues/1306)) ([11b3de4](https://github.com/carawayhome/caraway-meridian/commit/11b3de409c03ba8cfad906462ec8f26ae513c1c5))
* adding corso ab test to purchase disabled variant ([#1304](https://github.com/carawayhome/caraway-meridian/issues/1304)) ([b494a49](https://github.com/carawayhome/caraway-meridian/commit/b494a49d62e4a48702724aeb1d0893d114e1169e))
* adding mobile images to marketing modules ([#1262](https://github.com/carawayhome/caraway-meridian/issues/1262)) ([93a2c96](https://github.com/carawayhome/caraway-meridian/commit/93a2c96703a9014ea2bac038ae6ff65aa379209d))
* corso ab test ([#1301](https://github.com/carawayhome/caraway-meridian/issues/1301)) ([5dfbac1](https://github.com/carawayhome/caraway-meridian/commit/5dfbac131afdeeac2d72d034796568f54115376e))
* **faq:** fAQ Module |Add CTA to bottom of FAQ Module ([#1267](https://github.com/carawayhome/caraway-meridian/issues/1267)) ([33bb4ba](https://github.com/carawayhome/caraway-meridian/commit/33bb4ba42e745de95e642d395af99fa47e95877e))
* **fetch:** add forceCache and revalidate parameters to getPageBySlug and fetchGraphQL ([#1280](https://github.com/carawayhome/caraway-meridian/issues/1280)) ([b8add68](https://github.com/carawayhome/caraway-meridian/commit/b8add68f1fa86034f0bd4598f0f9eea4307a7be5))
* hierarchy ab test ([#1251](https://github.com/carawayhome/caraway-meridian/issues/1251)) ([5809c6a](https://github.com/carawayhome/caraway-meridian/commit/5809c6af192da1493733d90fa6769710efd1f69b))
* pdp add promotion badge to toggles on all [sc-18186] ([#1290](https://github.com/carawayhome/caraway-meridian/issues/1290)) ([b1165c9](https://github.com/carawayhome/caraway-meridian/commit/b1165c9e7cc1a4d1f7d48abc377a9b4783e469fb))
* **theme:** enhance components with unique keys and improve accessib… ([#1263](https://github.com/carawayhome/caraway-meridian/issues/1263)) ([4409127](https://github.com/carawayhome/caraway-meridian/commit/44091274a9e445de01c1c2de5c5100476ac935b5))
* **theme:** enhance key assignment for improved rendering in multipl… ([#1298](https://github.com/carawayhome/caraway-meridian/issues/1298)) ([2783a9e](https://github.com/carawayhome/caraway-meridian/commit/2783a9ea35f5126facda2cc9651debf2d9c0fb64))
* **theme:** update Hero components to use Container for text alignme… ([#1299](https://github.com/carawayhome/caraway-meridian/issues/1299)) ([d9a80a5](https://github.com/carawayhome/caraway-meridian/commit/d9a80a5730b9692c73fa3a34b939e8dac3ce6c3d))
* update SEO title to include Caraway ([#1305](https://github.com/carawayhome/caraway-meridian/issues/1305)) ([73f9753](https://github.com/carawayhome/caraway-meridian/commit/73f975314933b6c706a546a09ee10d471a23ad89))

## [0.66.0](https://github.com/carawayhome/caraway-meridian/compare/v0.65.0...v0.66.0) (2025-04-21)


### Features

* **bimi:** add bimi logo SVG file for branding ([#1269](https://github.com/carawayhome/caraway-meridian/issues/1269)) ([0a48421](https://github.com/carawayhome/caraway-meridian/commit/0a48421bd672b94aa2e45f23113cae36fb11ba65))
* **customhero:** add readable prop to CustomSlider for dynamic visib… ([#1264](https://github.com/carawayhome/caraway-meridian/issues/1264)) ([b6583c7](https://github.com/carawayhome/caraway-meridian/commit/b6583c71ccccca527a3f2b491291a3e0685a0e93))
* use custom loader to cleanup image src attribute ([#1249](https://github.com/carawayhome/caraway-meridian/issues/1249)) ([62ee4bf](https://github.com/carawayhome/caraway-meridian/commit/62ee4bf87ef86a1368f1583a19b825a72b3d8402))


### Bug Fixes

* add checkout button ID and disable div for accessibility ([#1265](https://github.com/carawayhome/caraway-meridian/issues/1265)) ([1060e87](https://github.com/carawayhome/caraway-meridian/commit/1060e876427a3c63f76de3f9775e7455965f4580))
* **customslider:** update notReadable style for better accessibility ([#1270](https://github.com/carawayhome/caraway-meridian/issues/1270)) ([b3e8015](https://github.com/carawayhome/caraway-meridian/commit/b3e8015f56e4c0ae4d8afaf9cb08ca9ff716cfac))
* no use client issue ([#1260](https://github.com/carawayhome/caraway-meridian/issues/1260)) ([37a187f](https://github.com/carawayhome/caraway-meridian/commit/37a187f1b5ef09acaf15f47aee6bb62f72b970b8))

## [0.65.0](https://github.com/carawayhome/caraway-meridian/compare/v0.64.0...v0.65.0) (2025-04-17)


### Features

* adding specific savings messages for tooltips on mothers day bundles ([#1255](https://github.com/carawayhome/caraway-meridian/issues/1255)) ([aa42dc6](https://github.com/carawayhome/caraway-meridian/commit/aa42dc6fa23b36e58a753aa3b9d5c4ad33c4851e))
* **cartfooter:** add checkout button ID and disable div for Corso integration ([#1253](https://github.com/carawayhome/caraway-meridian/issues/1253)) ([7f68926](https://github.com/carawayhome/caraway-meridian/commit/7f68926a35024aa15733b894202fea3b2c18d5fc))


### Bug Fixes

* auto generate all caps redirect links [sc-17205] ([#1246](https://github.com/carawayhome/caraway-meridian/issues/1246)) ([b376854](https://github.com/carawayhome/caraway-meridian/commit/b376854a2b4a586c67eb1b688252d69c95304494))
* make all unknown attributes extrinsic by default ([#1252](https://github.com/carawayhome/caraway-meridian/issues/1252)) ([a1d21b8](https://github.com/carawayhome/caraway-meridian/commit/a1d21b80c3a58b356ac0fa0556d3ecef975ae989))

## [0.64.0](https://github.com/carawayhome/caraway-meridian/compare/v0.63.0...v0.64.0) (2025-04-17)


### Features

* add Corso script component to  checkout functionality ([#1244](https://github.com/carawayhome/caraway-meridian/issues/1244)) ([13d08ab](https://github.com/carawayhome/caraway-meridian/commit/13d08ab8e978df939bfb87e14f9ba9a5d96c840d))
* found the problem - undefined in an array issue ([0050ff6](https://github.com/carawayhome/caraway-meridian/commit/0050ff6c7aa6dfe5ad68852db2cf31e37d59b5c4))


### Bug Fixes

* add group to the extrinsic attributes ([#1242](https://github.com/carawayhome/caraway-meridian/issues/1242)) [sc-19519] ([30df52f](https://github.com/carawayhome/caraway-meridian/commit/30df52f12ee86a5752c09ed8c4b3c7bfdc50d6ef))
* fixing conflicts ([9cbb757](https://github.com/carawayhome/caraway-meridian/commit/9cbb757d9946a4a6b3c1da01755c8fe424a32ca2))
* **navigationdropdown:** update dropdown styles to improve visibility transitions ([#1248](https://github.com/carawayhome/caraway-meridian/issues/1248)) ([bc1e19f](https://github.com/carawayhome/caraway-meridian/commit/bc1e19f7daacd9ec3891c00bbde0e82e9b9d99cb))
* update pricing logic to use configurable tiers and decimal places ([#1247](https://github.com/carawayhome/caraway-meridian/issues/1247)) ([d1708cd](https://github.com/carawayhome/caraway-meridian/commit/d1708cd95a1e4520cdf025a152b05437b77e5521))

## [0.63.0](https://github.com/carawayhome/caraway-meridian/compare/v0.62.0...v0.63.0) (2025-04-14)


### Features

* build error ([da2460c](https://github.com/carawayhome/caraway-meridian/commit/da2460c9077733ab25237e15825fe16c5102bd27))
* build error ([268e565](https://github.com/carawayhome/caraway-meridian/commit/268e565c5ae93a4a9e334138858e00df3ff2dfa2))
* emily wants the hover image behavior occuting even when you're on the orange copy field ([183a6cc](https://github.com/carawayhome/caraway-meridian/commit/183a6cc9f5b09e804dfc5f3dd0adbf4bd393d381))
* hoverImage to posterImage ([eff171d](https://github.com/carawayhome/caraway-meridian/commit/eff171d14d9bbceda3d9ff36baa5215af96df34c))
* offering support for reversed layout to timeline slider via layout 2 settings ([1ceb2ca](https://github.com/carawayhome/caraway-meridian/commit/1ceb2ca091cc88c4f37ab6d74f5310a134ee7527))
* restarting video off hover but maintaining behavior if theres no hover image ([9902a30](https://github.com/carawayhome/caraway-meridian/commit/9902a302759838bf25cfe09d579800e17c7f125d))


### Bug Fixes

* broken builds on collections with AB tests ([56d5f3d](https://github.com/carawayhome/caraway-meridian/commit/56d5f3d36151b23956913a6d8d8d9507df7f67ce))
* **theme:** update indicatorInactive color to gray200 for consistency ([8449ab9](https://github.com/carawayhome/caraway-meridian/commit/8449ab9edf8d2668e1166704221f0c3dd088b8e1))
* try to fix fallback body too large vercel error ([9de46df](https://github.com/carawayhome/caraway-meridian/commit/9de46dfa3f4480331eb7085fddb7766d3029afd0))
* update ecommerce tracking structure from 'add' to 'detail' ([6a7d923](https://github.com/carawayhome/caraway-meridian/commit/6a7d9231b0d2062d207b155acb45ceec48b999ad))
* update ecommerce tracking structure from 'detail' to 'add' ([1da0ce5](https://github.com/carawayhome/caraway-meridian/commit/1da0ce5db2b12b6a5971ccc8a0a2a98de6dc3c2b))
* wrap accessing window inside useEffect ([2d04f8b](https://github.com/carawayhome/caraway-meridian/commit/2d04f8be13277c32b9afe920aaa76e47a0dc2ecd))

## [0.62.0](https://github.com/carawayhome/caraway-meridian/compare/v0.61.0...v0.62.0) (2025-04-09)


### Features

* adding dialog trigger for size guide to single line swatch ab test ([130a806](https://github.com/carawayhome/caraway-meridian/commit/130a806285d8e02e5564f01ad8225bd0af1b7259))
* adding support for mobile image ([aec46f8](https://github.com/carawayhome/caraway-meridian/commit/aec46f86af25cad08f7583b3cb07473669bca3de))
* address comments by neer ([850725b](https://github.com/carawayhome/caraway-meridian/commit/850725bf42a69b2eec697c5a57b2b9a5d70f4c7f))
* addressing chat gpt comments ([3ce3c14](https://github.com/carawayhome/caraway-meridian/commit/3ce3c14de39eb2d1dd9291cfc7e5cad481b71f15))
* addressing comments ([d29b0df](https://github.com/carawayhome/caraway-meridian/commit/d29b0df4b9675b83d20c94d28e876fccfb17e89f))
* adjusting h2 to h4 ([3014d4b](https://github.com/carawayhome/caraway-meridian/commit/3014d4b8431cb1d268e45dda477e39d34850bcbe))
* **callouts:** enhance Callouts component with layout support ([ed1b08d](https://github.com/carawayhome/caraway-meridian/commit/ed1b08dc4c4bc1c431c0bb0e148db88b942f5b33))
* cleaningup file ([03ccc72](https://github.com/carawayhome/caraway-meridian/commit/03ccc72e87108eafd248fe84c382e0933a662a68))
* clicking on an active toggle no longer disables it ([c343595](https://github.com/carawayhome/caraway-meridian/commit/c343595b9f5c26ea635bd585d00ca2d190a10409))
* feature slider anchor functionality ([7f983e1](https://github.com/carawayhome/caraway-meridian/commit/7f983e178696029ba4b49660123e780e779a9099))
* interesting design ([894f637](https://github.com/carawayhome/caraway-meridian/commit/894f637631df1d53e4ece26f5efe95d9941a6a3b))
* keeping desktop style the same ([ac46be9](https://github.com/carawayhome/caraway-meridian/commit/ac46be90f38ddababb8b6045ebcda77ff5011dcc))
* lessened the gap on mobile ([919f6a3](https://github.com/carawayhome/caraway-meridian/commit/919f6a3d3b663008f2b73c5b8d9c42ef41e69a70))
* made active title take the title now ([812106d](https://github.com/carawayhome/caraway-meridian/commit/812106d39be7942f20452b04cf91913a7741e864))
* make sure multi swatches work in a/b test for swatches ([ce574a4](https://github.com/carawayhome/caraway-meridian/commit/ce574a4402313968b79ce9073951d90c50e2d52f))
* on mobile moved toggles down moved byline up ([dac652e](https://github.com/carawayhome/caraway-meridian/commit/dac652e8b73fce3451555d91af01b063794bd1c4))
* qa updates ([9e90609](https://github.com/carawayhome/caraway-meridian/commit/9e90609d91de0516c7951ffe5bc9d56b975b7231))
* **queries:** add layout field to settings in product and page section queries ([a97866f](https://github.com/carawayhome/caraway-meridian/commit/a97866f6d7dbb89c22007315a7519db139300cdf))
* removing arrows off whatsincluded sliders if it doesn't need arrows ([c24c273](https://github.com/carawayhome/caraway-meridian/commit/c24c273b7bdca56d8d4ebd272f1f2a3862db8f4a))
* removing hard-coded parallaxes ([9e8f0a6](https://github.com/carawayhome/caraway-meridian/commit/9e8f0a6f30b57132090db168752777a60e5eb62a))
* smoothening animations a bit more for qa ([4a75c7d](https://github.com/carawayhome/caraway-meridian/commit/4a75c7d30593363b2865c509dbb8acd67434115e))
* style update and added to readytouse ([52e8b85](https://github.com/carawayhome/caraway-meridian/commit/52e8b85f1fee208e181e1114d179209a4580ba9e))
* tablet centering ([64217ce](https://github.com/carawayhome/caraway-meridian/commit/64217cee933bd2187af3fbfc361bd1b4ec9704a5))
* **theme:** add skyBlueDark theme and update theme tokens ([5dc067b](https://github.com/carawayhome/caraway-meridian/commit/5dc067b33b6001c3c3bddba059b9e1f8baf9f258))
* **theme:** refactor coding structure details ([fc9ebc6](https://github.com/carawayhome/caraway-meridian/commit/fc9ebc6ddb8869feb879aa64096fdf319a9cd44c))
* update assets and colors ([88c7c99](https://github.com/carawayhome/caraway-meridian/commit/88c7c99ecd27e99c7520d1c835eae30df9eb8ddc))
* updates with new photos ([2d1541a](https://github.com/carawayhome/caraway-meridian/commit/2d1541a70d3c85c5539105e3a510bf42f57fde44))


### Bug Fixes

* **accordionsplit:** pDP |Split Image Accordion needs to hold mobile and desktop images ([a3b6178](https://github.com/carawayhome/caraway-meridian/commit/a3b6178d1518909a4b4c4fdb7706fc879b20dd3f))
* **callouts:** reorder styles for improved layout handling ([09bd72b](https://github.com/carawayhome/caraway-meridian/commit/09bd72be0aea696bd2c4f672b7c298a3171923a8))
* **customhero:** optimize reference processing and reduce query limit ([8e33185](https://github.com/carawayhome/caraway-meridian/commit/8e33185902e6a4408c8c5177ee5122951de367dd))
* **customhero:** update media types to use ContentfulImage and improve alignment types ([3d09131](https://github.com/carawayhome/caraway-meridian/commit/3d091318093334efc5abcb152799524f74575d9b))
* **heroslider:** adjust media rendering styles and update aspect ratio ([12800c3](https://github.com/carawayhome/caraway-meridian/commit/12800c3d0b0c66a931d60cfa13be8cd5271e53c9))
* prevent images from stretching on care and cleaning page ([c4c074e](https://github.com/carawayhome/caraway-meridian/commit/c4c074ea702c746e7f03be45af71a6797937d1cb))
* show compare sets on the product toggles ([f296b86](https://github.com/carawayhome/caraway-meridian/commit/f296b86ca6ec8284ea89beb2cb63e4fa396aca2b))
* **theme:** primaryCTASurfaceHover property to multiple themes; refactor SplitAssetModule RenderCTA ([6ab6644](https://github.com/carawayhome/caraway-meridian/commit/6ab6644c422cba02f06bd43a0f062daf37fabba7))
* trigger build ([6e51e21](https://github.com/carawayhome/caraway-meridian/commit/6e51e2122281cfee0302d9284a15bfdd55516129))
* use only intrinsic attributes to match selected variant ([4e1b458](https://github.com/carawayhome/caraway-meridian/commit/4e1b458174006de5e0ffa4560bbb8d30d4fa0e5a))
* use stylex over inline style ([891b6a0](https://github.com/carawayhome/caraway-meridian/commit/891b6a046bc88653acd17666720c4cd92aafe318))
* weight and dim table was too large on some desktop screens ([865e759](https://github.com/carawayhome/caraway-meridian/commit/865e75970d69125cb249060e6b6613878defe11d))

## [0.61.0](https://github.com/carawayhome/caraway-meridian/compare/v0.60.0...v0.61.0) (2025-04-01)


### Features

* **theme:** add 'rose' color to theme colors and define its properties ([59e8588](https://github.com/carawayhome/caraway-meridian/commit/59e8588691d20c190478276d05eb0190116406d7))


### Bug Fixes

* **navpromotional:** ensure component handles empty slides gracefully ([206c15d](https://github.com/carawayhome/caraway-meridian/commit/206c15deb8fab915eb166fa726a86285702b1d78))

## [0.60.0](https://github.com/carawayhome/caraway-meridian/compare/v0.59.0...v0.60.0) (2025-03-31)


### Features

* atf swatches a/b test ([c69732c](https://github.com/carawayhome/caraway-meridian/commit/c69732c3571a33d07f320963404dd2c5dbd826c4))
* **fetchproducts:** bug |Airtight Storage | PDP | Save with Set - Block Content ([95b88c3](https://github.com/carawayhome/caraway-meridian/commit/95b88c32c57e19cb6fb7318feb5a680f1ac3a0d6))
* **tracking:** add URL parameter retrieval for Facebook tracking ([9885d23](https://github.com/carawayhome/caraway-meridian/commit/9885d233f8eea075a4726eb85263520757054b9e))


### Bug Fixes

* oos status in new swatches ([497cc25](https://github.com/carawayhome/caraway-meridian/commit/497cc25a8ac882544004f0875441f6db253fa813))

## [0.59.0](https://github.com/carawayhome/caraway-meridian/compare/v0.58.2...v0.59.0) (2025-03-25)


### Features

* build error ([74a2e02](https://github.com/carawayhome/caraway-meridian/commit/74a2e0203bd9478ad4deff1c8eaf3f8c98ef73b4))
* **productcard:** pLP| Typography Update for Product Cards ([d92f6e8](https://github.com/carawayhome/caraway-meridian/commit/d92f6e8fb3fa97fc11fb29b3f9b01839acd44aa9))
* qa build link ([6ed785d](https://github.com/carawayhome/caraway-meridian/commit/6ed785d8f3bde4bf4adb00a3211bfd9840ef2353))
* unnecessary file edit ([1d734bb](https://github.com/carawayhome/caraway-meridian/commit/1d734bbf2858066c50173d94460105dc326f58aa))
* update success message style added videos bg and styles ([ff9d822](https://github.com/carawayhome/caraway-meridian/commit/ff9d8226fe7e1cd44296b33c9fe601cc96b143d1))
* updating copy content and style ([0bde6c0](https://github.com/carawayhome/caraway-meridian/commit/0bde6c0694f5b6eab1bf72da7843078709c30fe2))


### Bug Fixes

* **extractproductdialog:** update items validation ([e040d50](https://github.com/carawayhome/caraway-meridian/commit/e040d50d5037d2398add55e4a98ada4ca14a0892))
* **marquee:** remove layout consideration ([07e3c66](https://github.com/carawayhome/caraway-meridian/commit/07e3c66d1879306a5d1432f465acfc1f7486c9b3))
* **marquee:** remove nested iterations ([93edfce](https://github.com/carawayhome/caraway-meridian/commit/93edfce664463ea2a583dfce1eb72a46f1ba1a9e))
* **pdp:** bug | QMM | PDP | Dialog Doesn't Work on PDP ([c9929f2](https://github.com/carawayhome/caraway-meridian/commit/c9929f2ec76c15633c8fd20f62e1ddc70529838f))

## [0.58.2](https://github.com/carawayhome/caraway-meridian/compare/v0.58.1...v0.58.2) (2025-03-21)


### Bug Fixes

* **usesingleaccordions:** add new parameter to close all ([d491aec](https://github.com/carawayhome/caraway-meridian/commit/d491aec3ec9786c2d5c226bddc7f6d12ab1aac72))

## 0.58.1 (2025-03-15)


### Features

* able to get it on segment now - looking at improvements now ([3dea7b4](https://github.com/carawayhome/caraway-meridian/commit/3dea7b49f68a5185b352d90f30d45ab4813c1044))
* add callout placement option ([b5e5df7](https://github.com/carawayhome/caraway-meridian/commit/b5e5df75554f47277fcaeaf69badfa3cd2000872))
* add march promo flag ([21d54e5](https://github.com/carawayhome/caraway-meridian/commit/21d54e5fe747b5f98d45b0c23e8a5f8e32bd7af9))
* add middleware to handle home page coupon redirect ([c91fb7f](https://github.com/carawayhome/caraway-meridian/commit/c91fb7f94add5737ac89cc4a19b93204fee449c4))
* add new color themes for lavenderLight, violet, violetLight, and warmGrayExtraLight ([817d3a7](https://github.com/carawayhome/caraway-meridian/commit/817d3a7eba1d07cd88295a9722fea7224a1be767))
* added anchor targeting to how we compare charts now ([ba695ff](https://github.com/carawayhome/caraway-meridian/commit/ba695ff29a3a2919f0f170c0f4838c64a592c598))
* adding anchor targeting for how we compare chart ([1061a67](https://github.com/carawayhome/caraway-meridian/commit/1061a67b3538c772bf4f1f5c20b7fdca4f5df519))
* adding anchors to zpattern videomodule and faq ([998f293](https://github.com/carawayhome/caraway-meridian/commit/998f29398df6595bb5ee50c08e67e72e40812bb4))
* adding close dialog button ([057bfe2](https://github.com/carawayhome/caraway-meridian/commit/057bfe2a2f667609524dd9f556167029c4d7c4b4))
* adding contentful level checks for oos swatches ([fe2816b](https://github.com/carawayhome/caraway-meridian/commit/fe2816bbef5885f700d5b02fee91ab1035d175d5))
* adding cta reference logic ([70a9ec5](https://github.com/carawayhome/caraway-meridian/commit/70a9ec5b6599a16e8e19cd2077b570910551a862))
* adding featured image layout for Navigation ([872c492](https://github.com/carawayhome/caraway-meridian/commit/872c4921f50f5d8d5994e00ec51668a7377d96e8))
* adding layout 5 styles ([8185fb3](https://github.com/carawayhome/caraway-meridian/commit/8185fb3ac96cbd889a2282cff96ae305a03040b0))
* adding layout 6 ([c952d50](https://github.com/carawayhome/caraway-meridian/commit/c952d50764e81af27f7616ff7851a3461a1ec059))
* adding layouts and styles ([677cdbb](https://github.com/carawayhome/caraway-meridian/commit/677cdbbdef97e64bf263668220d433a414d4d5c6))
* adding layoyus 3 and 4 ([3b7f51f](https://github.com/carawayhome/caraway-meridian/commit/3b7f51f80030e17c6faa3b1d9db769df4c9c9503))
* adding logic for assets in block content ([940a160](https://github.com/carawayhome/caraway-meridian/commit/940a1602fe24c63d1cdc8793464f8439b4f8c515))
* adding missing style prop ([ebd5ffa](https://github.com/carawayhome/caraway-meridian/commit/ebd5ffa9440ff188995c91a893f165e82e496834))
* adding option for videos ([a69bfdc](https://github.com/carawayhome/caraway-meridian/commit/a69bfdce7f793ff4db56a269aea152d5e6f567bd))
* adding page level dialogs: dawid ([214636e](https://github.com/carawayhome/caraway-meridian/commit/214636e0fb93652fe74812177d4a609cbd4da07a))
* adding quick nav back on - this is the control ([89beb53](https://github.com/carawayhome/caraway-meridian/commit/89beb530870457b866da5e6c9b93e02a9ef4fc5f))
* adding some padding to single hero ([9541c11](https://github.com/carawayhome/caraway-meridian/commit/9541c1124ad195700dde79e90ec9e50ce6ce4ba2))
* adding support for images on layout 5 ([5680301](https://github.com/carawayhome/caraway-meridian/commit/5680301fd2b886c436b5dd5773cf4b0ee7aad5d6))
* adding theme defaults to mobile ([2c7360d](https://github.com/carawayhome/caraway-meridian/commit/2c7360da6a5b320598b40830e3fbf6f02ef99430))
* adding theme to desktop navigation links ([e4013a5](https://github.com/carawayhome/caraway-meridian/commit/e4013a52b35d93525ae09bb6e48fef2d822e4d70))
* adding video player dialog layout ([ffbe480](https://github.com/carawayhome/caraway-meridian/commit/ffbe480c5bfb8e59c871a06c862f1cf8ae7d6de1))
* address the other bulk of the compare chart ui ([4551466](https://github.com/carawayhome/caraway-meridian/commit/4551466ef2155620a22ee7f7fcee492988e07ae3))
* addressing dawid's comments ([6f465d5](https://github.com/carawayhome/caraway-meridian/commit/6f465d52a76d4f6c9646179b20a05a28f4001ef8))
* **anchornavigation:** ariel PR Comments ([8e4c986](https://github.com/carawayhome/caraway-meridian/commit/8e4c98643793acef40bb3c1e4ce10dba1f5f0197))
* **anchornavigation:** update boxShadow Variant ([f5a3179](https://github.com/carawayhome/caraway-meridian/commit/f5a317919d8aa0b2ff75aa12acb53eed2b32081b))
* **anchornavigation:** update color theme for Pill Anchors and make sticky to LP ([51425d9](https://github.com/carawayhome/caraway-meridian/commit/51425d97fedd957950746d322a99da5813f6c9ca))
* **anchornavigation:** validate sticky nav ([830befb](https://github.com/carawayhome/caraway-meridian/commit/830befb9240ebfe75b4b8a5f910ba3503e6439ba))
* attempt not successful ([4dd674b](https://github.com/carawayhome/caraway-meridian/commit/4dd674b52d1fce3691553684c2b7445e8bc3f14e))
* **base.tsx:** bug |QMM|Featured product card module|double line ([46b130b](https://github.com/carawayhome/caraway-meridian/commit/46b130b27d7901c884b6e67b0a8253351bd7b848))
* breaking into multiple layouts ([74aaccb](https://github.com/carawayhome/caraway-meridian/commit/74aaccb8644df8f5f9d1cd00d86c8ed08c9280a5))
* build error ([40757cc](https://github.com/carawayhome/caraway-meridian/commit/40757cc05bcbdddfeed1747c09d3f0811584c321))
* build errors ([6e051e8](https://github.com/carawayhome/caraway-meridian/commit/6e051e8f1e3aaaac7186a98e6dac0538cc15314d))
* build link ([f2e30bf](https://github.com/carawayhome/caraway-meridian/commit/f2e30bf40e29b659fa82138df0f8f345110e17b9))
* build link erorrs ([3b5b355](https://github.com/carawayhome/caraway-meridian/commit/3b5b355ddcfcffa7e529f4e361fdadf182a419d0))
* build? ([c5821d1](https://github.com/carawayhome/caraway-meridian/commit/c5821d1dafb25fcaa8b218a1c0d7e07e6f447833))
* cart line item march promo messaging ([9150d1f](https://github.com/carawayhome/caraway-meridian/commit/9150d1fc2c6b9fbc95a612742afa6e95e93dda71))
* cleanup ([349a0dc](https://github.com/carawayhome/caraway-meridian/commit/349a0dcfd61443d57a148702937789fcd38f5069))
* click handler error console ([353cb63](https://github.com/carawayhome/caraway-meridian/commit/353cb6304d6f4f2ec44a461b7276cd43721b70be))
* debugging ([5b03ac3](https://github.com/carawayhome/caraway-meridian/commit/5b03ac3747617c802c331f1ae720d60bc67ba53e))
* **Dialog:** add zpattern layout 3 and fix dialog modals ([2dcf87e](https://github.com/carawayhome/caraway-meridian/commit/2dcf87e704730ba2b25501f3b4fbb75774d981fe))
* **Dialogs:** add spacing and lint issues ([b48899f](https://github.com/carawayhome/caraway-meridian/commit/b48899fa684866206de9e3e4a6b93b3df91ebc27))
* **Dialog:** ui improves by QA ([1b7bcbe](https://github.com/carawayhome/caraway-meridian/commit/1b7bcbe52c4513c8058f6ee0571719feaf6a2b37))
* draft ([d8442f7](https://github.com/carawayhome/caraway-meridian/commit/d8442f7be6ef5f337636fde2740f3344812ca7ae))
* draft 1 of tooltip update ([d881656](https://github.com/carawayhome/caraway-meridian/commit/d88165696ffcc66c611a870900ae52f4503ea4d9))
* enable march promo minicart background color ([fd24322](https://github.com/carawayhome/caraway-meridian/commit/fd243225e1f2247c3ee2c48674aae2b972bc6e47))
* error checking saw a bug ([4f3cc90](https://github.com/carawayhome/caraway-meridian/commit/4f3cc908b5a5a27aa5b0cd00d5dfb1c25dd8a6a3))
* error logs ([68ba9d9](https://github.com/carawayhome/caraway-meridian/commit/68ba9d9db6df38733067af4a1795d3f0636c2843))
* extract collections page to static page ([e43fc7a](https://github.com/carawayhome/caraway-meridian/commit/e43fc7a1c236104332d020ea5907f2f67f446674))
* featured product module ab test ([9c59f0a](https://github.com/carawayhome/caraway-meridian/commit/9c59f0ab8444bde75d57463913691c5c628f814f))
* **FetchProducts:** ncrease query limit ([4da6db3](https://github.com/carawayhome/caraway-meridian/commit/4da6db358e06a1fd334e84703f7399d16f8ac759))
* first draft for eci sms program form ([7402f22](https://github.com/carawayhome/caraway-meridian/commit/7402f220582578d8396f04ec74db022b4a300a67))
* generated JSON ([d5493fa](https://github.com/carawayhome/caraway-meridian/commit/d5493fa1f6a5a0065d3fccd002db22494bee8a19))
* got segment to work again ([a9a973c](https://github.com/carawayhome/caraway-meridian/commit/a9a973cc64a25dfe0728465b21a8cdb519b8ee95))
* **hero:** hero Budgerd Details - Mini Mega ([803fe27](https://github.com/carawayhome/caraway-meridian/commit/803fe2702e517050c72d4b5ebfd6180f2e51d2a5))
* init QMM promo setup ([e8f99e6](https://github.com/carawayhome/caraway-meridian/commit/e8f99e63262c428b9484826b921d5b97143d0a22))
* initial render for content containers ([c85dbbc](https://github.com/carawayhome/caraway-meridian/commit/c85dbbc89de05e66da6cdb15098db7d6ef710bae))
* load default OOS variants at build time ([7f0f083](https://github.com/carawayhome/caraway-meridian/commit/7f0f083a079098a7b1e08f5b35d1dd66b0a14a31))
* make sure all perks icons can be full width ([44fe48d](https://github.com/carawayhome/caraway-meridian/commit/44fe48d1f018197add6dc6264865d2ab2991d966))
* makes redundant segment calls gone ([ba255f1](https://github.com/carawayhome/caraway-meridian/commit/ba255f1b24876e334541529d960834caebc9a83d))
* making form background image look less stretched horizontally ([cbb2fec](https://github.com/carawayhome/caraway-meridian/commit/cbb2fecc86868959b822c0c6fc6b72adc63a70ce))
* maybe it works on build ([cb66a48](https://github.com/carawayhome/caraway-meridian/commit/cb66a4874a3bff57dcd17c9ab391a2db32545f5f))
* might have to bring back the cart checker ([631dbf9](https://github.com/carawayhome/caraway-meridian/commit/631dbf9defbde13e787a967736387ebef3d2b42a))
* minicart perks - add special styles for the march promo ([0e10344](https://github.com/carawayhome/caraway-meridian/commit/0e10344c146cdb6a703e5c7da463386a7ca8585b))
* minor style adjustments ([b3d3dbf](https://github.com/carawayhome/caraway-meridian/commit/b3d3dbf307f61e28764e2dd395daade4879ad704))
* mobile potentially works now ([595f81a](https://github.com/carawayhome/caraway-meridian/commit/595f81aa4bc36b163f52c37e1ee200db0e8a15fa))
* more qa ([4f817a1](https://github.com/carawayhome/caraway-meridian/commit/4f817a1628b3db63f69e99da6a70a31c814b4e67))
* new badge callout ([b73fa1e](https://github.com/carawayhome/caraway-meridian/commit/b73fa1e41292ae55350c22fd335be1f177a81f2c))
* price override in product cards and mini details ([97629c7](https://github.com/carawayhome/caraway-meridian/commit/97629c7271d0e2ae3a40e159f8a859f35daa9826))
* product card - sort swatches in stock first and update default to always in stock swatch ([79a2f82](https://github.com/carawayhome/caraway-meridian/commit/79a2f82b5783a805b40b9d0dd61eafd9ab902879))
* **ProductPageSections:** increase the limit for blocks ([b186d16](https://github.com/carawayhome/caraway-meridian/commit/b186d16646c900e642131b8fe473c500c018cde9))
* promotional tab new layout ([2f220d9](https://github.com/carawayhome/caraway-meridian/commit/2f220d9ca043d842f9516da7ed66d551476163b1))
* q/a change requests ([7ade03e](https://github.com/carawayhome/caraway-meridian/commit/7ade03e37e7f108979b3e283dabf270659f15dea))
* q/a changes, adding media to extractor ([e90614a](https://github.com/carawayhome/caraway-meridian/commit/e90614ab745d3ab1ab9b6d0a3dd67e97d780eff7))
* refactoring to make look better ([37def5e](https://github.com/carawayhome/caraway-meridian/commit/37def5ed5ff221681c6deb9c88a281ed12f89d39))
* **referpage:** add Missing Refer Widget to New Site ([a4132b4](https://github.com/carawayhome/caraway-meridian/commit/a4132b46b12b30fd3837622771c7885f300c0eda))
* refining layout ([2dd5f02](https://github.com/carawayhome/caraway-meridian/commit/2dd5f028bd4ad221f3fd2483f899bbc72293d3c4))
* remove redirect handling from home page template ([be37818](https://github.com/carawayhome/caraway-meridian/commit/be3781802d4579d6ee2b46615c91c46e33566989))
* removing console ([1d28a63](https://github.com/carawayhome/caraway-meridian/commit/1d28a63f06f11312c80caa725b7f797691aa844e))
* removing logs ([8b9de32](https://github.com/carawayhome/caraway-meridian/commit/8b9de329e7fa28bae7fe34d21334e5b13462a6cf))
* reverting fetchGraphQl to what it was always ([afdd078](https://github.com/carawayhome/caraway-meridian/commit/afdd0784be80f96463fcf4f304c8226ef75b963f))
* seems to work a bit ([1ccbe5a](https://github.com/carawayhome/caraway-meridian/commit/1ccbe5a1158a088f058fa30856db7328da8099c5))
* simple update ([526bbcc](https://github.com/carawayhome/caraway-meridian/commit/526bbcc2a6fde5c2a7e0100f7710a62d9419cdf5))
* **singleheroslider:** bug | 404 Page | Hero Layout 11 | Background Image is showing incorrectly ([06d756b](https://github.com/carawayhome/caraway-meridian/commit/06d756b117128b7b846cd1edd48351f986f87d93))
* **singleheroslider:** bug |QMM|Home page layout 8 mobile ([71eb48e](https://github.com/carawayhome/caraway-meridian/commit/71eb48ef9b490c46a3eef13db0b75f9838f984a5))
* size comparison chart optional third row ([85fa480](https://github.com/carawayhome/caraway-meridian/commit/85fa4802d325ed73ce723a8307119776021c8484))
* **SlideCards:** add 3 column and ux improves ([415726d](https://github.com/carawayhome/caraway-meridian/commit/415726d37cfdda4d90f005d1349ee0a3e9156483))
* **SlideCards:** all color and styles can be controlled by cms ([ca6e441](https://github.com/carawayhome/caraway-meridian/commit/ca6e44146cdd07005f955a4adaa0fe907423520d))
* **SlideCards:** back to animations ([3466293](https://github.com/carawayhome/caraway-meridian/commit/34662937a773226804dd3b97b28c4a1f96d609e8))
* **SLideCards:** full behavior ([3010fa3](https://github.com/carawayhome/caraway-meridian/commit/3010fa304fe13c93350c0aa7a9d22a55a3aa21ef))
* **SlideCards:** structure and behavior ([f04f5f6](https://github.com/carawayhome/caraway-meridian/commit/f04f5f6782a400d8ffa6dffcac4b7f093a743642))
* **SlideCards:** ui improves ([6e93eb9](https://github.com/carawayhome/caraway-meridian/commit/6e93eb9c49eef8fa1dcf71652f942be995e8cd83))
* slight improvement ([88decb1](https://github.com/carawayhome/caraway-meridian/commit/88decb11607df98e04cdfd8ff6b8ff980269483a))
* **splitassetmodule:** airtight Storage | PDP | Split Asset Module with rounded corners ([8f2dcdb](https://github.com/carawayhome/caraway-meridian/commit/8f2dcdb3113fe9ea1f8057f2a225d796e68c2f2a))
* tetris module - play/pause video inside dialog ([4b00aec](https://github.com/carawayhome/caraway-meridian/commit/4b00aec8e2eb8decd1576c6f3b64e69472cde2fa))
* trying different approach ([d78e258](https://github.com/carawayhome/caraway-meridian/commit/d78e258ba6d1fe4c71bbe34a85ceb0b9eed7a4eb))
* type update ([9a56264](https://github.com/carawayhome/caraway-meridian/commit/9a5626470ed41a2e7d6a0ff2aca73a37bf88f51a))
* udating layouyt 2 styles ([8efbe2c](https://github.com/carawayhome/caraway-meridian/commit/8efbe2cf53ba8c9e69e77ac25f83a3e91a1489d2))
* updating console ([e45a41a](https://github.com/carawayhome/caraway-meridian/commit/e45a41ac75dca90f0ccd96cba284d0714c4c28d3))
* updating desktop link style ([074f4af](https://github.com/carawayhome/caraway-meridian/commit/074f4afd0aad7ec62d79a08b07c3c579f395e00f))
* updating form object coming soon specific ([d4c7816](https://github.com/carawayhome/caraway-meridian/commit/d4c78166ebf55bcbe085620270748e716d329294))
* updating grid styles to layout 4 ([83206a7](https://github.com/carawayhome/caraway-meridian/commit/83206a7980a93122d1b779f6ff2f9cd072f91572))
* updating h5 to h6 ([6ab06ec](https://github.com/carawayhome/caraway-meridian/commit/6ab06ece3332bc4626c9c08252afcb898e0d5351))
* updating mobile image ([250fbf8](https://github.com/carawayhome/caraway-meridian/commit/250fbf84539dfe6d2395110be961b3ec94174e8b))
* updating name ([5e2dff5](https://github.com/carawayhome/caraway-meridian/commit/5e2dff5f109ac189aa2b132178259316a4ec2e92))
* updating postscript keyword and terms and conditions ([68db5c2](https://github.com/carawayhome/caraway-meridian/commit/68db5c21287215e22f02c837f575633b504f32eb))
* updating success message ([64a3bda](https://github.com/carawayhome/caraway-meridian/commit/64a3bda728ce6c9c989c29354a04ccfcf52a17fe))
* updating the spot to add the padding ([a11853e](https://github.com/carawayhome/caraway-meridian/commit/a11853e2e89b7ccdba18543ca8b1a79270b90446))
* updating the way discounts are triggered by Contentful now ([2b4b086](https://github.com/carawayhome/caraway-meridian/commit/2b4b086e31bc577f95f2ba6cc40a5f06ebe3ed35))
* updating title by name instead ([0dfa24f](https://github.com/carawayhome/caraway-meridian/commit/0dfa24f9d5ea9c39a854fd0b1d97c5b402181463))
* updating to make the route work on build instead of manual triggering ([935cba6](https://github.com/carawayhome/caraway-meridian/commit/935cba6f26cd17263085eff4e9dfcc03abc294e8))
* use only available variants to find current variant ([e1a8663](https://github.com/carawayhome/caraway-meridian/commit/e1a8663311529872d8b3f2121f7c0c6fbed25cf0))
* video module ([900d92c](https://github.com/carawayhome/caraway-meridian/commit/900d92c7ee41695c66c089558304685c5af09dc2))
* works ([34ab703](https://github.com/carawayhome/caraway-meridian/commit/34ab703203b720f4ac7a93fa253762e64fb7ee05))
* works now seeing events on segment ([01332a0](https://github.com/carawayhome/caraway-meridian/commit/01332a0a9f84b43feacd955e779bde0b5365cba4))
* **zpatternlayout1:** update Layout 3 Structure ([59b764b](https://github.com/carawayhome/caraway-meridian/commit/59b764bd6d805cd3b3f319142126c5f729b6e8af))
* **zpattern:** mobie Image Aspect Ratio ([999c15c](https://github.com/carawayhome/caraway-meridian/commit/999c15c3a4e205c9a0ce20f005b1b1bb65ae4070))
* **zpattern:** qA Review Comments ([8606e64](https://github.com/carawayhome/caraway-meridian/commit/8606e6431123185a4756de2aca4ef7bf5b8486b1))
* **zpattern:** refactor mobile padding and image ([022364f](https://github.com/carawayhome/caraway-meridian/commit/022364fda2dded463447d92148e8b8fb12577b29))
* **zpattern:** z Pattern Split Module with linking ([0b4da8d](https://github.com/carawayhome/caraway-meridian/commit/0b4da8da7bd3e0254902344ce88613829acf66f0))


### Bug Fixes

* add properties for non standard AddToCart event ([b3b903a](https://github.com/carawayhome/caraway-meridian/commit/b3b903a4eede85b595aac0efc579b88a8c35c880))
* added anchor targeting to modules ([f3b5e6d](https://github.com/carawayhome/caraway-meridian/commit/f3b5e6de2cc071c50cf8ff9f2f0e00af2cb11390))
* addressing Emily's comments ([2dd6d2b](https://github.com/carawayhome/caraway-meridian/commit/2dd6d2be84b405b3ce3057696c9501028af15d92))
* addressing ui overflow problem brought up by neer ([b7848ef](https://github.com/carawayhome/caraway-meridian/commit/b7848ef660cfd9373ae6b7766b847a1e4e140efb))
* adjusted the mobile text description centering as well ([7592ff3](https://github.com/carawayhome/caraway-meridian/commit/7592ff375b43a041fc833434476c6834743b2587))
* **anatomy.tsx:** bug |Anatomy Module | Mobile copy spilling over ([510fa16](https://github.com/carawayhome/caraway-meridian/commit/510fa16d4b29316c6fd01fd2abe23010d6807ac0))
* better detecting detaulf variant on PDP ([143ad0a](https://github.com/carawayhome/caraway-meridian/commit/143ad0a0c025be9b2517f02c525aa101a0db6fa2))
* better error handling when fetching availability ([3091278](https://github.com/carawayhome/caraway-meridian/commit/3091278a16005b139b7ac31022235d99c9d4a3f7))
* build ([8307348](https://github.com/carawayhome/caraway-meridian/commit/830734871f5507df0a4316f60aaed9f47d434311))
* build ([8ef1bec](https://github.com/carawayhome/caraway-meridian/commit/8ef1bec80d17f7ed5dfa7202d0dfcd6718fb310c))
* build link ([b72ef4d](https://github.com/carawayhome/caraway-meridian/commit/b72ef4d482f646baf8c9a39685afe21335ac8d0a))
* conflicts ([680fa6b](https://github.com/carawayhome/caraway-meridian/commit/680fa6bad8ad47d2c15a5ff7651a8dbfe205edee))
* conflicts ([70a2dc9](https://github.com/carawayhome/caraway-meridian/commit/70a2dc9ec3bd7b34410cced7e749e17a56866211))
* conflicts ([e80fbef](https://github.com/carawayhome/caraway-meridian/commit/e80fbef23e6c87a6260deb1592db595d37308df3))
* double render of breaks during entry link rendering ([9af54e6](https://github.com/carawayhome/caraway-meridian/commit/9af54e662ae923f2886b880b64a74db224c9648e))
* errors and update dialogs ([6c79783](https://github.com/carawayhome/caraway-meridian/commit/6c797830ec5bcb20b3b0b1c165235d58a55139d4))
* eslint error for builds ([b268411](https://github.com/carawayhome/caraway-meridian/commit/b2684110b157ae30de1c994228a8cb5c40e5dd06))
* eslint errors ([a66d744](https://github.com/carawayhome/caraway-meridian/commit/a66d7446cefaa0a41e4423fab183093afecd1e79))
* filter out nulls from collection products list ([b5c4b52](https://github.com/carawayhome/caraway-meridian/commit/b5c4b528ea1cc64045942aded2f9348b84d2f6e9))
* fixed header wrapping on desktop ([fbd18ad](https://github.com/carawayhome/caraway-meridian/commit/fbd18add8eb641016d504e33e39c4d547f2991e6))
* fixing toggles logic and some UI requests ([ad8fb5b](https://github.com/carawayhome/caraway-meridian/commit/ad8fb5beaad8b83baee39fdb9f035096fc599f6a))
* glass lids swatches was defaulting to the first swatch all the time ([a05759b](https://github.com/carawayhome/caraway-meridian/commit/a05759b26d5547d4d01e487fd692878607566ca2))
* **hero:** hero Update CTA Theme ([f38e25d](https://github.com/carawayhome/caraway-meridian/commit/f38e25de7b3cc7a96786572331e1eebd2a9c8642))
* **Hero:** play pause bug ([d9c717f](https://github.com/carawayhome/caraway-meridian/commit/d9c717f807bcd64b5bdbf543e44de83196384591))
* increase product limit in pdp generateStaticParams ([695247e](https://github.com/carawayhome/caraway-meridian/commit/695247e1db2ac8b012fe15e5e17855096e949873))
* increase product limit to improve data fetching ([5122091](https://github.com/carawayhome/caraway-meridian/commit/51220910f2a48cea02fcfc1f2250bc94091ab1c0))
* lint ([bbfb59f](https://github.com/carawayhome/caraway-meridian/commit/bbfb59fe19c6941ff8cd1f6c1a1dea8faca9273e))
* lint and types ([0a9e6f2](https://github.com/carawayhome/caraway-meridian/commit/0a9e6f29afd4213cc2bb322a204b65d609f42c5f))
* linter error ([00fc882](https://github.com/carawayhome/caraway-meridian/commit/00fc88285ed30b84ce93ee789edd6ac2c7cefccf))
* merge ([a02fc26](https://github.com/carawayhome/caraway-meridian/commit/a02fc2638a1b7332812d81bf1889f845942c2646))
* merge ([dbe87c7](https://github.com/carawayhome/caraway-meridian/commit/dbe87c7a61b7314651171dbc4a069bfa1d42f4d4))
* mini details component small CSS updates QA ([4f8bd94](https://github.com/carawayhome/caraway-meridian/commit/4f8bd946063a602c905b391f3eae8b383ae2dfaf))
* minicart perks promo background ([a7b813e](https://github.com/carawayhome/caraway-meridian/commit/a7b813ec1a5f66bf14ca7131d4fe304b4c6197e5))
* mobile CSS improvement ([eb8b6b9](https://github.com/carawayhome/caraway-meridian/commit/eb8b6b90f54e4e0a05a5320cb3d06ee394216b30))
* **okendorefer:** remove setLoaded ([4c20336](https://github.com/carawayhome/caraway-meridian/commit/4c2033640d5e7e2b1a52df54217e6e352a50e330))
* potential fix to the arrows ([d81097f](https://github.com/carawayhome/caraway-meridian/commit/d81097f03f9aa380acbf399ce3d18c84d1baaca1))
* prevent crash when no or wrong layout set ([4aeac6b](https://github.com/carawayhome/caraway-meridian/commit/4aeac6b9f99a23bef4ad5173e8f67749b1e3fe9d))
* prevent site crashing when shopify product is on draft ([0c34338](https://github.com/carawayhome/caraway-meridian/commit/0c34338fb21dc1e4541180b0af9876075f4c5209))
* promotional tabs was causing shifts on pdps on small mobile screens ([774287a](https://github.com/carawayhome/caraway-meridian/commit/774287acfc488ee661d422c256fee78cd0d24827))
* quick fix for variants defaulting to the first one ([3068b06](https://github.com/carawayhome/caraway-meridian/commit/3068b06b926d92bca3ff2d8082a6876ad8d29484))
* removed dyanamic styles, added color prop to Typograaphy ([32ef043](https://github.com/carawayhome/caraway-meridian/commit/32ef04358acf9a3b0d3f451ca316f3c4ee29b31d))
* removing console logs ([2671c12](https://github.com/carawayhome/caraway-meridian/commit/2671c12ea8b6fa8044fdbe185406e05fe2a81837))
* rendering only link childern ([d93321b](https://github.com/carawayhome/caraway-meridian/commit/d93321b28bd9326c8eb9bb3676792705949c8a45))
* reverting previous updates, adding theme values to parent ([e7cad29](https://github.com/carawayhome/caraway-meridian/commit/e7cad291e8a57c8fa895dcdc215fcd8ef94377cb))
* sanitize quantity selector slugs befo ([450a36a](https://github.com/carawayhome/caraway-meridian/commit/450a36a51be1d8c3e28917000b1c95f2b8d9bd8c))
* **singleheroslider:** update padding ([aaf0d76](https://github.com/carawayhome/caraway-meridian/commit/aaf0d76a79044952586cb15c6cde1a08d51e185e))
* solved the issue when a product was used in multiple toggles ([b884915](https://github.com/carawayhome/caraway-meridian/commit/b88491525181003017a01c098039c8b7f1dfaad0))
* spacing ([57be895](https://github.com/carawayhome/caraway-meridian/commit/57be8959b6786821c29e9ce54a21ca51d2311deb))
* **splitAccordion:** add max height for desktop images ([05e964b](https://github.com/carawayhome/caraway-meridian/commit/05e964b951c0563252635e15aafa3e84f398d2af))
* **splitassetmodule:** emily QA Comments ([fc5034f](https://github.com/carawayhome/caraway-meridian/commit/fc5034fdc8dd90ed50d63966201449b5e33ef192))
* **themecolors:** update rust theme colors ([abdb663](https://github.com/carawayhome/caraway-meridian/commit/abdb663b75df1b790c09926a802273d10b9f2cc1))
* **themethemes.stylex:** add new theme Lavender Dark ([62fae44](https://github.com/carawayhome/caraway-meridian/commit/62fae448f4c50014f550744df06e2f64c58e65fb))
* **themethemesstylex:** new Theme Lavender Burgundy ([fb8b9f7](https://github.com/carawayhome/caraway-meridian/commit/fb8b9f7efdd6db815c633483016d0e85f1ebaa4e))
* try hard coding in the h1 secondary large theme and see what she says ([bebb751](https://github.com/carawayhome/caraway-meridian/commit/bebb7512a10091a5986b50f0716cd61e9a4cd108))
* ts errpr ([42b7bdb](https://github.com/carawayhome/caraway-meridian/commit/42b7bdb058c3e1d05c9dcbe06aad14e1beebf210))
* typo and removing unused style ([cb6fe8c](https://github.com/carawayhome/caraway-meridian/commit/cb6fe8cb91e3e604ce0f0e23a63012167f06e6d1))
* ui update on form ([34721e6](https://github.com/carawayhome/caraway-meridian/commit/34721e63878895fae45013c99b95c88f4c9bec8d))
* ui updates again ([ebb8388](https://github.com/carawayhome/caraway-meridian/commit/ebb8388ad6551e16dcac5cc5705d3053830947c3))
* unify server and client default OOS state ([1ead328](https://github.com/carawayhome/caraway-meridian/commit/1ead328d5bbcc428d68cfe86b5d8bc37a0a384d1))
* update height of form page ([dc18f22](https://github.com/carawayhome/caraway-meridian/commit/dc18f22fecae6391342676a9646d4d7ce8de7211))
* update item_variant to item_variant_id and add item_category_id in axon formatters ([ea03c60](https://github.com/carawayhome/caraway-meridian/commit/ea03c60feb1eb8913ddfbf8b10551aa3f9b41e18))
* update PDP with first in-stock variant ([f629b57](https://github.com/carawayhome/caraway-meridian/commit/f629b5714bde87e90facc6b5e37c4e852f7552ae))
* updates IDs location and span ([6aee373](https://github.com/carawayhome/caraway-meridian/commit/6aee3730ca9f149553f2ed4c76d0d80a6c8408bb))
* updating featured product card module title typography to be h4 with h4 secondary theme ([e997295](https://github.com/carawayhome/caraway-meridian/commit/e9972953abcbb81c5a72048abee4b545148197a6))
* updating gift card values also ([fb0a3f9](https://github.com/carawayhome/caraway-meridian/commit/fb0a3f9e00b26bef1632cb5e81ae179de70be5b7))
* updating global styles for promo bar ([8595128](https://github.com/carawayhome/caraway-meridian/commit/85951284226e8b560be1e2bc6c7facea91f79504))
* updating header to h4 ([eda68b1](https://github.com/carawayhome/caraway-meridian/commit/eda68b13c2d825ddc0d7540b18db74d998c60383))
* updating keyword for the coming-soon form ([806e831](https://github.com/carawayhome/caraway-meridian/commit/806e831c00578b358c86a095a44db59938d47143))
* updating object desctructure location ([291a066](https://github.com/carawayhome/caraway-meridian/commit/291a06650a3844c31a09ed5b886973cfc92d278b))
* **valueprops:** update image size ([d661a57](https://github.com/carawayhome/caraway-meridian/commit/d661a5723d7a0505f1aba357752d04fbdc783d74))
* **WhatsIncluded:** images width ([6d48ab7](https://github.com/carawayhome/caraway-meridian/commit/6d48ab7ebdf826601a8f42aaad6dab8299da374e))
* **zpattern 3:** zPatter Layout 3 Content ([0431092](https://github.com/carawayhome/caraway-meridian/commit/04310925f73820c9f8e31521e9ced79a8979d29b))
* **zpatternlayout3:** extract Layout 3 to a separate file ([f4c7531](https://github.com/carawayhome/caraway-meridian/commit/f4c753173617877c8de5997e1af630f46cb843a3))
* **zpatternlayout3:** update CTAs to work with Dialogs ([4ea9650](https://github.com/carawayhome/caraway-meridian/commit/4ea9650f57ebe9992e35473f6ae8e10865a56216))
* **zpattern:** update IsMobile logic ([1cfbd81](https://github.com/carawayhome/caraway-meridian/commit/1cfbd81fbc60254dfceee8290768cbed9234e171))


### Miscellaneous Chores

* release notes ([c0f3a1c](https://github.com/carawayhome/caraway-meridian/commit/c0f3a1c9d025c0dedd6e815e5f524f5d2c609f94))

## [0.58.0](https://github.com/carawayhome/caraway-meridian/compare/v0.57.0...v0.58.0) (2025-02-21)


### Features

* adding contentful level checks for oos swatches ([fe2816b](https://github.com/carawayhome/caraway-meridian/commit/fe2816bbef5885f700d5b02fee91ab1035d175d5))


### Bug Fixes

* add properties for non standard AddToCart event ([b3b903a](https://github.com/carawayhome/caraway-meridian/commit/b3b903a4eede85b595aac0efc579b88a8c35c880))
* increase product limit to improve data fetching ([5122091](https://github.com/carawayhome/caraway-meridian/commit/51220910f2a48cea02fcfc1f2250bc94091ab1c0))
* updating global styles for promo bar ([8595128](https://github.com/carawayhome/caraway-meridian/commit/85951284226e8b560be1e2bc6c7facea91f79504))

## [0.57.0](https://github.com/carawayhome/caraway-meridian/compare/v0.56.1...v0.57.0) (2025-02-14)


### Features

* add new color themes for lavenderLight, violet, violetLight, and warmGrayExtraLight ([817d3a7](https://github.com/carawayhome/caraway-meridian/commit/817d3a7eba1d07cd88295a9722fea7224a1be767))
* adding link tracking to columnm links ([5d7f441](https://github.com/carawayhome/caraway-meridian/commit/5d7f4417bf405f4ef3c9bac1e3c8ed917782e710))
* adding navigation tracking to Mobile links ([ffcea36](https://github.com/carawayhome/caraway-meridian/commit/ffcea36757ba175525e7875ba654119e2e60df9b))
* adding tracking on Navigation Images ([44cdecb](https://github.com/carawayhome/caraway-meridian/commit/44cdecb4a0905a4a62381c44878a4c64b1b1bf7e))
* adding tracking to feature image links ([d5d667b](https://github.com/carawayhome/caraway-meridian/commit/d5d667bb52b0330348904b08964e32c6a0f78e17))
* adding tracking to Navigation Image Product name and description links ([a80de82](https://github.com/carawayhome/caraway-meridian/commit/a80de82d77af90be55c17665198305dc039adb24))
* **anchornavigation:** ariel PR Comments ([8e4c986](https://github.com/carawayhome/caraway-meridian/commit/8e4c98643793acef40bb3c1e4ce10dba1f5f0197))
* **anchornavigation:** update boxShadow Variant ([f5a3179](https://github.com/carawayhome/caraway-meridian/commit/f5a317919d8aa0b2ff75aa12acb53eed2b32081b))
* **anchornavigation:** update color theme for Pill Anchors and make sticky to LP ([51425d9](https://github.com/carawayhome/caraway-meridian/commit/51425d97fedd957950746d322a99da5813f6c9ca))
* **anchornavigation:** validate sticky nav ([830befb](https://github.com/carawayhome/caraway-meridian/commit/830befb9240ebfe75b4b8a5f910ba3503e6439ba))
* categorizing Desktop Navigation Links ([7da1f83](https://github.com/carawayhome/caraway-meridian/commit/7da1f839f77c1d0c8145b0e395e780469da3b42a))
* confetti and trigger ([9499235](https://github.com/carawayhome/caraway-meridian/commit/94992354cd26f0e43766215c9928fea90274c372))
* **HowWeCompare:** use js to sync height of row ([ac09f72](https://github.com/carawayhome/caraway-meridian/commit/ac09f728923ecc3d8dc8b88ebe9ee35d44ebdb61))
* **Meilisearch:** comment analytics ([f4b227e](https://github.com/carawayhome/caraway-meridian/commit/f4b227e0f814a4f2c9338d96b95441d7d5645a69))
* passing titles to desktop links ([460c5e2](https://github.com/carawayhome/caraway-meridian/commit/460c5e2e6a7fc9fded742ad3a8d616d8dabead79))
* refacotoring custom navigation links, contentful integration, heart animation component ([7ce6a43](https://github.com/carawayhome/caraway-meridian/commit/7ce6a43421782fcd47fcf0ffa97c4dc2fa9bf59d))
* updating navigation query to accept animationTypes ([7a1b5e9](https://github.com/carawayhome/caraway-meridian/commit/7a1b5e97d54f68a75435421356d6bb4a62b82a7d))


### Bug Fixes

* **anchornavigationcollection:** bug | PLP | Anchor Link Global Theme Issue ([238eb50](https://github.com/carawayhome/caraway-meridian/commit/238eb50110cffc1f274d789b209db05cde291045))
* **debounce:** type ([77a7396](https://github.com/carawayhome/caraway-meridian/commit/77a73961dc2ca2483c1813a6c72e74c42ee4287e))
* eslint errors ([a66d744](https://github.com/carawayhome/caraway-meridian/commit/a66d7446cefaa0a41e4423fab183093afecd1e79))
* fixing toggles logic and some UI requests ([ad8fb5b](https://github.com/carawayhome/caraway-meridian/commit/ad8fb5beaad8b83baee39fdb9f035096fc599f6a))
* mini details component small CSS updates QA ([4f8bd94](https://github.com/carawayhome/caraway-meridian/commit/4f8bd946063a602c905b391f3eae8b383ae2dfaf))
* old desktop prop toggle would not work with the newer updates ([768afd3](https://github.com/carawayhome/caraway-meridian/commit/768afd368c57670ab573740086e9935b9a3e18df))
* **progress:** missed edge case for ab test delist ([21c0ca8](https://github.com/carawayhome/caraway-meridian/commit/21c0ca8f6a954425131bdaace778611af1ffeecf))
* promotional tabs was causing shifts on pdps on small mobile screens ([774287a](https://github.com/carawayhome/caraway-meridian/commit/774287acfc488ee661d422c256fee78cd0d24827))
* removing unused style ([97f4c92](https://github.com/carawayhome/caraway-meridian/commit/97f4c92bcbe8fea9f40548d11059adb1aeee64b0))
* solved the issue when a product was used in multiple toggles ([b884915](https://github.com/carawayhome/caraway-meridian/commit/b88491525181003017a01c098039c8b7f1dfaad0))

## [0.56.1](https://github.com/carawayhome/caraway-meridian/compare/v0.56.0...v0.56.1) (2025-02-06)


### Bug Fixes

* **CompareCharts:** prevent from crash withou callout ([9fb333c](https://github.com/carawayhome/caraway-meridian/commit/9fb333c278e464e9cdda7718d6fa67b010d1842f))
* **HowWeCompare:** align title to center ([57c079f](https://github.com/carawayhome/caraway-meridian/commit/57c079fe549c53248f629acf1d95daec391a924c))

## [0.56.0](https://github.com/carawayhome/caraway-meridian/compare/v0.55.0...v0.56.0) (2025-02-05)


### Features

* **HowWeCompare:** add custom title ([cd7ce91](https://github.com/carawayhome/caraway-meridian/commit/cd7ce91ce3cdb8fbca27be8a317529bd3a9bbbdd))
* **LazyLoad:** remove lazy load from images ([2cb9f68](https://github.com/carawayhome/caraway-meridian/commit/2cb9f6875a1d40dc8e2cdc6a06b1c64290434a17))
* **progress:** adding borders to accordion split images ([8d247a5](https://github.com/carawayhome/caraway-meridian/commit/8d247a59f4a8748d70fe2e55b5298f767e52a188))
* **progress:** adding it to mobile ([313c851](https://github.com/carawayhome/caraway-meridian/commit/313c851f89eab9da36295073abcae40f30e763b3))
* **progress:** adjusting dots positioning ([4e16ef8](https://github.com/carawayhome/caraway-meridian/commit/4e16ef8459db2595ea160d25b6177e8b2842aa06))
* **progress:** dots migrated to below the gallery ([fa555a3](https://github.com/carawayhome/caraway-meridian/commit/fa555a3f202e141e399ad9c7d380aa4a0322b68d))
* **progress:** h4 ([219c669](https://github.com/carawayhome/caraway-meridian/commit/219c669cb857258f2f133a7c9c053cdddd30091b))
* **progress:** having toggle v2 every product ([e0789c2](https://github.com/carawayhome/caraway-meridian/commit/e0789c2ec5b22237b585672f60b939efc12b1150))
* **progress:** magic numbers ([8b85c8f](https://github.com/carawayhome/caraway-meridian/commit/8b85c8f379426939cc78e130fe05b4443d1ad019))
* **progress:** product gallery dots css update ([dc583b0](https://github.com/carawayhome/caraway-meridian/commit/dc583b0b34d261c36d5c89074d4ef54e6ef6308e))
* **progress:** reduced spacing ([e414797](https://github.com/carawayhome/caraway-meridian/commit/e414797163d9fa33864f2a6675442eb1cb4573a1))
* **progress:** typography h4 has a line maybe ([9a68861](https://github.com/carawayhome/caraway-meridian/commit/9a6886123654140de34ed3d6870048a881c1ef0a))
* **progress:** update copy ([1bbf36a](https://github.com/carawayhome/caraway-meridian/commit/1bbf36aa9fa1793adc0955830c4789d1a6eeca7c))
* **progress:** updating whats included header font size ([63071a9](https://github.com/carawayhome/caraway-meridian/commit/63071a987a60a88a5fa22dd4080b2f4e1c3657c7))
* **progress:** v2 toggles ([e57c7c2](https://github.com/carawayhome/caraway-meridian/commit/e57c7c20927b7c988d2317858e54eccbf88e1810))
* **site:** add noindex meta controlled by cms ([c8a574c](https://github.com/carawayhome/caraway-meridian/commit/c8a574cc2a48d9c359754c6db57a0a9b54fd299e))


### Bug Fixes

* adding breaks in place of new line ([2c16606](https://github.com/carawayhome/caraway-meridian/commit/2c1660672dfd7b7b5f0d5481c5b1d1b6f834a6d0))
* **CompareChart:** alignment ([32cbb66](https://github.com/carawayhome/caraway-meridian/commit/32cbb666dc3898b13f64901f31e690a1fdeaf3e9))
* **component:** chore | Minis BW FS | PDP | Save with Set - Design Update ([9150d7e](https://github.com/carawayhome/caraway-meridian/commit/9150d7e222a51d996b7a0a09564bd8c9362a893c))
* **extractor:** save with Set - Pulling in backend data incorrectly ([16428d3](https://github.com/carawayhome/caraway-meridian/commit/16428d3e3c7cb584ca57ebdfa12457515eb0bd9c))
* **savewithaset:** fix CTA hover ([110b9dc](https://github.com/carawayhome/caraway-meridian/commit/110b9dcd7720728b87f715d879c4aaf1d707760e))
* **Toggle:** fix new way to handle changes ([7fb9c2d](https://github.com/carawayhome/caraway-meridian/commit/7fb9c2d2bbd29eb256080df1bd2ed5f30e94de8c))
* **Toggle:** merge conflicts fix ([826313e](https://github.com/carawayhome/caraway-meridian/commit/826313ec1a601c581895a7288c73a26dfaddc477))
* **Toggle:** spacing ([4fc9f96](https://github.com/carawayhome/caraway-meridian/commit/4fc9f96ce6daea26f43c03a97bf1a9464c4fbd74))
* **Toggle:** type ([09b70de](https://github.com/carawayhome/caraway-meridian/commit/09b70de8aa3af9c843d5397d28d2e07d1083140b))

## [0.55.0](https://github.com/carawayhome/caraway-meridian/compare/v0.54.1...v0.55.0) (2025-01-30)


### Features

* **Scripts:** add Neon script component for analytics ([f1704bd](https://github.com/carawayhome/caraway-meridian/commit/f1704bd85a67f803c7e4d69575e8b34e5a98377e))
* **WhatsIncluded:** add v3 ([f890167](https://github.com/carawayhome/caraway-meridian/commit/f890167a80bfbe68090ae3f037378f5267c8584e))


### Bug Fixes

* **Compare:** prevet crash page when asset is missing ([df28a24](https://github.com/carawayhome/caraway-meridian/commit/df28a24fafa192e593c2cfd5eb81b35d5bfd2d58))
* **RedPrice:** red price on test AB comming from cms ([01a0912](https://github.com/carawayhome/caraway-meridian/commit/01a09128801ca9e65bdec603509d9dfbb5549ae6))
* **SeoMetaTags:** fix wrong slug params ([289a9d8](https://github.com/carawayhome/caraway-meridian/commit/289a9d865f0a8439a8814f51ad7bea98ec344ef3))

## [0.54.1](https://github.com/carawayhome/caraway-meridian/compare/v0.54.0...v0.54.1) (2025-01-28)


### Bug Fixes

* **HowWeCompare:** build and query ([cfa7f1b](https://github.com/carawayhome/caraway-meridian/commit/cfa7f1bd4ffcd30543ed06e98fae35af61560210))
* **HowWeCompareCharts:** conflicts ([a1a097e](https://github.com/carawayhome/caraway-meridian/commit/a1a097e750bdc0cd93c9bea4d445f3e31c8db259))
* insert br for new lines in rich text renderer ([e99b37a](https://github.com/carawayhome/caraway-meridian/commit/e99b37a393ef88ac1e167550d3fcaf4abfd3fd0b))
* **redPrice:** red price controlled by cms and keep the cross-out ([f2be107](https://github.com/carawayhome/caraway-meridian/commit/f2be107b2a439b51b038e511b244515a363afb3e))
* split accordion positioning and video ([9c3dcc3](https://github.com/carawayhome/caraway-meridian/commit/9c3dcc35f1cad57d00e978173e2e0ea463df6a36))
* **TestPage:** merge ([e23ed38](https://github.com/carawayhome/caraway-meridian/commit/e23ed38ed6b74f7a435538e7d3a4a6bf96fbe94f))

## [0.54.0](https://github.com/carawayhome/caraway-meridian/compare/v0.53.0...v0.54.0) (2025-01-27)


### Features

* add "select" prefix to swatches collection name ([0b64df6](https://github.com/carawayhome/caraway-meridian/commit/0b64df612552463e69dc7cc66ca93542ea49281b))
* add collection name to the price diff message ([874f2cc](https://github.com/carawayhome/caraway-meridian/commit/874f2ccd5ca1e1915c1fc5be6d1ecd65f3ed4965))
* add double cards layout to recommender slider ([a953a74](https://github.com/carawayhome/caraway-meridian/commit/a953a74a9a8e55a4904afdabd59a62cf2f9affa1))
* add wheel gestrures to swatches dropdown slider ([f1d37c9](https://github.com/carawayhome/caraway-meridian/commit/f1d37c997c67ee4bd5d7999d8b9525ccb31afd0a))
* adding afterpay block and logic ([5c7119b](https://github.com/carawayhome/caraway-meridian/commit/5c7119bbf02ed5877734e24787ed475760913588))
* adding container queries to parent ([25d1b00](https://github.com/carawayhome/caraway-meridian/commit/25d1b0077cdde9dc802db57e6ea0b2f8cbe7353d))
* bring back force variant feature ([08d7390](https://github.com/carawayhome/caraway-meridian/commit/08d7390cb778bc0ee0e6a8050b582306e7b98ec7))
* compare button to product groups ([ed08feb](https://github.com/carawayhome/caraway-meridian/commit/ed08feb4ce1e8c5990d3a3e8aa6c6cc289d8d795))
* exclude banned and in-cart products from recommender slider ([d50e694](https://github.com/carawayhome/caraway-meridian/commit/d50e694e808fcf5f23377ca62645e7f08e36c424))
* force variant feature ([3242126](https://github.com/carawayhome/caraway-meridian/commit/3242126598920052ae2ef37fa495fb8b6312f4dd))
* increase gallery limit from 6 to 10 in product fields query ([38ac859](https://github.com/carawayhome/caraway-meridian/commit/38ac859514a53196afef9e9c0aed84324a26824b))
* make add ons A/B testable ([dc41f36](https://github.com/carawayhome/caraway-meridian/commit/dc41f363e988e880236db8c4264f7cfc86c10e20))
* make new swatches under A/B test ([30e1e81](https://github.com/carawayhome/caraway-meridian/commit/30e1e81c9730e282e6cee5b2dc193db60c4211ab))
* prepare for swatches ATF test ([0aa09be](https://github.com/carawayhome/caraway-meridian/commit/0aa09be4ccc5b9fe030d686cb0295a8e0481d7b7))
* **ProductPrice:** back to compare at price ([7be23a8](https://github.com/carawayhome/caraway-meridian/commit/7be23a8c458234eef69d3c9d8dd409a14ce8fed8))
* **producttoggles:** feature | Toggle Component | Add "Title" Custom Label to Toggle ([5f4005f](https://github.com/carawayhome/caraway-meridian/commit/5f4005f44d25d998439022d8b9a8c75ec9458576))
* remove collection name from swatches price diff message ([01cb3c0](https://github.com/carawayhome/caraway-meridian/commit/01cb3c0583ae6cf389f83793dcff0706db7b7c24))
* updating spacing ([0dfdc15](https://github.com/carawayhome/caraway-meridian/commit/0dfdc154cba60a6b3463b503b0c8605402c0f8ae))
* updating wrapping ([3db00fe](https://github.com/carawayhome/caraway-meridian/commit/3db00fea060371d78dec47d9607e3e527ff1c24e))


### Bug Fixes

* add $ sign to difference price message ([0798ab9](https://github.com/carawayhome/caraway-meridian/commit/0798ab9dee3f54e54c8003418dfff37e7ca7a6fb))
* add blending mode to add on images ([91c1fd9](https://github.com/carawayhome/caraway-meridian/commit/91c1fd969ccfeef8a4e8aff95bfacf902bd1e6e9))
* adding assets path to ts config ([8f6167c](https://github.com/carawayhome/caraway-meridian/commit/8f6167c38b1a5f188a10ce8673ff9f55a473dd50))
* build error ([ef92780](https://github.com/carawayhome/caraway-meridian/commit/ef92780efa81d7bfd36a9e2a243283f913f1582e))
* clean unused element ([a441942](https://github.com/carawayhome/caraway-meridian/commit/a441942c9390ef090d63744a04df3767b3b05017))
* correct spelling of 'Est Ship Date' to 'Est. Ship Date' in LineItem and AddToCart components ([018503b](https://github.com/carawayhome/caraway-meridian/commit/018503b2980773b3838f4b3d91a5cfd7ac960a57))
* css updates ([1d74a7e](https://github.com/carawayhome/caraway-meridian/commit/1d74a7e2b0095b86907708fb8f8907265bb35f32))
* **ESD:** update estimated shipping date format from 'short' to 'full' ([349fc7a](https://github.com/carawayhome/caraway-meridian/commit/349fc7acc74b41281e3cd3a96cd1f25a930162b2))
* fix build errors ([40e7e3a](https://github.com/carawayhome/caraway-meridian/commit/40e7e3adc98c5868a3db5d6c8c8b64afb1388405))
* fix builds with onSale field ([b30dfaf](https://github.com/carawayhome/caraway-meridian/commit/b30dfafbb33f6fa1f30e9834c107dcd2403aad1d))
* fixing 0 displaying when title is not present ([1c7dd70](https://github.com/carawayhome/caraway-meridian/commit/1c7dd708514de04eed230f5ffffef4664d2d0c56))
* handle edge case where first variant is filtered out from dropdown ([671c93d](https://github.com/carawayhome/caraway-meridian/commit/671c93d2b6ff62ca8e3c3ed4a44c4f2cd1d6e480))
* height layout shifts in pdp swatches ([218d069](https://github.com/carawayhome/caraway-meridian/commit/218d069d967b025ece8fe3dccc32ed4afe8a28ad))
* improve click outside for hold in release out case ([928d6b9](https://github.com/carawayhome/caraway-meridian/commit/928d6b927bc7c56b543823a39710a9c1c2b1a053))
* make add on checkbox correct size ([b5e012b](https://github.com/carawayhome/caraway-meridian/commit/b5e012b0ac1ec0aececed5bb96afa370b855b076))
* make dropdown work when some variants are filtered out ([736cdb3](https://github.com/carawayhome/caraway-meridian/commit/736cdb3fee1a269543f5d12d7811c66027a4a70a))
* make sure this doesn't blow up in Safari ([f2eedaf](https://github.com/carawayhome/caraway-meridian/commit/f2eedaf3fea7acaec7b2eda03ea6ccebe8377dba))
* make swatches dropdown more accessible ([504a13d](https://github.com/carawayhome/caraway-meridian/commit/504a13d744b2ff4b852b630f627625be028aa22b))
* **marquee:** desktop Closer Content ([b8f6c4a](https://github.com/carawayhome/caraway-meridian/commit/b8f6c4a1cb0fc1baef58d896164c235e8f7ddcba))
* metrics access ([5604200](https://github.com/carawayhome/caraway-meridian/commit/560420044f4d4cdacaf3646ad5b8f8ea077626f3))
* moved script logic over to its own file ([24314b3](https://github.com/carawayhome/caraway-meridian/commit/24314b3cbed1725afb271522581c99b5587abbb4))
* p nested in p error ([734cbca](https://github.com/carawayhome/caraway-meridian/commit/734cbca96fcdc4c10158eb8c8e02e07a70be1a59))
* prevent swatches scroll to interfere with recommender swipe ([13cf93d](https://github.com/carawayhome/caraway-meridian/commit/13cf93d7934e681ba3f1edd551f7e624af50a15e))
* **productcard:** cTA Hover state ([f5e6276](https://github.com/carawayhome/caraway-meridian/commit/f5e62768340ae7a8ce62b35b7903b1048baa0702))
* **productcard:** fix Stroke CTA to 1 ([c763401](https://github.com/carawayhome/caraway-meridian/commit/c7634016587c1ec401cd17a809fb921e9ab8abf3))
* qa request, wrap span in button text ([84cd3b7](https://github.com/carawayhome/caraway-meridian/commit/84cd3b75ef4cb392d34cd5aea9991c07c8ebc7c0))
* **red price:** solve conflicts ([f2c1d7d](https://github.com/carawayhome/caraway-meridian/commit/f2c1d7d2ac95a488c5ef920c90e83fd3c7b90b81))
* **RedPrice:** build ([e5d7e67](https://github.com/carawayhome/caraway-meridian/commit/e5d7e67382b5d6bcd197cc2fc9d9c6dc05d8014a))
* **RedPrice:** build ([a3c6f64](https://github.com/carawayhome/caraway-meridian/commit/a3c6f64ac7aacf832a72bb1317264bae4500e641))
* **RedPrice:** build ([414f2e9](https://github.com/carawayhome/caraway-meridian/commit/414f2e9279e1a433d2573790a3ec3ec6065a8ab7))
* **RedPrice:** lint ([c6f0b46](https://github.com/carawayhome/caraway-meridian/commit/c6f0b46842f2966806a021e06ba515d81191babd))
* **RedPrice:** lint ([a46849e](https://github.com/carawayhome/caraway-meridian/commit/a46849ec898fa8b4aac6067553f39fe9dc809192))
* **RedPrice:** lint ([a0a8fdf](https://github.com/carawayhome/caraway-meridian/commit/a0a8fdf4c68622ac4a021d993dc2d293cc641df3))
* **RedPrice:** merge conflicts ([bbed5aa](https://github.com/carawayhome/caraway-meridian/commit/bbed5aaf210fb9adca08db976f3ee92ddd3aa868))
* remove add ons from test page ([d984c07](https://github.com/carawayhome/caraway-meridian/commit/d984c078c38c6cf71dc968492f719f88822c5f9e))
* removed unnecessary styles ([a24dd23](https://github.com/carawayhome/caraway-meridian/commit/a24dd23bd3d87b6f8ce560f9a32828f45a4693ae))
* show ESD even if no custom message ([9d7024b](https://github.com/carawayhome/caraway-meridian/commit/9d7024b3113fb96b84cc36118d385e85d6337ee0))
* small CSS update to swatches collection name ([bd11fcf](https://github.com/carawayhome/caraway-meridian/commit/bd11fcfe355dc73503700e263a11720e2bb51c39))
* some style updates ([a4bc6cc](https://github.com/carawayhome/caraway-meridian/commit/a4bc6ccbccd9db4ab3d485829aff5bd41b827cd2))
* support up to 15 variants in the recommender slider ([8a5086f](https://github.com/carawayhome/caraway-meridian/commit/8a5086f325838a20ce5d7e2f6030966d5d498b20))
* update imports ([7b13555](https://github.com/carawayhome/caraway-meridian/commit/7b135550c357d3afdc7709cf9044f3085ce745b4))
* update mobile add on details gap ([074f239](https://github.com/carawayhome/caraway-meridian/commit/074f239823994ea5995383075e4341271575117f))
* updated logic for disabled on cta ([55509e1](https://github.com/carawayhome/caraway-meridian/commit/55509e18800d7ad6a85d585d66748366c3623e30))
* updated mobile styles ([93ab992](https://github.com/carawayhome/caraway-meridian/commit/93ab992b036342cac377f531bb2edd82d8d7addc))
* use container queries to style recs slider ([86058e9](https://github.com/carawayhome/caraway-meridian/commit/86058e997c36abbc1b1c81525b46d424e9103f97))
* **WhatsIncluded:** selector ([8487d14](https://github.com/carawayhome/caraway-meridian/commit/8487d144275a26f7732ed2242a425ee67bbd1234))
* z-index add ons issue on mobile ([c9e7d50](https://github.com/carawayhome/caraway-meridian/commit/c9e7d50a4277a2a4704117616ac8fad6a48bd8a8))

## [0.53.0](https://github.com/carawayhome/caraway-meridian/compare/v0.52.0...v0.53.0) (2025-01-21)


### Features

* adding conditional on Callout container ([68a9970](https://github.com/carawayhome/caraway-meridian/commit/68a9970207ac84a9076f19b541ab7827f0120030))
* adding intersection observer hook and using hook on test ([dda4923](https://github.com/carawayhome/caraway-meridian/commit/dda4923aba9f42db71aefe13218e852fe653e225))
* adding max width on nav svg updating circl positioning ([7ad7826](https://github.com/carawayhome/caraway-meridian/commit/7ad7826bd4dbeba5a7084db7b27b0554d492451f))
* adding navigation testing logic ([8cb2ae1](https://github.com/carawayhome/caraway-meridian/commit/8cb2ae15a51394b3ce1a1a34b3fdad54eb82d2e8))
* adding navigation, and testing component header file ([09eff0f](https://github.com/carawayhome/caraway-meridian/commit/09eff0f52763d2e91152fc03ab995e49cc9c1f96))
* adding product gallery updates ([18f1356](https://github.com/carawayhome/caraway-meridian/commit/18f13568ecbf9d7b9d8e9981663d5f1ad0c908fb))
* **component:** cast Iron Launch | PDP | Table of Contents Bar Hover/Default State Part 2 ([f598fd2](https://github.com/carawayhome/caraway-meridian/commit/f598fd2a734384116d7d08cd3c4fa1368d080e4f))
* **progress:** adding on toggles ([e8e86d8](https://github.com/carawayhome/caraway-meridian/commit/e8e86d854977804131eb30af74d4c8efa8d48ae0))
* **progress:** address comments ([21e2031](https://github.com/carawayhome/caraway-meridian/commit/21e2031bc57256b5ad5aec145117966e252d96e3))
* **progress:** fix for toggle variants not updating correctly ([54af658](https://github.com/carawayhome/caraway-meridian/commit/54af6584b286075907e47e3881c969c644950080))
* **progress:** fixing build error ([5a6197c](https://github.com/carawayhome/caraway-meridian/commit/5a6197c70314bb5ec5c3c4a5c9f1abaec6b2a7a2))
* **progress:** fixing style const ([efa456c](https://github.com/carawayhome/caraway-meridian/commit/efa456c995b7357b044134b2b64af8553cb8aede))
* **progress:** forms images update ([b92ae9d](https://github.com/carawayhome/caraway-meridian/commit/b92ae9de44ab55b8bca15d5aec1538eca73057eb))
* **progress:** locking in via slugs ([aefd8f3](https://github.com/carawayhome/caraway-meridian/commit/aefd8f36332c3f7297cab6b46820174f2c271b80))
* **progress:** mobile swatches test first ([575bd13](https://github.com/carawayhome/caraway-meridian/commit/575bd131f53860567113dc683befc98ffe94f921))
* **progress:** new form update ([219ad8a](https://github.com/carawayhome/caraway-meridian/commit/219ad8a413b5f82f322f4e4e30a0e22d389e5b3c))
* **progress:** product gallery ([c010710](https://github.com/carawayhome/caraway-meridian/commit/c010710d35a04e8d414ad2a7c4f18f6036caec2a))
* **progress:** sending in the props ([ded25e9](https://github.com/carawayhome/caraway-meridian/commit/ded25e93d0c53e66639b1199c16ec46f8bb2d44a))
* **progress:** smaller image for desktop ([1244cdc](https://github.com/carawayhome/caraway-meridian/commit/1244cdc81b6d3a0cc611430c4629ec061a405cc3))
* **progress:** title and byline and toggles ([f6171bd](https://github.com/carawayhome/caraway-meridian/commit/f6171bdaaa7856cdb9c87199cf416a60a189a012))
* **progress:** title and byline updates ([c33d685](https://github.com/carawayhome/caraway-meridian/commit/c33d685b90de6d35e0203456f341b9004f8abd1d))
* **progress:** tooltip merge conflicts ([3c733bb](https://github.com/carawayhome/caraway-meridian/commit/3c733bb365830bf2e5f16cfa79e23a89892d2161))
* **progress:** tooltips ui update ([31e400d](https://github.com/carawayhome/caraway-meridian/commit/31e400d672b8723df83a285c4344fd912d4a2cd5))
* refactoring logic so it works with test ([3c1938f](https://github.com/carawayhome/caraway-meridian/commit/3c1938f9e1a3e4599c436e7ff1885205d33d7f78))
* showing callouts over slider on first image ([b7eb835](https://github.com/carawayhome/caraway-meridian/commit/b7eb8351913c3fa703ac7f1525e0e115d1e46686))
* showing callouts over slider on first image ([7877618](https://github.com/carawayhome/caraway-meridian/commit/78776189082dc945ea99667676d633d862909369))


### Bug Fixes

* add scrolling to mobile swatches picker when too many swatches ([ef6c48e](https://github.com/carawayhome/caraway-meridian/commit/ef6c48e8f90400841c857d48fdacc50ba0fb22b4))
* build error ([e9ff851](https://github.com/carawayhome/caraway-meridian/commit/e9ff8513e9d1b899008c7dacad26464395dac76c))
* **eslint:** update eslint error ([1787a06](https://github.com/carawayhome/caraway-meridian/commit/1787a06144983ecb38f995fde86d1194d88ca212))
* **hero 404 page:** fix Hero 404 Page Layout 11 ([0623a9c](https://github.com/carawayhome/caraway-meridian/commit/0623a9cc227e3440a80b195fde2464c1829b4c00))
* **pillfilter:** match Old Version ([c2c163a](https://github.com/carawayhome/caraway-meridian/commit/c2c163a12313463bfb1d6c4dc65832cfc8a0ae36))
* **pillfilter:** match Old Version Completely ([638f20c](https://github.com/carawayhome/caraway-meridian/commit/638f20c21d28fefb325369e07ec83450d2878d2e))
* **ProductToggle:** crash page when no groups ([15dc2b8](https://github.com/carawayhome/caraway-meridian/commit/15dc2b8f96db858c3b3ef3117981dd30f4b73b85))
* refactoring dynamic styles ([e5e8620](https://github.com/carawayhome/caraway-meridian/commit/e5e8620effde844f1b1003aef9a41f7449af1c40))
* removed callout logic for phase one ([27ac5e7](https://github.com/carawayhome/caraway-meridian/commit/27ac5e71f2e6fa6db5b87d569faabdd8fa5b3cfa))
* temporary fix for group items on product toggles ([0fa747c](https://github.com/carawayhome/caraway-meridian/commit/0fa747ca30da76d60b4c93e312224d853066a826))
* **themethemes.stylex lavender:** bug | Navy Button | Hover State Accessibility Issue ([6045b2c](https://github.com/carawayhome/caraway-meridian/commit/6045b2cc8b7a7fa978de7fa156d231c7a771a2cd))

## [0.53.0](https://github.com/carawayhome/caraway-meridian/compare/v0.52.0...v0.53.0) (2025-01-21)


### Features

* adding conditional on Callout container ([68a9970](https://github.com/carawayhome/caraway-meridian/commit/68a9970207ac84a9076f19b541ab7827f0120030))
* adding intersection observer hook and using hook on test ([dda4923](https://github.com/carawayhome/caraway-meridian/commit/dda4923aba9f42db71aefe13218e852fe653e225))
* adding max width on nav svg updating circl positioning ([7ad7826](https://github.com/carawayhome/caraway-meridian/commit/7ad7826bd4dbeba5a7084db7b27b0554d492451f))
* adding navigation testing logic ([8cb2ae1](https://github.com/carawayhome/caraway-meridian/commit/8cb2ae15a51394b3ce1a1a34b3fdad54eb82d2e8))
* adding navigation, and testing component header file ([09eff0f](https://github.com/carawayhome/caraway-meridian/commit/09eff0f52763d2e91152fc03ab995e49cc9c1f96))
* adding product gallery updates ([18f1356](https://github.com/carawayhome/caraway-meridian/commit/18f13568ecbf9d7b9d8e9981663d5f1ad0c908fb))
* **component:** cast Iron Launch | PDP | Table of Contents Bar Hover/Default State Part 2 ([f598fd2](https://github.com/carawayhome/caraway-meridian/commit/f598fd2a734384116d7d08cd3c4fa1368d080e4f))
* **progress:** adding on toggles ([e8e86d8](https://github.com/carawayhome/caraway-meridian/commit/e8e86d854977804131eb30af74d4c8efa8d48ae0))
* **progress:** address comments ([21e2031](https://github.com/carawayhome/caraway-meridian/commit/21e2031bc57256b5ad5aec145117966e252d96e3))
* **progress:** fix for toggle variants not updating correctly ([54af658](https://github.com/carawayhome/caraway-meridian/commit/54af6584b286075907e47e3881c969c644950080))
* **progress:** fixing build error ([5a6197c](https://github.com/carawayhome/caraway-meridian/commit/5a6197c70314bb5ec5c3c4a5c9f1abaec6b2a7a2))
* **progress:** fixing style const ([efa456c](https://github.com/carawayhome/caraway-meridian/commit/efa456c995b7357b044134b2b64af8553cb8aede))
* **progress:** forms images update ([b92ae9d](https://github.com/carawayhome/caraway-meridian/commit/b92ae9de44ab55b8bca15d5aec1538eca73057eb))
* **progress:** locking in via slugs ([aefd8f3](https://github.com/carawayhome/caraway-meridian/commit/aefd8f36332c3f7297cab6b46820174f2c271b80))
* **progress:** mobile swatches test first ([575bd13](https://github.com/carawayhome/caraway-meridian/commit/575bd131f53860567113dc683befc98ffe94f921))
* **progress:** new form update ([219ad8a](https://github.com/carawayhome/caraway-meridian/commit/219ad8a413b5f82f322f4e4e30a0e22d389e5b3c))
* **progress:** product gallery ([c010710](https://github.com/carawayhome/caraway-meridian/commit/c010710d35a04e8d414ad2a7c4f18f6036caec2a))
* **progress:** sending in the props ([ded25e9](https://github.com/carawayhome/caraway-meridian/commit/ded25e93d0c53e66639b1199c16ec46f8bb2d44a))
* **progress:** smaller image for desktop ([1244cdc](https://github.com/carawayhome/caraway-meridian/commit/1244cdc81b6d3a0cc611430c4629ec061a405cc3))
* **progress:** title and byline and toggles ([f6171bd](https://github.com/carawayhome/caraway-meridian/commit/f6171bdaaa7856cdb9c87199cf416a60a189a012))
* **progress:** title and byline updates ([c33d685](https://github.com/carawayhome/caraway-meridian/commit/c33d685b90de6d35e0203456f341b9004f8abd1d))
* **progress:** tooltip merge conflicts ([3c733bb](https://github.com/carawayhome/caraway-meridian/commit/3c733bb365830bf2e5f16cfa79e23a89892d2161))
* **progress:** tooltips ui update ([31e400d](https://github.com/carawayhome/caraway-meridian/commit/31e400d672b8723df83a285c4344fd912d4a2cd5))
* refactoring logic so it works with test ([3c1938f](https://github.com/carawayhome/caraway-meridian/commit/3c1938f9e1a3e4599c436e7ff1885205d33d7f78))
* showing callouts over slider on first image ([b7eb835](https://github.com/carawayhome/caraway-meridian/commit/b7eb8351913c3fa703ac7f1525e0e115d1e46686))
* showing callouts over slider on first image ([7877618](https://github.com/carawayhome/caraway-meridian/commit/78776189082dc945ea99667676d633d862909369))


### Bug Fixes

* add scrolling to mobile swatches picker when too many swatches ([ef6c48e](https://github.com/carawayhome/caraway-meridian/commit/ef6c48e8f90400841c857d48fdacc50ba0fb22b4))
* build error ([e9ff851](https://github.com/carawayhome/caraway-meridian/commit/e9ff8513e9d1b899008c7dacad26464395dac76c))
* **eslint:** update eslint error ([1787a06](https://github.com/carawayhome/caraway-meridian/commit/1787a06144983ecb38f995fde86d1194d88ca212))
* **hero 404 page:** fix Hero 404 Page Layout 11 ([0623a9c](https://github.com/carawayhome/caraway-meridian/commit/0623a9cc227e3440a80b195fde2464c1829b4c00))
* **pillfilter:** match Old Version ([c2c163a](https://github.com/carawayhome/caraway-meridian/commit/c2c163a12313463bfb1d6c4dc65832cfc8a0ae36))
* **pillfilter:** match Old Version Completely ([638f20c](https://github.com/carawayhome/caraway-meridian/commit/638f20c21d28fefb325369e07ec83450d2878d2e))
* **ProductToggle:** crash page when no groups ([15dc2b8](https://github.com/carawayhome/caraway-meridian/commit/15dc2b8f96db858c3b3ef3117981dd30f4b73b85))
* refactoring dynamic styles ([e5e8620](https://github.com/carawayhome/caraway-meridian/commit/e5e8620effde844f1b1003aef9a41f7449af1c40))
* removed callout logic for phase one ([27ac5e7](https://github.com/carawayhome/caraway-meridian/commit/27ac5e71f2e6fa6db5b87d569faabdd8fa5b3cfa))
* temporary fix for group items on product toggles ([0fa747c](https://github.com/carawayhome/caraway-meridian/commit/0fa747ca30da76d60b4c93e312224d853066a826))
* **themethemes.stylex lavender:** bug | Navy Button | Hover State Accessibility Issue ([6045b2c](https://github.com/carawayhome/caraway-meridian/commit/6045b2cc8b7a7fa978de7fa156d231c7a771a2cd))

## [0.52.0](https://github.com/carawayhome/caraway-meridian/compare/v1.1.0...v0.52.0) (2025-01-14)


### Features

* adding petite cooker logic ([3d52417](https://github.com/carawayhome/caraway-meridian/commit/3d524174d2b756585275ef0da1528a5c68e435ee))
* adding the petite cooker logic ([b9897f5](https://github.com/carawayhome/caraway-meridian/commit/b9897f55dbb48c6484cd0212647ea33a337ead8a))
* **Anatomy:** able to add the module on product pdp and other pages ([41da79d](https://github.com/carawayhome/caraway-meridian/commit/41da79d8c0c49eaa5387430d5b9dd71ce5f70bf9))
* **Anatomy:** update swatches ([12aa436](https://github.com/carawayhome/caraway-meridian/commit/12aa436c6e1ebff023e43356f7b1f3755c0479c2))
* **component:** bug |PDP | Accordions incorrect sizing ([39eb999](https://github.com/carawayhome/caraway-meridian/commit/39eb999d039ef389aa58d61a3e53e3e7602db419))
* **component:** connect layout to Contentful ([4d5d33e](https://github.com/carawayhome/caraway-meridian/commit/4d5d33e11c9585cc1d107ae11d5da296e8738f12))
* **component:** pDP ATF | New Toggle for ALC Products ([ca8e5c3](https://github.com/carawayhome/caraway-meridian/commit/ca8e5c375f2a63edd365be4a4bf9a039cd8f60db))
* **custom404:** update custom404 and fetchGlobalPage404 get data from Global Settings ([7f5f41d](https://github.com/carawayhome/caraway-meridian/commit/7f5f41d377fabaa5c8c983a80b0163970f3f3396))
* **progress:** making me email required ([03091f6](https://github.com/carawayhome/caraway-meridian/commit/03091f606e4bf37bb9b0095594f6777ecc69a704))
* **progress:** ready for pr ([8c85a4e](https://github.com/carawayhome/caraway-meridian/commit/8c85a4e093891a4543ec1811f21ecbf5fcc2e113))
* **progress:** refactoring the api calls a bit ([0fc2c95](https://github.com/carawayhome/caraway-meridian/commit/0fc2c958ac3497cd7bd7bf01a3986a148532f490))
* **progress:** test setup for toggling mini nav ([fb773c3](https://github.com/carawayhome/caraway-meridian/commit/fb773c31ed52917992b8fd4d68d409b78dd938dd))
* updating mobile logic ([722de67](https://github.com/carawayhome/caraway-meridian/commit/722de67ddbc37f876128bad3799cb222495d24aa))
* **usesingleaccordion:** refactor validation ([dcd78bb](https://github.com/carawayhome/caraway-meridian/commit/dcd78bbc96e2d4c1f82424061bdf2d60fa64a135))


### Bug Fixes

* addig sticky styles to image container ([f36ef05](https://github.com/carawayhome/caraway-meridian/commit/f36ef0516e5e6c783e0a51f5ddc4576a479c8759))
* adding looping to slider ([8c1407b](https://github.com/carawayhome/caraway-meridian/commit/8c1407b91ef165c84a5b2a51512b43a3f6d19b5d))
* adding looping to slider ([2bb0738](https://github.com/carawayhome/caraway-meridian/commit/2bb0738ae3309bcc5422bd3721e0a0b8a91ee00b))
* change requests ([50ea8ad](https://github.com/carawayhome/caraway-meridian/commit/50ea8ad6e69f0519e8460110b3ce06695f282bbc))
* **component:** animation seems choppy ([e674019](https://github.com/carawayhome/caraway-meridian/commit/e674019152c0d90c3af535d10741d39068acfd92))
* fix site crashing on back button from PDP to collection page with product cards ([f12e676](https://github.com/carawayhome/caraway-meridian/commit/f12e67605ef6ec786df3617aefbeaa0182a005aa))
* fixing missing dependencies and aria labels ([30d05c5](https://github.com/carawayhome/caraway-meridian/commit/30d05c5830a8936f7e97c5772d328b23f58bbb1f))
* **header:** bug |Header| Expanding infinitely when resizing the screen ([a80f509](https://github.com/carawayhome/caraway-meridian/commit/a80f5098bbcdf25508bcd46bb9d8b7386ddfd9a6))
* moved component to ready to use ([cbf716d](https://github.com/carawayhome/caraway-meridian/commit/cbf716d47a147849850aded67a7cc395128eb679))
* update product category to first collection in ProductViewedEvent and listeners ([cacb368](https://github.com/carawayhome/caraway-meridian/commit/cacb3680a2a2e9e754ecdc5324fa9eba592622ce))
* updating active styles ([ed7f4bc](https://github.com/carawayhome/caraway-meridian/commit/ed7f4bc6a69edbe33c05af286e45efa7e50eae36))
* updating component names ([bcc4e6f](https://github.com/carawayhome/caraway-meridian/commit/bcc4e6f3c2a1544d7cb2d7e483f100a1b1504293))
* **usersingleaccordion:** minimize button on accordions does not work ([c7f6b5f](https://github.com/carawayhome/caraway-meridian/commit/c7f6b5f74f7a2bfd53ebd4259defeac1e8bfb1de))


### Miscellaneous Chores

* release 0.52.0 ([3a307d5](https://github.com/carawayhome/caraway-meridian/commit/3a307d520922575e8906cd0672b19508d5dbfe67))

## [1.1.0](https://github.com/carawayhome/caraway-meridian/compare/v1.0.0...v1.1.0) (2025-01-06)


### Features

* add default redirect on influencers LPs ([42d4071](https://github.com/carawayhome/caraway-meridian/commit/42d40714e12fa63e1aee5837c0e01f69beb89e3a))
* add notify me link to limited variants ([3877b08](https://github.com/carawayhome/caraway-meridian/commit/3877b0819020d550c66ac035e32f562ff45c976d))
* allow promotional tabs slider variant level override ([6ba39cb](https://github.com/carawayhome/caraway-meridian/commit/6ba39cb25b67d29bad3d27f4cff3ea2ab56ae2cc))
* create new purchase limit hook ([84619f4](https://github.com/carawayhome/caraway-meridian/commit/84619f46b990a3f574fa8f9d7a91252fd3d953f2))
* disabled ATC state when product purchase is limited ([bd85c7c](https://github.com/carawayhome/caraway-meridian/commit/bd85c7c4274bbae0b448a5c75ffc41687a50d846))
* display messaging below the PDP ATC button ([7540b97](https://github.com/carawayhome/caraway-meridian/commit/7540b97db3311f8b973d6816e09538543795e059))
* fix mobile message alignment ([4f9df02](https://github.com/carawayhome/caraway-meridian/commit/4f9df02058274087ff80e27aa2175e59d0f725da))
* limit line item quantity selector and messaging in cart ([1a0e9f6](https://github.com/carawayhome/caraway-meridian/commit/1a0e9f6a79d90cea027a9dcce50162f9e88fd5f5))
* new purchase limit messaging component ([9646144](https://github.com/carawayhome/caraway-meridian/commit/96461440e1f44f9c2991003da52f1b0ef07b8bb3))
* **progress:** getting rid of emails temporarily ([8afa156](https://github.com/carawayhome/caraway-meridian/commit/8afa1566ce87bf3d82996294023c6e67691cba93))
* query 15 product variants ([67a4d32](https://github.com/carawayhome/caraway-meridian/commit/67a4d3213b985a1d1ab8752bcc18b0dd7dcbd900))
* **TimelineSlider:** fix mobile aspect ratio ([b1e3e20](https://github.com/carawayhome/caraway-meridian/commit/b1e3e20b17cb8ae3b15a278afa7a8c5e0307ac4a))
* update notify me url and limited variant IDs to real variants ([58db618](https://github.com/carawayhome/caraway-meridian/commit/58db6187df44b2d3d2ddc423930c6a99dc7851b5))
* updated reviews logic ([b064cf6](https://github.com/carawayhome/caraway-meridian/commit/b064cf67607f92deed61897c32e7aefe187a3b5b))


### Bug Fixes

* accordion padding without promotion slider ([efd750e](https://github.com/carawayhome/caraway-meridian/commit/efd750eca3c72499c75e30a17cc4843deb2c1f48))
* add redirect to the url based on discount code ([a763e80](https://github.com/carawayhome/caraway-meridian/commit/a763e807ef2c9a0858b4777c4f9d8d5ab37ec9a6))
* **coupons:** remove default sitewide discount code from coupon logic ([8cccda4](https://github.com/carawayhome/caraway-meridian/commit/8cccda4949d414c5ef176bab9886e1f38e965e4a))
* **Hero:** dots overlap icon ([cb406f5](https://github.com/carawayhome/caraway-meridian/commit/cb406f544c07753d81b8c222575eb7217a14551d))
* **Hero:** remove icon from hero and and simplify the icon logic ([68d145c](https://github.com/carawayhome/caraway-meridian/commit/68d145c19246c71b7ad9690ffa47e7376527e2e0))
* mobile swatches wrap ([b9972a1](https://github.com/carawayhome/caraway-meridian/commit/b9972a1910245d8bf9858215c6af2a8b021bc069))
* notify me button fix ([7712fb8](https://github.com/carawayhome/caraway-meridian/commit/7712fb8d9f720f0ed7c5fc134cb6ca4a707e9f09))
* notify me logic ([39c7404](https://github.com/carawayhome/caraway-meridian/commit/39c7404efc61641645e19a4f1251a6500a0e5b83))
* unify spacing between promotional tabs slider and single item ([532d9f0](https://github.com/carawayhome/caraway-meridian/commit/532d9f03fc23ec84c6122ba8ebb7f271fecd8f57))
* updates to the varaint custom messaging ([7dbb961](https://github.com/carawayhome/caraway-meridian/commit/7dbb961736c9eda3b1118d50a6304c6879dc5ac1))

## 1.0.0 (2024-12-24)


### Features

* **AccordionSplit:** add mobile asset ([2d6779f](https://github.com/carawayhome/caraway-meridian/commit/2d6779fe454ddedbd8b7b19d047451f6a7ac80f1))
* add add-on ESD field ([7753558](https://github.com/carawayhome/caraway-meridian/commit/7753558f8ea92bb2e367dd382e1176274feb97d7))
* add id to PDP ATC button for intersection observer ([2b5f8a9](https://github.com/carawayhome/caraway-meridian/commit/2b5f8a93deb36b671bdf5236b34407000f6797aa))
* add identificator to the main nav html element ([2eeb5c1](https://github.com/carawayhome/caraway-meridian/commit/2eeb5c1ed83e534ad289f0b3041f5ec524f66765))
* add support for custom search params in discount v2 page ([1ee6bdc](https://github.com/carawayhome/caraway-meridian/commit/1ee6bdcb4b961bcb36ea2c069d31d561f142bab8))
* add-on attributes and separate ATC buttons for single/multi modes ([895d0ef](https://github.com/carawayhome/caraway-meridian/commit/895d0ef43ead203e913665ca25eb3c66f1b8f985))
* added klaviyo support to notify me ([8994cfc](https://github.com/carawayhome/caraway-meridian/commit/8994cfca5bf129bd2e4e144e94f9be495e82870e))
* adding active index ([fe700f9](https://github.com/carawayhome/caraway-meridian/commit/fe700f93bb97bce5025c5125204cad1ac75ae00c))
* adding active states/styles ([1fb2326](https://github.com/carawayhome/caraway-meridian/commit/1fb2326f7f6b06eee339476c6249aa0043c50249))
* adding card data ([76d696a](https://github.com/carawayhome/caraway-meridian/commit/76d696aff1f556d4d315f7f4921cd9103f0aeace))
* adding container quieries to make styles more flexible ([e1d8837](https://github.com/carawayhome/caraway-meridian/commit/e1d88372f3497df094c170aa9a108923c890a6e9))
* adding description width ([94614f4](https://github.com/carawayhome/caraway-meridian/commit/94614f4fb6b026d6fbafaa32311f266c995f9731))
* adding form components ([80e65e4](https://github.com/carawayhome/caraway-meridian/commit/80e65e4b0166589985d9bb860ff941d22f8d9fc4))
* adding form styles ([fbb2b40](https://github.com/carawayhome/caraway-meridian/commit/fbb2b40edb21ff879caa51025cd2fa53f2b560df))
* adding mobile content slider ([0c4ce9e](https://github.com/carawayhome/caraway-meridian/commit/0c4ce9e983886b4d8282389621a741634bc6c307))
* adding new theme color ([5252b37](https://github.com/carawayhome/caraway-meridian/commit/5252b377437445f7c87fa3c1b77ba29f30ef6dfd))
* adding style props ([3ad3d6e](https://github.com/carawayhome/caraway-meridian/commit/3ad3d6ef84ef96f95baa99c24fbab718a754eb6a))
* adding Subform to Navigation panels ([31778cb](https://github.com/carawayhome/caraway-meridian/commit/31778cb9765acbd0e6d376c2f134dcdeca630b39))
* adding themes to form ([ac4e7a8](https://github.com/carawayhome/caraway-meridian/commit/ac4e7a80d82ccdc4176da6242816acd0061841a7))
* adding typography styles ([6acb94c](https://github.com/carawayhome/caraway-meridian/commit/6acb94c19a2c433da1188e898f57f44a43c95bf0))
* adding video support for layout7 marketing module ([c44e2a8](https://github.com/carawayhome/caraway-meridian/commit/c44e2a8c6353f30bb68b44227ecdf4efb4f317aa))
* adding video support for layout7 marketing module ([371ebbe](https://github.com/carawayhome/caraway-meridian/commit/371ebbe51434776f4e6bb431c354ae0dbcebbcd4))
* adjust spacing ([84ad140](https://github.com/carawayhome/caraway-meridian/commit/84ad140bb636802c74143814a1f08dd11363a9f1))
* allow to provide your own pricing component to product title ([808c940](https://github.com/carawayhome/caraway-meridian/commit/808c9403036e8105de4c9bdd16852c98d30b25fc))
* apply sitewide discount code by default if available ([c39161d](https://github.com/carawayhome/caraway-meridian/commit/c39161da38159590f1d39ba21bf072d48fa2d720))
* **ArticleContent:** add support for links in RichTextRenderer ([7ec1147](https://github.com/carawayhome/caraway-meridian/commit/7ec1147834be4608e5aada8a7ea861cc0dbaa496))
* better intersection observer logic ([1d9cf43](https://github.com/carawayhome/caraway-meridian/commit/1d9cf43586cfa126a8c892588266ac80ef968719))
* change max rating for reviews logic ([766bf46](https://github.com/carawayhome/caraway-meridian/commit/766bf465bdd32f83154ac80ad8b1af0e121fb265))
* chaning global styles, they have been updated ([78cd65a](https://github.com/carawayhome/caraway-meridian/commit/78cd65a179e147377b3b1fb4153721931a19819f))
* **component:** add BlockContent type ([ad31312](https://github.com/carawayhome/caraway-meridian/commit/ad31312ef5b142197ac097eb55ef2d05c74c7d49))
* **component:** broken link ([8d97aff](https://github.com/carawayhome/caraway-meridian/commit/8d97aff490024bc81c6c5ad17b8cf20d40f080b4))
* **component:** bug |Home Page| Feature Card (Single) Module, CTA does not work ([4daaf24](https://github.com/carawayhome/caraway-meridian/commit/4daaf2455d45d65fdb4411830d4f15dfe9653a5a))
* **component:** check if source hasAudio ([6a7fb8a](https://github.com/carawayhome/caraway-meridian/commit/6a7fb8a5e78c4fda9f482b3996b0a74ca8fa4487))
* **component:** check isVideo ([88b8822](https://github.com/carawayhome/caraway-meridian/commit/88b8822ce7d9639e682d629bb17742ce3b5d405a))
* **component:** create Generic Toggle Component ([70e774c](https://github.com/carawayhome/caraway-meridian/commit/70e774cca5a67f15c6eb202865dc71c86d92d3d3))
* **component:** home Page | Hero Carousel Slider |Add a pause play button ([2672cb7](https://github.com/carawayhome/caraway-meridian/commit/2672cb79ecdd69e98f598041608ee30650682fcd))
* **component:** initial block content ([b291947](https://github.com/carawayhome/caraway-meridian/commit/b291947b4c047e9dade9e20810863f7c6737997a))
* **component:** marketing and LP | Add video and gif capability to timeline module ([d1fcec9](https://github.com/carawayhome/caraway-meridian/commit/d1fcec94b2a5d528d96fa38c532d81456f75cbfd))
* **component:** media Container Target ([d934b95](https://github.com/carawayhome/caraway-meridian/commit/d934b95363df799620bcc6be2dc9504c3e143574))
* **component:** pDP |Save with a Set Module | Follow-up updates ([686344d](https://github.com/carawayhome/caraway-meridian/commit/686344da79393216198d85b9de379cc4d9f2f4e8))
* **component:** playPauseButton ([42d44cd](https://github.com/carawayhome/caraway-meridian/commit/42d44cd79faf430d9785ba52ceb38d66ae7c6272))
* **component:** product Card Sizing, GWP Icon larger, quantity selectors ([a38afa5](https://github.com/carawayhome/caraway-meridian/commit/a38afa57d51b9127be2811ed05c4e1955d8a21e4))
* **component:** soundOnOffButton ([31d8922](https://github.com/carawayhome/caraway-meridian/commit/31d89226a19d8f1884486c9fa1a1923719f8e3cb))
* **component:** update cta ([5e2f019](https://github.com/carawayhome/caraway-meridian/commit/5e2f0195d95c0442bb26fdbfe891e32396962cef))
* **component:** update hover state ([14ae5ec](https://github.com/carawayhome/caraway-meridian/commit/14ae5ec3b2e66d7b42c373d9d4e3f81a0baee07f))
* **component:** update overview accordion ([cc577a4](https://github.com/carawayhome/caraway-meridian/commit/cc577a4dd6c23132a505fe7bc18845ceceba4799))
* **component:** vercel QA Bug |Full Bleed Video | Height is too tall ([40207af](https://github.com/carawayhome/caraway-meridian/commit/40207af5edc6a30b3c45f23b84d37d950fa06f0b))
* componitinizing ([44360b6](https://github.com/carawayhome/caraway-meridian/commit/44360b650dad6f8d7a601cc2f667c6260a6aff01))
* compute price and savings with promo tiers included ([98d1014](https://github.com/carawayhome/caraway-meridian/commit/98d1014dbcde855b48672aff9f6464dd815b05bb))
* create empty cart and observe cartReady state ([0570bf2](https://github.com/carawayhome/caraway-meridian/commit/0570bf279828096c49816ef5a693d4ac2881e811))
* create empty cart if no cartId found ([181c8f6](https://github.com/carawayhome/caraway-meridian/commit/181c8f626f12c9edb9ba11768e3d663e8cd05702))
* create new simplified client-side redirect component ([97ca606](https://github.com/carawayhome/caraway-meridian/commit/97ca60675786fbe456820dcaf8b3c974b8339325))
* create pdp sticky desktop bar component ([1b854de](https://github.com/carawayhome/caraway-meridian/commit/1b854de7f1c38f48e90d972edbdd7decc0e6df34))
* display selected swatch value indicator ([c898aab](https://github.com/carawayhome/caraway-meridian/commit/c898aab0f590b631e82c14d6285600de41b546ae))
* drop a hint ([1e8066d](https://github.com/carawayhome/caraway-meridian/commit/1e8066d69872bd50f8b2c9dbdf56cb50ffec49cc))
* drop the intersection observer rootMargin ([f8cef84](https://github.com/carawayhome/caraway-meridian/commit/f8cef841238cbdbb0b1473aa9bd54ba01b893310))
* esd in PDP mobile swatches ([d631b65](https://github.com/carawayhome/caraway-meridian/commit/d631b65c02375a841fa4aed6ae1307dbe2bccd83))
* extract sticky nav loginc to a separate hook ([4eaaa8d](https://github.com/carawayhome/caraway-meridian/commit/4eaaa8d6283cac8302b1451a1299861307102368))
* fetch ESD field for add-on variants ([3d1e61a](https://github.com/carawayhome/caraway-meridian/commit/3d1e61a7fdc638e82ada7b87e91bf9a724c4d312))
* fetch swatch type label ([ebc061b](https://github.com/carawayhome/caraway-meridian/commit/ebc061bc65606f21d886a6d3a29073d8a72aa7bb))
* fine tuning mobile slider ([30d461f](https://github.com/carawayhome/caraway-meridian/commit/30d461fdb84dba37eff1ff9258c3c1e57b8e61d0))
* force dynamic on redirect pages ([f9928fb](https://github.com/carawayhome/caraway-meridian/commit/f9928fb927dadddf200c263800e21a25c6b0ade4))
* grayed out review stars when rating is less than 4 ([a7c4b91](https://github.com/carawayhome/caraway-meridian/commit/a7c4b9101ee099a7dd4fd3358a59c9170365270f))
* **Growthbook:** add to test footer ([08c1255](https://github.com/carawayhome/caraway-meridian/commit/08c12553f729de83705a44cdaeb539f06918de0e))
* **GrowthBook:** initial approach ([64f9351](https://github.com/carawayhome/caraway-meridian/commit/64f9351ebc802198ab99bee2d8369b6ce70f14f4))
* **Growthbook:** testing controlled by url ([ff1aae4](https://github.com/carawayhome/caraway-meridian/commit/ff1aae428987f57e691ff3644d46a58f39527294))
* **GrowthBook:** tracking ([d85bc7d](https://github.com/carawayhome/caraway-meridian/commit/d85bc7d0299c7fa6c7a8032ff99af045560a689d))
* **hero:** add anchor prop and update related components ([928a4f9](https://github.com/carawayhome/caraway-meridian/commit/928a4f9034ee30b407a36829e44a336297ad176f))
* **Hero:** add logo variantion to hero ([b1edf43](https://github.com/carawayhome/caraway-meridian/commit/b1edf4336b24d28c3844aade0580bad99ed9be6b))
* hide full reviews widget from PDPs when reviews less than 4 ([2564c3d](https://github.com/carawayhome/caraway-meridian/commit/2564c3dbb103b42eb8d9e603b59b425a81113355))
* hide stars rating in stucky title if less than threshold ([62fc9f8](https://github.com/carawayhome/caraway-meridian/commit/62fc9f88fb853391e16403279cf07d097d8c7da6))
* implement Product Clicked in search results ([e5f385e](https://github.com/carawayhome/caraway-meridian/commit/e5f385e8c908bb97588c9f127d9f575a7210ff93))
* implement Products Searched event ([b10ccc4](https://github.com/carawayhome/caraway-meridian/commit/b10ccc4ed7dedeb93b34daa3aaeea1aad607ab43))
* initial render.  Adding extractor and placeholder header ([9a75dda](https://github.com/carawayhome/caraway-meridian/commit/9a75ddace3de9acc61162c130d68bede1463238e))
* loading state ([12005c5](https://github.com/carawayhome/caraway-meridian/commit/12005c5265c20de20a22e6ef15ea9b96b2cbe15a))
* **Meilisearch Result Page:** improve ui by displaying loading state ([9d9daca](https://github.com/carawayhome/caraway-meridian/commit/9d9daca698fa5501175f5eee8deb544192819e5d))
* **Meilisearch:** guard for missin fields ([857444c](https://github.com/carawayhome/caraway-meridian/commit/857444cad70965cda10d8ee2778c0a25ce9ad1ca))
* move new redirect page in place of the old one ([5d1a1d0](https://github.com/carawayhome/caraway-meridian/commit/5d1a1d078dc3404c3ec193850456b390df82f04d))
* moved form logic to hook ([79c67e5](https://github.com/carawayhome/caraway-meridian/commit/79c67e52dd0465f5fd0f83b2fb4c6894418b23b2))
* new experimental discount redirect page ([e75bc22](https://github.com/carawayhome/caraway-meridian/commit/e75bc225b719e2143e4b4f7f4a9213b4ea75a4cf))
* new general search params based redirect logic for all pages ([f5750b4](https://github.com/carawayhome/caraway-meridian/commit/f5750b42bd6f99ad5a8deab118d9baa0ffe098a7))
* new util to get discount code and create redirect url ([7141ae0](https://github.com/carawayhome/caraway-meridian/commit/7141ae04197c5af135b529d869d778a0867c400a))
* on pdp hide the whole nav bar when scrolled down ([8fd705b](https://github.com/carawayhome/caraway-meridian/commit/8fd705b8e7f7ac080a6066340d49eb366fdb9495))
* **page:** create Affiliate page ([acf1a58](https://github.com/carawayhome/caraway-meridian/commit/acf1a5807ebd037a2646546b52e764afc0a03b3b))
* pr change requests ([a78d19b](https://github.com/carawayhome/caraway-meridian/commit/a78d19b184d75723ecb848e20960f2fea36ac770))
* **ProductPage:** accordions with single opened behavior ([393c569](https://github.com/carawayhome/caraway-meridian/commit/393c5692491a02d9f5b24d6a1c22be7d77c47df8))
* **progress:** added label ([7ab57f8](https://github.com/carawayhome/caraway-meridian/commit/7ab57f83f04d0a70f7386b97baeab5c3344cdbc2))
* **progress:** addressing comments ([3e64e28](https://github.com/carawayhome/caraway-meridian/commit/3e64e2878080279840848d5d847294617ca0e82c))
* **progress:** addressing neers comments ([b41084a](https://github.com/carawayhome/caraway-meridian/commit/b41084a719795f8d83d2322a6a89f9ac7c010e90))
* **progress:** addressing pr comments and slug filters for test ([6a25add](https://github.com/carawayhome/caraway-meridian/commit/6a25adda79c3e152642ec7fd1e41a6976780af17))
* **progress:** alt tags ([3a35627](https://github.com/carawayhome/caraway-meridian/commit/3a35627b3e69e7d133b628a45fe57414ff30ad21))
* **progress:** anchor ([fd0efc3](https://github.com/carawayhome/caraway-meridian/commit/fd0efc39883a68525519710f5997d9f872a3f318))
* **progress:** bringing this back ([ed0c3a4](https://github.com/carawayhome/caraway-meridian/commit/ed0c3a462e58868ae586c23bc6d057bccef42e35))
* **progress:** broken link ([d34cde0](https://github.com/carawayhome/caraway-meridian/commit/d34cde07ee88421ded003510bc322e78b0d0e3e9))
* **progress:** build errors ([386e0c4](https://github.com/carawayhome/caraway-meridian/commit/386e0c48c5d203d8d5d8b612e4917bfa7e7fb8ff))
* **progress:** build fix ([85394a5](https://github.com/carawayhome/caraway-meridian/commit/85394a5f2535fd0f914240a5fdbccdc0f332ed02))
* **progress:** compressing further ([ccff49e](https://github.com/carawayhome/caraway-meridian/commit/ccff49eff5e7b431d019b956195c872ed2b748bc))
* **progress:** connor requested ui updates ([35cb4fe](https://github.com/carawayhome/caraway-meridian/commit/35cb4fe7ad94d650302ad36dc835f9ff5adaf55e))
* **progress:** console ([f0e4b4f](https://github.com/carawayhome/caraway-meridian/commit/f0e4b4f59edf7ddda8742ce7f705ebc1b592db2f))
* **progress:** cropped mobile image ([78541ab](https://github.com/carawayhome/caraway-meridian/commit/78541ab2eb340af38eb11108c7dfaed13e8baccf))
* **progress:** dynamicPricing hook does not accurately compare cart total ([475e1de](https://github.com/carawayhome/caraway-meridian/commit/475e1de935835165384cc13ab5d4d29fdb4d240e))
* **progress:** emily comments ([31098be](https://github.com/carawayhome/caraway-meridian/commit/31098be1d573ed9c98f7207e63eca2b41cebee96))
* **progress:** empty state ([33ef583](https://github.com/carawayhome/caraway-meridian/commit/33ef58303fdd9f60091d9428b9ba610ca3404f00))
* **progress:** fixing ui ([3c791a0](https://github.com/carawayhome/caraway-meridian/commit/3c791a0cd39982717b3a7fcbb5b244f4f5e963f8))
* **progress:** font size ([e4492fd](https://github.com/carawayhome/caraway-meridian/commit/e4492fd8c9334e635f7424007768262877a28501))
* **progress:** force build ([d1fe8c1](https://github.com/carawayhome/caraway-meridian/commit/d1fe8c1db1dd77bb3905511d25ae260a1bb1229f))
* **progress:** getting rid of header ([43fd996](https://github.com/carawayhome/caraway-meridian/commit/43fd996b262a7f878fd3a34de664f8eb0f46f8fd))
* **progress:** getting rid of some parts ([4369bf8](https://github.com/carawayhome/caraway-meridian/commit/4369bf806a7ebbee4aeb6f323e182464a71732d3))
* **progress:** getting rid of this meta tag ([38eef36](https://github.com/carawayhome/caraway-meridian/commit/38eef36e89c8c12553eca658e470e9dcf9a47a87))
* **progress:** holiday flash 2024 program form ([5581eb5](https://github.com/carawayhome/caraway-meridian/commit/5581eb573f669a6e41914c9b5c81fd6bf86db788))
* **progress:** hybrid ([2d2ce1c](https://github.com/carawayhome/caraway-meridian/commit/2d2ce1c16fc94ffbc9531cdf7beeaedb80cf5937))
* **progress:** images updated ([9f22b9f](https://github.com/carawayhome/caraway-meridian/commit/9f22b9f672c0e421bdaef28ae9b93e912fee8e48))
* **progress:** lineitem next link ([d3c3585](https://github.com/carawayhome/caraway-meridian/commit/d3c3585b8b01d6e8cacc14ad132c7339968428b2))
* **progress:** lint and build errors ([8458e34](https://github.com/carawayhome/caraway-meridian/commit/8458e344399aca57ad0e3f9dd177ad5703f10998))
* **progress:** making it so mobile zoom doesnt happen ([c514406](https://github.com/carawayhome/caraway-meridian/commit/c514406b05154db9c15d51d7b6aac5e6c7e2e7d0))
* **progress:** minor iu fix ([9dd8ae6](https://github.com/carawayhome/caraway-meridian/commit/9dd8ae660d6f0b4698a270ea1ec1b5d6fade81a2))
* **progress:** minor UI bug ([b10c0b5](https://github.com/carawayhome/caraway-meridian/commit/b10c0b5462ddc7378b1f39a02fd9400c687c23b4))
* **progress:** notify me ui ([d57a2e7](https://github.com/carawayhome/caraway-meridian/commit/d57a2e72a8c214af2e590999c931ff0600755fd7))
* **progress:** qa with build to test out link updates ([b64add0](https://github.com/carawayhome/caraway-meridian/commit/b64add0ba5f32db8aebf3fe215c931401c931721))
* **progress:** removing slug filters ([cb3f18e](https://github.com/carawayhome/caraway-meridian/commit/cb3f18ebfcd0a25d8d62f2bf90b44c9068e07505))
* **progress:** removing test thing ([e5163f7](https://github.com/carawayhome/caraway-meridian/commit/e5163f75e14cf417412ba7e7927ac96d8379d85b))
* **progress:** removing yarnlock ([4bf3fe6](https://github.com/carawayhome/caraway-meridian/commit/4bf3fe6044a94045300eaa3b06404fc9bb2af1f5))
* **progress:** reverting gladly chat script edits ([1d3e2d3](https://github.com/carawayhome/caraway-meridian/commit/1d3e2d37a5462420d2f9a4726f8196947e1af192))
* **progress:** stretching problem ([6859181](https://github.com/carawayhome/caraway-meridian/commit/6859181ab6ace05f35ffb26fc37c3d34596c4efa))
* **progress:** suspense boundary ([0eefaca](https://github.com/carawayhome/caraway-meridian/commit/0eefacaa13e13b97bde9d4f3a6347589b27873c2))
* **progress:** test setup and implementation ([43fe5b4](https://github.com/carawayhome/caraway-meridian/commit/43fe5b4a836d2225ac89431b064a39662cc6e719))
* **progress:** ts-error ([10ba4f8](https://github.com/carawayhome/caraway-meridian/commit/10ba4f83baf933a5c48740fb182864d47685b95e))
* **progress:** ui update ([e830ff9](https://github.com/carawayhome/caraway-meridian/commit/e830ff9f8ef13b414193fd14ce7f000c2f118ee5))
* **progress:** ui update ([19cd0f7](https://github.com/carawayhome/caraway-meridian/commit/19cd0f757124832a634920adc32e67e6c68daf84))
* **progress:** ui update for notify me variants ([93a90bb](https://github.com/carawayhome/caraway-meridian/commit/93a90bbe5a73427f9ed396b230a7237efd3ee90d))
* **progress:** updated ps keyword for coming soon form ([18432f6](https://github.com/carawayhome/caraway-meridian/commit/18432f68394653ea68e090f1c49173992e3391ff))
* **progress:** updating comments from teo and ariel ([ce00530](https://github.com/carawayhome/caraway-meridian/commit/ce00530ff5c50c000aa207cfc56d5be705a21031))
* **progress:** updating copy ([fc9b462](https://github.com/carawayhome/caraway-meridian/commit/fc9b4629295accf590db2310430e34ae95b520b3))
* **progress:** updating font size ([87141cb](https://github.com/carawayhome/caraway-meridian/commit/87141cb164565c7d1c05f3a6d4e32a0e823590c7))
* **progress:** updating forms ([7006ca7](https://github.com/carawayhome/caraway-meridian/commit/7006ca7cf640bde13b7f77f0d1d9f9daebff871e))
* **progress:** updating gladly chat bubble mobile ([212019c](https://github.com/carawayhome/caraway-meridian/commit/212019cda69b53807eeae5b1275080056be7b562))
* **progress:** updating gladly chat bubble position ([1094777](https://github.com/carawayhome/caraway-meridian/commit/1094777e9e1fe31e6b83110cb86075abdfef8fbd))
* **progress:** updating PR ([4410681](https://github.com/carawayhome/caraway-meridian/commit/4410681fbbe63ef41fdd60a8994d33059f89e5b3))
* **progress:** updating PS forms page for new one ([667ad76](https://github.com/carawayhome/caraway-meridian/commit/667ad769fa52569825caaf10650d2d75b85f6116))
* **progress:** using cover css ([539bf89](https://github.com/carawayhome/caraway-meridian/commit/539bf89a96fc45226eecf688b72514eb2ba4739f))
* refactoring Footer layout on desktop / mobile ([36c7fa2](https://github.com/carawayhome/caraway-meridian/commit/36c7fa2d61601c6fc906e4c07c9156d15bee38e5))
* remove forward slash from redirect param ([a1a6dfc](https://github.com/carawayhome/caraway-meridian/commit/a1a6dfc3711115774d6acd00b573552a605b5f0f))
* remove old redirect page and hook ([f24a938](https://github.com/carawayhome/caraway-meridian/commit/f24a938d40cb4edc321088070a77a85d588be4ed))
* remove separator block if reviews widget is hidden ([79f80e3](https://github.com/carawayhome/caraway-meridian/commit/79f80e3011e4190cf90f0e1791fa950ccafcf386))
* remove useDiscount hook usage from minicart ([a7a2f61](https://github.com/carawayhome/caraway-meridian/commit/a7a2f611f45f5e84a4d4e5ec6fae30c650dc4ec5))
* removes first and last child instances of br tags on Text for rtr ([d55b225](https://github.com/carawayhome/caraway-meridian/commit/d55b225980153fc550f9f289aff8b2cae48485f0))
* removing duplicate styles ([6f75eaa](https://github.com/carawayhome/caraway-meridian/commit/6f75eaacb73e1a7a08b5d4e4faedb2f1d85704b8))
* responsive styles ([5d639c3](https://github.com/carawayhome/caraway-meridian/commit/5d639c31a91c623b8ba89c778a770ab29b59f427))
* special price logic for weekly deals ([4d8f89a](https://github.com/carawayhome/caraway-meridian/commit/4d8f89a111264ccc58ef7305c00650f31398017b))
* special product price for weekly deals LP mini details ([0da9ee1](https://github.com/carawayhome/caraway-meridian/commit/0da9ee1b40b972b06a5247756c95732f21c2c1f9))
* stucky bar text swatch styles ([7244154](https://github.com/carawayhome/caraway-meridian/commit/7244154370b0f5fb363a37380f9fe253e4b757c8))
* style updates to the ESD sticky label ([9e4e46b](https://github.com/carawayhome/caraway-meridian/commit/9e4e46bbf5167a559f8b0fc2bd186c136da776ba))
* support flat swatches array in getActiveSwatch util ([9b1a04d](https://github.com/carawayhome/caraway-meridian/commit/9b1a04d534abe9967ac0d135c55a2fc15973fa99))
* **theme:** add new color themes and update color definitions ([5f00045](https://github.com/carawayhome/caraway-meridian/commit/5f0004529bd6cdba43e3e1e5211897ec0e37a234))
* toggle sticky title bar based on ATC button intersection observer ([7c5dc57](https://github.com/carawayhome/caraway-meridian/commit/7c5dc5782d3d759aa68837da8c0984940efe06be))
* updaing layout on Mobile ([c517af9](https://github.com/carawayhome/caraway-meridian/commit/c517af9d62ad85d7eb4f9b8b6c0a0b927ad8cd4b))
* update product title block to include sticky bar ([26ee131](https://github.com/carawayhome/caraway-meridian/commit/26ee131c989f1a231c0fcc2e7354d8c47ef81c03))
* updated for to react hook form ([b741a6b](https://github.com/carawayhome/caraway-meridian/commit/b741a6b49dc8a43a4ae300153048b9ce727518b5))
* use new useStickyNav hook in main nav ([c789212](https://github.com/carawayhome/caraway-meridian/commit/c789212346d8e096b93274c1d0c59e4a42a5b2e9))
* use real reviews in sticky bar ([c6f261d](https://github.com/carawayhome/caraway-meridian/commit/c6f261d51afd643584f961e28a7b7c451d4bf597))
* use real reviews in sticky bar ([9f8f05d](https://github.com/carawayhome/caraway-meridian/commit/9f8f05d2c9c99732a0320cbc8327e9a3abf269c4))
* use special price on weekly deals LPs ([8d4e836](https://github.com/carawayhome/caraway-meridian/commit/8d4e8364982e79a76a31df8d4a1bd7f77d0be4ab))
* **VariantCustomLevel:** fix the activeSwatch obj ([4e00983](https://github.com/carawayhome/caraway-meridian/commit/4e00983dd142ede50f78a8a06ac55112276471c2))
* **VariantCustomLevel:** pr changes ([a8633fd](https://github.com/carawayhome/caraway-meridian/commit/a8633fdfb2a39e49fd38dcd8fa922b8f9516c3d4))


### Bug Fixes

* **AccordionSplit:** build ([67bef33](https://github.com/carawayhome/caraway-meridian/commit/67bef335bf442044c329cd0c3d2b3ea89eae7efe))
* **AccordionSplit:** build ([3c4d4d4](https://github.com/carawayhome/caraway-meridian/commit/3c4d4d4d70635651df9d07d9ce2415deb432f12d))
* **AccordionSplit:** fix build ([e42044a](https://github.com/carawayhome/caraway-meridian/commit/e42044a5f65afe97f88a71ebed15d13078e566e2))
* **AccordionSplit:** last item with border ([902a15e](https://github.com/carawayhome/caraway-meridian/commit/902a15e8dbd453ebeac5313fedcdbbc7d8a4d29d))
* **AccordionSplit:** merge ([caf7550](https://github.com/carawayhome/caraway-meridian/commit/caf75508379dc2aa33865ba4b146e8f1b902f0ec))
* **AccordionSplit:** mobile issues ([9c6426a](https://github.com/carawayhome/caraway-meridian/commit/9c6426aaf363a66226bad5f4e8ec11c3fa8fe10c))
* **AccordionSplit:** prevent initial scroll ([cb679fb](https://github.com/carawayhome/caraway-meridian/commit/cb679fb14fac7dca22e76b34c0beb54e0469580f))
* adding title prop to notify me desktop ([0310f8a](https://github.com/carawayhome/caraway-meridian/commit/0310f8adb159cc64131a66ad127e8b034c345e67))
* **ArticleContent:** handle optional links in RichTextRenderer ([8d1e3a9](https://github.com/carawayhome/caraway-meridian/commit/8d1e3a9bbee194cbfc071e6e2ee56efcfda96235))
* **BlogHeroHeader:** ensure alt attribute is not undefined ([7dc7f2c](https://github.com/carawayhome/caraway-meridian/commit/7dc7f2c773d438b5db89435b48f39256e0ec340f))
* **component:** change events ([a09f30b](https://github.com/carawayhome/caraway-meridian/commit/a09f30bd7eb4205550b1ad169a2bbd39fc1c12fe))
* **component:** checkMark update ([d0335b3](https://github.com/carawayhome/caraway-meridian/commit/d0335b3ccb948ac2c050d8099fff59d18333308d))
* **component:** display arrows ([9174699](https://github.com/carawayhome/caraway-meridian/commit/917469934609a8dbb037a4a01d63fb67fd7b217a))
* **component:** extractor detail ([f307e45](https://github.com/carawayhome/caraway-meridian/commit/f307e45d0482fa8eaf6ca766d21fb19ea5a92711))
* **component:** hide PausePlayButton from Video ([e2fe396](https://github.com/carawayhome/caraway-meridian/commit/e2fe39642100f4a9ff82b3de66af084f24a75a84))
* **component:** home Page | Hero Carousel Slider | Direction of Slide Movements and Infinite Loop ([de5fa81](https://github.com/carawayhome/caraway-meridian/commit/de5fa81274e7a642e56c3c589f776a3de932d66c))
* **component:** hover color ([a7bb0bc](https://github.com/carawayhome/caraway-meridian/commit/a7bb0bcf60c2d76ed120f70d44c521d02f60803b))
* **component:** image crop ([4070ef3](https://github.com/carawayhome/caraway-meridian/commit/4070ef3aba461aba41053290986f16cf16fdeda7))
* **component:** image update ([397e36e](https://github.com/carawayhome/caraway-meridian/commit/397e36e0b7d1a63b6220c66dc462bfc932c34474))
* **component:** more generic component ([4aada2b](https://github.com/carawayhome/caraway-meridian/commit/4aada2b77cc0ad0e6cb61e9dea40342392b62475))
* **component:** neer comment ([5b92b9b](https://github.com/carawayhome/caraway-meridian/commit/5b92b9ba88ebe5cd501dd2d120512b639ebaf52c))
* **component:** neer Comments update ([6eac096](https://github.com/carawayhome/caraway-meridian/commit/6eac09609f3846795d9adc5de4330478d46b34e8))
* **component:** pDP | Remove set savings from below ATC button ([54e3a82](https://github.com/carawayhome/caraway-meridian/commit/54e3a820d4af259c86bc3667e0a28f56aba8534f))
* **component:** pR Comments ([252980d](https://github.com/carawayhome/caraway-meridian/commit/252980da7abde1a1dfa5272318dfb46faaeac346))
* **component:** pR Dawid Comments ([9a27f87](https://github.com/carawayhome/caraway-meridian/commit/9a27f870b03d2f65c2927f6f565b38754f85309f))
* **component:** qA Comments ([66b7722](https://github.com/carawayhome/caraway-meridian/commit/66b7722433e1ce9e7a8cf97ff5e225679ba57c69))
* **component:** update onClick ([4abcc26](https://github.com/carawayhome/caraway-meridian/commit/4abcc26ad302f7a616d0134f0423c245bb917de8))
* **component:** update onClick ([b714982](https://github.com/carawayhome/caraway-meridian/commit/b714982e569f80f04862abb4552d4d92657609e7))
* **component:** update padding & type ([7367cd2](https://github.com/carawayhome/caraway-meridian/commit/7367cd205d6512bd5f1a2ea330e1d4f2f05d2059))
* **component:** update Popover Structure ([0c9b6d2](https://github.com/carawayhome/caraway-meridian/commit/0c9b6d2adb0c8f3cc8571a352ac8d5efe677e7c5))
* **component:** update QA design ([2e3e1fe](https://github.com/carawayhome/caraway-meridian/commit/2e3e1fe6cf6005758c4882f6b0aca8668525492b))
* **component:** vercel Comments ([bca00bd](https://github.com/carawayhome/caraway-meridian/commit/bca00bda595d772ed811a927ba6c1e162a1cb35a))
* **component:** vercel QA | Product Card Carousel ([57e193f](https://github.com/carawayhome/caraway-meridian/commit/57e193fe13015056d1139f9cd2a8cc8257954f74))
* **component:** will Comments ([19faa5f](https://github.com/carawayhome/caraway-meridian/commit/19faa5f80fad5d1a1d782e3a8f127e7064025889))
* **componeny:** update option key ([b1f7807](https://github.com/carawayhome/caraway-meridian/commit/b1f78071f13a698d1cc550b1a8e1f055852d1ef9))
* **disclamer:** pDP | Make AB1200 text smaller (match reviews message * ([590c94e](https://github.com/carawayhome/caraway-meridian/commit/590c94eb88da1143663f72aa869fefffb0c7f88c))
* **extractor:** article optional ([a12158f](https://github.com/carawayhome/caraway-meridian/commit/a12158f6b0076b82e56026cb88cef25a9dadafa4))
* **FeaturedStories:** ensure alt attribute defaults to an empty string ([1ad00f6](https://github.com/carawayhome/caraway-meridian/commit/1ad00f6aa91fbf70d9474b57ea660bf95b6b0516))
* **fetch:** update FetchProducts ([06ac10d](https://github.com/carawayhome/caraway-meridian/commit/06ac10de2e64fc2574678b392128b515f9394acf))
* fix failing builds ([ce57ff9](https://github.com/carawayhome/caraway-meridian/commit/ce57ff96534c58d9b9be88e927282a15e590d680))
* fix localhost domain in the cookie set ([04af29b](https://github.com/carawayhome/caraway-meridian/commit/04af29b7d2c6dc2bd5af2c4dd9a0dc2fe3d98076))
* **function:** fix extractMarketingModule ([c513724](https://github.com/carawayhome/caraway-meridian/commit/c5137241411cefef2dade2a8d79d8f5c61479b4b))
* **Hero:** layout 10 single columns icons ([5a33620](https://github.com/carawayhome/caraway-meridian/commit/5a33620783e910eacc48a58bd405f45d9a7f8a2c))
* **Hero:** qa ([0bbc324](https://github.com/carawayhome/caraway-meridian/commit/0bbc324226739d723d25638eb2bd5d7b0904312b))
* **Hover:** empty popup ([cef016c](https://github.com/carawayhome/caraway-meridian/commit/cef016c101d4d1cae0890ef59f119f364882af39))
* linting errors ([dc67506](https://github.com/carawayhome/caraway-meridian/commit/dc67506c62ce8e39ca4d35dabeee1d1d273080b0))
* **meilisearch:** merge ([e84871d](https://github.com/carawayhome/caraway-meridian/commit/e84871da89f917f6f79cf61ab5373769de1e6d14))
* merge conflict ([e76fe14](https://github.com/carawayhome/caraway-meridian/commit/e76fe148868fac4432b51688e21c851f01ff36b3))
* merge conflicts ([a58e0a9](https://github.com/carawayhome/caraway-meridian/commit/a58e0a9494dd384ded674cd37a109d11b43364a8))
* missing prop ([010d580](https://github.com/carawayhome/caraway-meridian/commit/010d580327276f35d902369b0af7633c2fcaf529))
* **page:** last update ([b6de46e](https://github.com/carawayhome/caraway-meridian/commit/b6de46e9c73a3005d6508a9b76f008ccdce45b91))
* **PDP:** buy with zest button ([f564b6a](https://github.com/carawayhome/caraway-meridian/commit/f564b6a4e15b60fbacae7dc0d179cf333471a700))
* **Product Card Swatches:** mobile ([e2f17dd](https://github.com/carawayhome/caraway-meridian/commit/e2f17dd1a7a5cc0dd726bfd286ca00953da14637))
* **ProductSlider:** drag inception bug ([e57aac0](https://github.com/carawayhome/caraway-meridian/commit/e57aac04bcde14c8d3077289dc5c638aaf1a6be7))
* **progress:** linting error ([6b84e72](https://github.com/carawayhome/caraway-meridian/commit/6b84e72d099cb7a8cb7a111e4f9659f3abc2e08f))
* remove redundant pageview call in Tatari initialization ([d65933d](https://github.com/carawayhome/caraway-meridian/commit/d65933d0aa1e5da387121ddf85dd00aac8204eb3))
* resolving conflict ([2512c67](https://github.com/carawayhome/caraway-meridian/commit/2512c67a0281ba6539901ef300338d7c5f05a7ef))
* **style:** last details ([62c7857](https://github.com/carawayhome/caraway-meridian/commit/62c785766b46e1eb66a7759a827b04128d015445))
* **Swtches:** display element when only one collection type ([3742a93](https://github.com/carawayhome/caraway-meridian/commit/3742a93eb0fa4917e9eb746d78a8d7918f6113ab))
* typescript error ([aec8540](https://github.com/carawayhome/caraway-meridian/commit/aec8540500c01623e3955cd0f69f3e43f7772da7))
* update Countdown component to use localized time for calculations ([da9cec0](https://github.com/carawayhome/caraway-meridian/commit/da9cec04ca365cdd054a11d1336c428cf89bbcc6))
* **Variant Level Custom MSg:** build broke by save with a set ([ee6e94e](https://github.com/carawayhome/caraway-meridian/commit/ee6e94eab2749a7d89416e93c4c7c68061bac0ee))

## [0.50.0](https://github.com/carawayhome/caraway-meridian/compare/v0.49.0...v0.50.0) (2024-12-05)


### Features

* **Growthbook:** add to test footer ([08c1255](https://github.com/carawayhome/caraway-meridian/commit/08c12553f729de83705a44cdaeb539f06918de0e))


### Bug Fixes

* **component:** change events ([a09f30b](https://github.com/carawayhome/caraway-meridian/commit/a09f30bd7eb4205550b1ad169a2bbd39fc1c12fe))
* **component:** extractor detail ([f307e45](https://github.com/carawayhome/caraway-meridian/commit/f307e45d0482fa8eaf6ca766d21fb19ea5a92711))
* **component:** hover color ([a7bb0bc](https://github.com/carawayhome/caraway-meridian/commit/a7bb0bcf60c2d76ed120f70d44c521d02f60803b))
* **component:** pR Comments ([252980d](https://github.com/carawayhome/caraway-meridian/commit/252980da7abde1a1dfa5272318dfb46faaeac346))
* **component:** pR Dawid Comments ([9a27f87](https://github.com/carawayhome/caraway-meridian/commit/9a27f870b03d2f65c2927f6f565b38754f85309f))
* **component:** update Popover Structure ([0c9b6d2](https://github.com/carawayhome/caraway-meridian/commit/0c9b6d2adb0c8f3cc8571a352ac8d5efe677e7c5))
* **function:** fix extractMarketingModule ([c513724](https://github.com/carawayhome/caraway-meridian/commit/c5137241411cefef2dade2a8d79d8f5c61479b4b))

## [0.49.0](https://github.com/carawayhome/caraway-meridian/compare/v0.48.0...v0.49.0) (2024-12-03)


### Features

* add add-on ESD field ([7753558](https://github.com/carawayhome/caraway-meridian/commit/7753558f8ea92bb2e367dd382e1176274feb97d7))
* add-on attributes and separate ATC buttons for single/multi modes ([895d0ef](https://github.com/carawayhome/caraway-meridian/commit/895d0ef43ead203e913665ca25eb3c66f1b8f985))
* fetch ESD field for add-on variants ([3d1e61a](https://github.com/carawayhome/caraway-meridian/commit/3d1e61a7fdc638e82ada7b87e91bf9a724c4d312))


### Bug Fixes

* **component:** pDP | Remove set savings from below ATC button ([54e3a82](https://github.com/carawayhome/caraway-meridian/commit/54e3a820d4af259c86bc3667e0a28f56aba8534f))
* update Countdown component to use localized time for calculations ([da9cec0](https://github.com/carawayhome/caraway-meridian/commit/da9cec04ca365cdd054a11d1336c428cf89bbcc6))

## [0.48.0](https://github.com/carawayhome/caraway-meridian/compare/v0.47.0...v0.48.0) (2024-11-27)


### Features

* **Meilisearch Result Page:** improve ui by displaying loading state ([9d9daca](https://github.com/carawayhome/caraway-meridian/commit/9d9daca698fa5501175f5eee8deb544192819e5d))
* **page:** create Affiliate page ([acf1a58](https://github.com/carawayhome/caraway-meridian/commit/acf1a5807ebd037a2646546b52e764afc0a03b3b))
* **progress:** added label ([7ab57f8](https://github.com/carawayhome/caraway-meridian/commit/7ab57f83f04d0a70f7386b97baeab5c3344cdbc2))
* **progress:** addressing comments ([3e64e28](https://github.com/carawayhome/caraway-meridian/commit/3e64e2878080279840848d5d847294617ca0e82c))
* **progress:** bringing this back ([ed0c3a4](https://github.com/carawayhome/caraway-meridian/commit/ed0c3a462e58868ae586c23bc6d057bccef42e35))
* **progress:** broken link ([d34cde0](https://github.com/carawayhome/caraway-meridian/commit/d34cde07ee88421ded003510bc322e78b0d0e3e9))
* **progress:** connor requested ui updates ([35cb4fe](https://github.com/carawayhome/caraway-meridian/commit/35cb4fe7ad94d650302ad36dc835f9ff5adaf55e))
* **progress:** emily comments ([31098be](https://github.com/carawayhome/caraway-meridian/commit/31098be1d573ed9c98f7207e63eca2b41cebee96))
* **progress:** font size ([e4492fd](https://github.com/carawayhome/caraway-meridian/commit/e4492fd8c9334e635f7424007768262877a28501))
* **progress:** force build ([d1fe8c1](https://github.com/carawayhome/caraway-meridian/commit/d1fe8c1db1dd77bb3905511d25ae260a1bb1229f))
* **progress:** getting rid of some parts ([4369bf8](https://github.com/carawayhome/caraway-meridian/commit/4369bf806a7ebbee4aeb6f323e182464a71732d3))
* **progress:** getting rid of this meta tag ([38eef36](https://github.com/carawayhome/caraway-meridian/commit/38eef36e89c8c12553eca658e470e9dcf9a47a87))
* **progress:** removing yarnlock ([4bf3fe6](https://github.com/carawayhome/caraway-meridian/commit/4bf3fe6044a94045300eaa3b06404fc9bb2af1f5))
* **progress:** reverting gladly chat script edits ([1d3e2d3](https://github.com/carawayhome/caraway-meridian/commit/1d3e2d37a5462420d2f9a4726f8196947e1af192))
* **progress:** ui update ([19cd0f7](https://github.com/carawayhome/caraway-meridian/commit/19cd0f757124832a634920adc32e67e6c68daf84))
* **progress:** updating comments from teo and ariel ([ce00530](https://github.com/carawayhome/caraway-meridian/commit/ce00530ff5c50c000aa207cfc56d5be705a21031))
* **progress:** updating gladly chat bubble mobile ([212019c](https://github.com/carawayhome/caraway-meridian/commit/212019cda69b53807eeae5b1275080056be7b562))


### Bug Fixes

* **page:** last update ([b6de46e](https://github.com/carawayhome/caraway-meridian/commit/b6de46e9c73a3005d6508a9b76f008ccdce45b91))
* **progress:** linting error ([6b84e72](https://github.com/carawayhome/caraway-meridian/commit/6b84e72d099cb7a8cb7a111e4f9659f3abc2e08f))
* resolving conflict ([2512c67](https://github.com/carawayhome/caraway-meridian/commit/2512c67a0281ba6539901ef300338d7c5f05a7ef))
* **Swtches:** display element when only one collection type ([3742a93](https://github.com/carawayhome/caraway-meridian/commit/3742a93eb0fa4917e9eb746d78a8d7918f6113ab))
* **Variant Level Custom MSg:** build broke by save with a set ([ee6e94e](https://github.com/carawayhome/caraway-meridian/commit/ee6e94eab2749a7d89416e93c4c7c68061bac0ee))

## [0.47.0](https://github.com/carawayhome/caraway-meridian/compare/v0.46.0...v0.47.0) (2024-11-26)


### Features

* add arrow expanded indicator to the toggle ([c85d5f5](https://github.com/carawayhome/caraway-meridian/commit/c85d5f5b27355e7911db7a6427558ee4c65f4f1d))
* add axon ([02c5530](https://github.com/carawayhome/caraway-meridian/commit/02c5530bb79b6faf0a7a17ddce66b661c17ef46b))
* add onClick prop to ProductLink ([e57a732](https://github.com/carawayhome/caraway-meridian/commit/e57a732967f13de3494a74b46d55f4fb04d7c4a0))
* add option to get plain add to cart button ([bd1e0a1](https://github.com/carawayhome/caraway-meridian/commit/bd1e0a131203721f68f6552ee62983976437dc9f))
* add size prop to swatch icon ([e6eb8b6](https://github.com/carawayhome/caraway-meridian/commit/e6eb8b64c82fde45c3a2cce8752cad61f555c8bf))
* add sticky swatches to page ([e7a934a](https://github.com/carawayhome/caraway-meridian/commit/e7a934ab8c53265f62710b45f71d87e37c4f899e))
* add support for content block custom add ons ([b9e3186](https://github.com/carawayhome/caraway-meridian/commit/b9e3186dbde46c22668ce1055c456304026299ed))
* add to cart selected variant ([ca92b94](https://github.com/carawayhome/caraway-meridian/commit/ca92b94b39046c0f124a8d6114391db62e591d5c))
* adding detectiong for embla sliders ([6d74315](https://github.com/carawayhome/caraway-meridian/commit/6d74315ee9fba041a22fd633a66283985e51850a))
* adding quick nav query ([c311c6a](https://github.com/carawayhome/caraway-meridian/commit/c311c6ab404f197e3d21b369a352d746425d9e36))
* allow passing custom style prop to icons ([8db127a](https://github.com/carawayhome/caraway-meridian/commit/8db127a10b8ea24243cc8e9c52eb4300e00b053d))
* create sticky mobile swatches component ([8cde6d1](https://github.com/carawayhome/caraway-meridian/commit/8cde6d142e787745b2743b0ee8f828086bc262ba))
* **events:** track page view with Tatari integration ([e8a4f2d](https://github.com/carawayhome/caraway-meridian/commit/e8a4f2d4164833e425376102116c2099b7c48c8b))
* **extractor:** connect FeaturedStories to Contentfull ([4395610](https://github.com/carawayhome/caraway-meridian/commit/4395610fc6b166fa3c81eb45ad1e97d5403e4966))
* **HeroSlider:** add width prop to customize header width ([9efb834](https://github.com/carawayhome/caraway-meridian/commit/9efb8345a1a6b54e37aaea44208eef734e8da0cf))
* install axon, envoke three events ([1aff12d](https://github.com/carawayhome/caraway-meridian/commit/1aff12d2938f609cd3916e21a9705e0f66b0d99e))
* integrate Postscript event tracking for product views and add to cart actions ([9436486](https://github.com/carawayhome/caraway-meridian/commit/94364863b34a85fc2714d754701fd19a4db9c6c6))
* pass selected swatch to product swatches ([c095694](https://github.com/carawayhome/caraway-meridian/commit/c095694f333f63c25c6f75a99611b55703fbac45))
* quick nav optional update, icon styles update ([bc96dda](https://github.com/carawayhome/caraway-meridian/commit/bc96dda52db964eec7bd8e2ed2ec34e662a9a206))
* rendering link data on mobile ([6f8e8c6](https://github.com/carawayhome/caraway-meridian/commit/6f8e8c66a140886498971f4e0d72528d313136fd))
* set axon axwrt cookie ([d69cbb6](https://github.com/carawayhome/caraway-meridian/commit/d69cbb60af03ce3da3b97617f7e1a66755c1edab))
* show and hide mobile bar when scrolling up and down ([c5de729](https://github.com/carawayhome/caraway-meridian/commit/c5de7295e77ac135d1b2c2ce07671b8f8ccde9f5))
* show mobile swatches only on PDPs ([9255708](https://github.com/carawayhome/caraway-meridian/commit/92557083d0f67a90c858ba553c41dcde2d30ff58))
* **sitemap:** add shop to the sitebap ([be138c4](https://github.com/carawayhome/caraway-meridian/commit/be138c4e871a8305bc812eb7beceb8b26d76ee47))
* support non color attributes and swatches filtering ([182cc81](https://github.com/carawayhome/caraway-meridian/commit/182cc81133fba636b452241a810f3defc4ba8bb3))
* update cart page cta to navy ([bfe9d36](https://github.com/carawayhome/caraway-meridian/commit/bfe9d3608c501e226c26636a013a461a7d682d54))
* updating styles and render logic for quickNav ([ff8bb3a](https://github.com/carawayhome/caraway-meridian/commit/ff8bb3a862c5b8dfaf3c98f2bf3204b51cc77b01))


### Bug Fixes

* add begin_checkout event ([d4e3391](https://github.com/carawayhome/caraway-meridian/commit/d4e3391ef4700c7e6ea6ef5cd522d7445d279712))
* add on card styles QA ([a5484b7](https://github.com/carawayhome/caraway-meridian/commit/a5484b7dcfcfb0b053f73456304a6c73678c0052))
* build errors ([a96b5c2](https://github.com/carawayhome/caraway-meridian/commit/a96b5c232fa02ceceab19a15c369c3a75aa67d1c))
* **cart:** update ShopPayButton URL to use absolute path ([35ee6b4](https://github.com/carawayhome/caraway-meridian/commit/35ee6b46ef73e4aac311826435829664c140c9f4))
* **cart:** update ShopPayButton URL to use relative path ([3df544b](https://github.com/carawayhome/caraway-meridian/commit/3df544bf02e55c5456acfef14574a36659ed0e17))
* change mobile breakpoint ([d00de6e](https://github.com/carawayhome/caraway-meridian/commit/d00de6e4b782d5db717263036c45f930f9b47df3))
* default swatch ([ea8e83f](https://github.com/carawayhome/caraway-meridian/commit/ea8e83f0de3fa77056af604421d31367c3825397))
* **disclamer:** pDP | Make AB1200 text smaller (match reviews message * ([590c94e](https://github.com/carawayhome/caraway-meridian/commit/590c94eb88da1143663f72aa869fefffb0c7f88c))
* display mobile swatches only on mobile ([6552a68](https://github.com/carawayhome/caraway-meridian/commit/6552a68bc783ba15786082b8d6f6dd17312b922d))
* extend global window interface ([b484e0e](https://github.com/carawayhome/caraway-meridian/commit/b484e0edf7f2c95e2bbd1265fbf9ab66b23666bd))
* first add to cart event with new cart ([6cb05c4](https://github.com/carawayhome/caraway-meridian/commit/6cb05c438a5f439ed12c36e72188628ec55ec524))
* fix localhost domain in the cookie set ([04af29b](https://github.com/carawayhome/caraway-meridian/commit/04af29b7d2c6dc2bd5af2c4dd9a0dc2fe3d98076))
* issue with block description render ([0876b1f](https://github.com/carawayhome/caraway-meridian/commit/0876b1fe59799e623026536df2bb73f875a40b69))
* linter fix ([70d46dd](https://github.com/carawayhome/caraway-meridian/commit/70d46dd6312cf9359f56989c43d6c23f7afaa5cd))
* missing cta when href is undefined ([7bfb0d3](https://github.com/carawayhome/caraway-meridian/commit/7bfb0d32195adfaaff9c9de82291549ae2fa9817))
* missing query fragment fix ([c1f0d05](https://github.com/carawayhome/caraway-meridian/commit/c1f0d05f72cafce8e8b460439f6dd2dd7393ca35))
* missing query fragment fix ([71b1ba4](https://github.com/carawayhome/caraway-meridian/commit/71b1ba48bf6cb26427d9cb3f43ae713b47fddd84))
* move box shadow to correct element ([487b937](https://github.com/carawayhome/caraway-meridian/commit/487b937884970b4faef5d88c52379200101353cb))
* move variables out of useEffect ([bd942bc](https://github.com/carawayhome/caraway-meridian/commit/bd942bc31559acddec5689055ad52b7b472a37c2))
* no override byline ([c1f34c8](https://github.com/carawayhome/caraway-meridian/commit/c1f34c8f0204214ff318ad1b0a5f05adc99077c5))
* prevent double add on adding to redux store ([350d496](https://github.com/carawayhome/caraway-meridian/commit/350d4964b6a537c4169f3402c45d32ae6f8563e4))
* remove redundant pageview call in Tatari initialization ([d65933d](https://github.com/carawayhome/caraway-meridian/commit/d65933d0aa1e5da387121ddf85dd00aac8204eb3))
* swatches panel obscuring other elements when collapsed ([101ddc6](https://github.com/carawayhome/caraway-meridian/commit/101ddc6ddda319ac4d8ac1d7817ce33793085807))
* ts error ([3f0afde](https://github.com/carawayhome/caraway-meridian/commit/3f0afdeeebb6b1c7c23a1f90e5a0fe70c057ccc5))
* typescript errors ([e31eced](https://github.com/carawayhome/caraway-meridian/commit/e31eced677b9ba73132e1b6f44aae9c43be85e87))
* update axon event types ([40969d9](https://github.com/carawayhome/caraway-meridian/commit/40969d9a26555e2d476ebfec43ca23453de41dc5))
* update outdated swatch props ([e6b51f7](https://github.com/carawayhome/caraway-meridian/commit/e6b51f71a3cf2f3f038285057190ce7050ac4dfc))
* update pixel and events ([a6fd767](https://github.com/carawayhome/caraway-meridian/commit/a6fd76753e2071292b81de793f2c3ca1bda0c6f3))
* use correct order when combining attributes ([3ee01f5](https://github.com/carawayhome/caraway-meridian/commit/3ee01f53ac6f483dcffab23e8f88eb71e6914e0a))
* wait for initial cookies to be available before setting ([b1799c8](https://github.com/carawayhome/caraway-meridian/commit/b1799c8b501080054481a8db449c89c8117671ec))

## [0.46.0](https://github.com/carawayhome/caraway-meridian/compare/v0.45.0...v0.46.0) (2024-11-14)


### Features

* **categories:** simplify categorySliderContainer styles and add justifyStart prop ([ce326ed](https://github.com/carawayhome/caraway-meridian/commit/ce326edc7173394a20544761f364a317f00da963))
* **progress:** event typing always gets me ([bc56136](https://github.com/carawayhome/caraway-meridian/commit/bc5613603bbf488b0119176aa072455f69dce74e))
* **progress:** fixing phone factor delete ([35a2829](https://github.com/carawayhome/caraway-meridian/commit/35a28299b9b8352c00b219794ed9c418720a6ca3))
* **progress:** removing tooltip from gift card PDP ([a5301ce](https://github.com/carawayhome/caraway-meridian/commit/a5301ceca501aa5ea036bf3f9ed4945060f7e04a))
* **PromotionalBlockSlider:** standard the extractor and test cms ([8d79e44](https://github.com/carawayhome/caraway-meridian/commit/8d79e4470fc3afc73440bcce99d2ed2291660425))
* **slider:** add justifyStart prop for left alignment option ([57a68c0](https://github.com/carawayhome/caraway-meridian/commit/57a68c00d2dfa73a18210081d82560942133f502))


### Bug Fixes

* added filtering to payload to remove non products ([d9e1926](https://github.com/carawayhome/caraway-meridian/commit/d9e1926634752b2d0f72405f9a30b94e998bcda2))
* **extractor:** pR Review comments ([82e59ef](https://github.com/carawayhome/caraway-meridian/commit/82e59ef279bc5cf8f1ff2159abf9c1ccc00b20dc))
* use themes to properly use colors ([8d487ad](https://github.com/carawayhome/caraway-meridian/commit/8d487ad1677807ef6efc2bc3381bc4e13b96b70e))

## [0.45.0](https://github.com/carawayhome/caraway-meridian/compare/v0.44.0...v0.45.0) (2024-11-12)


### Features

* add hero banner support for articles ([2477893](https://github.com/carawayhome/caraway-meridian/commit/24778936916ce0f56154404241d94be1f4d0f13d))
* **progress:** adding captionLarge ([78dd5bf](https://github.com/carawayhome/caraway-meridian/commit/78dd5bfd58db9765e394f5b4d1b0bd48adb9ff9b))
* **progress:** copy round 2 ([027de71](https://github.com/carawayhome/caraway-meridian/commit/027de71f817eab75de1f1cebcd9784e685731088))
* **progress:** mini cart ui bug fix ([3dcf0aa](https://github.com/carawayhome/caraway-meridian/commit/3dcf0aa874137c7bd7a5a8b4a14e7dc867b78b9e))
* update SaveWithSet component to use overview description and key properties ([5861aea](https://github.com/carawayhome/caraway-meridian/commit/5861aeaf37c12e8061cdaa84e5fc4fdd9b3574a6))


### Bug Fixes

* **discount:** fix URL parameters handling in discount page ([175b4ce](https://github.com/carawayhome/caraway-meridian/commit/175b4ce2f4df9a24f449354640cf7a0fd16a9da0))
* height issue in the about us nav dropdown ([05b4794](https://github.com/carawayhome/caraway-meridian/commit/05b47941951ba50b99ac29f7094349510ae32120))
* prevent gallery height glitch on mobile ([c0b7ddb](https://github.com/carawayhome/caraway-meridian/commit/c0b7ddb7fec0084835160f33057480b6949b3e32))
* timestamp to utc ([73be374](https://github.com/carawayhome/caraway-meridian/commit/73be3743a196e67495e14df01b20013ccb02dffe))
* update import name for SaveWithASet component in Product.tsx ([389fe55](https://github.com/carawayhome/caraway-meridian/commit/389fe558d1ff6114c91ed19a92ff502b26c5a7c3))
* **zpattern:** layout inversed ([4557214](https://github.com/carawayhome/caraway-meridian/commit/455721499998d4b8e61edbeb0ede52dded30c72d))

## [0.44.0](https://github.com/carawayhome/caraway-meridian/compare/v0.43.0...v0.44.0) (2024-11-07)


### Features

* **progress:** making icon bigger and font smaller ([9790b88](https://github.com/carawayhome/caraway-meridian/commit/9790b88cbe9639bb2be4e23c8feb6ed8da8ebe27))
* **progress:** mini update ([a50c20d](https://github.com/carawayhome/caraway-meridian/commit/a50c20dccfbe2f61c56b608d25d9cb91d18df05b))


### Bug Fixes

* add checkout started to cart footer ([7d736ef](https://github.com/carawayhome/caraway-meridian/commit/7d736efbf24a51c00f55adeadd84d04e761bcae2))
* fix TS error ([51607c6](https://github.com/carawayhome/caraway-meridian/commit/51607c61294639f9b79e5b7f557cc1c3b311facb))
* implement checkout formatter ([608e1b8](https://github.com/carawayhome/caraway-meridian/commit/608e1b8eb11955fa54e12d38e777d96974537fab))
* **query:** update limit ([73a6c33](https://github.com/carawayhome/caraway-meridian/commit/73a6c339c590915f6f467a8cfbf19f66e3313e0e))

## [0.43.0](https://github.com/carawayhome/caraway-meridian/compare/v0.42.0...v0.43.0) (2024-11-07)


### Features

* add addtocart for revroll ([6a67483](https://github.com/carawayhome/caraway-meridian/commit/6a6748320bd5bf8786d69961c63cdf92aba16fe9))
* add mini-cart start checkout event ([1d453fe](https://github.com/carawayhome/caraway-meridian/commit/1d453fee72814f5e745f6b560b2d0e93aeb5b4d6))
* add pageSectionsContent query and integrate into fetchPages, limit to 50 ([9ae78d0](https://github.com/carawayhome/caraway-meridian/commit/9ae78d0e3725c53d34fa962fefb825dbce5feafc))
* add pageSectionsProduct query and integrate into fetchPages ([50aa86f](https://github.com/carawayhome/caraway-meridian/commit/50aa86fb59633c7347a8c5e41c39632858aef5a5))
* add tatari ([f1d3cb3](https://github.com/carawayhome/caraway-meridian/commit/f1d3cb3f1d69732a36d0a12b585d2d105a70eebf))
* add tatari pixel ([fc4efb3](https://github.com/carawayhome/caraway-meridian/commit/fc4efb326e1914fffa6622080a5fe97dfa1b1109))
* add viewed product event for revroll ([9b1c6de](https://github.com/carawayhome/caraway-meridian/commit/9b1c6dee5d069987833a62da441f04b5877809e9))
* **progress:** adding base theme when no theme is provided for links like accessibility ([3545345](https://github.com/carawayhome/caraway-meridian/commit/3545345f309d2376862dcf389e03389ff1440542))
* **progress:** getting rid of the link style override that comes from rich text ([5073069](https://github.com/carawayhome/caraway-meridian/commit/50730695047f03a113230daa0eab36940f7e3075))
* **progress:** pdp qa ([32cd3e6](https://github.com/carawayhome/caraway-meridian/commit/32cd3e62bae504d4bcb48e269d23b253ef81a77e))


### Bug Fixes

* **component:** update valueProps and FAQ Details ([f35e770](https://github.com/carawayhome/caraway-meridian/commit/f35e7704f740a68bc7e8913fd61ba44d491ab933))
* **extractor:** hotFix InstructionsListCircle ([52d38c2](https://github.com/carawayhome/caraway-meridian/commit/52d38c259cff8b0c43ec16afea4b165425c78dce))
* **extractors:** update Icon Logos extractor ([2fbd1ce](https://github.com/carawayhome/caraway-meridian/commit/2fbd1ce147a33573f4f7d37dad97bfa270688a85))
* **extractor:** update Instructions List Circle ([74f509c](https://github.com/carawayhome/caraway-meridian/commit/74f509c532e82427b301ff5322b32b96ffa1d562))

## [0.42.0](https://github.com/carawayhome/caraway-meridian/compare/v0.41.0...v0.42.0) (2024-11-04)


### Features

* **component:** update extractor timeline slider ([8c00e74](https://github.com/carawayhome/caraway-meridian/commit/8c00e7446c0890a17e29558e00fdac16cf8b7003))
* **component:** update product carousel ([e875832](https://github.com/carawayhome/caraway-meridian/commit/e87583208955be42c227a9ce837d491f07131d00))
* **progress:** qa update ([ace02dd](https://github.com/carawayhome/caraway-meridian/commit/ace02dddbac98d09fcb432f66c812e4af64b9568))
* **progress:** qaing with katie ([33830f8](https://github.com/carawayhome/caraway-meridian/commit/33830f86c2c8c56799d89d028cd34dc1b14d701c))
* **progress:** updating size of the desktop closed icon ([21480cc](https://github.com/carawayhome/caraway-meridian/commit/21480cc13348f815fe3261ff3d3c9356bce399c6))


### Bug Fixes

* **Tooltips:** conflicts ([25ff451](https://github.com/carawayhome/caraway-meridian/commit/25ff451ff28d82692872e7621fe436aaa5d600b1))

## [0.41.0](https://github.com/carawayhome/caraway-meridian/compare/v0.40.0...v0.41.0) (2024-11-01)


### Features

* default holiday24 ([433e3f2](https://github.com/carawayhome/caraway-meridian/commit/433e3f27eafe18415044989c78bae4760b24f661))
* **Hero:** back to richTExt ([5e51469](https://github.com/carawayhome/caraway-meridian/commit/5e514695e581ca425d23b65737dc10a74ec4e084))
* **progress:** tooltip decimal cutoff ([c81237b](https://github.com/carawayhome/caraway-meridian/commit/c81237bbb7b9b9fb3e81106c93fc46bb136ee218))
* **progress:** updating copy for tooltip ([32e42c9](https://github.com/carawayhome/caraway-meridian/commit/32e42c9ca2ec3c77bf3a33fe44a586e9fed93f37))
* **scripts:** add Amped script ([2be295b](https://github.com/carawayhome/caraway-meridian/commit/2be295be2308473610ba8b70881549bd3c0725ff))
* **scripts:** update Amped script URL ([70e52cd](https://github.com/carawayhome/caraway-meridian/commit/70e52cdac107cedcd3b02b6fc87b9aaf2d2f5069))


### Bug Fixes

* added container for overview accordion list ([59a63f7](https://github.com/carawayhome/caraway-meridian/commit/59a63f7b514a3c08f9202f2ce19c150b27030eee))
* **buid:** generateTtkTrackingId for all events ([4efcf98](https://github.com/carawayhome/caraway-meridian/commit/4efcf98df81190e0418f64485f687765b788f637))
* **build:** change uuid to random string ([4d5fa6a](https://github.com/carawayhome/caraway-meridian/commit/4d5fa6aa878aa51264579cc9703d2650132b5dda))
* **build:** issue with window types ([6973b51](https://github.com/carawayhome/caraway-meridian/commit/6973b512d5b479f58b43d4ea9b0f72491c6e1511))
* **build:** less especific ([064d4d2](https://github.com/carawayhome/caraway-meridian/commit/064d4d224ccd37175c1fb14caa6c772ef26511b5))
* **build:** test add types with new version ([e2a2a9e](https://github.com/carawayhome/caraway-meridian/commit/e2a2a9ee0fba77ae3ee070e3292ad3384b1bdac0))
* **build:** ts more especific ([4ae56f3](https://github.com/carawayhome/caraway-meridian/commit/4ae56f3cbb3265df2e3eed2eda9bac229dd9f06b))
* **build:** uuid types ([d62c209](https://github.com/carawayhome/caraway-meridian/commit/d62c209ed13bdcf4940f87a358a5e541d16ec1ce))
* **build:** uuid version and type definitions ([88adb44](https://github.com/carawayhome/caraway-meridian/commit/88adb441e7cddb7f4c2980a3381d52a82abec4fe))
* now using a field from contentful to specify the page category ([be6bde3](https://github.com/carawayhome/caraway-meridian/commit/be6bde3adb498797dcc44cba31b2dd8487ef72a1))
* overview accordion list ([b0068bb](https://github.com/carawayhome/caraway-meridian/commit/b0068bbb4202c33ec83aa85aaa019523db4cf097))

## [0.40.0](https://github.com/carawayhome/caraway-meridian/compare/v0.39.0...v0.40.0) (2024-10-31)


### Features

* add rich snippet price support [sc-13890] ([69d5b69](https://github.com/carawayhome/caraway-meridian/commit/69d5b6960c8c3c54b5b993df712726363e6afaa8))
* added optional size prop default to original size + mobile sizes Optional xmall prop for nav ([f403753](https://github.com/carawayhome/caraway-meridian/commit/f40375322f2e11bfe2eaccc9c7f59e7a35114f93))
* adding size to types and Callouts component ([1ac2316](https://github.com/carawayhome/caraway-meridian/commit/1ac23164ef5cf280bfd778068a4e80d66a54cf9e))
* **component:** product Card Carousel ([2e06d61](https://github.com/carawayhome/caraway-meridian/commit/2e06d617ff7d14dec33d767ca8c049839b1f1093))
* **component:** update Cart Icons ([5620d33](https://github.com/carawayhome/caraway-meridian/commit/5620d33f860da63d83f37615c1fe06822fae18e7))
* factor in savings from msrp into badges ([d7a06d6](https://github.com/carawayhome/caraway-meridian/commit/d7a06d607cbd0b3825afd4c964efc2408b7726e1))
* **gift card page:** zest component ([cdcdb05](https://github.com/carawayhome/caraway-meridian/commit/cdcdb058b837ec509c4e92f35568e8b5fb4d65b2))
* **Hero:** layout 9 and 10 with docs ([480af1f](https://github.com/carawayhome/caraway-meridian/commit/480af1fe1981fc9a68e3c9c0f45df1425e4d0dd0))
* **Hero:** layout 9 left aligned ([970defa](https://github.com/carawayhome/caraway-meridian/commit/970defac76faa1ad53c592b52e6519dcc435d46f))
* porting contact us form ([40e9cb4](https://github.com/carawayhome/caraway-meridian/commit/40e9cb4592efcc4932cd0a42247d937072103408))
* **ProductSwatches:** grid ([44e622b](https://github.com/carawayhome/caraway-meridian/commit/44e622b795166f3768abb2b30fd66947dc630dd4))
* **progress:** addressing comments ([cdc483f](https://github.com/carawayhome/caraway-meridian/commit/cdc483fbec54d18595e5cb847df2e584062eefa9))
* **progress:** cart behavior fix ([ba27b73](https://github.com/carawayhome/caraway-meridian/commit/ba27b73e9e46e8db4e623828bb69e4ecd28c5eb4))
* **progress:** clean up ([d3ccf68](https://github.com/carawayhome/caraway-meridian/commit/d3ccf6865e28527e5970e836bbcd64e2639895ab))
* **progress:** fixed the sticking issue of the slider ([4ae28d8](https://github.com/carawayhome/caraway-meridian/commit/4ae28d8846c172b6714cc823a741f2ccbe971158))
* **progress:** free product icon update qa ([d791acf](https://github.com/carawayhome/caraway-meridian/commit/d791acfe4fa974f2cf9e964f946c3c732cc897dd))
* **progress:** getting ready for qa ([b37aa42](https://github.com/carawayhome/caraway-meridian/commit/b37aa42a48cd16400e75e17ada6f040cb711d7ec))
* **progress:** updated the count to 3 so itll work for gift collections pages ([7dcc30c](https://github.com/carawayhome/caraway-meridian/commit/7dcc30c2bb7ede605f59900e3440f0d879de8525))
* **progress:** updating line item ([c6b9939](https://github.com/carawayhome/caraway-meridian/commit/c6b99394f9ba5788988ff50cc60e78676c1cbd04))
* **progress:** updating the before savings calculations logic ([debce0a](https://github.com/carawayhome/caraway-meridian/commit/debce0a056a7e03b144d15c37c57861b4dac758a))
* q/a updates with Kelsye ([afd0034](https://github.com/carawayhome/caraway-meridian/commit/afd003446394397b41775e63712b3e9e7bfd17db))
* support for single image image panels ([bea5362](https://github.com/carawayhome/caraway-meridian/commit/bea53621e33035af38481bf5a71a03048f349c54))
* **swatches:** buy with zest ([35d13e7](https://github.com/carawayhome/caraway-meridian/commit/35d13e7419a4ab9d8a5718e8c9e534969b9fea33))
* **Swatches:** change grid for no icon ([8458fcc](https://github.com/carawayhome/caraway-meridian/commit/8458fcc0b7d752f6156055d6bad858fdb1a51c2a))
* update Callout component with optional size prop ([30cccea](https://github.com/carawayhome/caraway-meridian/commit/30cccea4c179d9b7e552590167dad33192980c73))
* updated types to optional ([08a7175](https://github.com/carawayhome/caraway-meridian/commit/08a71752cd6f4f849be8ccbadacca335b97c37e9))


### Bug Fixes

* anchor navigation collection component to center align subheader and header text ([4d1c70c](https://github.com/carawayhome/caraway-meridian/commit/4d1c70c06d030134936fffb50fb2543ca13226ce))
* **gift card:** build ([1bd731f](https://github.com/carawayhome/caraway-meridian/commit/1bd731f876b867d9315eee11b3f2bf5d8dcc0d27))
* **hero:** merge ([ab7034d](https://github.com/carawayhome/caraway-meridian/commit/ab7034df1afc626dbcc0f010dc8db33fd14362bc))
* jsonLd.ts to update displayPrice to price in generateOffers function ([5280e80](https://github.com/carawayhome/caraway-meridian/commit/5280e80d397f1757b76f5669441f9a9fdfd28ca8))
* not-found page to display a custom message for 404 error ([dd2169a](https://github.com/carawayhome/caraway-meridian/commit/dd2169aef898e9a38e7f5cf29e605ef4ba51ca01))
* **product page:** back to group param ([c30cb5f](https://github.com/carawayhome/caraway-meridian/commit/c30cb5f044b2049d747f5a37ed1f714edf662457))
* **Promotional Blocks:** css ([0b7a496](https://github.com/carawayhome/caraway-meridian/commit/0b7a4961c8e70f8cc7182ad0c673503d0329e413))
* require track, pricing theme, anchor alignment ([f04d3ac](https://github.com/carawayhome/caraway-meridian/commit/f04d3acdc0485e8aad1c483a1bc94b77a1b0093a))
* **Zpattern:** fix layout prop ([ff9149a](https://github.com/carawayhome/caraway-meridian/commit/ff9149ad98a7957205ecd05d50b80442ec58e0b0))
* **Zpattern:** layout as camel ([37f5f8e](https://github.com/carawayhome/caraway-meridian/commit/37f5f8e479b10a870bb23839a35cc7952191745c))

## [0.40.0](https://github.com/carawayhome/caraway-meridian/compare/v0.39.0...v0.40.0) (2024-10-31)


### Features

* add rich snippet price support [sc-13890] ([69d5b69](https://github.com/carawayhome/caraway-meridian/commit/69d5b6960c8c3c54b5b993df712726363e6afaa8))
* added optional size prop default to original size + mobile sizes Optional xmall prop for nav ([f403753](https://github.com/carawayhome/caraway-meridian/commit/f40375322f2e11bfe2eaccc9c7f59e7a35114f93))
* adding size to types and Callouts component ([1ac2316](https://github.com/carawayhome/caraway-meridian/commit/1ac23164ef5cf280bfd778068a4e80d66a54cf9e))
* **component:** product Card Carousel ([2e06d61](https://github.com/carawayhome/caraway-meridian/commit/2e06d617ff7d14dec33d767ca8c049839b1f1093))
* **component:** update Cart Icons ([5620d33](https://github.com/carawayhome/caraway-meridian/commit/5620d33f860da63d83f37615c1fe06822fae18e7))
* factor in savings from msrp into badges ([d7a06d6](https://github.com/carawayhome/caraway-meridian/commit/d7a06d607cbd0b3825afd4c964efc2408b7726e1))
* **Hero:** layout 9 and 10 with docs ([480af1f](https://github.com/carawayhome/caraway-meridian/commit/480af1fe1981fc9a68e3c9c0f45df1425e4d0dd0))
* **Hero:** layout 9 left aligned ([970defa](https://github.com/carawayhome/caraway-meridian/commit/970defac76faa1ad53c592b52e6519dcc435d46f))
* porting contact us form ([40e9cb4](https://github.com/carawayhome/caraway-meridian/commit/40e9cb4592efcc4932cd0a42247d937072103408))
* **progress:** addressing comments ([cdc483f](https://github.com/carawayhome/caraway-meridian/commit/cdc483fbec54d18595e5cb847df2e584062eefa9))
* **progress:** cart behavior fix ([ba27b73](https://github.com/carawayhome/caraway-meridian/commit/ba27b73e9e46e8db4e623828bb69e4ecd28c5eb4))
* **progress:** clean up ([d3ccf68](https://github.com/carawayhome/caraway-meridian/commit/d3ccf6865e28527e5970e836bbcd64e2639895ab))
* **progress:** fixed the sticking issue of the slider ([4ae28d8](https://github.com/carawayhome/caraway-meridian/commit/4ae28d8846c172b6714cc823a741f2ccbe971158))
* **progress:** free product icon update qa ([d791acf](https://github.com/carawayhome/caraway-meridian/commit/d791acfe4fa974f2cf9e964f946c3c732cc897dd))
* **progress:** getting ready for qa ([b37aa42](https://github.com/carawayhome/caraway-meridian/commit/b37aa42a48cd16400e75e17ada6f040cb711d7ec))
* **progress:** updated the count to 3 so itll work for gift collections pages ([7dcc30c](https://github.com/carawayhome/caraway-meridian/commit/7dcc30c2bb7ede605f59900e3440f0d879de8525))
* **progress:** updating line item ([c6b9939](https://github.com/carawayhome/caraway-meridian/commit/c6b99394f9ba5788988ff50cc60e78676c1cbd04))
* **progress:** updating the before savings calculations logic ([debce0a](https://github.com/carawayhome/caraway-meridian/commit/debce0a056a7e03b144d15c37c57861b4dac758a))
* q/a updates with Kelsye ([afd0034](https://github.com/carawayhome/caraway-meridian/commit/afd003446394397b41775e63712b3e9e7bfd17db))
* support for single image image panels ([bea5362](https://github.com/carawayhome/caraway-meridian/commit/bea53621e33035af38481bf5a71a03048f349c54))
* update Callout component with optional size prop ([30cccea](https://github.com/carawayhome/caraway-meridian/commit/30cccea4c179d9b7e552590167dad33192980c73))
* updated types to optional ([08a7175](https://github.com/carawayhome/caraway-meridian/commit/08a71752cd6f4f849be8ccbadacca335b97c37e9))


### Bug Fixes

* anchor navigation collection component to center align subheader and header text ([4d1c70c](https://github.com/carawayhome/caraway-meridian/commit/4d1c70c06d030134936fffb50fb2543ca13226ce))
* **hero:** merge ([ab7034d](https://github.com/carawayhome/caraway-meridian/commit/ab7034df1afc626dbcc0f010dc8db33fd14362bc))
* jsonLd.ts to update displayPrice to price in generateOffers function ([5280e80](https://github.com/carawayhome/caraway-meridian/commit/5280e80d397f1757b76f5669441f9a9fdfd28ca8))
* not-found page to display a custom message for 404 error ([dd2169a](https://github.com/carawayhome/caraway-meridian/commit/dd2169aef898e9a38e7f5cf29e605ef4ba51ca01))
* **Promotional Blocks:** css ([0b7a496](https://github.com/carawayhome/caraway-meridian/commit/0b7a4961c8e70f8cc7182ad0c673503d0329e413))
* require track, pricing theme, anchor alignment ([f04d3ac](https://github.com/carawayhome/caraway-meridian/commit/f04d3acdc0485e8aad1c483a1bc94b77a1b0093a))
* **Zpattern:** fix layout prop ([ff9149a](https://github.com/carawayhome/caraway-meridian/commit/ff9149ad98a7957205ecd05d50b80442ec58e0b0))
* **Zpattern:** layout as camel ([37f5f8e](https://github.com/carawayhome/caraway-meridian/commit/37f5f8e479b10a870bb23839a35cc7952191745c))

## [0.39.0](https://github.com/carawayhome/caraway-meridian/compare/v0.38.0...v0.39.0) (2024-10-29)


### Features

* add AppsWidgets and TolstoySection components ([2185a4a](https://github.com/carawayhome/caraway-meridian/commit/2185a4a27283e559a5677ffc05300a602d3f0609))
* add support for mobile media in hero slider component ([804c9cf](https://github.com/carawayhome/caraway-meridian/commit/804c9cf3558ac8dab2f71da2a35503e1f3bbe1b3))
* **component:** connect to Contentful Categories Small ([a0fa028](https://github.com/carawayhome/caraway-meridian/commit/a0fa028a9752cf43193ff73d52e8178aa1b2bf1c))
* **component:** connect to Contentful the Tetris Module ([ed2c172](https://github.com/carawayhome/caraway-meridian/commit/ed2c1723a0a372817b2a38d0f352ea1a44f3018d))
* hero slider component and extractHero function ([428f8ea](https://github.com/carawayhome/caraway-meridian/commit/428f8eabaa41995eeb7f86b613f374adaa3491d1))
* merge: ([6305005](https://github.com/carawayhome/caraway-meridian/commit/63050055e20566ece08be47f4c1b5e48c93f5fe4))
* **progress:** addressing teos pr comments ([85d1622](https://github.com/carawayhome/caraway-meridian/commit/85d1622a567d75ed7d6948f5d0b0cc134143dfbd))
* **progress:** building ([384277b](https://github.com/carawayhome/caraway-meridian/commit/384277b74861545af012ad33b4f0b0d0e71787ee))
* **progress:** cart ui updates request teo ([13a317b](https://github.com/carawayhome/caraway-meridian/commit/13a317b9f08394e92b285ba71964280529b362b5))
* **progress:** fixing the mini cart ([4a6b43e](https://github.com/carawayhome/caraway-meridian/commit/4a6b43ea6244bc743477d37592e6506bf979308b))
* **progress:** got the product to add and delete and update ([b66e9ab](https://github.com/carawayhome/caraway-meridian/commit/b66e9abcdb937ff287d12405acbc09baa7c83bc8))
* **progress:** improvements ([989b8b4](https://github.com/carawayhome/caraway-meridian/commit/989b8b4af5436035a68d220d89917d4ab919886c))
* **progress:** lint ([2b61b70](https://github.com/carawayhome/caraway-meridian/commit/2b61b70c184ed2cb938f43ab9ebf26230e0c26b9))
* **progress:** linting ([8150de4](https://github.com/carawayhome/caraway-meridian/commit/8150de4bfb2806cde38bea3a28ce4896c32994cf))
* **progress:** subtotal doesnt reflect discounts line merchandise price amount does ([df2def7](https://github.com/carawayhome/caraway-meridian/commit/df2def73b657b16c67674a27af32376453ad3110))
* **PromotionalBlockSlider:** one child no slider ([2a90ca1](https://github.com/carawayhome/caraway-meridian/commit/2a90ca18500a56b262d229fa836fc14576d17c67))
* refactor table row component in HowWeCompare ([3232bd8](https://github.com/carawayhome/caraway-meridian/commit/3232bd86e54c9d807adffeaf57b698271a4445ef))
* revroll ([88925a5](https://github.com/carawayhome/caraway-meridian/commit/88925a587d532a3b03f783baec7dc3a084e02092))
* support dialog mobile content, and image ([85df411](https://github.com/carawayhome/caraway-meridian/commit/85df411389c353351093938e0bae6c4b008c9d1a))
* using percentages for about dropdown over px values ([cbf0423](https://github.com/carawayhome/caraway-meridian/commit/cbf0423d60bab267af2822f12af8d20890e07926))


### Bug Fixes

* **Ambassadors Program:** qa comments ([8e6af58](https://github.com/carawayhome/caraway-meridian/commit/8e6af5812274936f326770cab9a3326daf9956a3))
* anchor visibility for a11y ([cd38480](https://github.com/carawayhome/caraway-meridian/commit/cd3848050128ab16bfe3d4c16c04d07c5408d1e1))
* cta hover state ([6194dc8](https://github.com/carawayhome/caraway-meridian/commit/6194dc86ea70f5a6d9fe893b121c925903b44d26))
* dont build hidden collection pages ([fcab381](https://github.com/carawayhome/caraway-meridian/commit/fcab381057f814123b667368b68029d928c644f6))
* increase product collection limit to 30 ([d484a71](https://github.com/carawayhome/caraway-meridian/commit/d484a71435227ccbcb3eb1814b4bddd225d9a18d))
* misc cart updates and fix cart summary ([e911bfb](https://github.com/carawayhome/caraway-meridian/commit/e911bfb68792a3c4e8bbabf59aa157e45189a1d9))
* mobile grid row gap ([24dfe5e](https://github.com/carawayhome/caraway-meridian/commit/24dfe5e412cbdc2b9e81590f57e1f65390ffb8dc))
* place swatches right below card image on mobile ([1b47c97](https://github.com/carawayhome/caraway-meridian/commit/1b47c97436cb01d592584665159ed02ad062b5e7))
* product name typography ([bb1c4d2](https://github.com/carawayhome/caraway-meridian/commit/bb1c4d2d19f94a20a662152167017f50e64355d3))
* **Redirect:** many redirects ([2ac4bc8](https://github.com/carawayhome/caraway-meridian/commit/2ac4bc8062c7ba5c7967519892027c9d0cc0782b))
* resolve hover image not covering the whole card image ([5ce6e2c](https://github.com/carawayhome/caraway-meridian/commit/5ce6e2c754aa3f030030e5b130460e9c2892104d))
* revroll ([916938a](https://github.com/carawayhome/caraway-meridian/commit/916938a8f1f8ad1e26d5a432dbcbd82213d56eab))
* update MiniCartProgressBar component to include key prop in mapped elements ([c5c07f4](https://github.com/carawayhome/caraway-meridian/commit/c5c07f43dd525c448ed6bd4b4f9095c504c6caf6))
* use h4Seconday as product grid title ([b64dc4f](https://github.com/carawayhome/caraway-meridian/commit/b64dc4feec1b010365f78f9a4e4f50ba9db454b0))

## [0.38.0](https://github.com/carawayhome/caraway-meridian/compare/v0.37.0...v0.38.0) (2024-10-25)


### Features

* **component:** connect Timeline Component to Contentful ([4bc125c](https://github.com/carawayhome/caraway-meridian/commit/4bc125c3f5c43c8fa68450ac35b63c06cc222848))
* mobile typography ([0d6d052](https://github.com/carawayhome/caraway-meridian/commit/0d6d0526e87164388c520556920e9edfa9b9c43c))
* **trade program page:** components cta larger prop ([72f0c4c](https://github.com/carawayhome/caraway-meridian/commit/72f0c4c0c717305e1eb75814d032a1d09377357d))
* use product default swatch if no variant cards added ([fb68a62](https://github.com/carawayhome/caraway-meridian/commit/fb68a62a19a23b5e23a51679f7ec6e98a0cef437))


### Bug Fixes

* allow default swatch on a product card without any variant cards: ([f1a4367](https://github.com/carawayhome/caraway-meridian/commit/f1a43673845dd67b99d1f98187efd1a6b8ac99a7))
* handle product cards and use default swatch only for product based cards ([4cc2a11](https://github.com/carawayhome/caraway-meridian/commit/4cc2a11beff4100d8084067fcd2f61d98fc8b025))
* **MediaContainer:** flying icon ([2280981](https://github.com/carawayhome/caraway-meridian/commit/22809818eda4f3b14fde61eb4b89621017364937))
* missing ID issue ([539806d](https://github.com/carawayhome/caraway-meridian/commit/539806d131db868a8b7fd339feb3c89fb5aeeedf))
* **Trade Program Page:** deploy ([1de9cd9](https://github.com/carawayhome/caraway-meridian/commit/1de9cd96fefd239c5ce6d0f4d22ba586bafb149d))
* **Trade Program Page:** lint ([07a6653](https://github.com/carawayhome/caraway-meridian/commit/07a66530eba63374f095578ac322c58f4e63c354))
* **Trade Program Page:** merge ([1553254](https://github.com/carawayhome/caraway-meridian/commit/1553254f57ac77a6c559420c30c459c3c992e530))
* **Trade Program Page:** miss match hydratation ([451caec](https://github.com/carawayhome/caraway-meridian/commit/451caec8b42c0fbade3269f2251f5f1ba04aa13c))
* **Trade Program:** mergfe ([48b7b39](https://github.com/carawayhome/caraway-meridian/commit/48b7b3943d49b24a9e101c5b9e131acedd5d5791))
* use product metadata hover image if product card has none ([43cd45e](https://github.com/carawayhome/caraway-meridian/commit/43cd45ec8d11c0e9b668d12dd5ff1ebb2b8de659))
* various ui updates ([fc17fbf](https://github.com/carawayhome/caraway-meridian/commit/fc17fbfe4c03502555c7401d852b0ac5264580c5))

## [0.37.0](https://github.com/carawayhome/caraway-meridian/compare/v0.36.0...v0.37.0) (2024-10-24)


### Features

* add support for holiday feature flag ([a96e590](https://github.com/carawayhome/caraway-meridian/commit/a96e59038da3111eee31525eee64c1b1ccab21f6))
* add support for the anchor navigation to support the header and subheader ([bfd74fc](https://github.com/carawayhome/caraway-meridian/commit/bfd74fc2856c711f7b2b9f7bc17d229af52bfe29))
* **progress:** force build ([3504875](https://github.com/carawayhome/caraway-meridian/commit/3504875d88c5138861beea0c44c39396f0869f31))
* **progress:** qa addressing teos comments ([a68234f](https://github.com/carawayhome/caraway-meridian/commit/a68234fa5bf5dace58045d5385dc2041ca537125))
* **progress:** qa build error ([8f06f09](https://github.com/carawayhome/caraway-meridian/commit/8f06f09287d23b903f265390cfc0a786a2a05773))
* **progress:** removing unnecessary assets ([bff97ec](https://github.com/carawayhome/caraway-meridian/commit/bff97ecd47d50bd8ac4e75e29919c9f85e5602d5))
* **progress:** slight style adjustment to match design ([d40a51d](https://github.com/carawayhome/caraway-meridian/commit/d40a51dcb33aa5a7b25432d5b7a5e864b1674f08))
* **progress:** updating to use container and update its type ([a030fe7](https://github.com/carawayhome/caraway-meridian/commit/a030fe7a4e16546cbe0bbd424b939644aac1316c))
* update buttons in PageCTA and TextBanner components, plus query ([7a7075b](https://github.com/carawayhome/caraway-meridian/commit/7a7075be6f37981e49daf50b44e878f1f6bca95d))


### Bug Fixes

* add support for swatches on a PLP ([62770e8](https://github.com/carawayhome/caraway-meridian/commit/62770e8e8e2b21a5e68ec1b658f5f2d02e25d6e1))
* missing footer content ([56b6311](https://github.com/carawayhome/caraway-meridian/commit/56b63112972044140c16773ebb77104c52f91909))
* use themeTokens instead of custom style ([2249f93](https://github.com/carawayhome/caraway-meridian/commit/2249f9364d2b70943a317acf6e735b2c3480a252))
* use Typography props instead of fontFamily ([9f7fca9](https://github.com/carawayhome/caraway-meridian/commit/9f7fca9fc224daeea562116db64f960c98281532))

## [0.36.0](https://github.com/carawayhome/caraway-meridian/compare/v0.35.0...v0.36.0) (2024-10-23)


### Features

* add dialog triggers and connect to PageCTA ([1e0ed91](https://github.com/carawayhome/caraway-meridian/commit/1e0ed9145ca59bf7be804b822e33fde0127e32e3))
* add height to Promobar Message component ([b94dbdd](https://github.com/carawayhome/caraway-meridian/commit/b94dbddc887072d38259a9f4ea334281d597e64e))
* add support for dialogtriggers on page hero ([75fbaaa](https://github.com/carawayhome/caraway-meridian/commit/75fbaaa681d1260629c43ec9b9579b683ca2b024))
* default swatch product metadata ([4e2385e](https://github.com/carawayhome/caraway-meridian/commit/4e2385eaaeb5811de7f883ca50005a6272b0a5b0))
* dynamic aggregated reviews messaging ([6a84cd4](https://github.com/carawayhome/caraway-meridian/commit/6a84cd47f6120b7738724433eed7a962db777f9b))
* **Hero:** add layout highlight ([1e448ae](https://github.com/carawayhome/caraway-meridian/commit/1e448ae4cc1f6521946eae1af6eb37597b171a4d))
* **Hero:** improve docw ([b2e55ea](https://github.com/carawayhome/caraway-meridian/commit/b2e55eae2b26432c123124b0091a764067715de2))
* **ScrollFeatureSlider:** connect with contentful ([37cd61e](https://github.com/carawayhome/caraway-meridian/commit/37cd61e468824678487c6eaed1b05edfd2c685c2))
* **ThreeBloksModules:** refactor and connect with cms ([08f2bc0](https://github.com/carawayhome/caraway-meridian/commit/08f2bc07c9bc4d5729eb93894351c3d94aca1256))


### Bug Fixes

* add scroll=false prop to ProductLink component ([4745ee3](https://github.com/carawayhome/caraway-meridian/commit/4745ee33fb55cf00a5ffa038682a2d4d434267e8))
* add slugify function to dialog theme in fetchGlobalDialogs.ts ([6b27716](https://github.com/carawayhome/caraway-meridian/commit/6b2771623b0e171438654d866fc6c49afd3b0fb2))
* add support for gift card swatches ([f9720ac](https://github.com/carawayhome/caraway-meridian/commit/f9720ac8b189afbe60e420021e8f18c1336cc5ab))
* adjust table width in HowWeCompare component ([f7efdcd](https://github.com/carawayhome/caraway-meridian/commit/f7efdcd329b92b7e30fce92a88fc484369c6922b))
* bring back missing product metadata query ([3864fee](https://github.com/carawayhome/caraway-meridian/commit/3864fee315ea11f12ccd6eecb3e16460f5d15783))
* media container fixed ([f411aef](https://github.com/carawayhome/caraway-meridian/commit/f411aef198dbaf9734e784da4249a17d4c4f7cee))
* promobar when mobile or desktop is empty ([d6c5688](https://github.com/carawayhome/caraway-meridian/commit/d6c5688e3bd6dfc6c4fc8eba07f335158d68dcf6))
* remove flexWrap (not a prop) and add missing alt ([3ac2aac](https://github.com/carawayhome/caraway-meridian/commit/3ac2aac99775beda5eb1f1a22184cfa2e3592cf8))
* table shadow ([b911648](https://github.com/carawayhome/caraway-meridian/commit/b911648a090e3dbcb8edee4c904d76b3d7a74a20))

## [0.35.0](https://github.com/carawayhome/caraway-meridian/compare/v0.34.0...v0.35.0) (2024-10-19)


### Features

* comparison chart for catalog ([0c9d01a](https://github.com/carawayhome/caraway-meridian/commit/0c9d01ad24ecfbb2cd45f220c79ea84d6d31cd39))
* connect anchor tag links and anchors on grid ([c313984](https://github.com/carawayhome/caraway-meridian/commit/c3139848998e486899523507012d287c1bdf3761))


### Bug Fixes

* compare on mobile, nav shadow, anchor on mobile [sc-15063] ([8c3a8e6](https://github.com/carawayhome/caraway-meridian/commit/8c3a8e6c6ee041349347b72a8a2c16f1287b2c98))
* key on acnhors ([027c22c](https://github.com/carawayhome/caraway-meridian/commit/027c22cac821ed84b9e4cb774396dd54f6fd4495))
* optimize rendering of swatch collections in ProductSwatches component ([5a99710](https://github.com/carawayhome/caraway-meridian/commit/5a99710bfec7e7dfa5b7aec2447af2ff0ef3d9ad))

## [0.34.0](https://github.com/carawayhome/caraway-meridian/compare/v0.33.0...v0.34.0) (2024-10-18)


### Features

* **components:** update merge conflicts ([72d41bc](https://github.com/carawayhome/caraway-meridian/commit/72d41bc39f6cbf93a985cf6713e704e5f3accd81))
* **components:** update PR Review ([27fdd99](https://github.com/carawayhome/caraway-meridian/commit/27fdd99f32ab02d47aba2ab5b04a83ff0479f209))
* **component:** update AtomicLevelTooltip ([bb48941](https://github.com/carawayhome/caraway-meridian/commit/bb48941f77e813a23b88d439f745a7199bfff86b))
* **component:** update block Extractor ([77c53b4](https://github.com/carawayhome/caraway-meridian/commit/77c53b42eb5eb36c10f3406815ebecf0b85f5067))
* **component:** update FeatureSlider ([5973968](https://github.com/carawayhome/caraway-meridian/commit/59739688c62505b7ab757989b515ef7d45e4891d))
* **component:** update TextBanner ([6a5fc6c](https://github.com/carawayhome/caraway-meridian/commit/6a5fc6c47548fe186b212aa97b9a11ad5ed733cd))
* **component:** update ZPattern ([74920eb](https://github.com/carawayhome/caraway-meridian/commit/74920eba140633c80e3a7660f43c0092c7670e5d))
* klaviyo form connect Klaviyo Form x Connect [sc-15053] ([21775c8](https://github.com/carawayhome/caraway-meridian/commit/21775c8e68fe3878b63613970c01661d9094fef6))
* merge main ([af0f76d](https://github.com/carawayhome/caraway-meridian/commit/af0f76de8b5c26553d91cf3a5b2dfb49ebc45e99))
* merge main in, resolve conflict, improve toggles ([7b42dbe](https://github.com/carawayhome/caraway-meridian/commit/7b42dbeac6e607e04076d820fe2f763720c2b323))
* use product title instead of name ([c706e26](https://github.com/carawayhome/caraway-meridian/commit/c706e26fd2775f0eab6f0251ec86eb33bdc79332))


### Bug Fixes

* content assignment in Feature component ([cab6772](https://github.com/carawayhome/caraway-meridian/commit/cab6772571de79364e62344f2551072e1c995f98))
* **updates:** main merge conflicts ([54e01bb](https://github.com/carawayhome/caraway-meridian/commit/54e01bbed29bac086202f20fee67a00b9a427f42))

## [0.33.0](https://github.com/carawayhome/caraway-meridian/compare/v0.32.0...v0.33.0) (2024-10-17)


### Features

* add Microsoft Clarity [sc-15019] ([7d065c5](https://github.com/carawayhome/caraway-meridian/commit/7d065c592fffa84454aa89bec2e324174a9e676d))
* add mobile dot buttons controls ([3e063e4](https://github.com/carawayhome/caraway-meridian/commit/3e063e4960bdbb2e2611397e27027faec2472242))
* add ParcelLab [sc-12891] ([6c86fcf](https://github.com/carawayhome/caraway-meridian/commit/6c86fcfc07f506f93d3834d06e888f4f09796a20))
* add ParcelLabWidget to AppsWidgets for Contentful ([d51bc36](https://github.com/carawayhome/caraway-meridian/commit/d51bc36f704885bb6d46518deeffdfe067154793))
* add Roster app ([d72eac1](https://github.com/carawayhome/caraway-meridian/commit/d72eac108cbc1679a309cf415bdb20e4513ac37e))
* adding about us dropdown specific styles ([35cba58](https://github.com/carawayhome/caraway-meridian/commit/35cba58734370aad74c452b4bc40e12f48d45232))
* bring back the gap between gallery and details ([51e24de](https://github.com/carawayhome/caraway-meridian/commit/51e24de5d9c47233c8ccfaf6a7683695dbf9c0e2))
* **component:** connect Trade Component to Contentful ([f6f2f18](https://github.com/carawayhome/caraway-meridian/commit/f6f2f182a61c27dd768cedd2dbe9f4cb8a346d40))
* **component:** update extract logic ([8dc94fd](https://github.com/carawayhome/caraway-meridian/commit/8dc94fd65108c5a437663b163888b66671024cfe))
* **component:** update to use Blog Article ([5a2b716](https://github.com/carawayhome/caraway-meridian/commit/5a2b716c916adf7d03756bbb612cb03feea82fd0))
* resolve conflicts ([2badfc3](https://github.com/carawayhome/caraway-meridian/commit/2badfc3577ff4a3d982f353a1aac655e17da4b3c))


### Bug Fixes

* css issue on render ([1cbc849](https://github.com/carawayhome/caraway-meridian/commit/1cbc849a5822fc93d28eddadf936b80073367965))
* **plp:** build ([1dc69c5](https://github.com/carawayhome/caraway-meridian/commit/1dc69c51e703478d06644f83dacfe2f5d69f8482))
* **plp:** use same generateCallToActionProps as main branch ([737ec9c](https://github.com/carawayhome/caraway-meridian/commit/737ec9c676d20476c8fd9a14e61177b18b7fe619))
* remove duplicate icon ([01cf64c](https://github.com/carawayhome/caraway-meridian/commit/01cf64cf297934d75941f1088f6d7a71e1cf5d5b))

## [0.32.0](https://github.com/carawayhome/caraway-meridian/compare/v0.31.0...v0.32.0) (2024-10-16)


### Features

* add reviews via contentful ([91d5377](https://github.com/carawayhome/caraway-meridian/commit/91d53772965c265763dbffd24da6edd82a1b818b))
* **component:** connect Anchor Navigation to Contentful ([ebf1406](https://github.com/carawayhome/caraway-meridian/commit/ebf140673e4845e6b459c15374f387a7b28070f4))
* **component:** last details ([75618bd](https://github.com/carawayhome/caraway-meridian/commit/75618bdb4a7fa39247c714a46dc6560f5455f066))
* **robots:** update site url on robots ([6a1b6c6](https://github.com/carawayhome/caraway-meridian/commit/6a1b6c6d562cd7c8d2bf8322528ffff607045c02))
* **sitemaps:** add blog and collections ([4d983a3](https://github.com/carawayhome/caraway-meridian/commit/4d983a3aeddf6c497a181f7a6c09a2af57c3a65f))
* **sitemaps:** base file ([e1e1248](https://github.com/carawayhome/caraway-meridian/commit/e1e12484e3842d2d6bfd67bfb28f0366ab57c7cf))


### Bug Fixes

* button stylings on ATC ([de0c854](https://github.com/carawayhome/caraway-meridian/commit/de0c854400b1ebb4474436bf0e4a804f0c2b5b65))
* footer logo overflow ([c3a1852](https://github.com/carawayhome/caraway-meridian/commit/c3a1852728ed326be17cf4800c824fa93ccee2dd))
* **Press:** overflow issue ([10ecd82](https://github.com/carawayhome/caraway-meridian/commit/10ecd82a39b5e44bb3257d6673590a75f45719f9))
* **queries.ts:** query update ([97bd772](https://github.com/carawayhome/caraway-meridian/commit/97bd772fbfa80f482c4c0c0abfd72442c7797ce5))
* **queries.ts:** update graphql query ([e1e7540](https://github.com/carawayhome/caraway-meridian/commit/e1e7540f226e23747b8f9e51e6b1dda58b322c10))
* **sitemaps:** wrong url ([6300779](https://github.com/carawayhome/caraway-meridian/commit/6300779e72f51418335e7e35e39743dee33852cc))
* theme order so that hover surface on view full cart is correctly applied ([f180dfd](https://github.com/carawayhome/caraway-meridian/commit/f180dfdd237618b395e0577db55ae58ffba3e439))

## [0.31.0](https://github.com/carawayhome/caraway-meridian/compare/v0.30.0...v0.31.0) (2024-10-11)


### Features

* **Article Schema:** implement article json ld ([e25ca29](https://github.com/carawayhome/caraway-meridian/commit/e25ca292f2596639801bf4a31943be4515b63923))
* fix Atomic Tooltip ([3b1cbe4](https://github.com/carawayhome/caraway-meridian/commit/3b1cbe49303b7650532393ca1f7afc5621415683))
* fix git comments ([48bdd9a](https://github.com/carawayhome/caraway-meridian/commit/48bdd9aeee778bfb77e7f190787a275feb42694c))
* force build ([372b70d](https://github.com/carawayhome/caraway-meridian/commit/372b70d33d64c3de834fc10ac6d5a9678075c340))
* new PLP cards setup ([ea217cc](https://github.com/carawayhome/caraway-meridian/commit/ea217cca31826251d4f910f211e906c8f2ed62cd))
* **Page MEta:** add keywords and author ([4d46e34](https://github.com/carawayhome/caraway-meridian/commit/4d46e3498f7b66bb06ddae5f0fdf60eb67959d0f))
* **Page Metadata:** add meta for page, blog article and product ([185f1cc](https://github.com/carawayhome/caraway-meridian/commit/185f1cc606210753022556659eda59bcbeaa97bb))
* **Page Schema:** add social linkls ([69d1f09](https://github.com/carawayhome/caraway-meridian/commit/69d1f099724bb76d23a79d73e20f6de1043e46a7))
* render dynamically pages with nested slugs ([17a5494](https://github.com/carawayhome/caraway-meridian/commit/17a54945324d105b0c1a0c480e4e9ad0f2417e2a))
* **Seo Meta:** add manifest ([87b08c9](https://github.com/carawayhome/caraway-meridian/commit/87b08c9a674f6332e2d68a56dfba451e1d8bba07))
* statically generate all pages with nested slugs ([4ab9f96](https://github.com/carawayhome/caraway-meridian/commit/4ab9f96b4439794663ab8f7581db116402890a9b))
* use document-plain-text [sc-14863] ([5c71dfb](https://github.com/carawayhome/caraway-meridian/commit/5c71dfb6890b60b9c1c0fd995a0db587945a6a2a))


### Bug Fixes

* **Article Schema:** build ([1eb787b](https://github.com/carawayhome/caraway-meridian/commit/1eb787b715f267d02f25a7d82fb1875def9df0a9))
* missing image ([7c6b461](https://github.com/carawayhome/caraway-meridian/commit/7c6b461493215d56b8dadf1918aad876d2c25558))
* **Page Schema:** conflicts ([24dd44f](https://github.com/carawayhome/caraway-meridian/commit/24dd44f8dee4cc82be47fac90bb3cd3d72284743))
* press rendering update ([a775254](https://github.com/carawayhome/caraway-meridian/commit/a7752542352119a2bf3d1a4316548c25eddfeabe))
* product card CTA styles ([0cce075](https://github.com/carawayhome/caraway-meridian/commit/0cce0759bea44f053628590aa6d41ce7ac0b7dd1))
* remove unnecessary collection slug ([e6494d5](https://github.com/carawayhome/caraway-meridian/commit/e6494d5d121f19c8d749cfe43662c7010ab68f65))
* update slide when null ([b852d98](https://github.com/carawayhome/caraway-meridian/commit/b852d98cbe115ebb3ca2b351876aaad9bc59e332))

## [0.30.0](https://github.com/carawayhome/caraway-meridian/compare/v0.29.1...v0.30.0) (2024-10-09)


### Features

* add countdown timer to Promobar component ([99b65b0](https://github.com/carawayhome/caraway-meridian/commit/99b65b0c9148fb442943449b84515987a988ce2d))


### Bug Fixes

* update CallToAction variant in CartHeader component, sizes in promobar ([83edd08](https://github.com/carawayhome/caraway-meridian/commit/83edd08164b96ab80e82aba0c8dcf249352f89fc))

## [0.29.1](https://github.com/carawayhome/caraway-meridian/compare/v0.29.0...v0.29.1) (2024-10-08)


### Bug Fixes

* **component:** mobile testimonials bug ([8e56fbc](https://github.com/carawayhome/caraway-meridian/commit/8e56fbc8771e743a85a3c078cd99fc57f817e644))
* **component:** slide mobile count update ([08d5175](https://github.com/carawayhome/caraway-meridian/commit/08d5175c517f007ee3d084571f837ca4ccf468df))

## [0.29.0](https://github.com/carawayhome/caraway-meridian/compare/v0.28.0...v0.29.0) (2024-10-05)


### Features

* add offwhite ([1baf4a0](https://github.com/carawayhome/caraway-meridian/commit/1baf4a0590414f4ed1566012aed448993af69628))


### Bug Fixes

* handle null subtype in extractPageSections function ([901dcd7](https://github.com/carawayhome/caraway-meridian/commit/901dcd7433088a2944b2e654a1f819cf1a0d8f58))

## [0.28.0](https://github.com/carawayhome/caraway-meridian/compare/v0.27.0...v0.28.0) (2024-10-03)


### Features

* add red700 theme color and update ProductPrice component ([f1c6b26](https://github.com/carawayhome/caraway-meridian/commit/f1c6b260dc3a0c4f83dd66b7474b504d4824fc56))
* adding interactive text link feature ([8f2fefd](https://github.com/carawayhome/caraway-meridian/commit/8f2fefd99a56e129280cfe1fb169ed3bb9a17e15))
* adding new layout component ([6eee5d7](https://github.com/carawayhome/caraway-meridian/commit/6eee5d732b2c09ef70304958177985cfcee467cd))
* cleaning up offset styles ([7b6d8de](https://github.com/carawayhome/caraway-meridian/commit/7b6d8decb03b09af1634992e4e3ac634c0098e86))
* dynamic pricing on callouts enabled ([11955b7](https://github.com/carawayhome/caraway-meridian/commit/11955b756327c19401dbb0c7cbb44de4ad90571b))


### Bug Fixes

* add key ([3b39c26](https://github.com/carawayhome/caraway-meridian/commit/3b39c26c539b8b0b44dee1a9a8026a107ae9fd49))
* add title field to BlockContent in fetchPages.tsx and fetchProducts.tsx ([545e993](https://github.com/carawayhome/caraway-meridian/commit/545e99316abc8a456bf9375ebfbec214a50a0269))
* callouts on nav links and the dots ([091def0](https://github.com/carawayhome/caraway-meridian/commit/091def0fafa20b49c5351c9f58dbeaccad6ac310))
* zpattern bugs causing fails ([8e93b07](https://github.com/carawayhome/caraway-meridian/commit/8e93b076a1f6c87f5f37f7c8d0cd2d113f73af11))

## [0.27.0](https://github.com/carawayhome/caraway-meridian/compare/v0.26.1...v0.27.0) (2024-10-02)


### Features

* **zpattern:** use media container for assets ([30ee988](https://github.com/carawayhome/caraway-meridian/commit/30ee98879668d53b747f6f6ccac07f19f0717725))


### Bug Fixes

* keys in nav ([4b1abf4](https://github.com/carawayhome/caraway-meridian/commit/4b1abf4cb0a61e255ee11da303c9718c76d6d6ed))
* **redirect:** wrapper ([1a98eef](https://github.com/carawayhome/caraway-meridian/commit/1a98eef04309f8c56ab155a04ece215e14e16cbc))
* **zpattern:** mobile spacing ([b27eeaa](https://github.com/carawayhome/caraway-meridian/commit/b27eeaa66c21128e34defb5231de5895da7d9406))

## [0.26.1](https://github.com/carawayhome/caraway-meridian/compare/v0.26.0...v0.26.1) (2024-10-01)


### Bug Fixes

* various issues with data fetching ([abf630a](https://github.com/carawayhome/caraway-meridian/commit/abf630ac1e1b4c199b9d53e8eda33b23ded02169))


### Reverts

* json ([f869d8e](https://github.com/carawayhome/caraway-meridian/commit/f869d8ee6cd363b6babf4380277a74a0541acc54))

## [0.26.0](https://github.com/carawayhome/caraway-meridian/compare/v0.25.1...v0.26.0) (2024-10-01)


### Features

* accept discount codes, use cookies and remove after application ([9cdf1af](https://github.com/carawayhome/caraway-meridian/commit/9cdf1afb2a7d3948211192cf0a04afa012718000))
* add discount applicator ([31c06a8](https://github.com/carawayhome/caraway-meridian/commit/31c06a8e86516b521dc2a17b9837ec80e7428149))
* add new event tracking for events such as variant selection, email capture, and clicks ([4b336a9](https://github.com/carawayhome/caraway-meridian/commit/4b336a9d75b5756ec079ea5a7c15828e172b38ca))
* add redirects [sc-14174] ([9954698](https://github.com/carawayhome/caraway-meridian/commit/99546987d17a470dd2782645b0f5aae3a432b38c))
* add Scripts component ([ade3274](https://github.com/carawayhome/caraway-meridian/commit/ade32747dfb6c0fae3dea66f4da78464621418ac))
* add support for collections ([7dcef25](https://github.com/carawayhome/caraway-meridian/commit/7dcef25ccb16993a65015e04cd65ab982aaf9256))
* added klaviyo ([16e4e6e](https://github.com/carawayhome/caraway-meridian/commit/16e4e6e9994a16a74d35d4e6e9c8a3f85365abd7))
* new listeners for dispatching events ([304bce2](https://github.com/carawayhome/caraway-meridian/commit/304bce2cdc212119f3c2cb23d83c158b8f975a96))
* product details coming from contentful (re-order available). Also SEO title injected ([6f14257](https://github.com/carawayhome/caraway-meridian/commit/6f14257bce9334365d8d7d2cfbb2d5f214bc77a7))
* update redirects to support discounts and full cookies ([4068e31](https://github.com/carawayhome/caraway-meridian/commit/4068e31e10069b1b36552bd68243d150a209f1d5))


### Bug Fixes

* add key prop to list items in SaveWithSet component ([17a0a84](https://github.com/carawayhome/caraway-meridian/commit/17a0a84035faf95a1ed1e0ec4d4194689aa3fb8f))
* correct props ([d1c4a6b](https://github.com/carawayhome/caraway-meridian/commit/d1c4a6beb6559e740b300d6c204a0f12a7289033))
* discount logic ([40789b0](https://github.com/carawayhome/caraway-meridian/commit/40789b0ea81a42e81acdfeed4fac87b7b3396aa5))
* issues with extractors and types ([c1d784a](https://github.com/carawayhome/caraway-meridian/commit/c1d784ae01da73f60e3bbf4019d3033fad15147b))
* missing id ([be99058](https://github.com/carawayhome/caraway-meridian/commit/be99058b5975d85b64fb874b82399a2de420d2a5))
* server issues ([b5a9636](https://github.com/carawayhome/caraway-meridian/commit/b5a9636e020b668205cdbcd1d87bcabece165a9b))
* slug for params ([814c039](https://github.com/carawayhome/caraway-meridian/commit/814c039cf49c01f647f9c211b9c6b997b3bbc49b))
* ts and ui css issues ([a46b617](https://github.com/carawayhome/caraway-meridian/commit/a46b6176590eabc838e6c7f4a5974fecf04394b7))
* ts issues ([4d7549c](https://github.com/carawayhome/caraway-meridian/commit/4d7549c2a279b809398a8fc4aee9949f7c27b119))
* ts issues on new fetch updates ([574054e](https://github.com/carawayhome/caraway-meridian/commit/574054ed98299ee12066444ad40737dabeb0a05d))
* typescript props ([2f7cc24](https://github.com/carawayhome/caraway-meridian/commit/2f7cc24b5a9f15fa0726c1daa2a59780a6c838b4))

## [0.25.1](https://github.com/carawayhome/caraway-meridian/compare/v0.25.0...v0.25.1) (2024-09-24)


### Bug Fixes

* update imports ([6a21b4d](https://github.com/carawayhome/caraway-meridian/commit/6a21b4d2e09747a777c462ea6888b68a09e31575))

## [0.25.0](https://github.com/carawayhome/caraway-meridian/compare/v0.24.0...v0.25.0) (2024-09-24)


### Features

* **BlogArticle:** markdown working ([df2b98d](https://github.com/carawayhome/caraway-meridian/commit/df2b98d598d447c5e4b439ea5c06e29617985d0c))
* **BlogArticle:** Rich Text Test Rendering ([30b8901](https://github.com/carawayhome/caraway-meridian/commit/30b8901df2378583d7f925b1f00efbf8b3b634f5))
* **BlogArticle:** update fetchBlogs to be static ([c3ca98a](https://github.com/carawayhome/caraway-meridian/commit/c3ca98a54bd57a62ce057e7b1b869d4262eb509f))
* **component:** pR Comments ([5209785](https://github.com/carawayhome/caraway-meridian/commit/5209785a668ac88325d92346081faa79150ad833))
* **component:** update ([447cea0](https://github.com/carawayhome/caraway-meridian/commit/447cea0cfc4cfb182ac703044b52ce65490fb1de))
* **component:** vercel Teo Updates ([7c3662c](https://github.com/carawayhome/caraway-meridian/commit/7c3662ce55bbff14b873f358799e5e83e5d503ed))
* merge branch 'main' into feature/sc-13189/informational-text-component-migration ([58b52ce](https://github.com/carawayhome/caraway-meridian/commit/58b52ce4459e77576f3728eec54e587f40bafa78))
* new redux accordion ([6200829](https://github.com/carawayhome/caraway-meridian/commit/6200829e753b3992fbbde21e858c8ec51ef34826))
* remove images and use css to plus and minus ([ed77a0d](https://github.com/carawayhome/caraway-meridian/commit/ed77a0d7af07de63b4918979fc4f2886a5129db3))
* remove wrapper ([908b3d4](https://github.com/carawayhome/caraway-meridian/commit/908b3d4604468218ca0c0a4b14481031aaacc86f))
* remove wrong headers styles ([9928a00](https://github.com/carawayhome/caraway-meridian/commit/9928a000951cb7a50eb110825aa218bf43c3b846))
* **Typograph:** add rest prop ([bdb267a](https://github.com/carawayhome/caraway-meridian/commit/bdb267accd0778d919a260d10310285f0295f403))


### Bug Fixes

* **BlogArticle:** type and lint ([2416287](https://github.com/carawayhome/caraway-meridian/commit/2416287e037c5124c86872a7397f5f7091242524))
* **BlogArticle:** typescript issues ([5379e59](https://github.com/carawayhome/caraway-meridian/commit/5379e5967ea00f4f02b5befc3000086a45c6ab3a))
* cart stylings and spacing ([67548f7](https://github.com/carawayhome/caraway-meridian/commit/67548f777d3d0841beb4aa85c5309f8190209adc))
* css issue on popover ([fbef466](https://github.com/carawayhome/caraway-meridian/commit/fbef466a3095d511124d8f06cffeea1f79c09790))
* theme issue with colors ([0011662](https://github.com/carawayhome/caraway-meridian/commit/001166289f8657be179c587e65d966f558bddd58))

## [0.24.0](https://github.com/carawayhome/caraway-meridian/compare/v0.23.0...v0.24.0) (2024-09-17)


### Features

* add typographyTheme to themeTokens.stylex.ts and typographyThemes.stylex.ts ([5b516e8](https://github.com/carawayhome/caraway-meridian/commit/5b516e8bd0bf13771bfe3865575de69165824c0c))
* **component:** feature component with multiple layouts ([d049a15](https://github.com/carawayhome/caraway-meridian/commit/d049a158df5b5f7ef13801fb15f5195c87b42e0f))
* **doc:** docs ([347469f](https://github.com/carawayhome/caraway-meridian/commit/347469fcb066f98499563efd8cc0f777c4658f4c))
* force push ([353b2c4](https://github.com/carawayhome/caraway-meridian/commit/353b2c4aa482f484bcb8f54ac0c29fcc55656d7e))
* **layout:** add dialog component ([3585866](https://github.com/carawayhome/caraway-meridian/commit/35858666aaa53c3fdff76406e44515db9164abbe))
* **layout:** gladly update ([8202fb8](https://github.com/carawayhome/caraway-meridian/commit/8202fb8bfe887fa08d702475115bf52562a4eb99))
* **layout:** postscript file ([06c4795](https://github.com/carawayhome/caraway-meridian/commit/06c47958354c5af7bfcc9660f3c3a9fe4bebab16))
* **layout:** pR Review comment ([4d35382](https://github.com/carawayhome/caraway-meridian/commit/4d353823c682e149a866e50ee4319448bab6bd1f))
* **layout:** pR review comments ([0b7bdd6](https://github.com/carawayhome/caraway-meridian/commit/0b7bdd6e0eae5c4d97612e6062b53e4e9ea837a4))
* **layout:** updates ([367d9ff](https://github.com/carawayhome/caraway-meridian/commit/367d9ff59e559ba33ac2ddbbd233a1f8c3a849a6))
* **script:** gladly - Chat Widget ([f8d87b4](https://github.com/carawayhome/caraway-meridian/commit/f8d87b450d15374b9b69590545dc95b12e6246c0))
* **script:** postScript ([b1f2cf5](https://github.com/carawayhome/caraway-meridian/commit/b1f2cf5949c5d16585a3aa1842f0ef980655de86))


### Bug Fixes

* promobar animation glitch and hierarchy ([81e94ac](https://github.com/carawayhome/caraway-meridian/commit/81e94acad5b96d6ee0ff75ebb169c445e52a8e8a))
* the PageCTA component to improve code readability and maintainability with container wrappers ([7751060](https://github.com/carawayhome/caraway-meridian/commit/77510605559b5befa4dedc7915c08ecacb1b97f0))
* update Dialog component to avoid render glitch ([494255a](https://github.com/carawayhome/caraway-meridian/commit/494255a9f48893be55568ba31a220a654828f519))

## [0.23.0](https://github.com/carawayhome/caraway-meridian/compare/v0.22.0...v0.23.0) (2024-09-01)


### Features

* add multiple sticky promo bar slider with activation, animation ([4b41cf1](https://github.com/carawayhome/caraway-meridian/commit/4b41cf177b80dedc66a8b8573933d7b307318e94))
* add useCallback hook to improve performance in Countdown component ([588fee2](https://github.com/carawayhome/caraway-meridian/commit/588fee28648000afa36d1d036c194708611a2651))
* create Informational Text ([ee0ca35](https://github.com/carawayhome/caraway-meridian/commit/ee0ca354c779555f8c8620ab049a99470b6f059c))


### Bug Fixes

* auto-lint ([9f31a29](https://github.com/carawayhome/caraway-meridian/commit/9f31a2947e8e11c44d44cfef22cd550dc1b3d143))
* double message ([f286661](https://github.com/carawayhome/caraway-meridian/commit/f28666109415c37646dbab550fa26d76df054311))
* update eslint errors ([cf9bf00](https://github.com/carawayhome/caraway-meridian/commit/cf9bf00a917ed8cc00547756208015bfc338730f))

## [0.22.0](https://github.com/carawayhome/caraway-meridian/compare/v0.21.0...v0.22.0) (2024-08-25)


### Features

* add Callout component for displaying informative messages ([a161eee](https://github.com/carawayhome/caraway-meridian/commit/a161eee685494dad8cc7bd167b80673392e5d3be))
* add Callouts component to ProductCard and ProductDetails components ([0152ef1](https://github.com/carawayhome/caraway-meridian/commit/0152ef1d59b11facda76236bf5d541e896c3420c))
* add callouts to ProductCard component ([7f72f47](https://github.com/carawayhome/caraway-meridian/commit/7f72f47d20879a2ed78b572d2625b3016ff000ae))
* add FreeShippingBar component to Cart ([3322edf](https://github.com/carawayhome/caraway-meridian/commit/3322edff2f02ae124197b135ba58ab9e8eb07051))
* add FreeShippingBar component to Cart [sc-13060] ([9144297](https://github.com/carawayhome/caraway-meridian/commit/9144297e264fb0c20b5ffe03bfc3c53ffe036297))
* add theme color settings to callouts in ProductGrid component ([c1f0aa9](https://github.com/carawayhome/caraway-meridian/commit/c1f0aa968abd99f4498fc27789fed772a56cd893))
* add transparent variant to CallToAction component ([8d3d8f8](https://github.com/carawayhome/caraway-meridian/commit/8d3d8f8f474000a6a0ca9a73a39d0f3657420728))
* cart page logic added ([f62df02](https://github.com/carawayhome/caraway-meridian/commit/f62df02894b706f1d634337c3754451230e604d5))
* **component:** elements overlapping ([067e1cb](https://github.com/carawayhome/caraway-meridian/commit/067e1cb7d5b6c887baa5e795362c519b0e497600))
* **component:** media/Video - Component ([af86d3f](https://github.com/carawayhome/caraway-meridian/commit/af86d3f66405b8f3b8fd02e9bf7ad38b61af34fd))
* **component:** play/pause button mobile 16 pixels ([80ad3af](https://github.com/carawayhome/caraway-meridian/commit/80ad3afc74ab7bb546b65de5cdafcf844074b347))
* **component:** separator - Component Migration ([9d366ac](https://github.com/carawayhome/caraway-meridian/commit/9d366ac1ef472b246e21c2f7eebea960202b0b47))
* **create component cli:** helper to speed up create component process ([7c878a5](https://github.com/carawayhome/caraway-meridian/commit/7c878a5a9dcd6371d778ca2415bfe3e268351583))
* decoupled trigger component for global dialogs ([11654e7](https://github.com/carawayhome/caraway-meridian/commit/11654e73b62b3b4112110b640bd4f259ee4c5eec))
* **fetchPages:** create a build error ([dca60fc](https://github.com/carawayhome/caraway-meridian/commit/dca60fcc27a30b17f734e53e5cb31b10ae4fa433))
* improve FreeShippingBar component in Cart ([a4fcdc3](https://github.com/carawayhome/caraway-meridian/commit/a4fcdc380505a117b55818be79d751b17b90dc33))
* modal start ([50adc5e](https://github.com/carawayhome/caraway-meridian/commit/50adc5e69cefc2b45495c24e647c0c16a03e20e2))
* update ProductVariantSelector component to use state and default option ([7888ab7](https://github.com/carawayhome/caraway-meridian/commit/7888ab740d38a4aad46c148cd61d4a9a2ebab1d7))


### Bug Fixes

* import order eslint ([5471270](https://github.com/carawayhome/caraway-meridian/commit/5471270ca9ca696f287864dc704d1ae736ef38a0))
* improve Dialog component by adding backdrop and styling changes ([4d775f5](https://github.com/carawayhome/caraway-meridian/commit/4d775f5aae4a9b8682003c440810cf7747757cf3))

## [0.21.0](https://github.com/carawayhome/caraway-meridian/compare/v0.20.0...v0.21.0) (2024-08-09)


### Features

* add generateSlug utility function ([1213c80](https://github.com/carawayhome/caraway-meridian/commit/1213c808de33932ead637270210cce4ea0ba6920))
* add support for buttons via Contentful ([ce38e83](https://github.com/carawayhome/caraway-meridian/commit/ce38e834b012294918cab485613a31bbf8efd511))
* **component:** page CTA - Component ([e21793b](https://github.com/carawayhome/caraway-meridian/commit/e21793b4662c983fc9d37168d2568edd42a2cd37))
* create new PageCTA component ([5eb390e](https://github.com/carawayhome/caraway-meridian/commit/5eb390eb457420912c4405bf45555beebbe8bd50))
* rating component ([8f4fd35](https://github.com/carawayhome/caraway-meridian/commit/8f4fd351a46a5e4d223d984cc017302346691eb7))
* star with reviews fully customized ([f146b8c](https://github.com/carawayhome/caraway-meridian/commit/f146b8c74445a4b96ab4442ee279a49ad4c7cf44))


### Bug Fixes

* merge conflicts ([aa66cfd](https://github.com/carawayhome/caraway-meridian/commit/aa66cfd993108a25dc3333de9fb342e36be3f5e9))
* some lint issues ([79662ee](https://github.com/carawayhome/caraway-meridian/commit/79662ee21efb77c67c9afa01fcb6d9b48643e589))

## [0.20.0](https://github.com/carawayhome/caraway-meridian/compare/v0.19.0...v0.20.0) (2024-07-29)


### Features

* add multi searchParam support ([adfccf7](https://github.com/carawayhome/caraway-meridian/commit/adfccf7bac089611a75892814b620b621a031a55))

## [0.19.0](https://github.com/carawayhome/caraway-meridian/compare/v0.18.3...v0.19.0) (2024-07-27)


### Features

* content Model overhaul and upgrade to optimize for new environment ([d50a499](https://github.com/carawayhome/caraway-meridian/commit/d50a49996c8ab4fb99d79283f106a592fb107ee6))


### Bug Fixes

* update misconfigured types for image ([d1f8199](https://github.com/carawayhome/caraway-meridian/commit/d1f819909633f5c4faf3d6a58f4d85342365e8dd))

## [0.18.3](https://github.com/carawayhome/caraway-meridian/compare/v0.18.1...v0.18.3) (2024-06-28)


### Bug Fixes

* remove unused import ([1ce6e1e](https://github.com/carawayhome/caraway-meridian/commit/1ce6e1ea3b8404669a590c955dcd3566bb32107f))
* remove unused shop prop ([4bb6c97](https://github.com/carawayhome/caraway-meridian/commit/4bb6c9735b46e849cc775f2b7a40c78f91ac4269))
* update import ([8c3b2b9](https://github.com/carawayhome/caraway-meridian/commit/8c3b2b95d65216d8f21febd3c29e7110c57dea68))


### Code Refactoring

* added new layout components with a test page ([758cc19](https://github.com/carawayhome/caraway-meridian/commit/758cc194463b642b73dfb137b712f3e77fbabd18))
* update default theme support for CallToAction. Now supports Variants and Themes ([d882e63](https://github.com/carawayhome/caraway-meridian/commit/d882e639800b7e9cdf0e0f584c2e2e587d4dd74b))

## [0.18.1](https://github.com/carawayhome/caraway-meridian/compare/v0.18.0...v0.18.1) (2024-06-20)


### Features

* switch to URL state management to use RSC ([addf36e](https://github.com/carawayhome/caraway-meridian/commit/addf36ecafb5d5dffc17cd67837fdd84cd247925))


### Code Refactoring

* dispatch for analytics optimized to use lisetener ([5a2303c](https://github.com/carawayhome/caraway-meridian/commit/5a2303ccf1e13c480ca71db458fc9ec76ed372e7))

## [0.18.0](https://github.com/carawayhome/caraway-meridian/compare/v0.17.0...v0.18.0) (2024-05-17)


### Features

* add initial documentation files and configuration files with Nextra ([ff544df](https://github.com/carawayhome/caraway-meridian/commit/ff544df1c4eefe5c8af16ccd84a1ee28f062ae09))
* adding some theme styles and making LineItem its own component ([1b9b118](https://github.com/carawayhome/caraway-meridian/commit/1b9b118dcae30319f34f0ae1f657f8375680dd5c))
* rednering full width ZPattern ([717ce0a](https://github.com/carawayhome/caraway-meridian/commit/717ce0a17dcc1e4da3dbc604fa14a8ea3e77a4d8))
* refactoring zPattern styles ([8e91d52](https://github.com/carawayhome/caraway-meridian/commit/8e91d5265adb214470b2142744a68b09ab845794))
* refactoring zPattern styles ([3b0fa69](https://github.com/carawayhome/caraway-meridian/commit/3b0fa69635cca75132b07514c716507c03aad85c))
* typography added ([eee5d6d](https://github.com/carawayhome/caraway-meridian/commit/eee5d6d713bd1c48f2d31636f970ef20a172fd7a))


### Bug Fixes

* skeleton boolean and added typescript to props as well ([e277dc3](https://github.com/carawayhome/caraway-meridian/commit/e277dc335ef113a791bef5fe5053487b759182c9))

## [0.17.0](https://github.com/carawayhome/caraway-meridian/compare/v0.16.0...v0.17.0) (2024-05-14)


### Features

* adding type layout ([16e314d](https://github.com/carawayhome/caraway-meridian/commit/16e314d171909d456e744166e39e5b3b272028df))
* adding zPattern and layout ([cfa608c](https://github.com/carawayhome/caraway-meridian/commit/cfa608c242d4a8679bf96c1c3f78d31b84010473))


### Bug Fixes

* fix issue with missing richContent in getPageBlocks ([cdab7d0](https://github.com/carawayhome/caraway-meridian/commit/cdab7d09cabfcaa76936955e2070b11e78fdd198))
* update fetchPages.tsx to handle null values for block settings theme ([37eebe5](https://github.com/carawayhome/caraway-meridian/commit/37eebe5458ffac4eb08c7f10da0e10a17202ef5f))
* update themeThemes.stylex.ts with new color definitions ([520ed5d](https://github.com/carawayhome/caraway-meridian/commit/520ed5d7867bbb870bd9610b130367f0fed5cf19))

## [0.16.0](https://github.com/carawayhome/caraway-meridian/compare/v0.15.0...v0.16.0) (2024-05-14)


### Features

* add new util functions ([1319e8b](https://github.com/carawayhome/caraway-meridian/commit/1319e8b6673595a857f443a4b6175018ac58df5e))
* add themeThemes.stylex.ts file with theme color definitions to be used globally ([66fb87a](https://github.com/carawayhome/caraway-meridian/commit/66fb87a0d1f5bad6d5e1036dde83c98000517578))
* update Marquee component with theme support and utility functions ([912bb7f](https://github.com/carawayhome/caraway-meridian/commit/912bb7f2e0512b1b8f24825fed579864645006c5))


### Bug Fixes

* fix sizes props that was missing on image ([33254ab](https://github.com/carawayhome/caraway-meridian/commit/33254ab8923dbdb7526acdf784a2edc03cb70c60))
* marquee typescript issue has been resolved ([9c8de54](https://github.com/carawayhome/caraway-meridian/commit/9c8de545221d8f39c95fb7a520c2d9f60b99c0f1))
* update client vs server issue with money functions ([6233d03](https://github.com/carawayhome/caraway-meridian/commit/6233d035ad6eb7ea444ab6be7e0599b92985d096))

## [0.15.0](https://github.com/carawayhome/caraway-meridian/compare/v0.14.1...v0.15.0) (2024-05-13)


### Features

* new marquee component with rich text rendering ([c04093f](https://github.com/carawayhome/caraway-meridian/commit/c04093f21ca0c7bf25fe0ae6b99d2462eff05bb2))


### Bug Fixes

* discount code incorrectly displays a 0 on CartFooter component ([50542ca](https://github.com/carawayhome/caraway-meridian/commit/50542ca70ffe39093ccfb682bd8748aeefcc6e2f))
* rendering issue on rich text component ([bab4f49](https://github.com/carawayhome/caraway-meridian/commit/bab4f491ef27de6585d1c56ef31d7b8cfc07863c))

## [13.2.0](https://github.com/carawayhome/caraway-meridian/compare/v13.1.0...v13.2.0) (2024-05-13)


### Features

* new discount form to CartFooter component ([c12b75e](https://github.com/carawayhome/caraway-meridian/commit/c12b75e0e7a5bf79cdf736f1ebfeda429f695a70))

## [13.1.0](https://github.com/carawayhome/caraway-meridian/compare/v13.0.3...v13.1.0) (2024-05-11)


### Features

* add Skeleton component for loading placeholders, used in details ([baeff66](https://github.com/carawayhome/caraway-meridian/commit/baeff6698949ad6f15c74e252f6de5518d867c2a))


### Bug Fixes

* missing dependency on addtocart (unrelated) ([ba8fd26](https://github.com/carawayhome/caraway-meridian/commit/ba8fd2605811fafd9b8528989345dde7a59f94c3))


### Performance Improvements

* **sharp:** add sharp for production image perf ([8332c9f](https://github.com/carawayhome/caraway-meridian/commit/8332c9f8eb3db948a9ad27322def9c61c0661e67))

## [13.0.3](https://github.com/carawayhome/caraway-meridian/compare/v13.0.2...v13.0.3) (2024-05-03)


### Bug Fixes

* transformResponse function in availabilitySlice.ts to handle null values ([2c31bb0](https://github.com/carawayhome/caraway-meridian/commit/2c31bb0dd407b68bba7fb113a682d81384ce5146))

## [13.0.2](https://github.com/carawayhome/caraway-meridian/compare/v0.13.1...v13.0.2) (2024-05-03)


### Features

* updated and normalized shopify cart with state ([c672493](https://github.com/carawayhome/caraway-meridian/commit/c6724930bfea5b944093659b687700ba6a394fd1))


### Bug Fixes

* had a string type but is number ([f706c29](https://github.com/carawayhome/caraway-meridian/commit/f706c29e0f37095a2ff42a3956d7d2f96aa99c8f))
* linting errors ([5df06c0](https://github.com/carawayhome/caraway-meridian/commit/5df06c0dda77f33f5638676a101a35316950a959))
* productId type from number to string in AddToCart.tsx ([4d141ad](https://github.com/carawayhome/caraway-meridian/commit/4d141adc4a6add3daaac57f25f953ba736d75ba4))
* type error resolved do to no quantity on product ([f2bdaf6](https://github.com/carawayhome/caraway-meridian/commit/f2bdaf617f209c81e21ebf410fac373f75a5c3e9))


### Miscellaneous Chores

* update various packages for stylex, nextjs and babel ([3e65f5d](https://github.com/carawayhome/caraway-meridian/commit/3e65f5d577daf5619e248577305c36890b29a9df))

## [0.13.1](https://github.com/carawayhome/caraway-meridian/compare/v0.13.0...v0.13.1) (2024-05-01)


### Features

* adding disabled button when a variant is unavailable ([6f9c43e](https://github.com/carawayhome/caraway-meridian/commit/6f9c43eba028d948bbfe5e9a10ab5eb954e03fbb))


### Code Refactoring

* update AddToCart component and handle availability errors ([391a6c3](https://github.com/carawayhome/caraway-meridian/commit/391a6c3e3ca9f15da610ff7cfde435dc95489e57))

## [0.13.0](https://github.com/carawayhome/caraway-meridian/compare/v0.12.0...v0.13.0) (2024-04-24)


### Features

* add font-face declarations and update Hero component to showcase new components ([b03088c](https://github.com/carawayhome/caraway-meridian/commit/b03088cfedb705dcb681915a21ac1494aafbc69b))


### Bug Fixes

* eslint issue ([797e783](https://github.com/carawayhome/caraway-meridian/commit/797e783cf2d79b2ed52763521f62d60155599c41))

## [0.12.0](https://github.com/carawayhome/caraway-meridian/compare/v0.11.2...v0.12.0) (2024-04-19)


### Features

* add availabilitySlice.ts to handle API requests for real-time product/variant availability ([caecee7](https://github.com/carawayhome/caraway-meridian/commit/caecee7ef8b52f2666b852ccc210f5d888c6db04))
* add getVariantAvailability hook for fetching variant availability ([ef93e6f](https://github.com/carawayhome/caraway-meridian/commit/ef93e6fa0d0a9f811506a73884f5cdd739aca779))
* add shopifyBase64.ts utility functions for encoding and decoding Shopify IDs ([3b33a2a](https://github.com/carawayhome/caraway-meridian/commit/3b33a2a28c7d98871928b2a8cf025b6660f08570))
* add variant availability check to AddToCart component ([4b7df11](https://github.com/carawayhome/caraway-meridian/commit/4b7df113424f4a5c0bc34b9e5707334f25cc18cc))
* added container component, takes as prop and backgroundColor ([53f9da5](https://github.com/carawayhome/caraway-meridian/commit/53f9da5431181b724385edae411b6abe95090e17))
* added element api features, padding, size, alighment and transform ([446d74c](https://github.com/carawayhome/caraway-meridian/commit/446d74c77efbbb52a500ca8cfeb3ec10c58d9a88))
* added element api features, padding, size, alighment and transform ([4849a69](https://github.com/carawayhome/caraway-meridian/commit/4849a6901bbe324e27df5b85ab892fe199a58e54))
* added typography but removed scale ([48962db](https://github.com/carawayhome/caraway-meridian/commit/48962db529ec871098844f4447fccc03bcf945b2))
* renaming component ([8673f75](https://github.com/carawayhome/caraway-meridian/commit/8673f7546155a334a95bdb8750c2d34226c26197))
* spacer component added ([1796143](https://github.com/carawayhome/caraway-meridian/commit/1796143141197d821c543f2d695e7b1eb5068ab3))
* updated some values to accept boolean values over key value pairs ([797f8c4](https://github.com/carawayhome/caraway-meridian/commit/797f8c451f99c6d88852cccfd57307ca449456da))
* updating multiple files to use new white token ([873fea1](https://github.com/carawayhome/caraway-meridian/commit/873fea178efbbbdd0dd7a4c4dfe3161475d41cf4))


### Bug Fixes

* background color issue in CallToAction component ([08971c3](https://github.com/carawayhome/caraway-meridian/commit/08971c38b2fddce0da8922063f31eb90571e675d))

## [0.11.2](https://github.com/carawayhome/caraway-meridian/compare/v0.11.1...v0.11.2) (2024-04-11)


### Build System

* add clean-dev and clean-build scripts to package.json ([17b1da3](https://github.com/carawayhome/caraway-meridian/commit/17b1da3f6e954ebd12e2f6dd253a310b8b39736a))

## [0.11.1](https://github.com/carawayhome/caraway-meridian/compare/v0.11.0...v0.11.1) (2024-04-09)


### Code Refactoring

* currency formatting functions in Cart, Product, and ProductFeatureCard components ([6fe6a43](https://github.com/carawayhome/caraway-meridian/commit/6fe6a438cb57b49126db2feca0e1f7adcc12ebbb))

## [0.11.0](https://github.com/carawayhome/caraway-meridian/compare/v0.10.1...v0.11.0) (2024-04-01)


### Features

* fetch and display product data in ProductFeatureCard component ([c246d40](https://github.com/carawayhome/caraway-meridian/commit/c246d40be06263b788a6b50f912311c9c08d01cd))


### Bug Fixes

* promise all generator for hydrating blocks ([01d2cf7](https://github.com/carawayhome/caraway-meridian/commit/01d2cf78b36214fccebc9ab57921a7dbe1eb5720))

## [0.10.1](https://github.com/carawayhome/caraway-meridian/compare/v0.10.0...v0.10.1) (2024-03-30)


### Bug Fixes

* mismatch update on fetchGraphQL to accept tags parameter ([d66dfa3](https://github.com/carawayhome/caraway-meridian/commit/d66dfa3c1e756255d70b14af13ab3617fe7a4427))

## [0.10.0](https://github.com/carawayhome/caraway-meridian/compare/v0.9.0...v0.10.0) (2024-03-30)


### Features

* add availability to ATC, major refactor fetching live products ([78e514a](https://github.com/carawayhome/caraway-meridian/commit/78e514adc264a67021f43524a88e6158916e4ef9))
* add Product type ([fe3e739](https://github.com/carawayhome/caraway-meridian/commit/fe3e7397b40547b683de98255855f1f7400c4dbf))
* new content blocks ([acb46b6](https://github.com/carawayhome/caraway-meridian/commit/acb46b642db77a152b090c1286d9bf1c1a999890))
* new utils for regex ([51c3ee4](https://github.com/carawayhome/caraway-meridian/commit/51c3ee4ead7a3eee0097ca62e07986c34fa8ae32))
* page section support ([d7d7a72](https://github.com/carawayhome/caraway-meridian/commit/d7d7a724d3ae24928a8f71fb2256f8f91b996650))


### Bug Fixes

* eslint issue ([5405aa3](https://github.com/carawayhome/caraway-meridian/commit/5405aa3ddb8a8f60306b3b089d3a1d9e66a8f36f))
* props and TS fixed for existing product calls ([9d7e5e7](https://github.com/carawayhome/caraway-meridian/commit/9d7e5e7d643bc6b0df4923ca7752a47ac9c2c47d))

## [0.9.0](https://github.com/carawayhome/caraway-meridian/compare/v0.8.0...v0.9.0) (2024-03-26)


### Features

* **analytics:** update analytics event spec and add tracking for product viewed ([f370af7](https://github.com/carawayhome/caraway-meridian/commit/f370af7ec1440faba27c234e427f89a9b78e6519))

## [0.8.0](https://github.com/carawayhome/caraway-meridian/compare/v0.7.0...v0.8.0) (2024-03-25)


### Features

* add Segment analytics and track AddToCart event ([ed4e902](https://github.com/carawayhome/caraway-meridian/commit/ed4e902ebc7a4ee61f86a8c236ced2e492842c0d))


### Bug Fixes

* import naming in AddToCart and Segment components ([923192b](https://github.com/carawayhome/caraway-meridian/commit/923192b65a3ba644971afb9f32213c23fa15088d))
* import statement in segment.ts ([698406c](https://github.com/carawayhome/caraway-meridian/commit/698406cba09c9e8e308e9d8657d37372c34581c4))
* total amount for cart ([3f85b6f](https://github.com/carawayhome/caraway-meridian/commit/3f85b6fe1b5b58c36382ecf0d3b457dbc5d7b099))

## [0.7.0](https://github.com/carawayhome/caraway-meridian/compare/v0.6.0...v0.7.0) (2024-03-21)


### Features

* add @vercel/analytics package and integrate it into the layout ([c42d35c](https://github.com/carawayhome/caraway-meridian/commit/c42d35c7f5ea81abf82469348a343c8c1ebc0e1e))
* add ProductDetails component ([46bdb05](https://github.com/carawayhome/caraway-meridian/commit/46bdb05f77d122cf3ba6f028507ecc6eaade22e3))
* add productSlice to store configuration ([2f1de4e](https://github.com/carawayhome/caraway-meridian/commit/2f1de4e316662a36345da9f2af13c9edd823adb7))
* **addtocart:** add open cart functionality, variant id and  styling ([dec91ef](https://github.com/carawayhome/caraway-meridian/commit/dec91ef7a9b75e1bcb9d2ff029bbd08bf0d5e6b2))
* fetch variants to ProductCard and ProductDetails components ([a72a4db](https://github.com/carawayhome/caraway-meridian/commit/a72a4dbe8cacd9acb8e9d15de25204af6c98dd07))


### Bug Fixes

* import formatting in layout.tsx ([4c35b52](https://github.com/carawayhome/caraway-meridian/commit/4c35b52a3781b573ae925af853ab697f832df472))

## [0.6.0](https://github.com/carawayhome/caraway-meridian/compare/v0.5.0...v0.6.0) (2024-03-18)


### Features

* cart functionality and display cart items and total quantity ([8f3cded](https://github.com/carawayhome/caraway-meridian/commit/8f3cded6a4f20674998d00818654e05555b23aa6))
* **cart:** open cart panel when add to cart initiates ([01d2823](https://github.com/carawayhome/caraway-meridian/commit/01d2823001efab4b6fb1c61a23acf0b07c67ab7f))
* **redux:** add support for redux to trigger and manage cart state ([607107a](https://github.com/carawayhome/caraway-meridian/commit/607107a71fadfb87f2ae3186aa689e6d6f18fee6))


### Bug Fixes

* **cart:** clicking cart toggle no longer causes a redirect ([7a03ff3](https://github.com/carawayhome/caraway-meridian/commit/7a03ff3b0fb47662ab045be806d843c9820d65f0))
* remove hook ([7e3895c](https://github.com/carawayhome/caraway-meridian/commit/7e3895c32b6f6772913be966cca8bdb397110201))

## [0.5.0](https://github.com/carawayhome/caraway-meridian/compare/v0.4.0...v0.5.0) (2024-03-11)


### Features

* add Cart component and related files ([5170a60](https://github.com/carawayhome/caraway-meridian/commit/5170a60cd8feaae788865214ab57d3585411caf4))
* add cart toggle ([c9d2e04](https://github.com/carawayhome/caraway-meridian/commit/c9d2e0461b8db112002f1767abb19fb98ce55598))
* add dynamicParams and generateMetadata functions ([396bf9a](https://github.com/carawayhome/caraway-meridian/commit/396bf9a46d12a3b20d29849daf15678ab4802ae3))
* **hero:** hero component and update global theme tokens ([44618d3](https://github.com/carawayhome/caraway-meridian/commit/44618d38a247763fd102810e9d34199b0b430adf))
* **pdp:** add to cart component with static build v0.4.1 ([4668be9](https://github.com/carawayhome/caraway-meridian/commit/4668be9b21b0e1510f343da8ab81c5dd0c00c598))
* **promo:** new promo bar ([24d7400](https://github.com/carawayhome/caraway-meridian/commit/24d7400619590543538e46488ed45c7a68ccd49f))


### Bug Fixes

* minor spacing for lint issue resolved ([d6a563e](https://github.com/carawayhome/caraway-meridian/commit/d6a563e83f0a299e79b313158ed54ad13a5bf074))
* promobar eslint ([06ab824](https://github.com/carawayhome/caraway-meridian/commit/06ab82436baf2cae56570c39cc58f66dfac2d25f))
* **stylex:** import issue relative to dynamic imports ([83ac1bf](https://github.com/carawayhome/caraway-meridian/commit/83ac1bf69876fe6c746bded0f08b0a51d614aeac))

## [0.4.0](https://github.com/carawayhome/caraway-meridian/compare/v0.3.0...v0.4.0) (2024-03-06)


### Features

* add ProductCard and ProductGrid components ([7e33f83](https://github.com/carawayhome/caraway-meridian/commit/7e33f8351b4c93e8cf4a7b19cd52355d6cda1f00))
* **collections:** collections page now shows all products ([c1d836c](https://github.com/carawayhome/caraway-meridian/commit/c1d836c7dd2b9d420351c88fa3401a7606817729))
* **contentful:** added contentful integration and pulling of products ([b550f2a](https://github.com/carawayhome/caraway-meridian/commit/b550f2a95337aa084cc1566196c8e38f70628b59))
* **currency:** add currency utils ([848b39b](https://github.com/carawayhome/caraway-meridian/commit/848b39b0141e07d20a196eb5f31cd40066a9b212))


### Bug Fixes

* remove deprecated fetch ([c317129](https://github.com/carawayhome/caraway-meridian/commit/c3171294e2c04755220deceec61e402566b4fb9f))
* updated ts props ([7f41023](https://github.com/carawayhome/caraway-meridian/commit/7f41023cdfe1e70ab79ddc565c9e7cf37acb6d2f))

## [0.3.0](https://github.com/carawayhome/caraway-meridian/compare/v0.2.1...v0.3.0) (2024-02-25)


### Features

* **shopify:** add new files for Shopify cart products layout and page. Includes Checkout ([4940ef6](https://github.com/carawayhome/caraway-meridian/commit/4940ef6297966c73f11ed3cd4b31b83ed303e088))
* **shopify:** add Shopify provider components and utils, includes cart provider ([51f22b0](https://github.com/carawayhome/caraway-meridian/commit/51f22b08ee3d23aafa3f5aa580ca8234495c6817))
* **shopify:** setup shopify hydrogen react storefront client and environment ([9899379](https://github.com/carawayhome/caraway-meridian/commit/98993797159d04df5c2a210e813fe992b8a5b0ab))


### Bug Fixes

* eslint config ([f26fd25](https://github.com/carawayhome/caraway-meridian/commit/f26fd251ccee5800544870315285ab993952f083))
* import React in Nav component and fix formatting in Header component ([843c8e7](https://github.com/carawayhome/caraway-meridian/commit/843c8e74fc724e8b68fc2aa1d57dc342ff46f684))
* mismatch token assignment ([6f26a76](https://github.com/carawayhome/caraway-meridian/commit/6f26a760667eb4fd6fece62bb203ce885ea1195b))
* missing api ([ac60630](https://github.com/carawayhome/caraway-meridian/commit/ac6063050091d9b248ea3a56a7aba869a53c22c0))
* package lock update ([a01acb2](https://github.com/carawayhome/caraway-meridian/commit/a01acb2672f7e81981450fc8fe46dc3b68048ad1))
* type error in ColorSwatch component ([e214fc8](https://github.com/carawayhome/caraway-meridian/commit/e214fc82a0baad9b7e5f6b208557d3f3c73c2714))

## [0.2.1](https://github.com/carawayhome/caraway-meridian/compare/v0.2.0...v0.2.1) (2024-02-09)


### Bug Fixes

* default values for disabled prop in Button and Anchor components ([37f7d38](https://github.com/carawayhome/caraway-meridian/commit/37f7d38ea966f5dd8dd31064414699d974616b29))

## [0.2.0](https://github.com/carawayhome/caraway-meridian/compare/v0.1.0...v0.2.0) (2024-02-08)


### Features

* add @stylexjs/eslint-plugin and @stylexjs/nextjs-plugin ([1aa7e87](https://github.com/carawayhome/caraway-meridian/commit/1aa7e8760cfafaf20d4d156fb50fe2c0f107bf23))
* add localization for en-US.json file ([e2f0caf](https://github.com/carawayhome/caraway-meridian/commit/e2f0caf06a9ea1620278b72a2d671f3fd36d1366))
* **calltoaction:** new call to action button component ([533156c](https://github.com/carawayhome/caraway-meridian/commit/533156c1635acb1238f9ebb4d9679416bb9e3a97))


### Bug Fixes

* add as string ([bc1b046](https://github.com/carawayhome/caraway-meridian/commit/bc1b0469cfb93cf44e3d0698068f23376f9d8bcc))
* add client-side code to CallToAction component ([3dff1fb](https://github.com/carawayhome/caraway-meridian/commit/3dff1fbae692f8ce793119090696ec7e5c0534f9))
* and refactor CallToAction button component ([665d2f8](https://github.com/carawayhome/caraway-meridian/commit/665d2f8d172807d17b3f7d2742dec18a87e393b5))
* missing commands ([fa9eaf0](https://github.com/carawayhome/caraway-meridian/commit/fa9eaf0c7f25806ceb30e499d2a0eb83bea922cd))
* refactor CallToAction component styles ([4d1807f](https://github.com/carawayhome/caraway-meridian/commit/4d1807f304d02ff435d78d95fb189aebed0186f7))
* update CallToAction component styles ([44d9e91](https://github.com/carawayhome/caraway-meridian/commit/44d9e9197f022bca218cdbf658a8edb8e23709a6))

## 0.1.0 (2024-02-05)


### Features

* add @vercel/speed-insights package and integrate it into layout component ([cc4d78f](https://github.com/carawayhome/caraway-meridian/commit/cc4d78f2edc58bc687475d6d60496840ea268325))
* add husky with commit linting and commit zen for a streamlined commit process
