const fs = require('fs')
const path = require('path')

const query = `
    query {
      productCollection {
        items {
          __typename
          slug
          title
          name
          productId
          variants: variantsCollection(limit: 15) {
            items {
              variantId
              price
            }
          }
        }
      }
    }
  `

const TWO = 2

/* eslint-disable import/prefer-default-export */
async function generateJSON() {
  try {
    const response = await fetch(
      `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.CONTENTFUL_ACCESS_TOKEN}`,
        },
        body: JSON.stringify({ query }),
      }
    )

    if (!response.ok) {
      return Response.json({ error: 'Failed to fetch data' }, { status: 500 })
    }

    const { data } = await response.json()

    const products = data.productCollection.items

    const outputDir = path.join(process.cwd(), 'public', 'data/json-products')

    fs.mkdirSync(outputDir, { recursive: true })

    products.forEach((product) => {
      const filePath = path.join(outputDir, `${product.productId}.json`)
      const fileContent = JSON.stringify(product, null, TWO)
      fs.writeFileSync(filePath, fileContent, 'utf-8')
    })

    console.log('Congratulations! Contentful Product JSON data directory has been built successfully')
    return Response.json({ message: 'JSON files generated successfully!' })
  } catch (error) {
    console.log('Error - Contentful Product JSON data directory was not build successfully')
    return Response.json({ error: 'Failed to fetch data' }, { status: 500 })
  }
}

generateJSON()
