---
title: "Theme Color Pairings"
description: "Learn about theme tokens in Meridian."
---

# Theme Color Pairings

Theme Color Pairings are defined as an object, with keys being the color names and values being the color codes.
These pairings are StyleX Themes which override the existing color values.

```ts filename="src/app/themeThemes.stylex.ts"
const black300 = stylex.createTheme($T, {
  primarySurface: colors.black300,
  primaryText: colors.white,
})

const burgundy = stylex.createTheme($T, {
  primarySurface: colors.burgundy,
  primaryText: colors.white,
})

const burgundy300 = stylex.createTheme($T, {
  primarySurface: colors.burgundy300,
  primaryText: colors.cream,
})
// ... other colors ...
```

export function Swatch({ name, value, desc }) {
  return (
    <div>
      <span style={{ fontSize: '13px', display: 'block', fontWeight: '600' }}>{name}</span>
      <div style={{ backgroundColor: value, height: "45px", width: "100px", borderRadius: "5px", boxShadow: "0 0 10px rgba(0,0,0,.1)" }} />
      <span style={{ fontSize: '11px', display: 'block', fontWeight: '500' }}>{desc}</span>
    </div>
  )
}

export const themeColors = [
  {
    name: "babyBlue",
    surface: {
      name: 'babyBlue',
      value: '#A7CAD4'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "babyBlue300",
    surface: {
      name: 'babyBlue300',
      value: '#D3E5E9'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "black",
    surface: {
      name: 'black',
      value: '#333333'
    },
    text: {
      name: 'white',
      value: '#FFFFFF'
    }
  },
  {
    name: "black300",
    surface: {
      name: 'black300',
      value: '#999999'
    },
    text: {
      name: 'white',
      value: '#FFFFFF'
    }
  },
  {
    name: "burgundy",
    surface: {
      name: 'burgundy',
      value: '#6A2828'
    },
    text: {
      name: 'white',
      value: '#FFFFFF'
    }
  },
  {
    name: "burgundy300",
    surface: {
      name: 'burgundy300',
      value: '#7A4949'
    },
    text: {
      name: 'cream',
      value: '#F7E6C1'
    }
  },
  {
    name: "cream",
    surface: {
      name: 'cream',
      value: '#F7E6C1'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "cream300",
    surface: {
      name: 'cream300',
      value: '#FBF3E0'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "gray",
    surface: {
      name: 'gray',
      value: '#737373'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "gray100",
    surface: {
      name: 'gray100',
      value: '#F7F7F7'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "gray200",
    surface: {
      name: 'gray200',
      value: '#EFEFEF'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "gray300",
    surface: {
      name: 'gray300',
      value: '#C7C7C7'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "gray500",
    surface: {
      name: 'gray500',
      value: '#9F9F9F'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "marigold",
    surface: {
      name: 'marigold',
      value: '#F0B323'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "marigold300",
    surface: {
      name: 'marigold300',
      value: '#F7D991'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "mist",
    surface: {
      name: 'mist',
      value: '#B5C9C0'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "mist300",
    surface: {
      name: 'mist300',
      value: '#DAE4DF'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "navy",
    surface: {
      name: 'navy',
      value: '#1F3438'
    },
    text: {
      name: 'white',
      value: '#FFFFFF'
    }
  },
  {
    name: "navy300",
    surface: {
      name: 'navy300',
      value: '#8F999B'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "perracotta",
    surface: {
      name: 'perracotta',
      value: '#BC522F'
    },
    text: {
      name: 'white',
      value: '#FFFFFF'
    }
  },
  {
    name: "perracotta300",
    surface: {
      name: 'perracotta300',
      value: '#DF9780'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "perracotta500",
    surface: {
      name: 'perracotta500',
      value: '#D47555'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "sage",
    surface: {
      name: 'sage',
      value: '#5E6C51'
    },
    text: {
      name: 'cream',
      value: '#F7E6C1'
    }
  },
  {
    name: "sage300",
    surface: {
      name: 'sage300',
      value: '#90A088'
    },
    text: {
      name: 'white',
      value: '#FFFFFF'
    }
  },
  {
    name: "sage500",
    surface: {
      name: 'sage500',
      value: '#6B8060'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "slate",
    surface: {
      name: 'slate',
      value: '#57728B'
    },
    text: {
      name: 'white',
      value: '#FFFFFF'
    }
  },
  {
    name: "slate300",
    surface: {
      name: 'slate300',
      value: '#ABB8C5'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "warmGray",
    surface: {
      name: 'warmGray',
      value: '#C9C6B7'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "warmGray300",
    surface: {
      name: 'warmGray300',
      value: '#E4E3DB'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  },
  {
    name: "white",
    surface: {
      name: 'white',
      value: '#FFFFFF'
    },
    text: {
      name: 'navy',
      value: '#1F3438'
    }
  }
]

export function RenderSwatches({ themeColors }) {
  return (
    <div style={{display: 'flex', flexWrap: 'wrap', gap: '2rem'}}>
      {themeColors.map((theme) =>
        <div>
          <div style={{display: 'flex', gap: '0.5rem'}}>
            <Swatch key={theme.surface.name} name={theme.surface.name} value={theme.surface.value} desc={'surface'} />
            <Swatch key={theme.text.name} name={theme.text.name} value={theme.text.value} desc={'text'} />
          </div>
        </div>
      )}
    </div>
  )
}

<RenderSwatches themeColors={themeColors} />
