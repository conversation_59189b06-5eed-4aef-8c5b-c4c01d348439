## Commit Guidelines with Comm<PERSON>zen

To make commits following the [Commitizen](https://commitizen-tools.github.io/commitizen/) rules, you can follow these guidelines:

1. Start with a concise and descriptive commit message.
2. Use a prefix to indicate the type of change you are making. Here are some common prefixes:

   - **feat**: A new feature.
   - **fix**: A bug fix.
   - **docs**: Documentation only changes.
   - **style**: Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc.).
   - **refactor**: A code change that neither fixes a bug nor adds a feature.
   - **perf**: A code change that improves performance.
   - **test**: Adding missing tests or correcting existing tests.
   - **build**: Changes that affect the build system or external dependencies (example scopes: gulp, broccoli, npm).
   - **ci**: Changes to our CI configuration files and scripts (example scopes: Travis, Circle, BrowserStack, SauceLabs).
   - **chore**: Other changes that don't modify `src` or `test` files.
   - **revert**: Reverts a previous commit.

   For example, a commit message could be:
   `feat: Add user authentication feature.`

## Automated Releases with <PERSON><PERSON> and Release-Please

Additionally, we use [<PERSON><PERSON>](https://typicode.github.io/husky) and [Release-Please](https://github.com/googleapis/release-please) to automate our releases:

- **Husky** is a Git hook manager that allows us to run scripts before committing or pushing code. It helps enforce commit message conventions and run tests before allowing a commit.

- **Release-Please** is a tool that automates the release process. It analyzes the commit history and generates release notes, version tags, and changelogs automatically. This helps streamline the release process and ensures consistent and accurate release documentation.

By following these guidelines and utilizing Husky and Release-Please, we can maintain a structured and automated release workflow.
