### Getting Started

**Requirements:**

- Node.js version 20 or higher
- `npm` (or your package manager of choice, such as `yarn` or `pnpm`)

### Running Locally

You will need to use the environment variables defined in `.env.example` to run Project Meridian.
> **Note:** You should not commit your `.env` file or it will expose secrets that will allow others to control your Shopify store.

Clone the source code:

```bash
git clone https://github.com/carawayhome/meridian.git
```

### Usage

First, you need to set the following environment variables in the `.env` file or your deployment platform:

- `NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN`
- `NEXT_PUBLIC_SHOPIFY_PUBLIC_STOREFRONT_TOKEN`
- `NEXT_PUBLIC_SHOPIFY_STOREFRONT_API_VERSION`
- `SHOPIFY_PRIVATE_STOREFRONT_TOKEN`


### 🧞 Installation and Commands

| Command                | Action                                              |
| :--------------------- | :-------------------------------------------------- |
| `npm install`          | Installs dependencies                               |
| `npm run dev`          | Starts local dev server at [localhost:3000](http://localhost:3000/) |
| `npm run build`        | Builds your production site to `./dist/`            |
| `npm run start`        | Starts the project in production mode.              |
| `npm run lint`         | Analyzes the code to find problems with `eslint`    |
| `npm run clean`        | Removes the `.next` directory                       |
| `npm run clean-dev`    | Cleans the `.next` directory and starts the dev server |
| `npm run clean-build`  | Cleans the `.next` directory and builds the project |
| `npm run prepare`      | Sets up Husky for commit hooks                      |
| `npm run cz`           | Runs Commitizen for structured commit messages      |
| `npm run cc`           | Runs CLI to help create a new react component      |
