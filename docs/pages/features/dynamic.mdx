---
title: "Dynamic Callouts and Pricing"
description: "Dynamic Callouts and Pricing is a feature designed to enhance the user experience by providing contextual information based on the user's cart total or selected product price. This documentation outlines how it works, its implementation, and the benefits it brings to the application."
---

# Dynamic Callouts and Pricing

Dynamic Callouts and Pricing is a feature designed to enhance the user experience by providing contextual information based on the user's cart total or selected product price. This documentation outlines how it works, its implementation, and the benefits it brings to the application.

## Overview

The Dynamic Callouts feature leverages pricing tiers to display relevant callouts that inform users about discounts or promotions as they interact with products. When the user changes the price (e.g., by selecting different variants), the appropriate callout is dynamically determined based on predefined thresholds.

## How It Works

### Pricing Tiers

Pricing tiers are defined in a list where each tier includes:
- A **threshold** indicating the minimum price to qualify for the tier.
- A **discount** that applies when the threshold is met.
- **Callouts** that contain promotional messages or information relevant to the discount.

Here’s an example of a tier list:

```ts
const tierList = [
  {
    threshold: 85,
    discount: 0.1,
    callouts: [{ title: '10% Unlocked', id: '10off', settings: { theme: 'sage' } }]
  },
  {
    threshold: 425,
    discount: 0.15,
    callouts: [{ title: '15% Unlocked', id: '15off', settings: { theme: 'marigold' } }]
  },
  {
    threshold: 525,
    discount: 0.2,
    callouts: [{ title: '20% Unlocked', id: '20off', settings: { theme: 'perracotta' } }]
  },
];
```

### Dynamic Pricing Calculation

When a user interacts with the product, the application calculates the final price and selects the appropriate callout based on the current price and the user's cart total. The logic used to determine this can be summarized as follows:

1. **Check Each Tier**: Loop through the `tierList` to find the applicable tier based on the price or cart total.
2. **Select Highest Discount**: If the current tier's discount is greater than the previously selected discount, update the selected discount and callouts.
3. **Reset on Price Change**: Ensure that only one dynamic callout is shown at any time by resetting the callouts whenever the price changes.

