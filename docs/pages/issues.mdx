## Submitting an Issue

If you encounter a bug or need to report something, you can submit an issue through our project management tool, Shortcut. You can also notify a Project Manager or Team Lead to ensure the item is tracked.

### Enhancing your Workflow with the [TODO Tree App](https://marketplace.visualstudio.com/items?itemName=Gruntfuggly.todo-tree)

To streamline your workflow and keep track of important comments in your code, we recommend using the **TODO-TREE** extension in VS Code. This tool helps you visualize and manage comments like TODOs, BUGs, and FIXMEs directly in your codebase.


  **Example:**
  ```
  // TODO: Refactor this function to improve performance
  // FIXME: This logic is brittle and might break with edge cases
  // BUG: The carousel does not render correctly on mobile devices
  // NOTE: This component is used across multiple pages, changes here might have wide-reaching effects.
  ```


#### Installation:

1. **Install TODO-TREE:**
   - Go to the Extensions view in VS Code (`Ctrl+Shift+X`).
   - Search for "TODO-TREE" and install the extension.

2. **Add Configuration:**
   After installing, add the following configuration to your VS Code settings JSON (`.vscode/settings.json`):

   ```json
		"todo-tree.general.tags": [
			"BUG",
			"HACK",
			"FIXME",
			"FIX",
			"TODO",
			"[ ]",
			"[x]",
			"NOTE",
			"ALERT",
			"ATTENTION",
			"DANGER",
			"ERROR",
			"SECURITY",
			"DEPRECATED",
			"TASK",
			"TBD",
			"WARNING",
			"CAUTION",
			"TEST",
			"TESTING"
		],
		"todo-tree.general.tagGroups": {
			"FIXME": [
				"FIXME",
				"FIX",
			],
			"TODO": [
				"TODO",
				"TASK",
				"TBD"
			],
			"TEST": [
				"TEST",
				"TESTING",
			],
			"WARNING": [
				"WARNING",
				"CAUTION",
				"ALERT",
				"ATTENTION",
			]
		},
		"todo-tree.highlights.customHighlight": {
			"TODO": {
				"iconColour": "#00FA9A",
				"icon": "check-circle"
			},
			"[x]": {
				"iconColour": "#00FA9A"
			},
			"[ ]": {
				"iconColour": "#00FA9A"
			},
			"BUG": {
				"iconColour": "#FF8C00",
			},
			"HACK": {
				"iconColour": "#FF69B4"
			},
			"FIXME": {
				"iconColour": "#FF6347"
			},
			"WARNING": {
				"iconColour": "#FFFF00",
				"icon": "alert"
			},
			"DANGER": {
				"iconColour": "#FF6347",
				"icon": "flame"
				},
			"ERROR": {
					"iconColour": "#FF6347",
					"icon": "flame"
				},
			"NOTE": {
				"iconColour": "#FFD700",
				"icon": "info"
			},
			"SECURITY": {
				"iconColour": "#00BFFF",
				"icon": "shield"
			},
			"DEPRECATED": {
				"iconColour": "#E6E6FA",
				"icon": "archive"
			},
			"TEST": {
				"iconColour": "#00BFFF",
				"icon": "beaker"
			}
		},
   ```

   This configuration highlights various annotations like `TODO`, `BUG`, `FIXME`, and others with specific colors and icons, making them easier to spot in your code.

By following these steps, you can ensure that your issues are properly tracked, your code comments are organized, and your workflow is optimized for efficiency.
