---
title: "Colors"
description: "Learn about theme tokens in Meridian."
---

# Colors

Colors are defined as an object, with keys being the color names and values being the color codes.

```ts filename="src/app/themeTokens.stylex.ts"
const colors = stylex.defineVars({
  black: {
    default: '#333333',
    [DARK]: '#F1F7FC',
  },
  black300: {
    default: '#999999',
  },
  navy: {
    default: '#1F3438',
    [DARK]: '#F1F7FC',
  },
  navy300: {
    default: '#8F999B',
  },
  // ... other colors ...
})
```

export function Swatch({ name, value }) {
  return (
    <div>
      <div style={{ backgroundColor: value, height: "65px", width: "115px", borderRadius: "5px", boxShadow: "0 0 10px rgba(0,0,0,.1)" }} />
      <span style={{ fontSize: '13px' }}>{name}</span>
    </div>
  )
}

export const colors = [
  {
    name: "black",
    value: "#333333"
  },
  {
    name: "black300",
    value: "#999999"
  },
  {
    name: "navy",
    value: "#1F3438"
  },
  {
    name: "navy300",
    value: "#8F999B"
  },
  {
    name: "sage",
    value: "#5E6C51"
  },
  {
    name: "sage500",
    value: "#6B8060"
  },
  {
    name: "sage300",
    value: "#90A088"
  },
  {
    name: "warmGray",
    value: "#C9C6B7"
  },
  {
    name: "warmGray300",
    value: "#E4E3DB"
  },
  {
    name: "cream",
    value: "#F7E6C1"
  },
  {
    name: "cream300",
    value: "#FBF3E0"
  },
  {
    name: "perracotta",
    value: "#BC522F"
  },
  {
    name: "perracotta500",
    value: "#D47555"
  },
  {
    name: "perracotta300",
    value: "#DF9780"
  },
  {
    name: "marigold",
    value: "#F0B323"
  },
  {
    name: "marigold300",
    value: "#F7D991"
  },
  {
    name: "burgundy",
    value: "#6A2828"
  },
  {
    name: "burgundy300",
    value: "#7A4949"
  },
  {
    name: "mist",
    value: "#B5C9C0"
  },
  {
    name: "mist300",
    value: "#DAE4DF"
  },
  {
    name: "babyBlue",
    value: "#A7CAD4"
  },
  {
    name: "babyBlue300",
    value: "#D3E5E9"
  },
  {
    name: "slate",
    value: "#57728B"
  },
  {
    name: "slate300",
    value: "#ABB8C5"
  },
  {
    name: "white",
    value: "#FFFFFF"
  },
  {
    name: "gray",
    value: "#737373"
  },
  {
    name: "gray500",
    value: "#9F9F9F"
  },
  {
    name: "gray300",
    value: "#C7C7C7"
  },
  {
    name: "gray200",
    value: "#EFEFEF"
  },
  {
    name: "gray100",
    value: "#F7F7F7"
  },
];

export function RenderSwatches({ colors }) {
  return (
    <div style={{display: 'flex', flexWrap: 'wrap', gap: '1rem'}}>
      {colors.map((color) => <Swatch key={color} name={color.name} value={color.value} /> )}
    </div>
  )
}

<RenderSwatches colors={colors} />