---
title: Postscript Integration
description: Learn how to integrate Postscript with your Next.js project.
---

# Postscript Integration

Postscript allows you to easily integrate SMS marketing into your store. This guide will walk you through integrating Postscript into your Next.js application using a simple script.

## Installation

To integrate Postscript into your application, you need to include their SDK. Here's a simple example of how to do this in a Next.js project.

### Step 1: Create a Postscript Component

First, create a `Postscript` component that will load the Postscript SDK. Below is the code snippet you can use.

```tsx
'use client'

import Script from 'next/script'

const Postscript = () => (
  <Script src="https://sdk.postscript.io/sdk.bundle.js?shopId=4845" />
)

export default Postscript
