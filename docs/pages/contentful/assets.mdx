# Asset Management in Contentful

## How to Upload Assets to Contentful

1. Once you’re logged into Contentful, navigate to the "**Media**" tab.
2. Click on the "**Add Asset**" or via the Dropdown add “**Multiple Assets**” button and select the media to upload.
3. Once the media has uploaded, it must also be published. Before doing so, use the Asset Taxonomy/Naming Convention outlined below.
4. Now that all the details have been updated → publish the assets to make them available for use.

![Image for Contentful Images.png](./images/contentful.png)

## Asset Taxonomy/Naming Convention

1. Rename each image to the following format using **dashes to indicate a space and underscore to indicate a subsection within the formatting**:

    **Product/Item Name - Variant/Category - Shot/Style - Version (only when needed)**

    Example Title: Ceramic Cookware Set - Cream - Lifestyle in Kitchen - v1

    Example Filename: `Ceramic-Cookware-Set_Cream_Lifestyle-in-Kitchen_v1.(jpg|png)`

    <aside>
    🔎 This is done to both improve our **SEO performance** and increase **search capabilities for our assets** across our platforms.

    </aside>

    **Components**

    - **Product/Item Name**: The product name and or subject the asset is tied to.
    - **Variant/Category:** The product variant (e.g., color or model) or group the asset is tied to.
    - **Shot/Style**: Asset type (e.g., `front`, `back`, `detail`, `icon`). Can also be used as a short description that does not exceed 5 words.
    - **Version**: Asset version number (e.g., `v1`, `v2`).
    - **Extension**: File format (e.g., `.jpg`, `.png`, `.svg`).

## **Image Guidelines**

Ensure uniformity in quality and size across assets. Never upload images that are larger than their required purpose. **For example don’t upload a 2000px by 2000px image for the purpose of a thumbnail.**

- **Resolution**:
    - **Thumbnails**: 150x150 px
    - **Product Detail Images**: 800x600 px
    - **High-Resolution**: 1920x1080 px for hero or banner images
- **Format**:
    - Use `.jpg` for photographs (optimized for the web).
    - Use `.png` for images requiring transparency and graphic images.
    - Use `.svg` for icons, logos or vector assets.
    - `.webm` or `.mp4`  - Used for short-form and long-form videos
- **File Size**:
    - Limit assets to under 1 MB where possible for faster load times.
- **Alt Text**: Provide accurate descriptions for accessibility (e.g., `Blue t-shirt front view`).

## Video Guidelines

**Aspect Ratio**:

- **Mobile**: 9:16 for portrait orientation.
- **Desktop**: 16:9 for optimal widescreen display.

**Resolution**:

- **Mobile**: Minimum: 480x270 pixels | Maximum: 1280x720 pixels*(Recommended: 1280x720 for a balance between quality and file size)*
- **Desktop**: Minimum: 640x360 pixels | Maximum: 1920x1080 pixels

**Frame Rate**: Maximum of 29.97 FPS to ensure smooth playback without excessive file size.

**Formats**: MP4 or WEBM are required for compatibility and performance.

**File Size**:

- **Mobile**: Aim for 300-500KB for faster load times on slower connections.
- **Desktop**: Ideal: Under 1MB | Acceptable: 2-5MB (Maximum: 7MB)

    *if over 10mb we need to stream the video with [mux.com](http://mux.com) (costs money)*


**Video Length**:

- Aim for 10 seconds.
- Maximum: 15 seconds.

**Audio**: Remove the audio track to reduce file size and improve page performance.