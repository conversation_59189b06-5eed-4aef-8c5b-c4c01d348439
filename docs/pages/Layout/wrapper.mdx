---
metaTitle: Wrapper
metaDescription: Fundamental layout building block.
sourcePath: layout/Wrapper
---

```jsx live=true
<Wrapper theme="cream">
  <Container size="1">

  </Container>
</Wrapper>
```

## API Reference

This component is based on the `div` element.

| Prop   | Type   | Default |
| :----- | :----- | :------ |
| as     | "div" \| "section" \| "span" |  "section"  |
| theme  | ThemeColors  |  —  |
