---
metaTitle: Container
metaDescription: Fundamental layout building block.
sourcePath: layout/Container
---

```jsx live=true
<Container>
</Container>
```

## API Reference

| Prop           | Type                      | Default |
| :------------- | :------------------------ | :------ |
| children       | React.ReactNode           |    —    |
| as             | "main" \| "section" \| "div" \| "span" \| "article" \| "header" \| "nav" \| "aside" \|  "footer" |  "section"  |
| theme          | ThemeColors               |    —    |
| flex           | boolean?                  |    —    |
| flexCentered   | boolean?                  |    —    |
| grid           | boolean?                  |    —    |
| gridCentered   | boolean?                  |    —    |
| gridColumns    | boolean?                  |    —    |
| gridAutoFit    | boolean?                  |    —    |
| gridAutoFill   | boolean?                  |    —    |
| gridPile       | boolean?                  |    —    |
| align          | Align?                    |    —    |



The following props are shared between [Wrapper](./wrapper), [Container](./container), and [Item](./item) components. Read more about layout components in [Layout](/themes/docs/overview/layout).

| Prop           | center | right |
| :------------- | :----: | ----: |
| size           | Size?  |    —   |
| gap            | Size?  |    —   |
| paddingBlock   | Size?  |    —   |
| styleProp      | {}?    |    —   |


### Available Props

`m`, `mt`, `mb`, `ml`, `mr`, `mx`, `my`, `p`, `pt`, `pb`, `pl`, `pr`, `px`, `py`