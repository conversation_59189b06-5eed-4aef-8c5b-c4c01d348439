# Interactive Image Link

The Interactive Image Link Dropdown (Mega Menu) includes an image that changes when you hover over an interactive text link. This image and its content serve as a link to a page or product.

---

### Interactive Image Desktop Layout 

![Interactive Image](/assets/navigations/desktop/inter-link.gif)

# Contentful Documentation for Creating a Navigation Dropdown Layout

## Overview
Our Desktop and Mobile navigations reside in the Global Settings. To update the Desktop Navigation, users should follow these steps:

1. Navigate to **Global Settings**.
2. Scroll down and click on **Desktop Navigation**.

Once in Desktop Navigation, users can create or update an existing navigation link and its collection. This guide focuses on creating a Navigation Link Collection using an Interactive Image Layout.

## Step-by-Step Instructions

### Step 1: Create a New Navigation Link
-  **Link Title**: This will be displayed on the Desktop Navigation.
-  **Link Destination**: Provide a destination link that points to either a Page or a Product Page.
-  **Optional Callout**: Users can add a callout that will be rendered slightly above and to the right of the link.

*Follow the steps above will create a new navigation link without any layout (dropdown).*
![Interactive Image](/assets/navigations/desktop/navigation-link.jpg)

### Step 2: Create a Navigation Link Collection (Interactive Image Layout Dropdown)
To emulate the image provided above, follow these steps:

#### 1. Navigation Link Collection
-  **Name**: This is used for Contentful purposes only.
-  **Type (required)**: Select **Interactive Image Link** from the dropdown.
-  **Link Collection**: Add a Navigation Link. Ensure the selected navigation link includes:
  - **Link Title**
  - **Description** (if required)
  - **Image**
  - **Hover Image**: This image will appear when hovering over the interactive text link.
  - **Link Destination** (Page or Product)

![Interactive Image](/assets/navigations/desktop/inter-image-link.jpg)
#### 2. Add a Navigation Link Divider
Insert a divider to separate different sections of your navigation.

#### 3. Navigation Link Collection (Column Links)
-  **Name**: This is used for Contentful purposes only.
-  **Type (required)**: Select **Text Link** from the dropdown.
-  **Add Navigation Links**: Include the links you wish to display in your Column Links. Each navigation link will require:
  - **Link Title**
  - **Link Destination**
  - **Optional Callout**: Adding a callout here will result in a callout "dot" appearing to the left of the Navigation Link.

![Interactive Image](/assets/navigations/desktop/inter-text.jpg)

#### 4. Repeat Step 3
Continue adding additional Navigation Link Collections as needed.

--- 

## Conclusion
Following these steps will help you create a structured and visually appealing navigation dropdown layout for your Desktop Navigation. Ensure all fields are filled out correctly to maintain consistency and functionality.
