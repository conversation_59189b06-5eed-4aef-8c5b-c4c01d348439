# Animation for Navigation Links

Animations allow you to enhance the appearance of top-level navigation links in Desktop views by applying an animation type. This guide explains how to apply animations and the current limitations.

---

### Animation for Navigation Links

![Animation Example](/assets/navigations/desktop/animation.jpg)

# Contentful Documentation for Animating Navigation Links

## Overview
Animations allow you to add visual effects to top-level navigation links by applying an animation type. Currently, this feature is only available for Desktop navigation.

## Step-by-Step Instructions

### Step 1: Create or Edit a Navigation Link
- **Name**: This is used for Contentful purposes only.
- **Link Title**: This will be displayed on the Desktop Navigation.
- **Link Destination**: Provide a destination link that points to either a Page or a Product Page.

### Step 2: Add an Animation Type
- **Animation Type**: Select the animation type from the available options. Currently, the only available animation is "Hearts," used for February Favorites. This will add animated SVG hearts behind the top-level link.

### Note on Animation Availability
- **Current Limitations**: At present, only the "Hearts" animation is available. Adding new animations will require development work.
- **Desktop Only**: This feature is currently set up to work only on Desktop navigation.

--- 

## Conclusion
Animations provide a dynamic and engaging way to enhance navigation links. Ensure that the chosen animation complements the overall design and theme of your navigation. 