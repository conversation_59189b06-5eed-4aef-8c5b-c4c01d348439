# Image Links for Desktop

Image Links can be used within Desktop navigation dropdown collections. They display images that can include callouts and are versatile in layout combinations.

---

### Image Links Desktop Layout

![Image Links](/assets/navigations/desktop/img-link.jpg)

# Contentful Documentation for Creating Image Links

## Overview
Image Links are designed to be used within a collection that has a dropdown (Desktop). They can be added individually to create various layout combinations with Column Links.

## Step-by-Step Instructions

### Step 1: Create a New Image Link
- **Name**: This is used for Contentful purposes only.
- **Type (required)**: Select **Image Link** from the dropdown.
- **Link Collection**: Add a Navigation Link. Ensure the selected navigation link includes:
  - **Link Title**
  - **Image**
  - **Link Destination** (Page or Product)
  - **Optional Callouts**: Up to 2 callouts can be added, appearing on the top left of the image.

![Image Links](/assets/navigations/desktop/image-link.jpg)

### Step 2: Add Image Links to a Collection
- **Desktop Collection**: Add Image Links individually to a dropdown collection. Combine with Column Links and dividers as needed.

### Step 3: Customize the Layout
- **Layout Combinations**: Use combinations like 1 image, a divider, and up to 3 column links. Be mindful of spacing to avoid wrapping.

--- 

## Conclusion
Image Links provide a flexible way to enhance navigation with visuals. Ensure all fields are filled out correctly to maintain consistency and functionality. 