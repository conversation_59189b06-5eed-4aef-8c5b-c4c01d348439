# Column Links (Text Links)

Column Links, labeled as "Text Links" in Contentful, are used within Desktop or Mobile Collections. They display only the link name, without images or descriptions, and are stacked vertically.

---

### Column Links

![Column Links](/assets/navigations/desktop/col-link.jpg)

# Contentful Documentation for Creating Column Links

## Overview
Column Links are designed to be used within a collection that has a dropdown (Desktop) or a slide-in panel (Mobile). They can be created independently and added to other collections as needed.

## Step-by-Step Instructions

### Step 1: Create a New Column Link Collection
- **Name**: This is used for Contentful purposes only.
- **Type (required)**: Select **Text Link** from the dropdown.
- **Link Collection**: Add a Navigation Link. Ensure the selected navigation link includes:
  - **Link Title**: This will be displayed in the collection.
  - **Link Destination**: If provided, the link will be clickable. If not, it will render as plain text with smaller font styles and capitalized typography, often used as a header-like link (e.g., "Shop All Bakeware").

*Note: Column Links do not render images or descriptions.*

![Column Links](/assets/navigations/desktop/col-link-collection.jpg)

### Step 2: Add Column Links to a Collection
- **Desktop Collection**: Add the Column Links to a dropdown collection.
- **Mobile Collection**: Add the Column Links to a slide-in panel collection.

### Step 3: Customize the Appearance
- **Typography Styles**: Links without a destination will automatically have smaller, capitalized typography, serving as a header-like link.

--- 

## Conclusion
Column Links provide a simple and clean way to display navigation options within a collection. They are versatile and can be used in both Desktop and Mobile layouts. Ensure all fields are filled out correctly to maintain consistency and functionality. 