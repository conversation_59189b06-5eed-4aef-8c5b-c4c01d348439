# Theming for Navigation Links

Theming allows you to customize the appearance of top-level navigation links in both Desktop and Mobile views by applying a theme color. This guide explains how to apply theme colors and the effects on typography.

---

### Theming Navigation Links

![Theming Example](/assets/navigations/desktop/theme.jpg)

# Contentful Documentation for Theming Navigation Links

## Overview
Theming allows you to customize the appearance of top-level navigation links by applying a theme color. This color is applied to the static link and not to any attached collections.

## Step-by-Step Instructions

### Step 1: Create or Edit a Navigation Link
- **Name**: This is used for Contentful purposes only.
- **Link Title**: This will be displayed on the Desktop or Mobile Navigation.
- **Link Destination**: Provide a destination link that points to either a Page or a Product Page.

### Step 2: Add a Token Setting for Theming
- **Token Settings**: Add a Token setting to the navigation link.
- **Theme Color**: Choose a theme color from the available options. This color will be applied to the top-level link (static link) only.

| Link Theme | Setting Theme Value |
| ------- | ------- |
| ![Theming Example](/assets/navigations/desktop/theme-link.jpg) | ![Theming Example](/assets/navigations/desktop/theme-setting.jpg) |
  



### Note on Typography and Contrast
- **Typography Style**: Adding a theme color will change the text to use the Typography theme `h2Primary` and will italicize the text. This is currently hard-coded and cannot be changed.
- **Contrast Consideration**: Always choose a theme color that provides enough contrast with the white background used on both Desktop and Mobile to ensure readability.

--- 

## Conclusion
Theming allows for a personalized and visually appealing navigation experience. Ensure that the chosen theme color provides sufficient contrast with the background to maintain readability and accessibility. 