# Featured Image Links for Mobile

Featured Image Links, labeled as "Interactive Image Links" in Contentful, are used to create a top-level image slider in Mobile navigation. They display images that can include callouts and are versatile in layout combinations.

---

### Featured Image Links Mobile Layout

![Featured Image Links](/assets/navigations/mobile/feat-image.jpg)

# Contentful Documentation for Creating Featured Image Links

## Overview
Featured Image Links are designed to be used within a collection that renders as a top-level image slider in Mobile navigation. Unlike Desktop, these do not change images on hover but display a collection of images in a slider format.

## Step-by-Step Instructions

### Step 1: Create a New Featured Image Link
- **Name**: This is used for Contentful purposes only.
- **Type (required)**: Select **Interactive Image Link** from the dropdown.
- **Link Collection**: Add a Navigation Link. Ensure the selected navigation link includes:
  - **Link Title**
  - **Image**
  - **Link Destination** (Page or Product)
  - **Optional Callouts**: Up to 2 callouts can be added, appearing on the top left of the image.

![Featured Image Links](/assets/navigations/desktop/featured-image-link.jpg)

### Step 2: Add Featured Image Links to a Collection
- **Mobile Collection**: Add Featured Image Links to a collection labeled as "Interactive Image Link" to render as a top-level image slider.

### Step 3: Add Multiple Image Links
- **Multiple Images**: To create a slider, add multiple Image Links within the same "Interactive Image Link" collection. Each image will be part of the slider, allowing users to swipe through them.

### Step 4: Customize the Layout
- **Image Slider**: Ensure images are optimized for slider display. Callouts will appear on the top left of each image.

### Note on Screen Sizes
- **Responsive Design**: Depending on the screen size, the images may render as a slider or a grid. To ensure optimal display, use either 2 or 4 images in the collection.

--- 

## Conclusion
Featured Image Links provide a dynamic way to enhance mobile navigation with a top-level image slider. Ensure all fields are filled out correctly to maintain consistency and functionality. 