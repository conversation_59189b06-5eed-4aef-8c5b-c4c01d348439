# Caraway x Meridian

![Meridian Logo](/meridian-logo.png)

<p align="center">Meridian is <a href="https://carawayhome.com/">Caraway Home's</a> headless commerce store. It comes fully equipped with Shopify's Storefront and Hydrogen stack. It also  includes tools, utilities, and a best-in-class approach for building dynamic and performant commerce applications with Next.js 14.</p>

# 🛍 Meridian

A Next.js 14 and App Router-ready ecommerce standalone featuring:
- [x] ⚡ [Next.js 14](https://nextjs.org) with App Router support
- [x] ⚛️ [React 18](https://react.dev) for React Server Components (RSCs) and Suspense
- [x] 💻 [Node.js 20](https://nodejs.org/en/download) for enhanced performance and security
- [x] 🛍️ [Shopify Hydrogen React](https://shopify.dev/docs/api/hydrogen-react)
  - [ ] ℹ️ Shopify Analytics
  - [x] 🏗️ Shopify Hydrogen Components
- [x] 🛍️ [Shopify Storefront GraphQL API](https://shopify.dev/docs/api/storefront)
- [x] ⛑ [TypeScript](https://www.typescriptlang.org/) to Safely Create React Hooks, and Components and ensure Type Checking
- [x] 💖 Styling with [StyleX](https://stylexjs.com/) to build a scalable design system with tokens
- [x] 📏 Using [ESLint](https://eslint.org/) to Follow Next.js Best Practices
- [ ] 🏃 Edge Runtime
- [x] 🛒 Checkout and payments with Shopify
- [ ] 🌓 Automatic light/dark mode based on system settings
- [ ] 🌎 Multi-language (i18n)
- [x] ⏲️ Real-time inventory checks
- [ ] 👀 Live Preview for realtime collaboration
- [ ] 🗺️  Sitemap.xml and robots.txt
- [x] 🏪 Redux Toolkit for streamlined state management
- [ ] 🏪 RTK Query for streamlined data management
  - [ ] 👮 ZOD or Valibot for data Type Safety
- [ ] 🤖 Optimized SEO metadata, JSON-LD and Open Graph tags
- [ ] ♿ A11y first design
- [ ] 🦺 Unit Testing
- [ ] 🚨 Error Monitoring with [Sentry](https://sentry.io)
- [x] 💯 Track and monitor lighthouse scores with [Vercel Speed Insights](https://github.com/vercel/speed-insights)
- [x] 📊 [Vercel Analytics](https://vercel.com/analytics)
- [x] ☕ Minify HTML & CSS
- [x] 🧼 ESLint Github Action to sanitize code. Using Airbnb TypeScript Config.
- [x] 🐾 Husky to enable comment linting automation
- [x] 🧽 Automated commit linting with [Commit Lint](https://github.com/conventional-changelog/commitlint) & pre-commit messages with [Commitizen](https://github.com/commitizen/cz-cli)
  - [x] Adopted [conventional commits](https://www.conventionalcommits.org/en/v1.0.0/)
- [x] ⏫ Update dependencies with Dependabot
- [x] 🆕 Automated releases with [Release Please](https://github.com/googleapis/release-please) or [Semantic Release](https://semantic-release.gitbook.io/semantic-release/)
- [x] 💨 Live reload
- [ ] ✅ Cache busting

## Hydrogen
📚 [Docs](https://shopify.dev/custom-storefronts/hydrogen) | 🗣 [Discord](https://discord.gg/shopifydevs) | 💬 [Discussions](https://github.com/Shopify/hydrogen/discussions) | 📝 [Changelog](./packages/hydrogen/CHANGELOG.md)
