## How to Contribute

Please follow the guidelines below to ensure a smooth process.

### 1. Use Shortcut.com for Ticket Management

We manage our tasks using [Shortcut.com](https://shortcut.com/), our project management tool. Before starting any work, please ensure that you have an assigned ticket in Shortcut that outlines the task. If a ticket doesn't exist, request one to be created.

### 2. Creating a Feature Branch

When working on a new feature or bug fix, it's essential to create a feature branch. This helps keep your work organized and ensures that the main branch remains stable.

#### Steps to Create a Feature Branch:

1. **Checkout the main branch:**
   ```bash
   git checkout main
   ```

2. **Pull the latest changes:**
   ```bash
   git pull origin main
   ```

3. **Create a new branch:**
   Use a descriptive name for your branch, usually following the format `feature/sc-1234/your-feature-name` or `bugfix/sc-1234/issue-description`.
   Shortcut Ticket ID is auto-generated from the ticket.

   ```bash
   git checkout -b feature/sc-1234/your-feature-name
   ```

For more detailed instructions on using branches and pull requests with the Shortcut VCS integrations, you can refer to [this guide](https://help.shortcut.com/hc/en-us/articles/207540323-Using-Branches-and-Pull-Requests-with-the-Shortcut-VCS-Integrations).


### 3. Follow Coding Standards

1. When contributing code, please adhere to the following standards:

- **Code Style**: Follow the existing code style. We use [ESLint](https://eslint.org/) to maintain consistency.
- **Commit Messages**: Use [Commitizen](https://commitizen-tools.github.io/commitizen/) rules for commit messages. See our [Commit Guidelines](./commit-guidelines.mdx) for details.

2. **Commit your changes:**
   As you make progress, commit your changes with meaningful messages. Ensure you follow the commit message guidelines outlined in our [Commit Guidelines](#commit-guidelines).
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

### 4. Submit a Pull Request

 **Create a Pull Request:**
   Once your work is complete and ready for review, open a Pull Request (PR) against the main branch. Be sure to link the relevant Shortcut ticket in your PR description and request a review from your team.

 **Push your branch to the repository:**
   ```bash
   git push origin feature/1234/your-feature-name
   ```

### 5. Review and Feedback

After submitting your PR, it will be reviewed by the team. Please be responsive to any feedback and make necessary adjustments. 2 approvals are needed before merging.

### 6. Code of Conduct

We strive to maintain a respectful and welcoming environment for all contributors. Happy Coding 👾!

---

Thank you for contributing!
