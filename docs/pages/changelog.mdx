# Changelog

## [0.38.0](https://github.com/carawayhome/caraway-meridian/compare/v0.37.0...v0.38.0) (2024-10-25)


### Features

* **component:** connect Timeline Component to Contentful ([4bc125c](https://github.com/carawayhome/caraway-meridian/commit/4bc125c3f5c43c8fa68450ac35b63c06cc222848))
* mobile typography ([0d6d052](https://github.com/carawayhome/caraway-meridian/commit/0d6d0526e87164388c520556920e9edfa9b9c43c))
* **trade program page:** components cta larger prop ([72f0c4c](https://github.com/carawayhome/caraway-meridian/commit/72f0c4c0c717305e1eb75814d032a1d09377357d))
* use product default swatch if no variant cards added ([fb68a62](https://github.com/carawayhome/caraway-meridian/commit/fb68a62a19a23b5e23a51679f7ec6e98a0cef437))


### Bug Fixes

* allow default swatch on a product card without any variant cards: ([f1a4367](https://github.com/carawayhome/caraway-meridian/commit/f1a43673845dd67b99d1f98187efd1a6b8ac99a7))
* handle product cards and use default swatch only for product based cards ([4cc2a11](https://github.com/carawayhome/caraway-meridian/commit/4cc2a11beff4100d8084067fcd2f61d98fc8b025))
* **MediaContainer:** flying icon ([2280981](https://github.com/carawayhome/caraway-meridian/commit/22809818eda4f3b14fde61eb4b89621017364937))
* missing ID issue ([539806d](https://github.com/carawayhome/caraway-meridian/commit/539806d131db868a8b7fd339feb3c89fb5aeeedf))
* **Trade Program Page:** deploy ([1de9cd9](https://github.com/carawayhome/caraway-meridian/commit/1de9cd96fefd239c5ce6d0f4d22ba586bafb149d))
* **Trade Program Page:** lint ([07a6653](https://github.com/carawayhome/caraway-meridian/commit/07a66530eba63374f095578ac322c58f4e63c354))
* **Trade Program Page:** merge ([1553254](https://github.com/carawayhome/caraway-meridian/commit/1553254f57ac77a6c559420c30c459c3c992e530))
* **Trade Program Page:** miss match hydratation ([451caec](https://github.com/carawayhome/caraway-meridian/commit/451caec8b42c0fbade3269f2251f5f1ba04aa13c))
* **Trade Program:** mergfe ([48b7b39](https://github.com/carawayhome/caraway-meridian/commit/48b7b3943d49b24a9e101c5b9e131acedd5d5791))
* use product metadata hover image if product card has none ([43cd45e](https://github.com/carawayhome/caraway-meridian/commit/43cd45ec8d11c0e9b668d12dd5ff1ebb2b8de659))
* various ui updates ([fc17fbf](https://github.com/carawayhome/caraway-meridian/commit/fc17fbfe4c03502555c7401d852b0ac5264580c5))

## [0.37.0](https://github.com/carawayhome/caraway-meridian/compare/v0.36.0...v0.37.0) (2024-10-24)


### Features

* add support for holiday feature flag ([a96e590](https://github.com/carawayhome/caraway-meridian/commit/a96e59038da3111eee31525eee64c1b1ccab21f6))
* add support for the anchor navigation to support the header and subheader ([bfd74fc](https://github.com/carawayhome/caraway-meridian/commit/bfd74fc2856c711f7b2b9f7bc17d229af52bfe29))
* **progress:** force build ([3504875](https://github.com/carawayhome/caraway-meridian/commit/3504875d88c5138861beea0c44c39396f0869f31))
* **progress:** qa addressing teos comments ([a68234f](https://github.com/carawayhome/caraway-meridian/commit/a68234fa5bf5dace58045d5385dc2041ca537125))
* **progress:** qa build error ([8f06f09](https://github.com/carawayhome/caraway-meridian/commit/8f06f09287d23b903f265390cfc0a786a2a05773))
* **progress:** removing unnecessary assets ([bff97ec](https://github.com/carawayhome/caraway-meridian/commit/bff97ecd47d50bd8ac4e75e29919c9f85e5602d5))
* **progress:** slight style adjustment to match design ([d40a51d](https://github.com/carawayhome/caraway-meridian/commit/d40a51dcb33aa5a7b25432d5b7a5e864b1674f08))
* **progress:** updating to use container and update its type ([a030fe7](https://github.com/carawayhome/caraway-meridian/commit/a030fe7a4e16546cbe0bbd424b939644aac1316c))
* update buttons in PageCTA and TextBanner components, plus query ([7a7075b](https://github.com/carawayhome/caraway-meridian/commit/7a7075be6f37981e49daf50b44e878f1f6bca95d))


### Bug Fixes

* add support for swatches on a PLP ([62770e8](https://github.com/carawayhome/caraway-meridian/commit/62770e8e8e2b21a5e68ec1b658f5f2d02e25d6e1))
* missing footer content ([56b6311](https://github.com/carawayhome/caraway-meridian/commit/56b63112972044140c16773ebb77104c52f91909))
* use themeTokens instead of custom style ([2249f93](https://github.com/carawayhome/caraway-meridian/commit/2249f9364d2b70943a317acf6e735b2c3480a252))
* use Typography props instead of fontFamily ([9f7fca9](https://github.com/carawayhome/caraway-meridian/commit/9f7fca9fc224daeea562116db64f960c98281532))

## [0.36.0](https://github.com/carawayhome/caraway-meridian/compare/v0.35.0...v0.36.0) (2024-10-23)


### Features

* add dialog triggers and connect to PageCTA ([1e0ed91](https://github.com/carawayhome/caraway-meridian/commit/1e0ed9145ca59bf7be804b822e33fde0127e32e3))
* add height to Promobar Message component ([b94dbdd](https://github.com/carawayhome/caraway-meridian/commit/b94dbddc887072d38259a9f4ea334281d597e64e))
* add support for dialogtriggers on page hero ([75fbaaa](https://github.com/carawayhome/caraway-meridian/commit/75fbaaa681d1260629c43ec9b9579b683ca2b024))
* default swatch product metadata ([4e2385e](https://github.com/carawayhome/caraway-meridian/commit/4e2385eaaeb5811de7f883ca50005a6272b0a5b0))
* dynamic aggregated reviews messaging ([6a84cd4](https://github.com/carawayhome/caraway-meridian/commit/6a84cd47f6120b7738724433eed7a962db777f9b))
* **Hero:** add layout highlight ([1e448ae](https://github.com/carawayhome/caraway-meridian/commit/1e448ae4cc1f6521946eae1af6eb37597b171a4d))
* **Hero:** improve docw ([b2e55ea](https://github.com/carawayhome/caraway-meridian/commit/b2e55eae2b26432c123124b0091a764067715de2))
* **ScrollFeatureSlider:** connect with contentful ([37cd61e](https://github.com/carawayhome/caraway-meridian/commit/37cd61e468824678487c6eaed1b05edfd2c685c2))
* **ThreeBloksModules:** refactor and connect with cms ([08f2bc0](https://github.com/carawayhome/caraway-meridian/commit/08f2bc07c9bc4d5729eb93894351c3d94aca1256))


### Bug Fixes

* add scroll=false prop to ProductLink component ([4745ee3](https://github.com/carawayhome/caraway-meridian/commit/4745ee33fb55cf00a5ffa038682a2d4d434267e8))
* add slugify function to dialog theme in fetchGlobalDialogs.ts ([6b27716](https://github.com/carawayhome/caraway-meridian/commit/6b2771623b0e171438654d866fc6c49afd3b0fb2))
* add support for gift card swatches ([f9720ac](https://github.com/carawayhome/caraway-meridian/commit/f9720ac8b189afbe60e420021e8f18c1336cc5ab))
* adjust table width in HowWeCompare component ([f7efdcd](https://github.com/carawayhome/caraway-meridian/commit/f7efdcd329b92b7e30fce92a88fc484369c6922b))
* bring back missing product metadata query ([3864fee](https://github.com/carawayhome/caraway-meridian/commit/3864fee315ea11f12ccd6eecb3e16460f5d15783))
* media container fixed ([f411aef](https://github.com/carawayhome/caraway-meridian/commit/f411aef198dbaf9734e784da4249a17d4c4f7cee))
* promobar when mobile or desktop is empty ([d6c5688](https://github.com/carawayhome/caraway-meridian/commit/d6c5688e3bd6dfc6c4fc8eba07f335158d68dcf6))
* remove flexWrap (not a prop) and add missing alt ([3ac2aac](https://github.com/carawayhome/caraway-meridian/commit/3ac2aac99775beda5eb1f1a22184cfa2e3592cf8))
* table shadow ([b911648](https://github.com/carawayhome/caraway-meridian/commit/b911648a090e3dbcb8edee4c904d76b3d7a74a20))

## [0.35.0](https://github.com/carawayhome/caraway-meridian/compare/v0.34.0...v0.35.0) (2024-10-19)


### Features

* comparison chart for catalog ([0c9d01a](https://github.com/carawayhome/caraway-meridian/commit/0c9d01ad24ecfbb2cd45f220c79ea84d6d31cd39))
* connect anchor tag links and anchors on grid ([c313984](https://github.com/carawayhome/caraway-meridian/commit/c3139848998e486899523507012d287c1bdf3761))


### Bug Fixes

* compare on mobile, nav shadow, anchor on mobile [sc-15063] ([8c3a8e6](https://github.com/carawayhome/caraway-meridian/commit/8c3a8e6c6ee041349347b72a8a2c16f1287b2c98))
* key on acnhors ([027c22c](https://github.com/carawayhome/caraway-meridian/commit/027c22cac821ed84b9e4cb774396dd54f6fd4495))
* optimize rendering of swatch collections in ProductSwatches component ([5a99710](https://github.com/carawayhome/caraway-meridian/commit/5a99710bfec7e7dfa5b7aec2447af2ff0ef3d9ad))

## [0.34.0](https://github.com/carawayhome/caraway-meridian/compare/v0.33.0...v0.34.0) (2024-10-18)


### Features

* **components:** update merge conflicts ([72d41bc](https://github.com/carawayhome/caraway-meridian/commit/72d41bc39f6cbf93a985cf6713e704e5f3accd81))
* **components:** update PR Review ([27fdd99](https://github.com/carawayhome/caraway-meridian/commit/27fdd99f32ab02d47aba2ab5b04a83ff0479f209))
* **component:** update AtomicLevelTooltip ([bb48941](https://github.com/carawayhome/caraway-meridian/commit/bb48941f77e813a23b88d439f745a7199bfff86b))
* **component:** update block Extractor ([77c53b4](https://github.com/carawayhome/caraway-meridian/commit/77c53b42eb5eb36c10f3406815ebecf0b85f5067))
* **component:** update FeatureSlider ([5973968](https://github.com/carawayhome/caraway-meridian/commit/59739688c62505b7ab757989b515ef7d45e4891d))
* **component:** update TextBanner ([6a5fc6c](https://github.com/carawayhome/caraway-meridian/commit/6a5fc6c47548fe186b212aa97b9a11ad5ed733cd))
* **component:** update ZPattern ([74920eb](https://github.com/carawayhome/caraway-meridian/commit/74920eba140633c80e3a7660f43c0092c7670e5d))
* klaviyo form connect Klaviyo Form x Connect [sc-15053] ([21775c8](https://github.com/carawayhome/caraway-meridian/commit/21775c8e68fe3878b63613970c01661d9094fef6))
* merge main ([af0f76d](https://github.com/carawayhome/caraway-meridian/commit/af0f76de8b5c26553d91cf3a5b2dfb49ebc45e99))
* merge main in, resolve conflict, improve toggles ([7b42dbe](https://github.com/carawayhome/caraway-meridian/commit/7b42dbeac6e607e04076d820fe2f763720c2b323))
* use product title instead of name ([c706e26](https://github.com/carawayhome/caraway-meridian/commit/c706e26fd2775f0eab6f0251ec86eb33bdc79332))


### Bug Fixes

* content assignment in Feature component ([cab6772](https://github.com/carawayhome/caraway-meridian/commit/cab6772571de79364e62344f2551072e1c995f98))
* **updates:** main merge conflicts ([54e01bb](https://github.com/carawayhome/caraway-meridian/commit/54e01bbed29bac086202f20fee67a00b9a427f42))

## [0.33.0](https://github.com/carawayhome/caraway-meridian/compare/v0.32.0...v0.33.0) (2024-10-17)


### Features

* add Microsoft Clarity [sc-15019] ([7d065c5](https://github.com/carawayhome/caraway-meridian/commit/7d065c592fffa84454aa89bec2e324174a9e676d))
* add mobile dot buttons controls ([3e063e4](https://github.com/carawayhome/caraway-meridian/commit/3e063e4960bdbb2e2611397e27027faec2472242))
* add ParcelLab [sc-12891] ([6c86fcf](https://github.com/carawayhome/caraway-meridian/commit/6c86fcfc07f506f93d3834d06e888f4f09796a20))
* add ParcelLabWidget to AppsWidgets for Contentful ([d51bc36](https://github.com/carawayhome/caraway-meridian/commit/d51bc36f704885bb6d46518deeffdfe067154793))
* add Roster app ([d72eac1](https://github.com/carawayhome/caraway-meridian/commit/d72eac108cbc1679a309cf415bdb20e4513ac37e))
* adding about us dropdown specific styles ([35cba58](https://github.com/carawayhome/caraway-meridian/commit/35cba58734370aad74c452b4bc40e12f48d45232))
* bring back the gap between gallery and details ([51e24de](https://github.com/carawayhome/caraway-meridian/commit/51e24de5d9c47233c8ccfaf6a7683695dbf9c0e2))
* **component:** connect Trade Component to Contentful ([f6f2f18](https://github.com/carawayhome/caraway-meridian/commit/f6f2f182a61c27dd768cedd2dbe9f4cb8a346d40))
* **component:** update extract logic ([8dc94fd](https://github.com/carawayhome/caraway-meridian/commit/8dc94fd65108c5a437663b163888b66671024cfe))
* **component:** update to use Blog Article ([5a2b716](https://github.com/carawayhome/caraway-meridian/commit/5a2b716c916adf7d03756bbb612cb03feea82fd0))
* resolve conflicts ([2badfc3](https://github.com/carawayhome/caraway-meridian/commit/2badfc3577ff4a3d982f353a1aac655e17da4b3c))


### Bug Fixes

* css issue on render ([1cbc849](https://github.com/carawayhome/caraway-meridian/commit/1cbc849a5822fc93d28eddadf936b80073367965))
* **plp:** build ([1dc69c5](https://github.com/carawayhome/caraway-meridian/commit/1dc69c51e703478d06644f83dacfe2f5d69f8482))
* **plp:** use same generateCallToActionProps as main branch ([737ec9c](https://github.com/carawayhome/caraway-meridian/commit/737ec9c676d20476c8fd9a14e61177b18b7fe619))
* remove duplicate icon ([01cf64c](https://github.com/carawayhome/caraway-meridian/commit/01cf64cf297934d75941f1088f6d7a71e1cf5d5b))

## [0.32.0](https://github.com/carawayhome/caraway-meridian/compare/v0.31.0...v0.32.0) (2024-10-16)


### Features

* add reviews via contentful ([91d5377](https://github.com/carawayhome/caraway-meridian/commit/91d53772965c265763dbffd24da6edd82a1b818b))
* **component:** connect Anchor Navigation to Contentful ([ebf1406](https://github.com/carawayhome/caraway-meridian/commit/ebf140673e4845e6b459c15374f387a7b28070f4))
* **component:** last details ([75618bd](https://github.com/carawayhome/caraway-meridian/commit/75618bdb4a7fa39247c714a46dc6560f5455f066))
* **robots:** update site url on robots ([6a1b6c6](https://github.com/carawayhome/caraway-meridian/commit/6a1b6c6d562cd7c8d2bf8322528ffff607045c02))
* **sitemaps:** add blog and collections ([4d983a3](https://github.com/carawayhome/caraway-meridian/commit/4d983a3aeddf6c497a181f7a6c09a2af57c3a65f))
* **sitemaps:** base file ([e1e1248](https://github.com/carawayhome/caraway-meridian/commit/e1e12484e3842d2d6bfd67bfb28f0366ab57c7cf))


### Bug Fixes

* button stylings on ATC ([de0c854](https://github.com/carawayhome/caraway-meridian/commit/de0c854400b1ebb4474436bf0e4a804f0c2b5b65))
* footer logo overflow ([c3a1852](https://github.com/carawayhome/caraway-meridian/commit/c3a1852728ed326be17cf4800c824fa93ccee2dd))
* **Press:** overflow issue ([10ecd82](https://github.com/carawayhome/caraway-meridian/commit/10ecd82a39b5e44bb3257d6673590a75f45719f9))
* **queries.ts:** query update ([97bd772](https://github.com/carawayhome/caraway-meridian/commit/97bd772fbfa80f482c4c0c0abfd72442c7797ce5))
* **queries.ts:** update graphql query ([e1e7540](https://github.com/carawayhome/caraway-meridian/commit/e1e7540f226e23747b8f9e51e6b1dda58b322c10))
* **sitemaps:** wrong url ([6300779](https://github.com/carawayhome/caraway-meridian/commit/6300779e72f51418335e7e35e39743dee33852cc))
* theme order so that hover surface on view full cart is correctly applied ([f180dfd](https://github.com/carawayhome/caraway-meridian/commit/f180dfdd237618b395e0577db55ae58ffba3e439))

## [0.31.0](https://github.com/carawayhome/caraway-meridian/compare/v0.30.0...v0.31.0) (2024-10-11)


### Features

* **Article Schema:** implement article json ld ([e25ca29](https://github.com/carawayhome/caraway-meridian/commit/e25ca292f2596639801bf4a31943be4515b63923))
* fix Atomic Tooltip ([3b1cbe4](https://github.com/carawayhome/caraway-meridian/commit/3b1cbe49303b7650532393ca1f7afc5621415683))
* fix git comments ([48bdd9a](https://github.com/carawayhome/caraway-meridian/commit/48bdd9aeee778bfb77e7f190787a275feb42694c))
* force build ([372b70d](https://github.com/carawayhome/caraway-meridian/commit/372b70d33d64c3de834fc10ac6d5a9678075c340))
* new PLP cards setup ([ea217cc](https://github.com/carawayhome/caraway-meridian/commit/ea217cca31826251d4f910f211e906c8f2ed62cd))
* **Page MEta:** add keywords and author ([4d46e34](https://github.com/carawayhome/caraway-meridian/commit/4d46e3498f7b66bb06ddae5f0fdf60eb67959d0f))
* **Page Metadata:** add meta for page, blog article and product ([185f1cc](https://github.com/carawayhome/caraway-meridian/commit/185f1cc606210753022556659eda59bcbeaa97bb))
* **Page Schema:** add social linkls ([69d1f09](https://github.com/carawayhome/caraway-meridian/commit/69d1f099724bb76d23a79d73e20f6de1043e46a7))
* render dynamically pages with nested slugs ([17a5494](https://github.com/carawayhome/caraway-meridian/commit/17a54945324d105b0c1a0c480e4e9ad0f2417e2a))
* **Seo Meta:** add manifest ([87b08c9](https://github.com/carawayhome/caraway-meridian/commit/87b08c9a674f6332e2d68a56dfba451e1d8bba07))
* statically generate all pages with nested slugs ([4ab9f96](https://github.com/carawayhome/caraway-meridian/commit/4ab9f96b4439794663ab8f7581db116402890a9b))
* use document-plain-text [sc-14863] ([5c71dfb](https://github.com/carawayhome/caraway-meridian/commit/5c71dfb6890b60b9c1c0fd995a0db587945a6a2a))


### Bug Fixes

* **Article Schema:** build ([1eb787b](https://github.com/carawayhome/caraway-meridian/commit/1eb787b715f267d02f25a7d82fb1875def9df0a9))
* missing image ([7c6b461](https://github.com/carawayhome/caraway-meridian/commit/7c6b461493215d56b8dadf1918aad876d2c25558))
* **Page Schema:** conflicts ([24dd44f](https://github.com/carawayhome/caraway-meridian/commit/24dd44f8dee4cc82be47fac90bb3cd3d72284743))
* press rendering update ([a775254](https://github.com/carawayhome/caraway-meridian/commit/a7752542352119a2bf3d1a4316548c25eddfeabe))
* product card CTA styles ([0cce075](https://github.com/carawayhome/caraway-meridian/commit/0cce0759bea44f053628590aa6d41ce7ac0b7dd1))
* remove unnecessary collection slug ([e6494d5](https://github.com/carawayhome/caraway-meridian/commit/e6494d5d121f19c8d749cfe43662c7010ab68f65))
* update slide when null ([b852d98](https://github.com/carawayhome/caraway-meridian/commit/b852d98cbe115ebb3ca2b351876aaad9bc59e332))

## [0.30.0](https://github.com/carawayhome/caraway-meridian/compare/v0.29.1...v0.30.0) (2024-10-09)


### Features

* add countdown timer to Promobar component ([99b65b0](https://github.com/carawayhome/caraway-meridian/commit/99b65b0c9148fb442943449b84515987a988ce2d))


### Bug Fixes

* update CallToAction variant in CartHeader component, sizes in promobar ([83edd08](https://github.com/carawayhome/caraway-meridian/commit/83edd08164b96ab80e82aba0c8dcf249352f89fc))

## [0.29.1](https://github.com/carawayhome/caraway-meridian/compare/v0.29.0...v0.29.1) (2024-10-08)


### Bug Fixes

* **component:** mobile testimonials bug ([8e56fbc](https://github.com/carawayhome/caraway-meridian/commit/8e56fbc8771e743a85a3c078cd99fc57f817e644))
* **component:** slide mobile count update ([08d5175](https://github.com/carawayhome/caraway-meridian/commit/08d5175c517f007ee3d084571f837ca4ccf468df))

## [0.29.0](https://github.com/carawayhome/caraway-meridian/compare/v0.28.0...v0.29.0) (2024-10-05)


### Features

* add offwhite ([1baf4a0](https://github.com/carawayhome/caraway-meridian/commit/1baf4a0590414f4ed1566012aed448993af69628))


### Bug Fixes

* handle null subtype in extractPageSections function ([901dcd7](https://github.com/carawayhome/caraway-meridian/commit/901dcd7433088a2944b2e654a1f819cf1a0d8f58))

## [0.28.0](https://github.com/carawayhome/caraway-meridian/compare/v0.27.0...v0.28.0) (2024-10-03)


### Features

* add red700 theme color and update ProductPrice component ([f1c6b26](https://github.com/carawayhome/caraway-meridian/commit/f1c6b260dc3a0c4f83dd66b7474b504d4824fc56))
* adding interactive text link feature ([8f2fefd](https://github.com/carawayhome/caraway-meridian/commit/8f2fefd99a56e129280cfe1fb169ed3bb9a17e15))
* adding new layout component ([6eee5d7](https://github.com/carawayhome/caraway-meridian/commit/6eee5d732b2c09ef70304958177985cfcee467cd))
* cleaning up offset styles ([7b6d8de](https://github.com/carawayhome/caraway-meridian/commit/7b6d8decb03b09af1634992e4e3ac634c0098e86))
* dynamic pricing on callouts enabled ([11955b7](https://github.com/carawayhome/caraway-meridian/commit/11955b756327c19401dbb0c7cbb44de4ad90571b))


### Bug Fixes

* add key ([3b39c26](https://github.com/carawayhome/caraway-meridian/commit/3b39c26c539b8b0b44dee1a9a8026a107ae9fd49))
* add title field to BlockContent in fetchPages.tsx and fetchProducts.tsx ([545e993](https://github.com/carawayhome/caraway-meridian/commit/545e99316abc8a456bf9375ebfbec214a50a0269))
* callouts on nav links and the dots ([091def0](https://github.com/carawayhome/caraway-meridian/commit/091def0fafa20b49c5351c9f58dbeaccad6ac310))
* zpattern bugs causing fails ([8e93b07](https://github.com/carawayhome/caraway-meridian/commit/8e93b076a1f6c87f5f37f7c8d0cd2d113f73af11))

## [0.27.0](https://github.com/carawayhome/caraway-meridian/compare/v0.26.1...v0.27.0) (2024-10-02)


### Features

* **zpattern:** use media container for assets ([30ee988](https://github.com/carawayhome/caraway-meridian/commit/30ee98879668d53b747f6f6ccac07f19f0717725))


### Bug Fixes

* keys in nav ([4b1abf4](https://github.com/carawayhome/caraway-meridian/commit/4b1abf4cb0a61e255ee11da303c9718c76d6d6ed))
* **redirect:** wrapper ([1a98eef](https://github.com/carawayhome/caraway-meridian/commit/1a98eef04309f8c56ab155a04ece215e14e16cbc))
* **zpattern:** mobile spacing ([b27eeaa](https://github.com/carawayhome/caraway-meridian/commit/b27eeaa66c21128e34defb5231de5895da7d9406))

## [0.26.1](https://github.com/carawayhome/caraway-meridian/compare/v0.26.0...v0.26.1) (2024-10-01)


### Bug Fixes

* various issues with data fetching ([abf630a](https://github.com/carawayhome/caraway-meridian/commit/abf630ac1e1b4c199b9d53e8eda33b23ded02169))


### Reverts

* json ([f869d8e](https://github.com/carawayhome/caraway-meridian/commit/f869d8ee6cd363b6babf4380277a74a0541acc54))

## [0.26.0](https://github.com/carawayhome/caraway-meridian/compare/v0.25.1...v0.26.0) (2024-10-01)


### Features

* accept discount codes, use cookies and remove after application ([9cdf1af](https://github.com/carawayhome/caraway-meridian/commit/9cdf1afb2a7d3948211192cf0a04afa012718000))
* add discount applicator ([31c06a8](https://github.com/carawayhome/caraway-meridian/commit/31c06a8e86516b521dc2a17b9837ec80e7428149))
* add new event tracking for events such as variant selection, email capture, and clicks ([4b336a9](https://github.com/carawayhome/caraway-meridian/commit/4b336a9d75b5756ec079ea5a7c15828e172b38ca))
* add redirects [sc-14174] ([9954698](https://github.com/carawayhome/caraway-meridian/commit/99546987d17a470dd2782645b0f5aae3a432b38c))
* add Scripts component ([ade3274](https://github.com/carawayhome/caraway-meridian/commit/ade32747dfb6c0fae3dea66f4da78464621418ac))
* add support for collections ([7dcef25](https://github.com/carawayhome/caraway-meridian/commit/7dcef25ccb16993a65015e04cd65ab982aaf9256))
* added klaviyo ([16e4e6e](https://github.com/carawayhome/caraway-meridian/commit/16e4e6e9994a16a74d35d4e6e9c8a3f85365abd7))
* new listeners for dispatching events ([304bce2](https://github.com/carawayhome/caraway-meridian/commit/304bce2cdc212119f3c2cb23d83c158b8f975a96))
* product details coming from contentful (re-order available). Also SEO title injected ([6f14257](https://github.com/carawayhome/caraway-meridian/commit/6f14257bce9334365d8d7d2cfbb2d5f214bc77a7))
* update redirects to support discounts and full cookies ([4068e31](https://github.com/carawayhome/caraway-meridian/commit/4068e31e10069b1b36552bd68243d150a209f1d5))


### Bug Fixes

* add key prop to list items in SaveWithSet component ([17a0a84](https://github.com/carawayhome/caraway-meridian/commit/17a0a84035faf95a1ed1e0ec4d4194689aa3fb8f))
* correct props ([d1c4a6b](https://github.com/carawayhome/caraway-meridian/commit/d1c4a6beb6559e740b300d6c204a0f12a7289033))
* discount logic ([40789b0](https://github.com/carawayhome/caraway-meridian/commit/40789b0ea81a42e81acdfeed4fac87b7b3396aa5))
* issues with extractors and types ([c1d784a](https://github.com/carawayhome/caraway-meridian/commit/c1d784ae01da73f60e3bbf4019d3033fad15147b))
* missing id ([be99058](https://github.com/carawayhome/caraway-meridian/commit/be99058b5975d85b64fb874b82399a2de420d2a5))
* server issues ([b5a9636](https://github.com/carawayhome/caraway-meridian/commit/b5a9636e020b668205cdbcd1d87bcabece165a9b))
* slug for params ([814c039](https://github.com/carawayhome/caraway-meridian/commit/814c039cf49c01f647f9c211b9c6b997b3bbc49b))
* ts and ui css issues ([a46b617](https://github.com/carawayhome/caraway-meridian/commit/a46b6176590eabc838e6c7f4a5974fecf04394b7))
* ts issues ([4d7549c](https://github.com/carawayhome/caraway-meridian/commit/4d7549c2a279b809398a8fc4aee9949f7c27b119))
* ts issues on new fetch updates ([574054e](https://github.com/carawayhome/caraway-meridian/commit/574054ed98299ee12066444ad40737dabeb0a05d))
* typescript props ([2f7cc24](https://github.com/carawayhome/caraway-meridian/commit/2f7cc24b5a9f15fa0726c1daa2a59780a6c838b4))

## [0.25.1](https://github.com/carawayhome/caraway-meridian/compare/v0.25.0...v0.25.1) (2024-09-24)


### Bug Fixes

* update imports ([6a21b4d](https://github.com/carawayhome/caraway-meridian/commit/6a21b4d2e09747a777c462ea6888b68a09e31575))

## [0.25.0](https://github.com/carawayhome/caraway-meridian/compare/v0.24.0...v0.25.0) (2024-09-24)


### Features

* **BlogArticle:** markdown working ([df2b98d](https://github.com/carawayhome/caraway-meridian/commit/df2b98d598d447c5e4b439ea5c06e29617985d0c))
* **BlogArticle:** Rich Text Test Rendering ([30b8901](https://github.com/carawayhome/caraway-meridian/commit/30b8901df2378583d7f925b1f00efbf8b3b634f5))
* **BlogArticle:** update fetchBlogs to be static ([c3ca98a](https://github.com/carawayhome/caraway-meridian/commit/c3ca98a54bd57a62ce057e7b1b869d4262eb509f))
* **component:** pR Comments ([5209785](https://github.com/carawayhome/caraway-meridian/commit/5209785a668ac88325d92346081faa79150ad833))
* **component:** update ([447cea0](https://github.com/carawayhome/caraway-meridian/commit/447cea0cfc4cfb182ac703044b52ce65490fb1de))
* **component:** vercel Teo Updates ([7c3662c](https://github.com/carawayhome/caraway-meridian/commit/7c3662ce55bbff14b873f358799e5e83e5d503ed))
* merge branch 'main' into feature/sc-13189/informational-text-component-migration ([58b52ce](https://github.com/carawayhome/caraway-meridian/commit/58b52ce4459e77576f3728eec54e587f40bafa78))
* new redux accordion ([6200829](https://github.com/carawayhome/caraway-meridian/commit/6200829e753b3992fbbde21e858c8ec51ef34826))
* remove images and use css to plus and minus ([ed77a0d](https://github.com/carawayhome/caraway-meridian/commit/ed77a0d7af07de63b4918979fc4f2886a5129db3))
* remove wrapper ([908b3d4](https://github.com/carawayhome/caraway-meridian/commit/908b3d4604468218ca0c0a4b14481031aaacc86f))
* remove wrong headers styles ([9928a00](https://github.com/carawayhome/caraway-meridian/commit/9928a000951cb7a50eb110825aa218bf43c3b846))
* **Typograph:** add rest prop ([bdb267a](https://github.com/carawayhome/caraway-meridian/commit/bdb267accd0778d919a260d10310285f0295f403))


### Bug Fixes

* **BlogArticle:** type and lint ([2416287](https://github.com/carawayhome/caraway-meridian/commit/2416287e037c5124c86872a7397f5f7091242524))
* **BlogArticle:** typescript issues ([5379e59](https://github.com/carawayhome/caraway-meridian/commit/5379e5967ea00f4f02b5befc3000086a45c6ab3a))
* cart stylings and spacing ([67548f7](https://github.com/carawayhome/caraway-meridian/commit/67548f777d3d0841beb4aa85c5309f8190209adc))
* css issue on popover ([fbef466](https://github.com/carawayhome/caraway-meridian/commit/fbef466a3095d511124d8f06cffeea1f79c09790))
* theme issue with colors ([0011662](https://github.com/carawayhome/caraway-meridian/commit/001166289f8657be179c587e65d966f558bddd58))

## [0.24.0](https://github.com/carawayhome/caraway-meridian/compare/v0.23.0...v0.24.0) (2024-09-17)


### Features

* add typographyTheme to themeTokens.stylex.ts and typographyThemes.stylex.ts ([5b516e8](https://github.com/carawayhome/caraway-meridian/commit/5b516e8bd0bf13771bfe3865575de69165824c0c))
* **component:** feature component with multiple layouts ([d049a15](https://github.com/carawayhome/caraway-meridian/commit/d049a158df5b5f7ef13801fb15f5195c87b42e0f))
* **doc:** docs ([347469f](https://github.com/carawayhome/caraway-meridian/commit/347469fcb066f98499563efd8cc0f777c4658f4c))
* force push ([353b2c4](https://github.com/carawayhome/caraway-meridian/commit/353b2c4aa482f484bcb8f54ac0c29fcc55656d7e))
* **layout:** add dialog component ([3585866](https://github.com/carawayhome/caraway-meridian/commit/35858666aaa53c3fdff76406e44515db9164abbe))
* **layout:** gladly update ([8202fb8](https://github.com/carawayhome/caraway-meridian/commit/8202fb8bfe887fa08d702475115bf52562a4eb99))
* **layout:** postscript file ([06c4795](https://github.com/carawayhome/caraway-meridian/commit/06c47958354c5af7bfcc9660f3c3a9fe4bebab16))
* **layout:** pR Review comment ([4d35382](https://github.com/carawayhome/caraway-meridian/commit/4d353823c682e149a866e50ee4319448bab6bd1f))
* **layout:** pR review comments ([0b7bdd6](https://github.com/carawayhome/caraway-meridian/commit/0b7bdd6e0eae5c4d97612e6062b53e4e9ea837a4))
* **layout:** updates ([367d9ff](https://github.com/carawayhome/caraway-meridian/commit/367d9ff59e559ba33ac2ddbbd233a1f8c3a849a6))
* **script:** gladly - Chat Widget ([f8d87b4](https://github.com/carawayhome/caraway-meridian/commit/f8d87b450d15374b9b69590545dc95b12e6246c0))
* **script:** postScript ([b1f2cf5](https://github.com/carawayhome/caraway-meridian/commit/b1f2cf5949c5d16585a3aa1842f0ef980655de86))


### Bug Fixes

* promobar animation glitch and hierarchy ([81e94ac](https://github.com/carawayhome/caraway-meridian/commit/81e94acad5b96d6ee0ff75ebb169c445e52a8e8a))
* the PageCTA component to improve code readability and maintainability with container wrappers ([7751060](https://github.com/carawayhome/caraway-meridian/commit/77510605559b5befa4dedc7915c08ecacb1b97f0))
* update Dialog component to avoid render glitch ([494255a](https://github.com/carawayhome/caraway-meridian/commit/494255a9f48893be55568ba31a220a654828f519))

## [0.23.0](https://github.com/carawayhome/caraway-meridian/compare/v0.22.0...v0.23.0) (2024-09-01)


### Features

* add multiple sticky promo bar slider with activation, animation ([4b41cf1](https://github.com/carawayhome/caraway-meridian/commit/4b41cf177b80dedc66a8b8573933d7b307318e94))
* add useCallback hook to improve performance in Countdown component ([588fee2](https://github.com/carawayhome/caraway-meridian/commit/588fee28648000afa36d1d036c194708611a2651))
* create Informational Text ([ee0ca35](https://github.com/carawayhome/caraway-meridian/commit/ee0ca354c779555f8c8620ab049a99470b6f059c))


### Bug Fixes

* auto-lint ([9f31a29](https://github.com/carawayhome/caraway-meridian/commit/9f31a2947e8e11c44d44cfef22cd550dc1b3d143))
* double message ([f286661](https://github.com/carawayhome/caraway-meridian/commit/f28666109415c37646dbab550fa26d76df054311))
* update eslint errors ([cf9bf00](https://github.com/carawayhome/caraway-meridian/commit/cf9bf00a917ed8cc00547756208015bfc338730f))

## [0.22.0](https://github.com/carawayhome/caraway-meridian/compare/v0.21.0...v0.22.0) (2024-08-25)


### Features

* add Callout component for displaying informative messages ([a161eee](https://github.com/carawayhome/caraway-meridian/commit/a161eee685494dad8cc7bd167b80673392e5d3be))
* add Callouts component to ProductCard and ProductDetails components ([0152ef1](https://github.com/carawayhome/caraway-meridian/commit/0152ef1d59b11facda76236bf5d541e896c3420c))
* add callouts to ProductCard component ([7f72f47](https://github.com/carawayhome/caraway-meridian/commit/7f72f47d20879a2ed78b572d2625b3016ff000ae))
* add FreeShippingBar component to Cart ([3322edf](https://github.com/carawayhome/caraway-meridian/commit/3322edff2f02ae124197b135ba58ab9e8eb07051))
* add FreeShippingBar component to Cart [sc-13060] ([9144297](https://github.com/carawayhome/caraway-meridian/commit/9144297e264fb0c20b5ffe03bfc3c53ffe036297))
* add theme color settings to callouts in ProductGrid component ([c1f0aa9](https://github.com/carawayhome/caraway-meridian/commit/c1f0aa968abd99f4498fc27789fed772a56cd893))
* add transparent variant to CallToAction component ([8d3d8f8](https://github.com/carawayhome/caraway-meridian/commit/8d3d8f8f474000a6a0ca9a73a39d0f3657420728))
* cart page logic added ([f62df02](https://github.com/carawayhome/caraway-meridian/commit/f62df02894b706f1d634337c3754451230e604d5))
* **component:** elements overlapping ([067e1cb](https://github.com/carawayhome/caraway-meridian/commit/067e1cb7d5b6c887baa5e795362c519b0e497600))
* **component:** media/Video - Component ([af86d3f](https://github.com/carawayhome/caraway-meridian/commit/af86d3f66405b8f3b8fd02e9bf7ad38b61af34fd))
* **component:** play/pause button mobile 16 pixels ([80ad3af](https://github.com/carawayhome/caraway-meridian/commit/80ad3afc74ab7bb546b65de5cdafcf844074b347))
* **component:** separator - Component Migration ([9d366ac](https://github.com/carawayhome/caraway-meridian/commit/9d366ac1ef472b246e21c2f7eebea960202b0b47))
* **create component cli:** helper to speed up create component process ([7c878a5](https://github.com/carawayhome/caraway-meridian/commit/7c878a5a9dcd6371d778ca2415bfe3e268351583))
* decoupled trigger component for global dialogs ([11654e7](https://github.com/carawayhome/caraway-meridian/commit/11654e73b62b3b4112110b640bd4f259ee4c5eec))
* **fetchPages:** create a build error ([dca60fc](https://github.com/carawayhome/caraway-meridian/commit/dca60fcc27a30b17f734e53e5cb31b10ae4fa433))
* improve FreeShippingBar component in Cart ([a4fcdc3](https://github.com/carawayhome/caraway-meridian/commit/a4fcdc380505a117b55818be79d751b17b90dc33))
* modal start ([50adc5e](https://github.com/carawayhome/caraway-meridian/commit/50adc5e69cefc2b45495c24e647c0c16a03e20e2))
* update ProductVariantSelector component to use state and default option ([7888ab7](https://github.com/carawayhome/caraway-meridian/commit/7888ab740d38a4aad46c148cd61d4a9a2ebab1d7))


### Bug Fixes

* import order eslint ([5471270](https://github.com/carawayhome/caraway-meridian/commit/5471270ca9ca696f287864dc704d1ae736ef38a0))
* improve Dialog component by adding backdrop and styling changes ([4d775f5](https://github.com/carawayhome/caraway-meridian/commit/4d775f5aae4a9b8682003c440810cf7747757cf3))

## [0.21.0](https://github.com/carawayhome/caraway-meridian/compare/v0.20.0...v0.21.0) (2024-08-09)


### Features

* add generateSlug utility function ([1213c80](https://github.com/carawayhome/caraway-meridian/commit/1213c808de33932ead637270210cce4ea0ba6920))
* add support for buttons via Contentful ([ce38e83](https://github.com/carawayhome/caraway-meridian/commit/ce38e834b012294918cab485613a31bbf8efd511))
* **component:** page CTA - Component ([e21793b](https://github.com/carawayhome/caraway-meridian/commit/e21793b4662c983fc9d37168d2568edd42a2cd37))
* create new PageCTA component ([5eb390e](https://github.com/carawayhome/caraway-meridian/commit/5eb390eb457420912c4405bf45555beebbe8bd50))
* rating component ([8f4fd35](https://github.com/carawayhome/caraway-meridian/commit/8f4fd351a46a5e4d223d984cc017302346691eb7))
* star with reviews fully customized ([f146b8c](https://github.com/carawayhome/caraway-meridian/commit/f146b8c74445a4b96ab4442ee279a49ad4c7cf44))


### Bug Fixes

* merge conflicts ([aa66cfd](https://github.com/carawayhome/caraway-meridian/commit/aa66cfd993108a25dc3333de9fb342e36be3f5e9))
* some lint issues ([79662ee](https://github.com/carawayhome/caraway-meridian/commit/79662ee21efb77c67c9afa01fcb6d9b48643e589))

## [0.20.0](https://github.com/carawayhome/caraway-meridian/compare/v0.19.0...v0.20.0) (2024-07-29)


### Features

* add multi searchParam support ([adfccf7](https://github.com/carawayhome/caraway-meridian/commit/adfccf7bac089611a75892814b620b621a031a55))

## [0.19.0](https://github.com/carawayhome/caraway-meridian/compare/v0.18.3...v0.19.0) (2024-07-27)


### Features

* content Model overhaul and upgrade to optimize for new environment ([d50a499](https://github.com/carawayhome/caraway-meridian/commit/d50a49996c8ab4fb99d79283f106a592fb107ee6))


### Bug Fixes

* update misconfigured types for image ([d1f8199](https://github.com/carawayhome/caraway-meridian/commit/d1f819909633f5c4faf3d6a58f4d85342365e8dd))

## [0.18.3](https://github.com/carawayhome/caraway-meridian/compare/v0.18.1...v0.18.3) (2024-06-28)


### Bug Fixes

* remove unused import ([1ce6e1e](https://github.com/carawayhome/caraway-meridian/commit/1ce6e1ea3b8404669a590c955dcd3566bb32107f))
* remove unused shop prop ([4bb6c97](https://github.com/carawayhome/caraway-meridian/commit/4bb6c9735b46e849cc775f2b7a40c78f91ac4269))
* update import ([8c3b2b9](https://github.com/carawayhome/caraway-meridian/commit/8c3b2b95d65216d8f21febd3c29e7110c57dea68))


### Code Refactoring

* added new layout components with a test page ([758cc19](https://github.com/carawayhome/caraway-meridian/commit/758cc194463b642b73dfb137b712f3e77fbabd18))
* update default theme support for CallToAction. Now supports Variants and Themes ([d882e63](https://github.com/carawayhome/caraway-meridian/commit/d882e639800b7e9cdf0e0f584c2e2e587d4dd74b))

## [0.18.1](https://github.com/carawayhome/caraway-meridian/compare/v0.18.0...v0.18.1) (2024-06-20)


### Features

* switch to URL state management to use RSC ([addf36e](https://github.com/carawayhome/caraway-meridian/commit/addf36ecafb5d5dffc17cd67837fdd84cd247925))


### Code Refactoring

* dispatch for analytics optimized to use lisetener ([5a2303c](https://github.com/carawayhome/caraway-meridian/commit/5a2303ccf1e13c480ca71db458fc9ec76ed372e7))

## [0.18.0](https://github.com/carawayhome/caraway-meridian/compare/v0.17.0...v0.18.0) (2024-05-17)


### Features

* add initial documentation files and configuration files with Nextra ([ff544df](https://github.com/carawayhome/caraway-meridian/commit/ff544df1c4eefe5c8af16ccd84a1ee28f062ae09))
* adding some theme styles and making LineItem its own component ([1b9b118](https://github.com/carawayhome/caraway-meridian/commit/1b9b118dcae30319f34f0ae1f657f8375680dd5c))
* rednering full width ZPattern ([717ce0a](https://github.com/carawayhome/caraway-meridian/commit/717ce0a17dcc1e4da3dbc604fa14a8ea3e77a4d8))
* refactoring zPattern styles ([8e91d52](https://github.com/carawayhome/caraway-meridian/commit/8e91d5265adb214470b2142744a68b09ab845794))
* refactoring zPattern styles ([3b0fa69](https://github.com/carawayhome/caraway-meridian/commit/3b0fa69635cca75132b07514c716507c03aad85c))
* typography added ([eee5d6d](https://github.com/carawayhome/caraway-meridian/commit/eee5d6d713bd1c48f2d31636f970ef20a172fd7a))


### Bug Fixes

* skeleton boolean and added typescript to props as well ([e277dc3](https://github.com/carawayhome/caraway-meridian/commit/e277dc335ef113a791bef5fe5053487b759182c9))

## [0.17.0](https://github.com/carawayhome/caraway-meridian/compare/v0.16.0...v0.17.0) (2024-05-14)


### Features

* adding type layout ([16e314d](https://github.com/carawayhome/caraway-meridian/commit/16e314d171909d456e744166e39e5b3b272028df))
* adding zPattern and layout ([cfa608c](https://github.com/carawayhome/caraway-meridian/commit/cfa608c242d4a8679bf96c1c3f78d31b84010473))


### Bug Fixes

* fix issue with missing richContent in getPageBlocks ([cdab7d0](https://github.com/carawayhome/caraway-meridian/commit/cdab7d09cabfcaa76936955e2070b11e78fdd198))
* update fetchPages.tsx to handle null values for block settings theme ([37eebe5](https://github.com/carawayhome/caraway-meridian/commit/37eebe5458ffac4eb08c7f10da0e10a17202ef5f))
* update themeThemes.stylex.ts with new color definitions ([520ed5d](https://github.com/carawayhome/caraway-meridian/commit/520ed5d7867bbb870bd9610b130367f0fed5cf19))

## [0.16.0](https://github.com/carawayhome/caraway-meridian/compare/v0.15.0...v0.16.0) (2024-05-14)


### Features

* add new util functions ([1319e8b](https://github.com/carawayhome/caraway-meridian/commit/1319e8b6673595a857f443a4b6175018ac58df5e))
* add themeThemes.stylex.ts file with theme color definitions to be used globally ([66fb87a](https://github.com/carawayhome/caraway-meridian/commit/66fb87a0d1f5bad6d5e1036dde83c98000517578))
* update Marquee component with theme support and utility functions ([912bb7f](https://github.com/carawayhome/caraway-meridian/commit/912bb7f2e0512b1b8f24825fed579864645006c5))


### Bug Fixes

* fix sizes props that was missing on image ([33254ab](https://github.com/carawayhome/caraway-meridian/commit/33254ab8923dbdb7526acdf784a2edc03cb70c60))
* marquee typescript issue has been resolved ([9c8de54](https://github.com/carawayhome/caraway-meridian/commit/9c8de545221d8f39c95fb7a520c2d9f60b99c0f1))
* update client vs server issue with money functions ([6233d03](https://github.com/carawayhome/caraway-meridian/commit/6233d035ad6eb7ea444ab6be7e0599b92985d096))

## [0.15.0](https://github.com/carawayhome/caraway-meridian/compare/v0.14.1...v0.15.0) (2024-05-13)


### Features

* new marquee component with rich text rendering ([c04093f](https://github.com/carawayhome/caraway-meridian/commit/c04093f21ca0c7bf25fe0ae6b99d2462eff05bb2))


### Bug Fixes

* discount code incorrectly displays a 0 on CartFooter component ([50542ca](https://github.com/carawayhome/caraway-meridian/commit/50542ca70ffe39093ccfb682bd8748aeefcc6e2f))
* rendering issue on rich text component ([bab4f49](https://github.com/carawayhome/caraway-meridian/commit/bab4f491ef27de6585d1c56ef31d7b8cfc07863c))

## [13.2.0](https://github.com/carawayhome/caraway-meridian/compare/v13.1.0...v13.2.0) (2024-05-13)


### Features

* new discount form to CartFooter component ([c12b75e](https://github.com/carawayhome/caraway-meridian/commit/c12b75e0e7a5bf79cdf736f1ebfeda429f695a70))

## [13.1.0](https://github.com/carawayhome/caraway-meridian/compare/v13.0.3...v13.1.0) (2024-05-11)


### Features

* add Skeleton component for loading placeholders, used in details ([baeff66](https://github.com/carawayhome/caraway-meridian/commit/baeff6698949ad6f15c74e252f6de5518d867c2a))


### Bug Fixes

* missing dependency on addtocart (unrelated) ([ba8fd26](https://github.com/carawayhome/caraway-meridian/commit/ba8fd2605811fafd9b8528989345dde7a59f94c3))


### Performance Improvements

* **sharp:** add sharp for production image perf ([8332c9f](https://github.com/carawayhome/caraway-meridian/commit/8332c9f8eb3db948a9ad27322def9c61c0661e67))

## [13.0.3](https://github.com/carawayhome/caraway-meridian/compare/v13.0.2...v13.0.3) (2024-05-03)


### Bug Fixes

* transformResponse function in availabilitySlice.ts to handle null values ([2c31bb0](https://github.com/carawayhome/caraway-meridian/commit/2c31bb0dd407b68bba7fb113a682d81384ce5146))

## [13.0.2](https://github.com/carawayhome/caraway-meridian/compare/v0.13.1...v13.0.2) (2024-05-03)


### Features

* updated and normalized shopify cart with state ([c672493](https://github.com/carawayhome/caraway-meridian/commit/c6724930bfea5b944093659b687700ba6a394fd1))


### Bug Fixes

* had a string type but is number ([f706c29](https://github.com/carawayhome/caraway-meridian/commit/f706c29e0f37095a2ff42a3956d7d2f96aa99c8f))
* linting errors ([5df06c0](https://github.com/carawayhome/caraway-meridian/commit/5df06c0dda77f33f5638676a101a35316950a959))
* productId type from number to string in AddToCart.tsx ([4d141ad](https://github.com/carawayhome/caraway-meridian/commit/4d141adc4a6add3daaac57f25f953ba736d75ba4))
* type error resolved do to no quantity on product ([f2bdaf6](https://github.com/carawayhome/caraway-meridian/commit/f2bdaf617f209c81e21ebf410fac373f75a5c3e9))


### Miscellaneous Chores

* update various packages for stylex, nextjs and babel ([3e65f5d](https://github.com/carawayhome/caraway-meridian/commit/3e65f5d577daf5619e248577305c36890b29a9df))

## [0.13.1](https://github.com/carawayhome/caraway-meridian/compare/v0.13.0...v0.13.1) (2024-05-01)


### Features

* adding disabled button when a variant is unavailable ([6f9c43e](https://github.com/carawayhome/caraway-meridian/commit/6f9c43eba028d948bbfe5e9a10ab5eb954e03fbb))


### Code Refactoring

* update AddToCart component and handle availability errors ([391a6c3](https://github.com/carawayhome/caraway-meridian/commit/391a6c3e3ca9f15da610ff7cfde435dc95489e57))

## [0.13.0](https://github.com/carawayhome/caraway-meridian/compare/v0.12.0...v0.13.0) (2024-04-24)


### Features

* add font-face declarations and update Hero component to showcase new components ([b03088c](https://github.com/carawayhome/caraway-meridian/commit/b03088cfedb705dcb681915a21ac1494aafbc69b))


### Bug Fixes

* eslint issue ([797e783](https://github.com/carawayhome/caraway-meridian/commit/797e783cf2d79b2ed52763521f62d60155599c41))

## [0.12.0](https://github.com/carawayhome/caraway-meridian/compare/v0.11.2...v0.12.0) (2024-04-19)


### Features

* add availabilitySlice.ts to handle API requests for real-time product/variant availability ([caecee7](https://github.com/carawayhome/caraway-meridian/commit/caecee7ef8b52f2666b852ccc210f5d888c6db04))
* add getVariantAvailability hook for fetching variant availability ([ef93e6f](https://github.com/carawayhome/caraway-meridian/commit/ef93e6fa0d0a9f811506a73884f5cdd739aca779))
* add shopifyBase64.ts utility functions for encoding and decoding Shopify IDs ([3b33a2a](https://github.com/carawayhome/caraway-meridian/commit/3b33a2a28c7d98871928b2a8cf025b6660f08570))
* add variant availability check to AddToCart component ([4b7df11](https://github.com/carawayhome/caraway-meridian/commit/4b7df113424f4a5c0bc34b9e5707334f25cc18cc))
* added container component, takes as prop and backgroundColor ([53f9da5](https://github.com/carawayhome/caraway-meridian/commit/53f9da5431181b724385edae411b6abe95090e17))
* added element api features, padding, size, alighment and transform ([446d74c](https://github.com/carawayhome/caraway-meridian/commit/446d74c77efbbb52a500ca8cfeb3ec10c58d9a88))
* added element api features, padding, size, alighment and transform ([4849a69](https://github.com/carawayhome/caraway-meridian/commit/4849a6901bbe324e27df5b85ab892fe199a58e54))
* added typography but removed scale ([48962db](https://github.com/carawayhome/caraway-meridian/commit/48962db529ec871098844f4447fccc03bcf945b2))
* renaming component ([8673f75](https://github.com/carawayhome/caraway-meridian/commit/8673f7546155a334a95bdb8750c2d34226c26197))
* spacer component added ([1796143](https://github.com/carawayhome/caraway-meridian/commit/1796143141197d821c543f2d695e7b1eb5068ab3))
* updated some values to accept boolean values over key value pairs ([797f8c4](https://github.com/carawayhome/caraway-meridian/commit/797f8c451f99c6d88852cccfd57307ca449456da))
* updating multiple files to use new white token ([873fea1](https://github.com/carawayhome/caraway-meridian/commit/873fea178efbbbdd0dd7a4c4dfe3161475d41cf4))


### Bug Fixes

* background color issue in CallToAction component ([08971c3](https://github.com/carawayhome/caraway-meridian/commit/08971c38b2fddce0da8922063f31eb90571e675d))

## [0.11.2](https://github.com/carawayhome/caraway-meridian/compare/v0.11.1...v0.11.2) (2024-04-11)


### Build System

* add clean-dev and clean-build scripts to package.json ([17b1da3](https://github.com/carawayhome/caraway-meridian/commit/17b1da3f6e954ebd12e2f6dd253a310b8b39736a))

## [0.11.1](https://github.com/carawayhome/caraway-meridian/compare/v0.11.0...v0.11.1) (2024-04-09)


### Code Refactoring

* currency formatting functions in Cart, Product, and ProductFeatureCard components ([6fe6a43](https://github.com/carawayhome/caraway-meridian/commit/6fe6a438cb57b49126db2feca0e1f7adcc12ebbb))

## [0.11.0](https://github.com/carawayhome/caraway-meridian/compare/v0.10.1...v0.11.0) (2024-04-01)


### Features

* fetch and display product data in ProductFeatureCard component ([c246d40](https://github.com/carawayhome/caraway-meridian/commit/c246d40be06263b788a6b50f912311c9c08d01cd))


### Bug Fixes

* promise all generator for hydrating blocks ([01d2cf7](https://github.com/carawayhome/caraway-meridian/commit/01d2cf78b36214fccebc9ab57921a7dbe1eb5720))

## [0.10.1](https://github.com/carawayhome/caraway-meridian/compare/v0.10.0...v0.10.1) (2024-03-30)


### Bug Fixes

* mismatch update on fetchGraphQL to accept tags parameter ([d66dfa3](https://github.com/carawayhome/caraway-meridian/commit/d66dfa3c1e756255d70b14af13ab3617fe7a4427))

## [0.10.0](https://github.com/carawayhome/caraway-meridian/compare/v0.9.0...v0.10.0) (2024-03-30)


### Features

* add availability to ATC, major refactor fetching live products ([78e514a](https://github.com/carawayhome/caraway-meridian/commit/78e514adc264a67021f43524a88e6158916e4ef9))
* add Product type ([fe3e739](https://github.com/carawayhome/caraway-meridian/commit/fe3e7397b40547b683de98255855f1f7400c4dbf))
* new content blocks ([acb46b6](https://github.com/carawayhome/caraway-meridian/commit/acb46b642db77a152b090c1286d9bf1c1a999890))
* new utils for regex ([51c3ee4](https://github.com/carawayhome/caraway-meridian/commit/51c3ee4ead7a3eee0097ca62e07986c34fa8ae32))
* page section support ([d7d7a72](https://github.com/carawayhome/caraway-meridian/commit/d7d7a724d3ae24928a8f71fb2256f8f91b996650))


### Bug Fixes

* eslint issue ([5405aa3](https://github.com/carawayhome/caraway-meridian/commit/5405aa3ddb8a8f60306b3b089d3a1d9e66a8f36f))
* props and TS fixed for existing product calls ([9d7e5e7](https://github.com/carawayhome/caraway-meridian/commit/9d7e5e7d643bc6b0df4923ca7752a47ac9c2c47d))

## [0.9.0](https://github.com/carawayhome/caraway-meridian/compare/v0.8.0...v0.9.0) (2024-03-26)


### Features

* **analytics:** update analytics event spec and add tracking for product viewed ([f370af7](https://github.com/carawayhome/caraway-meridian/commit/f370af7ec1440faba27c234e427f89a9b78e6519))

## [0.8.0](https://github.com/carawayhome/caraway-meridian/compare/v0.7.0...v0.8.0) (2024-03-25)


### Features

* add Segment analytics and track AddToCart event ([ed4e902](https://github.com/carawayhome/caraway-meridian/commit/ed4e902ebc7a4ee61f86a8c236ced2e492842c0d))


### Bug Fixes

* import naming in AddToCart and Segment components ([923192b](https://github.com/carawayhome/caraway-meridian/commit/923192b65a3ba644971afb9f32213c23fa15088d))
* import statement in segment.ts ([698406c](https://github.com/carawayhome/caraway-meridian/commit/698406cba09c9e8e308e9d8657d37372c34581c4))
* total amount for cart ([3f85b6f](https://github.com/carawayhome/caraway-meridian/commit/3f85b6fe1b5b58c36382ecf0d3b457dbc5d7b099))

## [0.7.0](https://github.com/carawayhome/caraway-meridian/compare/v0.6.0...v0.7.0) (2024-03-21)


### Features

* add @vercel/analytics package and integrate it into the layout ([c42d35c](https://github.com/carawayhome/caraway-meridian/commit/c42d35c7f5ea81abf82469348a343c8c1ebc0e1e))
* add ProductDetails component ([46bdb05](https://github.com/carawayhome/caraway-meridian/commit/46bdb05f77d122cf3ba6f028507ecc6eaade22e3))
* add productSlice to store configuration ([2f1de4e](https://github.com/carawayhome/caraway-meridian/commit/2f1de4e316662a36345da9f2af13c9edd823adb7))
* **addtocart:** add open cart functionality, variant id and  styling ([dec91ef](https://github.com/carawayhome/caraway-meridian/commit/dec91ef7a9b75e1bcb9d2ff029bbd08bf0d5e6b2))
* fetch variants to ProductCard and ProductDetails components ([a72a4db](https://github.com/carawayhome/caraway-meridian/commit/a72a4dbe8cacd9acb8e9d15de25204af6c98dd07))


### Bug Fixes

* import formatting in layout.tsx ([4c35b52](https://github.com/carawayhome/caraway-meridian/commit/4c35b52a3781b573ae925af853ab697f832df472))

## [0.6.0](https://github.com/carawayhome/caraway-meridian/compare/v0.5.0...v0.6.0) (2024-03-18)


### Features

* cart functionality and display cart items and total quantity ([8f3cded](https://github.com/carawayhome/caraway-meridian/commit/8f3cded6a4f20674998d00818654e05555b23aa6))
* **cart:** open cart panel when add to cart initiates ([01d2823](https://github.com/carawayhome/caraway-meridian/commit/01d2823001efab4b6fb1c61a23acf0b07c67ab7f))
* **redux:** add support for redux to trigger and manage cart state ([607107a](https://github.com/carawayhome/caraway-meridian/commit/607107a71fadfb87f2ae3186aa689e6d6f18fee6))


### Bug Fixes

* **cart:** clicking cart toggle no longer causes a redirect ([7a03ff3](https://github.com/carawayhome/caraway-meridian/commit/7a03ff3b0fb47662ab045be806d843c9820d65f0))
* remove hook ([7e3895c](https://github.com/carawayhome/caraway-meridian/commit/7e3895c32b6f6772913be966cca8bdb397110201))

## [0.5.0](https://github.com/carawayhome/caraway-meridian/compare/v0.4.0...v0.5.0) (2024-03-11)


### Features

* add Cart component and related files ([5170a60](https://github.com/carawayhome/caraway-meridian/commit/5170a60cd8feaae788865214ab57d3585411caf4))
* add cart toggle ([c9d2e04](https://github.com/carawayhome/caraway-meridian/commit/c9d2e0461b8db112002f1767abb19fb98ce55598))
* add dynamicParams and generateMetadata functions ([396bf9a](https://github.com/carawayhome/caraway-meridian/commit/396bf9a46d12a3b20d29849daf15678ab4802ae3))
* **hero:** hero component and update global theme tokens ([44618d3](https://github.com/carawayhome/caraway-meridian/commit/44618d38a247763fd102810e9d34199b0b430adf))
* **pdp:** add to cart component with static build v0.4.1 ([4668be9](https://github.com/carawayhome/caraway-meridian/commit/4668be9b21b0e1510f343da8ab81c5dd0c00c598))
* **promo:** new promo bar ([24d7400](https://github.com/carawayhome/caraway-meridian/commit/24d7400619590543538e46488ed45c7a68ccd49f))


### Bug Fixes

* minor spacing for lint issue resolved ([d6a563e](https://github.com/carawayhome/caraway-meridian/commit/d6a563e83f0a299e79b313158ed54ad13a5bf074))
* promobar eslint ([06ab824](https://github.com/carawayhome/caraway-meridian/commit/06ab82436baf2cae56570c39cc58f66dfac2d25f))
* **stylex:** import issue relative to dynamic imports ([83ac1bf](https://github.com/carawayhome/caraway-meridian/commit/83ac1bf69876fe6c746bded0f08b0a51d614aeac))

## [0.4.0](https://github.com/carawayhome/caraway-meridian/compare/v0.3.0...v0.4.0) (2024-03-06)


### Features

* add ProductCard and ProductGrid components ([7e33f83](https://github.com/carawayhome/caraway-meridian/commit/7e33f8351b4c93e8cf4a7b19cd52355d6cda1f00))
* **collections:** collections page now shows all products ([c1d836c](https://github.com/carawayhome/caraway-meridian/commit/c1d836c7dd2b9d420351c88fa3401a7606817729))
* **contentful:** added contentful integration and pulling of products ([b550f2a](https://github.com/carawayhome/caraway-meridian/commit/b550f2a95337aa084cc1566196c8e38f70628b59))
* **currency:** add currency utils ([848b39b](https://github.com/carawayhome/caraway-meridian/commit/848b39b0141e07d20a196eb5f31cd40066a9b212))


### Bug Fixes

* remove deprecated fetch ([c317129](https://github.com/carawayhome/caraway-meridian/commit/c3171294e2c04755220deceec61e402566b4fb9f))
* updated ts props ([7f41023](https://github.com/carawayhome/caraway-meridian/commit/7f41023cdfe1e70ab79ddc565c9e7cf37acb6d2f))

## [0.3.0](https://github.com/carawayhome/caraway-meridian/compare/v0.2.1...v0.3.0) (2024-02-25)


### Features

* **shopify:** add new files for Shopify cart products layout and page. Includes Checkout ([4940ef6](https://github.com/carawayhome/caraway-meridian/commit/4940ef6297966c73f11ed3cd4b31b83ed303e088))
* **shopify:** add Shopify provider components and utils, includes cart provider ([51f22b0](https://github.com/carawayhome/caraway-meridian/commit/51f22b08ee3d23aafa3f5aa580ca8234495c6817))
* **shopify:** setup shopify hydrogen react storefront client and environment ([9899379](https://github.com/carawayhome/caraway-meridian/commit/98993797159d04df5c2a210e813fe992b8a5b0ab))


### Bug Fixes

* eslint config ([f26fd25](https://github.com/carawayhome/caraway-meridian/commit/f26fd251ccee5800544870315285ab993952f083))
* import React in Nav component and fix formatting in Header component ([843c8e7](https://github.com/carawayhome/caraway-meridian/commit/843c8e74fc724e8b68fc2aa1d57dc342ff46f684))
* mismatch token assignment ([6f26a76](https://github.com/carawayhome/caraway-meridian/commit/6f26a760667eb4fd6fece62bb203ce885ea1195b))
* missing api ([ac60630](https://github.com/carawayhome/caraway-meridian/commit/ac6063050091d9b248ea3a56a7aba869a53c22c0))
* package lock update ([a01acb2](https://github.com/carawayhome/caraway-meridian/commit/a01acb2672f7e81981450fc8fe46dc3b68048ad1))
* type error in ColorSwatch component ([e214fc8](https://github.com/carawayhome/caraway-meridian/commit/e214fc82a0baad9b7e5f6b208557d3f3c73c2714))

## [0.2.1](https://github.com/carawayhome/caraway-meridian/compare/v0.2.0...v0.2.1) (2024-02-09)


### Bug Fixes

* default values for disabled prop in Button and Anchor components ([37f7d38](https://github.com/carawayhome/caraway-meridian/commit/37f7d38ea966f5dd8dd31064414699d974616b29))

## [0.2.0](https://github.com/carawayhome/caraway-meridian/compare/v0.1.0...v0.2.0) (2024-02-08)


### Features

* add @stylexjs/eslint-plugin and @stylexjs/nextjs-plugin ([1aa7e87](https://github.com/carawayhome/caraway-meridian/commit/1aa7e8760cfafaf20d4d156fb50fe2c0f107bf23))
* add localization for en-US.json file ([e2f0caf](https://github.com/carawayhome/caraway-meridian/commit/e2f0caf06a9ea1620278b72a2d671f3fd36d1366))
* **calltoaction:** new call to action button component ([533156c](https://github.com/carawayhome/caraway-meridian/commit/533156c1635acb1238f9ebb4d9679416bb9e3a97))


### Bug Fixes

* add as string ([bc1b046](https://github.com/carawayhome/caraway-meridian/commit/bc1b0469cfb93cf44e3d0698068f23376f9d8bcc))
* add client-side code to CallToAction component ([3dff1fb](https://github.com/carawayhome/caraway-meridian/commit/3dff1fbae692f8ce793119090696ec7e5c0534f9))
* and refactor CallToAction button component ([665d2f8](https://github.com/carawayhome/caraway-meridian/commit/665d2f8d172807d17b3f7d2742dec18a87e393b5))
* missing commands ([fa9eaf0](https://github.com/carawayhome/caraway-meridian/commit/fa9eaf0c7f25806ceb30e499d2a0eb83bea922cd))
* refactor CallToAction component styles ([4d1807f](https://github.com/carawayhome/caraway-meridian/commit/4d1807f304d02ff435d78d95fb189aebed0186f7))
* update CallToAction component styles ([44d9e91](https://github.com/carawayhome/caraway-meridian/commit/44d9e9197f022bca218cdbf658a8edb8e23709a6))

## 0.1.0 (2024-02-05)


### Features

* add @vercel/speed-insights package and integrate it into layout component ([cc4d78f](https://github.com/carawayhome/caraway-meridian/commit/cc4d78f2edc58bc687475d6d60496840ea268325))
* add husky with commit linting and commit zen for a streamlined commit process
