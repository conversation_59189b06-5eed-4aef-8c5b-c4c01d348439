# Progress Bar

This is a progress bar that can be used globally across the site. It comes in Desktop and Mobile form. 
Currently most of the setup for is all code-based. No Contentful set up except the Dialog portion.  

## Import Directions

```tsx
import ProgressBar from '@/components/Generic/ProgressBar/Desktop'
import MobileProgressBar from '@/components/Generic/ProgressBar/Mobile'
```

## Component Properties

Desktop Progress Bars do not take any props. The only module it relies on is the tierlist / thresholds from the utils directory

Mobile Progress Bars takes 1 prop:
- `pdpHelper` - Boolean value - If enabled, will place the Mobile Progressbar a few pixels above and on top of the Sticky Swatches on PDPs. 

## Component props

```tsx
type Props = {
  pdpHelper?: boolean
  gbValue?: string
}

```


## Where to Render

Typically has been used on the `@/app/layout.tsx`

```tsx
  <Component>
    <ProgressBar />
    <MobileProgressBar />
  </Component>
```

And to enable PDP Specific Updates: You'd have to go to `src/components/Product/ProductSwatches/StickyMobileSwatches.tsx`
The pdpHelper flag enables the behavior where it sits atop the mobile progress bar swatches.
```tsx
  <MobileProgressBar pdpHelper />
```

## How to Style 
All styling must be CSS based as this is hard-coded onto the site. 
For any updates for future launches: update the CSS on whichever Progerss Bar; update the thresholds / tiers in the utils directory below. 


## Threshold / Tierlist 
At the time of writing this post (Feb / March 2025 - ECI launch), we used this file `@/components/Generic/ProgressBar/utils/qmmUtils.ts`

Utils files like these power the thresholds / tierlists of the progress bars. This is where you'd update the number of tiers; copy; helper functions; etc. 

```txx
type Tier = {
  threshold: number
  discount: string
  callouts: [{
    title: string
  }]
  icon: string
  iconActive: string
}

const tierList: Tier[] = [
  {
    threshold: 90,
    discount: 'Free Shipping at $90+',
    callouts: [{
      title: 'Free Shipping'
    }],
    icon: '/assets/Delivery-Icon.svg',
    iconActive: '/assets/Delivery-Icon-Marigold.svg'
  },
  {
    threshold: 525,
    discount: 'Free Gift at $525+',
    callouts: [{
      title: '+ Free Gift'
    }],
    icon: '/assets/Gift-Box.svg',
    iconActive: '/assets/Gift-Box-Marigold.svg'
  },
  {
    threshold: 975,
    discount: 'Free Gift at $975+',
    callouts: [{
      title: '+ Free Gift'
    }],
    icon: '/assets/Gift-Box.svg',
    iconActive: '/assets/Gift-Box-Marigold.svg'
  }
]

const MAX_PROGRESS = 100

const TWO = 2

const ANIMATION_TIMEOUT = 400

const getSegmentedProgress = (currentAmount: number) => {
  const segmentCount = tierList.length

  let progress = 0

  for (let i = 0; i < segmentCount; i += 1) {
    if (currentAmount >= tierList[i].threshold) {
      progress += MAX_PROGRESS / segmentCount
    } else {
      const previousThreshold = i === 0 ? 0 : tierList[i - 1].threshold
      const segmentSize = tierList[i].threshold - previousThreshold
      const segmentProgress = ((currentAmount - previousThreshold) / segmentSize) * (MAX_PROGRESS / segmentCount)
      progress += segmentProgress
      break
    }
  }

  return Math.min(progress, MAX_PROGRESS)
}

const DECIMAL_PLACES = 2

const formatNumber = (num: number) => (num % 1 === 0 ? num.toString() : num.toFixed(DECIMAL_PLACES))

export {
  tierList,
  MAX_PROGRESS,
  TWO,
  ANIMATION_TIMEOUT,
  getSegmentedProgress,
  formatNumber
}
```

## Dialogs 
Progress Bars can all take one dialog. Both desktop and mobile dialogs are triggered by pressing on the information circle icon. 

![](/assets/progress-bar/dialog-pb.jpg)

Adding a Dialog to these Progress Bar are trivial. 
Create a Dialog the normal way (Block: Dialog component)  
Then copy and paste the Dialog component's ID onto the code. 
During the construction of these components, we didn't have enough bandwidth to connect Progress Bars with dialogs. 

![](/assets/progress-bar/dialog-cms-directions.jpg)
![](/assets/progress-bar/dialog-code.jpg)
