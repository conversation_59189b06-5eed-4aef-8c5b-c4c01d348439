---
title: "FeaturedStories"
description: "The FeaturedStories component displays a grid of featured articles, each containing an image, category, and title, and links to the respective article."
---

# FeaturedStories

The `FeaturedStories` component showcases a list of articles in a grid format, each containing an image, category, and title. It provides clickable links to the respective articles for easy navigation.

### Import Directions

```ts
import FeaturedStories from '@components/Content/FeaturedStories';
```

### FeaturedStories Usage

The `FeaturedStories` component accepts the following props:

| **Prop**     | **Type**                                                                                 | **Required** | **Default Value** |
|--------------|-------------------------------------------------------------------------------------------|--------------|-------------------|
| `articles`   | `{ image: string, category: string, title: string, id: string, url: string }[]`           | Yes          | `[]`              |
| `header`     | `string`                                                                                  | No           | `'Featured Stories'` |

```ts
const articles = [
  {
    id: '1',
    image: '/images/article1.jpg',
    category: 'Technology',
    title: 'Exploring the Latest Tech Trends',
    url: '/article/1',
  },
  {
    id: '2',
    image: '/images/article2.jpg',
    category: 'Health',
    title: 'Breakthroughs in Healthcare Innovation',
    url: '/article/2',
  },
];

<FeaturedStories
  articles={articles}
  header="Latest Features"
/>;
```

### FeaturedStories Component Structure

- **`articles` (required)**: An array of article objects, where each article contains:
  - **`id`**: A unique identifier for the article.
  - **`image`**: The URL or path to the article’s image.
  - **`category`**: A string representing the article’s category, displayed above the title.
  - **`title`**: The article's title, displayed prominently on the card.
  - **`url`**: The URL of the article. The entire card links to this URL.
  
- **`header` (optional)**: A string that serves as the section's title. Defaults to `"Featured Stories"`.

### Example Usage

```ts
<FeaturedStories
  articles={articles}
  header="Editor's Picks"
/>;
```
