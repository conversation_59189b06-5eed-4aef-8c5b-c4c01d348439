# Weight and Dimensions Contentful Integration

The Weight and Dimensions component is a dialog table used to display weight and dimensions information for a given product and its accompanying storage.

## Screenshots

### Desktop

#### Dialog Trigger
![Dialog Trigger](/assets/weight-and-dimensions/weight-and-dim-trigger.png)

#### Product Chart
![Product Chart](/assets/weight-and-dimensions/weight-and-dim-product.png)

#### Storage Chart
![Storage Chart](/assets/weight-and-dimensions/weight-and-dim-storage.png)

### Mobile

#### Product Chart
![Product Chart](/assets/weight-and-dimensions/weight-and-dim-product-mobile.png)

#### Storage Chart
![Storage Chart](/assets/weight-and-dimensions/weight-and-dim-storage-mobile.png)

## How to Use

### Creating a Product Metadata Item

1. **Navigate** to any **Product** in Contentful.
2. Locate **Product Metadata** and check for an existing **Product Metadata Item** containing **Dialogs**:
   - **If an item exists**, proceed to step 4.
   - **If no item exists**, continue to step 3.

![](/assets/weight-and-dimensions/contentful-steps/product_metadata.jpeg)

3. Under **Product Metadata**, select **Product Metadata Item** from the **Add content** dropdown:
   - **Enter a name** to locate within Contentful.
   - Select **Dialogs** from the **Type** dropdown.

![](/assets/weight-and-dimensions/contentful-steps/product-metadata-item.jpeg)

4. **Configure the Dialog Block**:
   - In the **References** section on **Product Metadata Item, add a new **Block: Dialog**.

![](/assets/weight-and-dimensions/contentful-steps/configure_dialog_block.jpeg)

   - **Enter a name** for locating within Contentful.
   - **Set the Header** (this will be the label for the first toggle option).
   - **Create tables**:
     - **Desktop content** → Add a table in the **Content** section.
     - **Mobile content** → Add a table in the **Mobile Content** section.

![](/assets/weight-and-dimensions/contentful-steps/configure_dialog_block2.jpeg)

![](/assets/weight-and-dimensions/contentful-steps/desktop_table.jpeg)

![](/assets/weight-and-dimensions/contentful-steps/mobile_table.jpeg)

5. **Set Up Token Settings**:
   - Navigate to the **Settings** section.
   - **Enter a name** for locating within Contentful.
   - Select **Layout 3** from the **Layout** dropdown.
   - **Return to the Block: Dialog.**
6. **Add Storage Information**:
   - Add a new **Block: Content** to the **Toggles and References** section (second toggle chart) on **Block: Dialog**.
   - **Enter a name** for locating within Contentful.
   - **Set the Title** (this will be the label for the second toggle).
   - **Create tables** for both **Content** (desktop) and **Mobile Content** sections.
   - **Upload media** to the **Assets** section (used for storage info).
   - **Return to the Product page.**

![](/assets/weight-and-dimensions/contentful-steps/toggles_and_references_content.jpeg)

![](/assets/weight-and-dimensions/contentful-steps/desktop_storage_chart.jpeg)

![](/assets/weight-and-dimensions/contentful-steps/mobile_storage_chart.jpeg)

### Linking Product Metadata Item to Call to Action

1. **Navigate** to **PageSections** on the **Product** page.
   - Open **What’s Included**.
2. Add **Block: Call to Action** to the **Blocks** section:
   - **Enter a name** for locating within Contentful.
   - **Text** will appear as the dialog anchor on the component.
3. Add the **Block: Dialog** you previously created to **Page**.
