# Weight and Dimensions
The `WeightAndDimensionsDialog` component is a dialog table used to display weight and dimensions information for a given product and it's accompanying storage.


## File Tree Hierarchy
- **Query:** 
  - `src/lib/contentful/fetchProducts.tsx`

- **Dialog Extractor:** 
  - `src/lib/contentful/blockExtractor/extractProductDialogs.ts`

- **What's Included Extractor:** 
  - `src/lib/contentful/blockExtractor/extractorWhatsIncluded.ts`

- **Anchor to open Dialog:** 
  - `src/components/Product/WhatsIncluded/V2/layout/WhatsIncludedV2Content.tsx`

- **WeightAndDimensionsDialog:** 
  - `src/components/Generic/Dialog/WeightAndDimensionsDialog/index.tsx`

## Dialog Props (Global)

```tsx
export type DialogProps = {
  id: string
  header: string
  subheader: string
  asset?: string
  content: string | ReactNode
  mobileContent: string | ReactNode
  buttons?: ExtendedAnchorProps[]
  footerContent?: string
  theme?: ThemeColors
  layout?: LayoutProps
  text?: string
  dialogToggles?: {
    title: string
    content: string | ReactNode
    mobileContent: string | ReactNode
    asset: {
      items: {
        fileName: string
        url: string
      }[]
    }
  }[],
  compareChart: {
    header: string
    elementsToCompare: ElementsToCompare
  }
}
```
## Weight and Dimensions Dialog Props

```tsx
export type WeightAndDimProps = {
  content: string | ReactNode
  mobileContent: string | ReactNode
  header: string
  dialogToggles: {
    title: string
    content: string | ReactNode
    mobileContent: string | ReactNode
    asset: {
      items: {
      fileName: string
      url: string
      }[]
    }
  }[]
}
```

### `content`

- **Type:** `string | ReactNode`
- **Description:** Content can be of type `string` but must be overridden as Content will be passed through RichTextRenderer during the extraction process.

### `mobileContent`

- **Type:** `string | ReactNode`
- **Description:** Content can be of type `string` but must be overridden as Content will be passed through RichTextRenderer during the extraction process.

### `header`

- **Type:** `string`
- **Description:** Displayed as label in first Toggle.

### `dialogToggles`

- **Type:**
```tsx
export type DialogToggles = {
  dialogToggles: {
    title: string
    content: string | ReactNode
    mobileContent: string | ReactNode
    asset: {
      items: {
      fileName: string
      url: string
      }[]
    }
  }[]
}
```
- **Description:** Dialog Toggles' data contains: 
  - Secondary toggle lable.
  - Desktop content.
  - Mobile content.


## Usage

Update any PDP component with RenderCTAS to open the Weight and Dimensions Modal. 
Example usage for updating.

```jsx
import { useDialogContext } from '@/providers/config/DialogContext'
import RenderCTAS from '@/components/Generic/RenderCTAS'
import Typography from '@/components/Typography'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  link: {
    color: colors.navy,
    fontWeight: 400,
    fontSize: '1rem'
  }
})

export const ExampleWeightAndDimTrigger = ({}) => {
  const { triggerDialog } = useDialogContext()

  return (
    <>
      {dialog && (
        <Typography as="p" typographyTheme="bodyLarge">
          <RenderCTAS
            buttons={[
              {
                children: dialog.text,
                id: dialog.id,
                styleProp: styles.link,
                variant: 'underlined'
              },
            ]}
          />
        </Typography>
      )}
    </>
  )
}

export default ExampleWeightAndDimTrigger
```