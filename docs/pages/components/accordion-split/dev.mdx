# Accordion Split

For contentful instructions and Screenshots [Here](/components/accordion-split/get-started)

## Import Directions

```tsx
import AccordionSplit from '@/components/Product/AccordionSplit';
```

## Component Properties

- `title` - Title of the accordion
- `theme` - Theme of the accordion
- `items` - Array of objects with the following properties
  - item `id` - Unique id for the accordion item
  - item `title` - Title of the accordion item
  - item `content` - Content of the accordion item
  - item `media` - Media object with the following properties
    - media `url` - URL of the media (Image/Video)
    - media `height` - Height of the media, default is 300px
    - media `mobileHeight` - Height of the media on mobile, default is 500px

## Component props

```tsx
type mediaProps = {
  url: string;
  height: string,
  mobileHeight: string
}

export type accordionSplitProps = {
  title: string,
  theme: ThemeColors,
  items: Array<{
    id: string
    title: string
    content: string
    media: mediaProps
  }>
}

```




## Component props

```tsx
  <AccordionSplit
      title="Lorem Ipsum Dolor Quantium Sequant"
      theme="gray100"
      items={[
        {
          id: '1',
          title: 'Lorem Ipsum Dolor',
          content: 'Lorem ipsum dolor sit amet consectetur. Arcu sit ac velit ligula ultricies id tortor. Goalind hasaferd intreo desis. Lorem ipsum dolor.',
          media: {
            url: '/assets/Bw_Before_50.jpg',
            height: '500px',
            mobileHeight: '300px',
          }
        },
        {
          id: '2',
          title: 'Lorem Ipsum Dolor',
          content: 'Lorem ipsum dolor sit amet consectetur. Arcu sit ac velit ligula ultricies id tortor. Goalind hasaferd intreo desis. Lorem ipsum dolor.',
          media: {
            url: '/assets/BW_After_50.jpg',
            height: '500px',
            mobileHeight: '300px',
          }
        },
        {
          id: '3',
          title: 'Lorem Ipsum Dolor',
          content: 'Lorem ipsum dolor sit amet consectetur. Arcu sit ac velit ligula ultricies id tortor. Goalind hasaferd intreo desis. Lorem ipsum dolor.',
          media: {
            url: 'https://videos.ctfassets.net/vitsw3itv0qj/3nQC0Bk8nslRtQdoJn6sPQ/6142b645faaa45eb8476a61e512c0202/Cast_Iron_Skillet_-_Navy_-_Cooking_Vegetables_on_Gas_Stove.mp4',
            height: '500px',
            mobileHeight: '300px',
          }
        },
      ]}
    />
```