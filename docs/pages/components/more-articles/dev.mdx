---
title: "MoreArticles"
description: "The MoreArticles component displays a grid of articles with an image, category, and title, providing a call-to-action button for users to view more articles."
---

# MoreArticles

The `MoreArticles` component is designed to display a grid of article cards, each featuring an image, category, and title. It also includes a customizable call-to-action button that allows users to navigate to a more comprehensive article listing or related content.

### Import Directions

```ts
import MoreArticles from '@components/Content/MoreArticles';
```

### MoreArticles Usage

The `MoreArticles` component accepts the following props:

| **Prop**     | **Type**                                                                                 | **Required** | **Default Value** |
|--------------|-------------------------------------------------------------------------------------------|--------------|-------------------|
| `articles`   | `{ image: string, category: string, title: string, id: string }[]`                        | Yes          | `[]`              |
| `header`     | `string`                                                                                  | No           | `'More Articles'` |
| `buttonText` | `string`                                                                                  | No           | `'More Articles'` |
| `buttonLink` | `string`                                                                                  | No           | `'/blogs'`        |

```tsx
const articles = [
  {
    id: '1',
    image: '/images/article1.jpg',
    category: 'Technology',
    title: 'How AI is Revolutionizing Technology',
  },
  {
    id: '2',
    image: '/images/article2.jpg',
    category: 'Health',
    title: 'The Future of Healthcare Innovation',
  },
];

<MoreArticles
  articles={articles}
  header="Latest Articles"
  buttonText="View All Articles"
  buttonLink="/all-articles"
/>;
```

### MoreArticles Component Structure

- **`articles` (required)**: An array of article objects, where each article contains:
  - **`id`**: A unique identifier for the article.
  - **`image`**: The URL or path to the article’s image.
  - **`category`**: A string representing the article’s category, displayed above the title.
  - **`title`**: The article's title, displayed prominently on the card.

- **`header` (optional)**: A string that serves as the section's title. Defaults to `"More Articles"`.
- **`buttonText` (optional)**: The text to display on the call-to-action button. Defaults to `"More Articles"`.
- **`buttonLink` (optional)**: The URL to navigate when the call-to-action button is clicked. Defaults to `'/blogs'`.

### Customization Options

The `MoreArticles` component offers flexibility in layout, appearance, and content. You can adjust the section title, button label, and button link based on the context or purpose of the article section.

Example usage:

```tsx
<MoreArticles
  articles={articles}
  header="Latest in Technology"
  buttonText="Explore More"
  buttonLink="/technology"
/>;
```
