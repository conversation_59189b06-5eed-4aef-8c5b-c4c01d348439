---
title: "AddOnsWrapper"
description: "The AddOnsWrapper component is a dynamic, reusable wrapper that renders a list of add-on options for users to select or deselect. It handles the selection logic and passes data to individual AddOnCard components."
---

# AddOnsWrapper

The AddOnsWrapper component is a dynamic, reusable wrapper that renders a list of add-on options for users to select or deselect. It handles the selection logic and passes data to individual AddOnCard components.

### Import Directions

```tsx filename="From any component"
import AddOnsWrapper from '@/components/Product/AddOnsWrapper';
```

### AddOnsWrapper Usage

The AddOnsWrapper component accepts the following props:

| **Prop**     | **Type**                                                                                | **Required** | **Default Value** |
|:-------------|:----------------------------------------------------------------------------------------|:-------------|:------------------|
| `addOnsTitle`| `string`                                                                                | Yes          | N/A               |
| `addOns`     | `{ id: string, name: string, price: number, description: string, image: string }[]`     | Yes          | N/A               |

```tsx
const addOnsData = [
  {
    id: '1',
    name: 'Extra Storage',
    price: 9.99,
    description: 'Get extra 50GB storage for your plan.',
    image: '/assets/storage.png',
  },
  {
    id: '2',
    name: 'Premium Support',
    price: 19.99,
    description: '24/7 premium support for any issue.',
    image: '/assets/support.png',
  },
];

<AddOnsWrapper addOnsTitle="Available Add-Ons" addOns={addOnsData} />;
```

### Selection Logic

The AddOnsWrapper component manages the selection of add-ons internally using React's `useState` hook. The following methods are available:

- **`onSelectAddOn(id: string)`**: Toggles the selection of an add-on by its `id`.
- **`isSelected(id: string)`**: Returns `true` if the add-on with the given `id` is currently selected.

### `AddOnCard` Component

Each add-on is displayed using the `AddOnCard` component. It can also be used outside the `AddOnsWrapper` component. This component receives the following props:

- `addOn` (required): The add-on data, which includes `id`, `name`, `price`, `description`, and `image`.
- `onSelect` (required): A function to toggle the selection state of the add-on.
- `selected` (required): A boolean indicating whether the add-on is selected.

Example usage:

```tsx
import AddOnCard from '@/components/Product/AddOnCard';

<AddOnCard addOn={addOn} onSelect={onSelectAddOn} selected={isSelected(addOn.id)} />
```

### Managing Multiple Add-Ons

With the AddOnsWrapper component, you can easily manage multiple add-ons by passing an array of add-on objects. The selection of add-ons is managed within the component, and you can customize the display based on user interactions.

