# Accordions

Accordions are a way to manage content that is broken down into sections that can be expanded or collapsed. They are useful for managing content that is too long to display all at once, but that users may want to see all at once.

### Self Controlled Accordion Usage

```jsx
import Accordion from '@components/Generic/Accordion'

 <Accordion title={title} open>
    <div>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</div>
  </Accordion>
```

### Signle Opened Accordion Usage

```jsx
'use client'

import { SingleOpenedAccordion } from '@/components/Generic/Accordion'
import Container from '@/components/layout/Container'
import Wrapper from '@/components/layout/Wrapper'
import { useSingleAccordion } from '@/hooks/useSingleAccordion'

const Page = () => {
  const accordionState = useSingleAccordion('FirstAccordion')
  return (
    <Container theme="cream">
      <Wrapper size="lg">
        <SingleOpenedAccordion {...accordionState} title="FirstAccordion">
          <div>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</div>
        </SingleOpenedAccordion>

        <SingleOpenedAccordion {...accordionState} title="Second">
          <div>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</div>
        </SingleOpenedAccordion>
        <SingleOpenedAccordion {...accordionState} title="Third">
          <div>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</div>
        </SingleOpenedAccordion>
      </Wrapper>
    </Container>
  )
}

export default Page
```