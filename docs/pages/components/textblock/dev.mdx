# TextBlock Component

The TextBlock component is a simple component that allows you to render text in a block format.


### Props

```tsx
type TextBlockProps = {
  theme?: ThemeColors;
  children: React.ReactNode;
  dialogTriggerProps?: DialogTriggerProps
}

//Just for reference
type DialogTriggerProps = {
  id: string
  text: string
  variant?: CallToActionVariants
  theme?: ThemeColors
  size?: 'small' | 'round' | 'inline'
};

//Just for reference
export type CallToActionVariants = 'primary' | 'secondary' | 'tertiary' | 'underlined' | 'transparent';
```

### Usage

```tsx
<TextBlock
  theme="cream"
  dialogTriggerProps={{
    id: 'dialog1',
    text: 'Learn More',
    variant: 'underlined',
    size: 'small',
  }}>
    <strong>Unlock Cyber Savings:</strong> Save up to 20% with our best savings of the year
</TextBlock>
```