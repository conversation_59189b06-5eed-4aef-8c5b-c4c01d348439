---
metaTitle: ZPattern Component Contentful Integration
metaDescription: A guide to managing the ZPattern component in Contentful, including structure, usage, and customization.
---

# ZPattern Component Contentful Integration

The **ZPattern** component is a modular content section that you can configure within Contentful to create dynamic and visually appealing layouts. This component supports rich media content, flexible layouts, and call-to-action blocks, making it a versatile tool for building engaging pages.

## Content Structure Overview

The **ZPattern** component in Contentful is organized into several key blocks, each serving a distinct purpose within the layout. Below is a breakdown of the structure:

### Page Sections: Content (ZPattern)

This is the primary container for the ZPattern component, encompassing all content blocks and settings. Within this section, various blocks and tokens are defined to build the component's layout.

### Block: Content (Lorem Ipsum Dolor)

- **Purpose**: This block is used to add content sections with text, images, and other media.
- **Example Content**:
  - **Title**: *Lorem Ipsum Dolor*
  - **Media**:
    - **Ceramic Cookware Set - Gray - Hero** (Image)
    - **How to Cook with Caraway** (Image)

### Block: Call to Action (Button)

- **Purpose**: This block is dedicated to call-to-action buttons, providing users with interactive elements that can lead to other pages or actions.
- **Settings**:
  - **Button Text**: The text displayed on the button (e.g., "Learn More", "Buy Now").
  - **Link**: The URL or action that the button will trigger.
  - **Variant**: The style of the button, which can be customized (e.g., primary, secondary).

### Token Settings (Settings)

- **Purpose**: Tokens are reusable settings that can be applied across multiple blocks. These typically include design tokens such as colors, spacing, and typography.
- **Example Settings**:
  - **Spacing**: Control the padding and margins around content.
  - **Color Schemes**: Define the color theme for the block.
  - **Typography**: Set the font styles and sizes for headers and body text.

## How to Use

### Creating a New ZPattern Section

1. **Navigate** to the **Page Sections: Content (ZPattern)** in Contentful.
2. **Add a Block**:
   - Choose either **Content** or **Call to Action** depending on the desired layout.
   - Fill in the relevant fields such as the title, media, and button settings.

### Configuring Blocks

- **Content Blocks**: 
  - Insert the relevant media (images or videos) and text.
  - Ensure that the media is correctly linked to its content block.

- **Call to Action Blocks**:
  - Define the button’s text, link, and appearance.
  - Adjust token settings to match the overall design language of the page.

### Managing Token Settings

1. **Navigate** to the **Token Settings (Settings)** block within the ZPattern section.
2. **Adjust**:
   - Spacing values to control the layout’s flow.
   - Colors to match the brand’s theme.
   - Typography to ensure consistency across the section.

## Tips for Developers

- **Reusability**: Leverage token settings to maintain consistency across different ZPattern sections. This reduces redundancy and ensures a cohesive design.
- **Preview**: Always preview the content after making changes in Contentful to ensure the layout appears as expected on the frontend.
- **Customization**: Use the variant property to toggle between different layouts (e.g., reversed or default) and make your content more dynamic.

