---
metaTitle: ZPattern Component
metaDescription: A flexible layout component with media support and customizable layout options.
sourcePath: components/ZPattern
---



## API Reference

### Props

| Prop          | Type                                               | Default | Description                                                                                                                                                   |
| ------------- | -------------------------------------------------- | ------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **header**    | `string?`                                          | `''`    | The main heading text for the component.                                                                                                                      |
| **subheader** | `string?`                                          | `''`    | The subheading text for the component.                                                                                                                        |
| **layout**    | `"Layout 1" \| "Layout 2" \| string?`              | `''`    | Determines the layout structure of the component. Can switch between default and reversed layouts.                                                            |
| **media**     | `string \| object?`                                | `null`  | The media file to display. Supports both images and videos.                                                                                                   |
| **variant**   | `"default" \| "reversed" \| ''`                    | `''`    | Variant of the layout, aligning content differently. Affects the order of media and content blocks.                                                           |
| **button**    | `{ text: string; href: string; variant: string; }` | `null`  | An optional button object. Includes button text, link, and variant for styling.                                                                                |
| **globalLayout** | `string?`                                       | `''`    | A global layout override that can affect padding and spacing across the component.                                                                            |

### Example Usage

```jsx

Layout Variants
Layout 1: Default content layout with media on one side and text on the other.
Layout 2: Reversed layout where the order of media and content is switched.

Media Support
Images: Supports various image formats. Automatically adjusts to responsive sizes.
Videos: Supports MP4 videos, with autoplay, mute, and loop options by default.

Styling with Stylex
The ZPattern component uses stylex for styling. Here are some key style properties used:

imgContainer: Controls the styling for the media container, including size and object fit.
contentContainer: Manages the grid layout for media and content blocks, adapting to screen sizes.
bodyContainer: Styles the content block, managing spacing and alignment.
paddingContainer: Applies consistent padding across the component, with adjustments for different screen sizes.
