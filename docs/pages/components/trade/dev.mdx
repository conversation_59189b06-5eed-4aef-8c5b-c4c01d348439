---
title: "Trade"
description: "The Trade component is a flexible and customizable component that displays a header, body content, an optional call-to-action button, and additional content with tags. It supports different themes and responsive layouts."
---

# Trade

The Trade component is a flexible and customizable component that displays a header, body content, an optional call-to-action button, and additional content with tags. It supports different themes and responsive layouts.

### Desktop View:
![Desktop](/components/trade-desktop.png)

### Mobile View:
![Mobile](/components/trade-mobile.png)

### Import Directions

```ts filename="From any component"
import Trade from '@components/Generic/Trade';
```

### Trade Usage

The Trade component accepts the following props:

| **Prop**     | **Type**                  | **Required** | **Default Value** |
|:-------------|:--------------------------|:-------------|:------------------|
| `header`     | `string`                  | Yes          | N/A               |
| `body`       | `string`                  | Yes          | N/A               |
| `cta`        | `string`                  | No           | N/A               |
| `ctaUrl`     | `string`                  | No           | `'/'`             |
| `theme`      | `ThemeColors`             | No           | `'white'`         |
| `content`    | `{ label: string; tags: string[]; }` | No | N/A        |

```ts
<Trade 
  header="Trade Header"
  body="This is the body content of the Trade component."
  cta="Learn More"
  ctaUrl="/learn-more"
  theme="dark"
  content={{
    label: "Content Label",
    tags: ["Tag 1", "Tag 2", "Tag 3"],
  }}
/>
```

### `Trade` Component

The `Trade` component provides a structured way to display content with a header, body, optional call-to-action button, and additional tagged content. The component is highly customizable and can adapt to different themes and screen sizes.

- **`header`** (required): A string representing the main header text of the component.
- **`body`** (required): A string representing the main body content of the component.
- **`cta`** (optional): A string that specifies the text for the call-to-action button.
- **`ctaUrl`** (optional): A string defining the URL the call-to-action button links to. Defaults to `'/'`.
- **`theme`** (optional): A `ThemeColors` type that applies a theme color to the component. Defaults to `'white'`.
- **`content`** (optional): An object containing additional content information, including:
  - `label`: A string for the label of the content section.
  - `tags`: An array of strings representing tags associated with the content.

### Example Usage

The example below demonstrates how to use the `Trade` component with a dark theme and additional content tags:

```ts
<Trade 
  header="Market Insights"
  body="Stay updated with the latest market trends and analysis."
  cta="Explore Now"
  ctaUrl="/explore"
  theme="dark"
  content={{
    label: "Trending Topics",
    tags: ["Stocks", "Forex", "Cryptocurrency"],
  }}
/>
```

### Managing Trade Content

The `Trade` component allows you to manage its content dynamically. You can update the `header`, `body`, `cta`, and `content` props as needed, and the component will adjust its layout and styling accordingly. The `renderTags` method is used to display tags in groups of three, with visual separators to enhance readability.
