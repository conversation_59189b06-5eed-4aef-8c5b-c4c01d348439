# Progress

Component to show the progress of a process. For contentful instructions and Screenshots [Here](/components/progress/get-started)

## Import Directions

```tsx
import Progress from '@/components/Content/Progress';
```

## Component props

See more about ThemeColors [here](/theme-colors)

```tsx
type ProgressProps = {
   header: String;
   steps: any[]; //can be array of strings, rich text, or react nodes/components
   theme?: ThemeColors;
   callToAction: CTAProps
 };

export type CallToActionVariants = 'primary' | 'secondary' | 'tertiary' | 'underlined' | 'transparent';

 type CTAProps = {
  children?: React.ReactNode
  size?: 'small' | 'round' | 'inline'
  variant?: CallToActionVariants
  onClick?: () => void
  submit?: boolean
  theme?: ThemeColors
  href?: string
  disabled?: boolean
  useArrow?: boolean
  id?: string
 }
```

## Component Usage

```tsx
<Progress
      theme="black"
      header="Easy as, 1, 2, 3"
      steps={[
        'Lorem Ipsum Dolor',
        'Lorem Ipsum Dolor',
      ]}
      callToAction={{
        children: 'Lorem Ipsum Dolor',
        href: '/demo-page',
        variant: 'primary'
      }}
    />
```