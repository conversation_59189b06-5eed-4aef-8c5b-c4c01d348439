# Progress

Component to show the progress of a process.

## Screenshots

### Desktop

![](/assets/progress/desktop.png)

### Mobile


<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/progress/mobile.png" alt="" width="450"/></p>

## Contentful Usage

Just create a new or update a page and go to ```Page Sections```

##### Add a new section

Select the type ```Progress``` and the Header field with the phrase that will be **highlighted**.

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/progress/tuto-1.png" alt="" width="385"/></p>

##### Add a step

Each Block with the type ```Content``` will render a step in the progress component, and you have to fill the content field with the step value.

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/progress/tuto-2.png" alt="" width="340"/></p>

##### Add Call To Action

Just add to the blocks a new block with the type ```Call To Action``` and fill the fields according to your needs.

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/progress/tuto-3.png" alt="" width="301"/></p>

##### Settings

You change settings like **Theme** color by adding a Token Setting at Setting Option.