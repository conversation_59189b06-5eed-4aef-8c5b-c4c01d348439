# Press

The Press component features a carousel displaying brand logos and accompanying quotes. It highlights endorsements and partnerships, enhancing the credibility and appeal of the page.

## Screenshots 

### Press Desktop With Border

![Press Desktop With Border](/assets/press/press-desktop-border.png)

### Press Desktop Without Border
 
![Press Desktop Without Border](/assets/press/press-desktop-no-border.png)

### Press Desktop With Border Secondary TypeFace

![Press Desktop Without Border](/assets/press/press-desktop-secondary.png)

### Press Mobile With Border

<p class="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/press/press-mobile-border.png" alt="Press Mobile With Border" width="450"/></p>

### Press Mobile Without Border

<p class="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/press/press-mobile-no-border.png" alt="Press Mobile Without Border" width="450"/></p>

### Press Desktop Simple

![Press Desktop Simple](/assets/press/press-desktop-simple.png)

### Press Mobile Simple

<p class="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/press/press-mobile-simple.png" alt="Press Mobile simple" width="450"/></p>

#### Contentful Usage

Just create a new page and go to ```Page Sections```

##### Add a new section

Select the type "Press" and fill in the **header with the phrase** that will be <u>highlighted</u>

![Create a section](/assets/press/tuto-1.png)

##### Settings

You change settings like **Theme** color and **TypeFace** for the quote by adding a Token Setting at Setting Option.

![Create a section](/assets/press/tuto-3.png)

##### Add blocks to display slide content

Each block will represent a slide.

![Create a section](/assets/press/tuto-2.png)

##### The Content Field will be the quote.

![Create a section](/assets/press/tuto-4.png)

##### The Asset Field will be Logo.

![Create a section](/assets/press/tuto-5.png)

remember to upload the image in PNG or SVG in the color that has the best contrast for the theme you chose in the settings.

After that save and publish respectively all the elements added in contentful.