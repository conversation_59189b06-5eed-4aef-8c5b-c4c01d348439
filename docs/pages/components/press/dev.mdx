# Press

The Press component features a carousel displaying brand logos and accompanying quotes. It highlights endorsements and partnerships, enhancing the credibility and appeal of the page.

Component screenshots [here](/components/press/get-started)

### Import Directions

```ts filename="From any component"
import Press from '@components/Content/Press';
```

## Press Props

The Press component accepts the following props:

```ts

type PressProps = {
  withBorder?: boolean;
  quoteTypeFace?: TypeFaceQuote;
  theme?: ThemeColors;
  slides: PressSlide[];
  slideOptions?: EmblaOptionsType;
  sliderDelay?: number;
  sliderAutoPlay?: boolean;
  header: string;
  ratingProps: RatingProps;
  linkToReviews: string;
  linkLabel?: string;
}

export type TypeFaceQuote = 'primaryFontFamily' | 'secondaryFontFamily';

type PressSlide = {
  id: string;
  text: string;
  image: {
    width: number;
    height: number;
    src: string;
    isSvg: boolean;
  } | null;
}
```

### `withBorder?: boolean`
- **Type**: `boolean`
- **Optional**
- Description: Adds a border between internal elements the component if set to `true`.

### `quoteTypeFace?: TypeFaceQuote`
- **Type**: `'primaryFontFamily' | 'secondaryFontFamily'`
- **Optional**
- Description: Sets the typeface for the quote text in the component. Choose between `'primaryFontFamily'` or `'secondaryFontFamily'`.

### `theme?: ThemeColors`
- **Type**: `ThemeColors`
- **Optional**
- Description: Applies theme colors to the component. The `ThemeColors` type should define the available theme options (you may want to link or document `ThemeColors` separately).

### `slides: PressSlide[]`
- **Type**: `PressSlide[]`
- **Required**
- Description: An array of slide data to display in the component. Each slide consists of text and an optional image.

#### `PressSlide` Object

- **id**: `string` - Unique identifier for each slide.
- **text**: `string` - The text content of the slide.
- **image?**: `object | null` - The image data for the slide. If set to `null`, no image will be shown.

  - **width**: `number` - Width of the image in pixels.
  - **height**: `number` - Height of the image in pixels.
  - **src**: `string` - The source URL of the image.
  - **isSvg**: `boolean` - Whether the image is an SVG.

### `slideOptions?: EmblaOptionsType`
- **Type**: `EmblaOptionsType`
- **Optional**
- Description: Configuration options for the slider, passed to the Embla carousel.

### `sliderDelay?: number`
- **Type**: `number`
- **Optional**
- Description: Delay in milliseconds for the slider’s autoplay feature.

### `sliderAutoPlay?: boolean`
- **Type**: `boolean`
- **Optional**
- Description: If `true`, the slider will autoplay.

### `header: string`
- **Type**: `string`
- **Required**
- Description: The header text for the component.

### `ratingProps: RatingProps`
- **Type**: `RatingProps`
- **Required**
- Description: Props to configure the rating system inside the component. (Link or document `RatingProps` separately).

### `linkToReviews: string`
- **Type**: `string`
- **Required**
- Description: The URL to the reviews page.

### `linkLabel?: string`
- **Type**: `string`
- **Optional**
- Description: The label for the link that navigates to the reviews page.

#### Usage

#### Example of slider JSON

```tsx
const slides = [
  {
    id: '1',
    text: '“I tried the internet-famous Caraway cookware set and I can confirm it’s worth the hype.”',
    image: {
      width: 140,
      height: 36,
      src: '/caraway.svg',
      isSvg: true
    }
  },
  {
    id: '2',
    text: '“I tried the internet-famous Caraway cookware set and I can confirm it’s worth the hype.”',
    image: {
      width: 140,
      height: 36,
      src: '/caraway.svg',
      isSvg: true
    }
  },
]
```

### Press Usage with all props

```tsx
<Press
  withBorder
  quoteTypeFace="secondaryFontFamily"
  header="Over 65,000 Cooks Have Spoken"
  theme="babyBlue"
  linkToReviews="#"
  ratingProps={{ rating: 5 reviewCount: 10000 }}
  slides={slides}
/>
```
