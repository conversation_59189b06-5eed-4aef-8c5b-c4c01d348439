# Testimonials

Showcases user feedback and reviews in a visually appealing format.

## Import Directions

```jsx
import Tetimonials from '@/components/Content/Testimonials';
```

### Props 

```tsx
export type TestimonialSlide = {
  type: 'quote' | 'image';
  url?: string;
  rating?: number;
  content: any; //Contentful RichText
  author: string,
  logo: {
    url: string
  },
}

type TetimonialsProps = {
  theme?: ThemeColors
  header?: string;
  subheader: string;
  slides?: TestimonialSlide[];
}
```

### `theme?: ThemeColors`
- **Type**: `ThemeColors`
- **Optional**
- Description: Applies theme colors to the component. The `ThemeColors` type should define the available theme options (you may want to link or document `ThemeColors` separately).

### `header?: string`
- **Type**: `string`
- **Optional**
- Description: The main header text for the testimonials section. If not provided, the header will be omitted.

### `subheader: string`
- **Type**: `string`
- **Required**
- Description: The subheader text for the testimonials section.

### `slides?: TestimonialSlide[]`
- **Type**: `TestimonialSlide[]`
- **Optional**
- Description: An array of testimonial slides. Each slide can either be a quote or an image, with associated content and author information.

#### `TestimonialSlide` Object

- **type**: `'quote' | 'image'` - The type of the slide, determining whether the slide will display a quote or an image.
- **url?**: `string` - The URL for the image or any media associated with the slide. This is optional and depends on the `type`.
- **rating?**: `number` - A rating associated with the testimonial (e.g., 1-5 stars). This is optional.
- **content**: `any` - The main Quote of the slide, which is the Contentful RichText Field "Quote".
- **author**: `string` - The author or the source of the testimonial.
- **logo**: `object`
  - **url**: `string` - The URL of the logo or brand associated with the testimonial.

## Example Usage

```jsx
<Tetimonials
  theme="light"
  header="Customer Testimonials"
  subheader="What our clients are saying"
  slides={[
    {
      type: 'image',
      url: '/path-to-image.jpg',
    },
    {
      type: 'quote',
      content: "This is the best service I've ever used!",
      author: 'John Doe',
      logo: { url: '/path-to-logo.png' },
      rating: 5
    },
    {
      type: 'image',
      url: '/path-to-image.jpg',
    }
  ]}
/>