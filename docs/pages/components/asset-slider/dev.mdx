# AssetSlider Component

The `AssetSlider` component is a versatile slider that can display media content (videos or images) and optional call-to-action buttons. It adapts to mobile screens by adjusting the number of slides per page.

## Props

```tsx
export type AssetSliderProps = Partial<SliderProps> & {
  theme?: ThemeColors;
  slides: MediaVideoProps[];
  header?: string;
}

//For Reference

export type SliderProps = {
  extractSlideKey?: (slide: any) => string,
  renderSlide: <T>(slide: T | any) => React.ReactNode,
  renderDotsButtons?: RenderDotsFunction,
  renderPrevNextButtons?: RenderPrevNextButtons,
  dotsStyles?: {},
  selectedDotsStyles?: {},
  slidesPerPage?: number,
  slides: any[],
  options?: EmblaOptionsType,
  plugins?: EmblaPluginType[],
  renderDotsWithArrows?: boolean,
  renderArrowsCondition?: boolean,
}

export type MediaVideoProps = {
  name?: string;
  media?: any;
  autoPlay?: boolean;
  loop?: boolean;
  muted?: boolean;
  playIcon?: boolean;
  playsInline?: boolean;
  videoHeight?: string;
  imageHeight?: string;
  imageHeightMobile?: string;
}
```

### `theme`

- **Type:** `ThemeColors` (optional)
- **Description:** Defines the theme colors to be applied to the slider.

### `slides`

- **Type:** `MediaVideoProps[]`
- **Description:** An array of media objects to be displayed as slides. Each object should adhere to the `MediaVideoProps` interface.

### `header`

- **Type:** `string` (optional)
- **Description:** An optional header to display above the slider.

### `slidesPerPage`

- **Type:** `number` (optional)
- **Default:** `3`
- **Description:** The number of slides to display per page. The default value is `3`, but it will adjust to `1.35` on mobile devices.

### `extractSlideKey`

- **Type:** `(slide: any) => string` (optional)
- **Description:** A function that extracts a unique key from each slide. This key is used for identifying slides in the slider.

### `renderSlide`

- **Type:** `<T>(slide: T | any) => React.ReactNode`
- **Description:** A function that renders each slide. It takes a slide object and returns a React node to display. This is a required prop.

### `renderDotsButtons`

- **Type:** `RenderDotsFunction` (optional)
- **Description:** A function for rendering dot navigation buttons. Customizes the appearance or behavior of dot indicators.

### `renderPrevNextButtons`

- **Type:** `RenderPrevNextButtons` (optional)
- **Description:** A function for rendering previous and next navigation buttons. Allows customization of navigation controls.

### `dotsStyles`

- **Type:** `object` (optional)
- **Description:** Styles for the dot navigation buttons. Use this to apply custom styles to the dots.

### `selectedDotsStyles`

- **Type:** `object` (optional)
- **Description:** Styles for the selected (active) dot. Customizes the appearance of the active indicator.

### `slidesPerPage`

- **Type:** `number` (optional)
- **Default:** `3`
- **Description:** Number of slides to display per page. Adjusts how many slides are shown at once in the slider view.

### `slides`

- **Type:** `any[]`
- **Description:** An array of slides to be displayed. Each element represents a slide.

### `options`

- **Type:** `EmblaOptionsType` (optional)
- **Description:** Configuration options for the Embla slider instance. Customizes the slider's behavior and features.

### `plugins`

- **Type:** `EmblaPluginType[]` (optional)
- **Description:** Array of plugins to extend or modify the functionality of the slider.

### `renderDotsWithArrows`

- **Type:** `boolean` (optional)
- **Description:** Whether to render dot indicators with arrow controls. When `true`, dots are accompanied by arrows for navigation.

### `renderArrowsCondition`

- **Type:** `boolean` (optional)
- **Description:** Condition for rendering previous and next arrows. When `true`, arrows will be displayed based on the condition provided.

---

# MediaVideo Component Props

The `MediaVideo` component allows customization of media playback and appearance. Below is a detailed description of each prop available for `MediaVideoProps`.

## `MediaVideoProps`

### `name`

- **Type:** `string` (optional)
- **Description:** The name or title of the media. Used for display purposes or metadata.

### `media`

- **Type:** `any` (optional)
- **Description:** The media source, such as a URL or file reference. Determines what media (video or image) will be played or displayed.

### `autoPlay`

- **Type:** `boolean` (optional)
- **Description:** If `true`, the media will automatically start playing when loaded.

### `loop`

- **Type:** `boolean` (optional)
- **Description:** If `true`, the media will loop continuously after reaching the end.

### `muted`

- **Type:** `boolean` (optional)
- **Description:** If `true`, the media will be muted by default.

### `playIcon`

- **Type:** `boolean` (optional)
- **Description:** If `true`, a play icon will be displayed on top of the media to prompt users to start playback.

### `playsInline`

- **Type:** `boolean` (optional)
- **Description:** If `true`, the media will play inline on mobile devices instead of fullscreen.

### `videoHeight`

- **Type:** `string` (optional)
- **Description:** Specifies the height of the video. Accepts CSS units like `px`, `%`, `vh`, etc.

### `imageHeight`

- **Type:** `string` (optional)
- **Description:** Specifies the height of the image. Accepts CSS units like `px`, `%`, `vh`, etc.

### `imageHeightMobile`

- **Type:** `string` (optional)
- **Description:** Specifies the height of the image specifically for mobile devices. Allows responsive adjustments.

## Usage

Here's an example of how to use the `AssetSlider` component:

```jsx
import AssetSlider from '@/components/AssetSlider';
import { ThemeColors } from '@/app/themeThemes.stylex';

const slides = [
  {
    id: 'slide1',
    mediaSrc: 'video.mp4',
    callToAction: { text: 'Learn More', href: '/learn-more' }
  },
  {
    id: 'slide2',
    mediaSrc: 'image.jpg'
  }
];

const MyComponent = () => (
  <AssetSlider
    theme={ThemeColors.default}
    header="Featured Media"
    slides={slides}
    slidesPerPage={2}
  />
);
