# Asset Slider

The Image Slideshow component is designed to display a sequence of images in a carousel format.

## Screenshots 

### Desktop Version

![Desktop Version](/assets/asset-slider/desktop.png)

### Mobile Version

![Mobile Version](/assets/asset-slider/mobile.png)

### Contentful Usage

##### To Add new Asset to <PERSON>lider

Go to **Block** Sections and add a new block of type ```Content```. Each block will be a slide.

##### Slider Fields

Inside the block you can add the following fields:

- ✅ To insert Image of vídeo just set a new asset (You can only add one asset per slide).
- ✅ To insert Call to Action just add a new references block of type ```Call to Action``` and fill the fiels as your needs.

##### Slider Settings

- ✅ To theme select the option you want.