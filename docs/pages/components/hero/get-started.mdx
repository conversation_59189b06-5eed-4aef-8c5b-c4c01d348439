# Hero

The Page Hero component serves as a prominent introductory section at the top of a page, with two initial layout options. It features visuals (image/video), text & CTAs to captivate users and highlight key messages or calls to action.

## Features

- Can take in images or videos
- Video auto-plays, no sounds, loops automatically
- Video can pause and play
- Pausing video will also slider
- Header, Subheader, Body, 2 CTAS or more, Disclaimer
- 6 Layouts: Center, Left, Right , PLP Center, PLP Left, PLP Right

## Screenshots of all Variants

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl"> Hero Center with Typeface Primary | Desktop <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 1</span></h3>

![](/assets/hero/Center-with-Typeface-Primary.png)

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl">Hero Center with Typeface Primary | Mobile <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 1</span></h3>

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/Center-with-Typeface-Primary-Mobile.png" alt="" width="400"/></p>

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl">Hero Left with Typeface Secondary | Desktop <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 2</span></h3>

![](/assets/hero/Left-with-Typeface-Secondary.png)

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl"> Hero Left with Typeface Secondary | Mobile  <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 2</span></h3>

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/Left-with-Typeface-Secondary-Mobile.png" alt="" width="400"/></p>

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl"> Hero Right with Typeface Secondary | Desktop <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 3</span></h3>

![](/assets/hero/Right-with-Typeface-Secondary.png)

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl"> Hero Right with Typeface Secondary | Mobile <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 3</span> </h3>

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/Right-with-Typeface-Secondary-Mobile.png" alt="" width="400"/></p>

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl"> Hero PLP Center Aligned <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 4</span></h3>

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/plp-center.png" alt=""/></p>

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl"> Hero PLP Left Aligned <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 5</span></h3>

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/plp-left.png" alt=""/></p>

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl"> Hero PLP Right Aligned <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 6</span></h3>

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/plp-right.png" alt=""/></p>

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl">Hero Left Aligned and Hidding mobile image  <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 7</span></h3>

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl"> Hero PLP Left Aligned no SPLIT Image <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 8</span></h3>

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/hero-left-aligned-no-splitlayout-8.png" alt=""/></p>

<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl"> Hero PLP left Aligned no cropped image <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 9</span></h3>

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/hero-no-crop-layout-9.png" alt=""/></p>


<h3 class="nx-font-semibold nx-tracking-tight nx-text-slate-900 dark:nx-text-slate-100 nx-mt-8 nx-text-2xl"> Hero PLP Right Aligned no cropped image <span style={{ backgroundColor: 'orange', padding: '3px 8px' }}>Layout 10</span></h3>

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/hero-no-crop-right-layout-10.png" alt=""/></p>

## Contentful Usage

Just create a new or update a page and go to ```Page Sections```

##### Add a new section

Fill the name and Select the Type ```Hero```.

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/tuto-1.png" alt="" width="350"/></p>

##### Add new Hero / Slider


Go to **Block** Sections and add a new block of type ```Content```. Each block will be a slide in the slider component as long as there is more than one block. In case of only one block present the component will not behave like a slide (just as a static hero).

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/tuto-2.png" alt="" width="626"/></p>

##### Slider Fields

Inside the block you can add the following fields:

- ✅ To insert Image of vídeo just set a new asset. (Adding two Images the image container will display both images in a row like PLP component, see the layout option to more datails)
- ✅ To insert Call to Action just add a new references block of type ```Call to Action``` and fill the fiels as your needs.
- ✅ To insert Subheader just add a new block of type ```Content``` and fill the content field.

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/tuto-3.png" alt="" width="466"/></p>

##### Slider Settings

- ✅ To change Font Family and the theme select the option you want.
<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/hero/tuto-4.png" alt="" width="466"/></p>

##### Layout Option

- Select Layout 1 for Center alignment

Note: Layout 1 > If you add one image the image will be the background, if you add two images the first will be splitted for the left and the second for the right.

- Select Layout 2 for Left alignment
- Select Layout 3 for Right alignment
- Select Layout 4 for PLP Center alignment
- Select Layout 5 for PLP Left alignment
- Select Layout 6 for PLP Right alignment
- Select Layout 7 for Left alignment and Hidding mobile image
- Select Layout 8 for PLP Left alignment no SPLIT Image
- Select Layout 9 for PLP left alignment no cropped image
- Select Layout 10 for PLP right alignment no cropped image
