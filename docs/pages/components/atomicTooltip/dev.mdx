---
title: "AtomicTooltip"
description: "AtomicTooltip is an interactive component that displays a tooltip with dynamic content. It allows the user to toggle visibility of the tooltip by clicking on a button, with smooth animations for showing and hiding the content."
---

# AtomicTooltip

The `AtomicTooltip` component is a reusable and interactive UI element that displays a tooltip with customizable content. It offers a simple toggle mechanism for showing and hiding the tooltip and includes dynamic animations for smooth transitions.

### Import Directions

```ts filename="From any component"
import AtomicTooltip from '@/components/Content/AtomicLevelTooltip';
```

### AtomicTooltip Usage

The `AtomicTooltip` component accepts the following props:

- **`title` (required)**: The title text that will be displayed inside the tooltip.
- **`description` (required)**: A text description that will appear below the title.
- **`theme` (required)**: The color theme of the tooltip, based on the `ThemeColors` type.

```ts
<AtomicTooltip
  title="Non-Stick Coating"
  description="Our non-stick cookware offers effortless cooking and quick cleanup, designed with a durable surface that ensures your food slides off with ease."
  theme="gray200"
/>
```

### Props for `AtomicTooltip`

- **`title`**: The title of the tooltip, displayed prominently within the tooltip content.
- **`description`**: A detailed description shown beneath the title inside the tooltip.
- **`theme`**: A string defining the color theme of the tooltip.

### How It Works

The `AtomicTooltip` component wraps a button with an interactive plus icon (`+`) and a tooltip that can be toggled by the user. When the button is clicked, the tooltip's visibility toggles between hidden and visible states.

The button's icon changes depending on the state:
- When the tooltip is hidden, a plus icon (`+`) is displayed.
- When the tooltip is visible, a minus icon (`-`) replaces the plus icon.

The tooltip appears with smooth animations and adapts its position based on the component’s placement.

### State Management

The component uses the `useState` hook to manage the visibility of the tooltip:

```ts
const [isVisible, setIsVisible] = useState(false);
```

The `isVisible` state determines whether the tooltip is displayed or hidden, and it is toggled when the button is clicked:

```ts
onClick={() => setIsVisible(!isVisible)}
```

### Example Usage

```ts
<AtomicTooltip
  title="Eco-Friendly Material"
  description="Crafted from eco-friendly materials, our cookware reduces your environmental impact while delivering exceptional performance."
  theme="babyBlue"
/>
```

### Animations and Styles

The `AtomicTooltip` component includes smooth animations for toggling the tooltip's visibility. The `rotatePlus` class rotates the plus icon when toggled, and the `visible` and `hidden` classes control the opacity and display transitions of the tooltip content.

```ts
const styles = stylex.create({
  wrapper: { /* styles for the container */ },
  button: { /* styles for the interactive button */ },
  clickedButton: { /* background color change when clicked */ },
  icon: { /* styles for the icons */ },
  rotatePlus: { /* rotate plus icon when clicked */ },
  hidden: { /* opacity and display transition for hidden state */ },
  visible: { /* opacity and display transition for visible state */ },
});
```

---

# Tooltip (Child Component)

The `Tooltip` component is the child component of `AtomicTooltip`. It handles the actual display of the tooltip content, which includes a title and description, styled based on the provided theme.

### Tooltip Props

- **`title` (required)**: The title of the tooltip content.
- **`description` (required)**: The description text within the tooltip.
- **`theme` (required)**: The theme color for the tooltip, matching the parent `AtomicTooltip` theme.

### Example Usage

```ts
<Tooltip
  title="Eco-Friendly Cookware"
  description="Crafted from sustainable materials to reduce environmental impact."
  theme="green200"
/>
```

### Styling and Layout

The `Tooltip` component is styled using the `stylex` utility to control positioning, padding, and layout. It is positioned absolutely relative to the button in `AtomicTooltip` and includes responsive dimensions.

```ts
const styles = stylex.create({
  wrapper: {
    position: 'absolute',
    minWidth: '300px',
    padding: '15px',
    borderRadius: '16px',
    top: '35px',
    zIndex: '1',
  },
  title: {
    marginBottom: '8px',
  },
});
```

The `Typography` component is used for rendering both the title and description, ensuring consistent text styling and layout.

### Conclusion

The `AtomicTooltip` and `Tooltip` components provide an interactive and customizable way to display contextual information in a visually appealing manner. The tooltip's theme, content, and animations make it flexible for a variety of use cases.
