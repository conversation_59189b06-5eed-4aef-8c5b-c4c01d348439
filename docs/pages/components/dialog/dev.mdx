---
title: "Dialogs"
description: "The Dialog component is a generic, reusable modal dialog that can be triggered from any component in the application. It is designed to be flexible and customizable, with the ability to pass in different layouts and children components. With the DialogProvider and useDialogContext hook, you can easily manage multiple dialogs"
---

# Dialogs

The Dialog component is a generic, reusable modal dialog that can be triggered from any component in the application. It is designed to be flexible and customizable, with the ability to pass in different layouts and children components. With the DialogProvider and useDialogContext hook, you can easily manage multiple dialogs

### Import Directions

```ts filename="From any component"
import Dialog from '@components/Generic/Dialog';
import DialogTrigger from '@components/Generic/Dialog/DialogTrigger';
```

### Dialog Usage
The Dialog component accepts the following props:
| **Prop**     | **Type**                  | **Required** | **Default Value** |
|:-------------|:--------------------------|:-------------|:------------------|
| `id`         | `string`                  | Yes          | N/A               |
| `header`     | `string`                  | Yes          | N/A               |
| `subheader`  | `string`                  | No           | N/A               |
| `image`      | `string`                  | No           | N/A               |
| `content`    | `string`                  | Yes          | N/A               |
| `buttons`    | `ExtendedAnchorProps[]`    | No           | N/A               |
| `disclaimer` | `string`                  | No           | N/A               |
| `theme`      | `ThemeColors`             | No           | 'white'           |
| `layout`     | `LayoutProps`             | No           | 'Layout 1'        |


```ts
<DialogTrigger id="myDialog1" text="Open Dialog 1" variant="primary" theme="navy" />

<Dialog
  id="myDialog1"
  header="Dialog 1 Header"
  subheader="Dialog 1 Subheader"
  content="This is the content of Dialog 1"
  theme="dark"
  layout="Layout 1"
/>
```

### `useDialogContext` Hook

The `useDialogContext` hook is used to interact with the dialog's context. It allows you to open and close dialogs programmatically from any component in your application. The hook returns the following methods:

- `registerDialog(id: string, dialogRef: React.RefObject<HTMLDialogElement>)`: Registers a dialog with a unique ID.
- `triggerDialog(id: string)`: Toggles the open/close state of a dialog by ID.
- `closeModal(id: string)`: Closes the dialog with the specified ID.

To use this hook, you must ensure that your components are wrapped within the `DialogProvider`.


### `DialogTrigger` Component
The `DialogTrigger` component is a convenient way to create a trigger for opening a dialog. It accepts the following props:

- `id` (required): The ID of the dialog to be triggered.
- `text` (required): The text to be displayed within the trigger button.
- `variant` (optional): The style variant for the button. Can be 'primary' or 'secondary'.
- `theme` (optional): The color theme for the button. Matches the dialog's theme.

Example usage:

```ts
<DialogTrigger id="myDialog1" text="Open Dialog" variant="primary" theme="dark" />
```

### Managing Multiple Dialogs
With the `DialogProvider`, you can manage multiple dialogs across your application. Each dialog must have a unique `id`, and you can use multiple `DialogTrigger` components to trigger different dialogs.


