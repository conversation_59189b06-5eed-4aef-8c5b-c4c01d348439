---
title: "Countdown"
description: "The Countdown component displays the remaining time until a specified end date. It supports callbacks when the countdown completes and automatically adjusts for the user's timezone."
---

# Countdown

The Countdown component is designed to display the time remaining until a specified end date. It automatically adjusts for Eastern Time (ET) and allows you to trigger a callback when the countdown is complete.

### Import Directions

```ts filename="From any component"
import Countdown from '@components/Generic/Countdown';
```

### Countdown Usage

The Countdown component accepts the following props:

| **Prop**     | **Type**      | **Required** | **Default Value** |
|:-------------|:--------------|:-------------|:------------------|
| `endDate`    | `string`      | Yes          | N/A               |
| `onComplete` | `() => void`  | No           | N/A               |

- `endDate`: The date and time at which the countdown should end, provided in a string format.
- `onComplete`: An optional callback function that will be executed when the countdown reaches zero.

### Example Usage

```ts
<Countdown endDate="2024-12-31" onComplete={() => console.log('Countdown Complete!')} />
```

### Features

- **Timezone Adjustment**: The countdown automatically adjusts the displayed time to Eastern Time (ET), ensuring consistency across different timezones.
- **Threshold Logic**: When the countdown exceeds three days, it shows the remaining time in days; otherwise, it switches to a more detailed format, displaying hours, minutes, and seconds.
- **Completion Callback**: The `onComplete` function is triggered when the countdown finishes, allowing for additional actions when time runs out.

### Code Example

```ts
import Countdown from '@components/Generic/Countdown';

const handleComplete = () => {
  console.log('The countdown has completed!');
};

<Countdown endDate="2024-12-31T23:59:59" onComplete={handleComplete} />;
```

In this example, the countdown runs until New Year's Eve 2024 and logs a message when the countdown completes.

### Time Formatting Logic

- **Days Display**: If the remaining time is more than three days, the countdown displays the time in the format of `X days`.
- **Detailed Time Display**: When the time is less than three days, it switches to a more precise format showing `hours`, `minutes`, and `seconds`.

This flexibility ensures that users see the appropriate level of detail depending on how much time is left.
