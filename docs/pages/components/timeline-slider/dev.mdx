---
title: "TimelineSlider"
description: "The TimelineSlider component is a customizable, responsive slider that allows users to navigate through a timeline of slides. Each slide contains an image and accompanying text, with controls for navigation and progress tracking."
---

# TimelineSlider

The TimelineSlider component is a customizable, responsive slider that allows users to navigate through a timeline of slides. Each slide contains an image and accompanying text, with controls for navigation and progress tracking. It includes Next/Prev buttons, progress indicators, and slide dots for a smooth user experience.

### Import Directions

```ts filename="From any component"
import TimelineSlider from '@components/TimelineSlider';
```

### TimelineSlider Usage

The `TimelineSlider` component accepts the following props:

| **Prop**  | **Type**                                                      | **Required** | **Default Value** |
|:----------|:--------------------------------------------------------------|:-------------|:------------------|
| `slides`  | `{ id: string, media: string, header: string, description: string }[]` | Yes          | `[]`              |

```ts
const slides = [
  {
    id: '1',
    media: '/images/slide1.jpg',
    header: 'Slide 1 Header',
    description: 'This is the description for slide 1.',
  },
  {
    id: '2',
    media: '/images/slide2.jpg',
    header: 'Slide 2 Header',
    description: 'This is the description for slide 2.',
  },
];

<TimelineSlider slides={slides} />;
```

### TimelineSlider Component Structure

- **`slides` (required)**: An array of slide objects, where each slide contains:
  - **`id`**: A unique identifier for the slide.
  - **`media`**: The image URL or path to be displayed.
  - **`header`**: A string that serves as the slide title.
  - **`description`**: A string that describes the slide content.

### Navigation and Progress Indicators

The TimelineSlider provides the following built-in features for enhanced navigation:

- **Next/Prev Buttons**: Users can scroll through the slides using the Next and Prev buttons.
- **Progress Bar**: A progress bar indicates the current slide’s position relative to the total number of slides.
- **Slide Dots**: Dot indicators represent each slide, and users can click on a dot to navigate directly to the corresponding slide.

Example usage:

```ts
<TimelineSlider slides={slides} />;
```

### Responsive Design

The TimelineSlider is optimized for different screen sizes, with a responsive layout that adjusts for mobile screens:

- On desktop, slides are arranged in a row with text on the left and an image on the right.
- On mobile, the layout switches to a column format, placing the image below the text for better readability.

### Customization Options

You can easily customize the slider by adjusting the slide content or layout. The progress bar and dot indicators offer visual feedback on the user’s current position within the slide sequence.
