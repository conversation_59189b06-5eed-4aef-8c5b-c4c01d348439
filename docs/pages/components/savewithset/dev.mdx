---
title: "SaveWithSet Component"
description: "The SaveWithSet component renders a product card with customizable content, images, and buttons. It uses Stylex for styling and is flexible across different screen sizes."
---

# SaveWithSet Component

The `SaveWithSet` component is a customizable product card that displays various information such as title, content, a list of features, and buttons. It also uses responsive styles for different screen sizes and includes a rating system.

### Import Directions

```tsx filename="From any component"
import SaveWithSet from '@components/SaveWithSet';

| Prop              | Type         | Required | Default Value           |
|-------------------|--------------|----------|-------------------------|
| header            | string       | No       | 'Product Card'          |
| content           | string       | No       | 'Product Card Content'  |
| list              | string[]     | No       | ['item 1', 'item 2', 'item 3'] |
| button            | string       | No       | 'Add to Cart'           |
| itemImage         | string       | No       | 'https://via.placeholder.com/150' |
| itemTitle         | string       | No       | 'Product Title'         |
| itemPrice         | string       | No       | '$9.99'                 |
| itemComparePrice  | string       | No       | '$19.99'                |
| itemRating        | number       | No       | 4.8                     |
| theme             | ThemeColors  | No       | 'white'                 |

Responsive Styles
| Class            | Description                                      |
|------------------|--------------------------------------------------|
| headerDesktop    | Displays the header on desktop devices only      |
| headerMobile     | Displays the header on mobile devices only       |
| mainContainer    | Adjusts the layout and spacing for responsiveness|
| listContainer    | Container for the list of items                  |
| itemImage        | Responsive styling for product image             |
| cta              | Call-to-action button with responsive styles     |
| ratingText       | Styling for the rating text below the product card|

Features
Flexible Layout: The SaveWithSet component adapts to both desktop and mobile layouts.
Customizable: You can customize the text, images, and call-to-action button easily with props.
Themeable: It supports multiple themes through ThemeColors.
Content List: Optionally display a list of features or benefits with icons.
Rating System: The component includes a built-in rating system with customizable review count and star rating display.