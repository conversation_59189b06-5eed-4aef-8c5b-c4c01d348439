Here’s the updated documentation using cookware-related text instead of "Lorem ipsum":

```md
---
title: "WhySlider"
description: "The WhySlider component is a dynamic slider that displays items in a flexible, customizable layout. It is designed to be responsive, supporting both large and small screen layouts, with an easy-to-use API for adding slider items. You can manage the active slide state using internal state management."
---

# WhySlider

The `WhySlider` component is a dynamic, reusable slider designed to display content-rich slider items. It supports both large and small screen layouts and allows for customizable themes for each slide. The component can be customized with different items, each containing an image, title, description, and text.

### Import Directions

```ts filename="From any component"
import WhySlider from '@/components/Content/WhySlider';
```

### WhySlider Usage

The `WhySlider` component accepts the following props:

- **`theme` (required)**: Defines the overall theme of the slider. Accepts values of the `ThemeColors` type.
- **`items` (required)**: An array of items to be displayed in the slider. Each item must have the following structure:
  - **`image` (required)**: A URL for the image to be displayed within the slider item.
  - **`description` (required)**: A text description of the slider item.
  - **`title` (required)**: The title of the slider item.
  - **`text` (required)**: Additional supporting text for the slider item.
  - **`theme` (required)**: Defines the individual theme for each slider item.

```ts
<WhySlider
  theme="gray200"
  items={[
    {
      image: '/assets/cookware-non-stick.jpg',
      description: `Our non-toxic coating is designed to resist scratches and keep your food safe from harmful chemicals. Enjoy healthier cooking without the risk of toxins leaching into your meals.`,
      title: 'Non-Toxic Coating',
      text: 'Designed for safe cooking',
      theme: 'gray200',
    },
    {
      image: '/assets/cookware-non-stick.jpg',
      description: `Say goodbye to food sticking to your pans! Our non-stick cookware ensures easy cooking and quick clean-up, with durable coatings that last for years.`,
      title: 'Non-Stick',
      text: 'Effortless cooking and cleaning',
      theme: 'marigold',
    },
    {
      image: '/assets/cookware-eco-friendly.jpg',
      description: `Crafted from eco-friendly materials, our cookware helps you create meals while reducing your environmental impact. Cook sustainably with our eco-friendly range.`,
      title: 'Eco-Friendly',
      text: 'Sustainable materials for green living',
      theme: 'babyBlue',
    },
  ]}
/>
```

### Slider Item Structure

Each slider item consists of the following properties:

- **`image`**: A string representing the URL of the image to display.
- **`description`**: A text description of the item, shown in both large and small views.
- **`title`**: The title of the item, shown prominently in both large and small slider views.
- **`text`**: Supplementary text to provide additional details.
- **`theme`**: A string defining the color theme for each item.

### Layouts

The `WhySlider` component adapts its layout depending on the screen size:
- **Large screen layout**: Displays a horizontally scrollable slider where items expand when hovered.
- **Small screen layout**: Displays the slider items vertically, with tabs for navigating between items.

### `SliderItem` Component

The `SliderItem` is the child component used within `WhySlider` for displaying each item on larger screens. It handles both the expanded and collapsed states for slider items.

Props for `SliderItem`:
- **`image`**: The URL of the image.
- **`description`**: The description of the item.
- **`title`**: The title of the item.
- **`text`**: Supplementary text.
- **`theme`**: The color theme.
- **`isOpen`**: A boolean indicating whether the item is expanded.
- **`setActiveIndex`**: A function to set the active index of the expanded item.
- **`index`**: The index of the item.

### `SmallSliderItem` Component

The `SmallSliderItem` is responsible for rendering slider items in a vertical format for small screens.

Props for `SmallSliderItem`:
- **`image`**: The URL of the image.
- **`description`**: The description of the item.
- **`title`**: The title of the item.
- **`theme`**: The color theme.
- **`index`**: The index of the item.
- **`activeIndex`**: The index of the currently active item.

### `SmallSliderTab` Component

The `SmallSliderTab` component is used in small screens to render navigation tabs for each slider item.

Props for `SmallSliderTab`:
- **`title`**: The title of the item.
- **`theme`**: The color theme.
- **`index`**: The index of the item.
- **`setActiveIndex`**: A function to set the active index.

### Managing Active Slider

The `WhySlider` component maintains an internal state using the `useState` hook to manage which slider item is active. By default, the first item (index 0) is active, and hovering over other items or clicking on tabs updates the active index accordingly.

```ts
const [activeIndex, setActiveIndex] = useState(0);
```

### Example Usage

```ts
<WhySlider
  theme="gray200"
  items={[
    {
      image: '/assets/cookware-non-stick.jpg',
      description: `Our non-toxic coating is designed to resist scratches and keep your food safe from harmful chemicals. Enjoy healthier cooking without the risk of toxins leaching into your meals.`,
      title: 'Non-Toxic Coating',
      text: 'Designed for safe cooking',
      theme: 'gray200',
    },
    {
      image: '/assets/cookware-non-stick.jpg',
      description: `Say goodbye to food sticking to your pans! Our non-stick cookware ensures easy cooking and quick clean-up, with durable coatings that last for years.`,
      title: 'Non-Stick',
      text: 'Effortless cooking and cleaning',
      theme: 'marigold',
    },
    {
      image: '/assets/cookware-eco-friendly.jpg',
      description: `Crafted from eco-friendly materials, our cookware helps you create meals while reducing your environmental impact. Cook sustainably with our eco-friendly range.`,
      title: 'Eco-Friendly',
      text: 'Sustainable materials for green living',
      theme: 'babyBlue',
    },
  ]}
/>
```

With the `WhySlider` component, you can create responsive, content-rich sliders that adapt to various screen sizes and offer customizable themes for a polished and flexible user experience.
```

In this version, the placeholder text has been replaced with cookware-related descriptions that highlight different features of cookware items.