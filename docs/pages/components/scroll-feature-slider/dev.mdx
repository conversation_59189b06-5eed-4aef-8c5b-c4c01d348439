---
title: "ScrollFeatureSlider"
description: "The ScrollFeatureSlider component is a dynamic and interactive slider that highlights content with a scrolling effect. It supports media elements, adjustable content opacity, and an animated progress indicator."
---

# ScrollFeatureSlider

The ScrollFeatureSlider component is a dynamic and interactive slider that highlights content with a scrolling effect.

### Import Directions

```ts filename="From any component"
import ScrollFeatureSlider from '@components/Generic/ScrollFeatureSlider';
```

### ScrollFeatureSlider Usage

The ScrollFeatureSlider component accepts the following props:

| **Prop**     | **Type**                  | **Required** | **Default Value** |
|:-------------|:--------------------------|:-------------|:------------------|
| `header`     | `string`                  | Yes          | N/A               |
| `subheader`  | `string`                  | Yes          | N/A               |
| `body`       | `{ id?: number; title: string; content: string; }[]` | Yes | N/A |
| `media`      | `string`                  | Yes          | N/A               |

```ts
<ScrollFeatureSlider 
  header="Explore Features"
  subheader="Our Unique Offerings"
  body={[
    { id: 1, title: "Feature 1", content: "Description of Feature 1." },
    { id: 2, title: "Feature 2", content: "Description of Feature 2." },
    { id: 3, title: "Feature 3", content: "Description of Feature 3." }
  ]}
  media="/path/to/media/video.mp4"
/>
```

### `ScrollFeatureSlider` Component

The `ScrollFeatureSlider` component provides a visually engaging way to display content with scrolling effects. The component dynamically adjusts the opacity of content sections based on scroll progress and moves a media element (like an image or video) to create a parallax effect.

- **`header`** (required): A string representing the main header text of the slider.
- **`subheader`** (required): A string representing the secondary header text of the slider.
- **`body`** (required): An array of objects, each representing a content item with:
  - `id` (optional): A unique identifier for the item.
  - `title`: A string for the title of the content item.
  - `content`: A string for the description or content text.
- **`media`** (required): A string representing the URL or path to the media (e.g., an image or video) displayed within the slider.

### Example Usage

The example below demonstrates how to use the `ScrollFeatureSlider` component with a header, subheader, and content items:

```ts
<ScrollFeatureSlider 
  header="Discover Our Capabilities"
  subheader="Innovation at Its Best"
  body={[
    { id: 1, title: "Advanced Technology", content: "Harness cutting-edge technology for your needs." },
    { id: 2, title: "Expert Team", content: "Collaborate with top professionals in the field." },
    { id: 3, title: "Customer-Centric Approach", content: "Your satisfaction is our priority." }
  ]}
  media="/assets/videos/intro.mp4"
/>
```

### Scrolling Effects and Animations

The `ScrollFeatureSlider` component leverages Framer Motion to create smooth animations and transitions. The `useScroll` hook is used to track the scroll position and dynamically adjust the opacity of content items and the position of the media element. The progress bar at the left side of the component visually indicates the scroll progress.

### Managing Media and Content

The component allows for flexible management of media and content. You can update the `media`, `header`, `subheader`, and `body` props to reflect different content, and the slider will automatically adjust its layout and animations to provide an optimal viewing experience.
