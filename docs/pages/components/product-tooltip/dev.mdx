# Product Tooltip

### Props Definition


```tsx
export type popupProps = {
  id?: string
  title: string
  location?: 'popupLeft' | 'popupRight' | 'popupTop' | 'popupBottom'
  description: string
  cta?: CTAProps
}

export type HotspotProps = {
  position: {
    mobile: {
      x: number
      y: number
    },
    desktop: {
      x: number
      y: number
    }
  },
  popup: popupProps
}

export type toolTipsProps = {
  image: string
  mobileImage: string
  hotspots: HotspotProps[]
}
```

### Props Details For Humans

| Prop name | Description | Default value | Required |
| --------- | ----------- | ------------- | -------- |
| image | Image URL for desktop | None | true |
| mobileImage | Image URL for mobile | None | true |
| hotspots | Array of hotspots | None | true |
| hotspots.position | Position of the hotspot | None| true |
| hotspots.position.mobile | Position of the hotspot for mobile | None | true |
| hotspots.position.desktop | Position of the hotspot for desktop | None | true |
| hotspots.popup | Popup content | None | true |
| hotspots.popup.id | Popup id | None| false |
| hotspots.popup.title | Popup title | None | true |
| hotspots.popup.location | Popup location | 'popupTop' | false |
| hotspots.popup.description | Popup description | None | true |
| hotspots.popup.cta | Popup CTA | None | false |


### Import

```tsx
import Tooltips from '@/components/Product/Tooltips'
```

### Usage

```tsx
 <Tooltips
      image="/assets/test/bg-tooltips.jpg"
      mobileImage="/assets/test/bg-tooltips-mobile.jpg"
      hotspots={[
        {
          position: {
            desktop: {
              x: 18,
              y: 55
            },
            mobile: {
              x: 35,
              y: 25
            }
          },
          popup: {
            id: 'popup1',
            title: 'Enameled Cast Iron Braiser',
            description: 'Perfect for braising, roasting, and developing rich, flavorful dishes with even heat.',

          }
        },
        {
          position: {
            desktop: {
              x: 45,
              y: 40
            },
            mobile: {
              x: 64,
              y: 50
            }
          },
          popup: {
            id: 'popup2',
            title: 'Enameled Cast Iron Dutch Oven',
            description: 'Great for slow-cooked meals, baking bread, and simmering soups with consistent heat.',

          }
        },
        {
          position: {
            desktop: {
              x: 45,
              y: 65
            },
            mobile: {
              x: 65,
              y: 70
            }
          },
          popup: {
            id: 'popup3',
            title: 'Enameled Cast Iron Grill Pan',
            description: 'Grill indoors effortlessly, achieving perfect sear marks on meats, veggies, and more.',

          }
        },
        {
          position: {
            desktop: {
              x: 78,
              y: 65
            },
            mobile: {
              x: 65,
              y: 83
            }
          },
          popup: {
            id: 'popup4',
            title: 'Enameled Cast Iron Skillet',
            description: 'An essential for daily cooking, ideal for searing, frying, and baking with even heat.',

          }
        },
      ]}
    />
```