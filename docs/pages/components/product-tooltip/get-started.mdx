# Product Tooltip

Component with pulsing "hotspots" which show product information.

### Screenshots

#### Default State
![Product Tooltip](/assets/product-tooltip/tooltips-1.png)
#### Opened
![Product Tooltip](/assets/product-tooltip/tooltips-2.png)

### CMS Usage

By following the steps below, you will be able to add a Product Tooltip to your page.

- Add to your page a new "Page Section: Content"
- Choose the Product Tooltips as the type.
- For the Desktop Image Add a new Asset to the Section
- For the Mobile Image Add a new Mobile Asset to the Section
- Each Block Will be a new Tooltip
- Add a Title, and content, for the body of the tooltip
- Add Call to Action Block as References to display de CTA.
- To position the tooltip, you can use the "Settings" and fill the "Custom Props" field with the following JSON:
```json
{
    // Percentage value of the x position = 35%
    // Percentage value of the y position = 25%
    "mobile": {
        "x": 35, 
        "y": 25 
    },
    "desktop": {
        "x": 18,
        "y": 55
    }
}
```

Importante: if the "Custom Props" field is not filled, or is missing some property then the tooltip will not be displayed to prevent the page crash by javascript errors.
