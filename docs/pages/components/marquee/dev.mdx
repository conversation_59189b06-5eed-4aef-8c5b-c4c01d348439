---
title: "Marquee"
description: "The Marquee component displays scrolling text content, styled with themes and customizable through props. It supports continuous scrolling and can be styled with different themes."
---

# Marquee

The Marquee component is designed to display scrolling text in a continuous loop. It is highly customizable, allowing you to pass in a list of slides and choose from various color themes. The scrolling animation is smooth and responsive, pausing automatically when reduced-motion preferences are detected.

### Import Directions

```ts filename="From any component"
import Marquee from '@components/Generic/Marquee';
```

### Marquee Usage

The Marquee component accepts the following props:

| **Prop**   | **Type**                | **Required** | **Default Value** |
|:-----------|:------------------------|:-------------|:------------------|
| `slides`   | `{ text: string, id: string }[]` | Yes          | N/A               |
| `theme`    | `ThemeColors`           | No           | 'navy'             |

- `slides`: An array of objects containing the text and ID for each slide.
- `theme`: Optional. Defines the color theme for the marquee, defaulting to 'navy'.

### Example Usage

```ts
<Marquee slides={[
  { text: 'Our Ceramic Cookware is 100% Non-Toxic', id: '1' },
  { text: 'Perfect Heat Distribution for Effortless Cooking', id: '3' },
]} theme="navy" />
```

### Features

- **Animation**: The marquee scrolls continuously with an infinite loop, pausing if the user prefers reduced motion.
- **Theming**: You can apply different themes (e.g., 'navy') to match your design system.
- **Accessibility**: When there are duplicate items for the infinite scroll effect, the second instance is marked with `aria-hidden` to ensure screen readers don’t repeat the content.

### Code Example

```ts
import Marquee from '@components/Generic/Marquee';

const slides = [
  { text: 'Our Ceramic Cookware is 100% Non-Toxic', id: 'slide1' },
  { text: 'Perfect Heat Distribution for Effortless Cooking', id: 'slide3' },
];

<Marquee slides={slides} theme="navy" />;