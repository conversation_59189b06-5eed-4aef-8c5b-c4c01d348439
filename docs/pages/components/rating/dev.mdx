# Rating Star

For contentful instructions and Screenshots [Here](/components/rating/get-started)

## Import Directions

```tsx
import Rating from '@/components/Generic/Rating';
```

## Component props

```tsx
export type RatingProps = StarsProps & {
  withoutRating?: boolean;
  withoutReviews?: boolean;
  reviewCount?: number;
  isAggregated?: boolean;
  iconColor?: ThemeColors;
  rating?: number;
  textContentOn?: 'left' | 'right' | 'none';
  textStyleProps?: object | undefined;
};
```

## Component props

```tsx
 <Rating
      isAggregated
      withoutReviews
      textContentOn="right"
      rating={5}
      reviewCount={10000}
      iconColor="black"
      size="small"
    />
```