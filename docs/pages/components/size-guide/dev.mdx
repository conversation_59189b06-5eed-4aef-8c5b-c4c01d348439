# Size Guide
The `Size Guide` component is a dialog table used to compare product features and dimensions.


## File Tree Hierarchy
- **Query:**
  - `src/lib/contentful/fetchProducts.tsx`

- **Dialog Extractor:**
  - `src/lib/contentful/blockExtractor/extractProductDialogs.ts`

- **Anchor to open Dialog:**
  - `src/components/Product/ProductToggles.tsx`

- **Size Guide:**
  - `src/components/Generic/Dialog/SizeGuide/index.tsx`

## Size Guide Dialog Props

```tsx
export type PairedWith = {
  content: string
  title: string
  asset: {
    description: string
    url: string
  },
  mobileAsset: {
    description: string
    url: string
  }
}[]

export type ElementsToCompare = {
  items: {
    name: string
    description: string
    selectedAsset: {
      description: string
      url: string
    }
    pairedWith: PairedWith
    callToAction?: {
      text: string
      page: {
        __typename: string
        slug: string
      }
    }
  }[]
}

export type DialogProps = {
  id: string
  header: string
  subheader: string
  asset?: string
  content: string | ReactNode
  mobileContent: string | ReactNode
  buttons?: ExtendedAnchorProps[]
  footerContent?: string
  theme?: ThemeColors
  layout?: LayoutProps
  text?: string
  dialogToggles?: {
    title?: string
    content: string | ReactNode
    mobileContent: string | ReactNode
    asset?: {
      items: {
        fileName: string
        url: string
      }[]
    }
  }[],
  compareChart?: {
    header: string
    elementsToCompare: ElementsToCompare
  }
}
```

### `compareChart`
- **Description:** Provides Header and elements to Compare for chart data.

### `elementsToCompare`
- **Type:**
```tsx
export type ElementsToCompare = {
  items: {
    name: string
    description: string
    selectedAsset: {
      description: string
      url: string
    }
    pairedWith: PairedWith
    callToAction?: {
      text: string
      page: {
        __typename: string
        slug: string
      }
    }
  }[]
}
```
- **Description:** Content of each Product column.

### `header`

- **Type:** `string`
- **Description:** Displayed as title for chart.

```jsx
import { useDialogContext } from '@/providers/config/DialogContext'
import RenderCTAS from '@/components/Generic/RenderCTAS'
import Typography from '@/components/Typography'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  link: {
    color: colors.navy,
    fontWeight: 400,
    fontSize: '1rem'
  }
})

export const ExampleSizeGuideTrigger = ({}) => {
  const { triggerDialog } = useDialogContext()

  return (
    <>
      {dialog && (
        <Typography as="p" typographyTheme="bodyLarge">
          <RenderCTAS
            buttons={[
              {
                children: dialog.text,
                id: dialog.id,
                styleProp: styles.link,
                variant: 'underlined'
              },
            ]}
          />
        </Typography>
      )}
    </>
  )
}

export default ExampleSizeGuideTrigger
```