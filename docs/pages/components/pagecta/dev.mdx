# PageCTA

For contentful instructions and Screenshots [Here](/components/pagecta/get-started)

## Import Directions

```tsx
import PageCTA from '@/components/Content/PageCTA';
```

## Component props

```tsx

type ExtendedAnchorProps = AnchorProps & { text: string };

type PageCTAProps = {
  image?: string;
  theme?: ThemeColors
  header?: string;
  buttons?: ExtendedAnchorProps[];
  contentAlignment?: 'top' | 'center';
}
```

### Usage with Theme

```tsx
 <PageCTA
      heading="Discover Marigold"
      theme="marigold"
      buttons={[
        {
          children: 'Lorem Ipsum Dolor', 
          href: '/demo-page', 
          variant: 'primary', 
          id: 'cta-1'
        }
      ]}
      contentAlignment="top"
    />
```

### Usage with Background Image

```tsx
 <PageCTA
      image="/assets/kitchen-gadgets-hero.jpg" // <= The Image URL or Path
      heading="Discover Marigold"
      theme="marigold"
      buttons={[
        {
          children: 'Lorem Ipsum Dolor', 
          href: '/demo-page', 
          variant: 'primary', 
          id: 'cta-1'
        }
      ]}
      contentAlignment="top"
    />
```