# PageCTA

The Page CTA Banner component displays a prominent call-to-action banner on a page. It encourages users to take a specific action.

## Screenshots

### Desktop with theme (Center aligned)

![PageCTA](/assets/page-cta/cta.png)

### Mobile with theme (Center aligned)

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/page-cta/cta-mobile.png" alt="" width="450"/></p>

### Desktop with background (Top aligned)

![PageCTA](/assets/page-cta/cta-bg.png)

### Mobile with background (Top aligned)

<p className="nx-mt-6 nx-leading-7 first:nx-mt-0"><img src="/assets/page-cta/cta-bg-mobile.png" alt="" width="450"/></p>

#### Contentful Usage

Just create a new or update a page and go to ```Page Sections```

##### Add a new section

Select the type "Page CTA" and fill in the **header with the phrase** that will be <u>highlighted</u>

![Create a section](/assets/page-cta/tuto-1.png)

##### Settings

You change settings like **Theme** color by adding a Token Setting at Setting Option.

##### Add blocks to display Call to Action

Each call to action block will render a button.

![Create Call to Action Block](/assets/page-cta/tuto-2.png)