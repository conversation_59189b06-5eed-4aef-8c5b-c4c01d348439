# Size Guide Contentful Integration

The `Size Guide` component is a dialog table used to compare product features and dimensions.

## Screenshots

### Size Guides

#### 2 Row
![2 Row Chart](/assets/size-guide/two-row-sg-chart.jpg)

#### 3 Row
![3 Row Chart](/assets/size-guide/three-row-sg-chart.jpg)

## How to Use

### Creating a Product Metadata Item

1. **Navigate** to any **Product** in Contentful.
2. Locate **Product Metadata** and check for an existing **Product Metadata Item** containing **Dialogs**:
  - **If an item exists**, proceed to step 4.
  - **If no item exists**, continue to step 3.

![](/assets/size-guide/contentful-steps/product_metadata.jpeg)

3. Under **Product Metadata**, select **Product Metadata Item** from the **Add content** dropdown:
  - **Enter a name** to locate within Contentful.
  - Select **Dialogs** from the **Type** dropdown.

![](/assets/size-guide/contentful-steps/product_metadata_item.jpeg)

4. **Configure Block: Dialog**:
  - In the **References** section, add a new **Block: Dialog**.
  - **Enter a name** for locating within Contentful.

![](/assets/size-guide/contentful-steps/references_block_dialog.jpeg)

5. **Set Up Token Settings**:
  - Navigate to the **Settings** section.
  - **Enter a name** for locating within Contentful.
  - Select **Layout 4** from the **Layout** dropdown.
  - **Return to the Block: Dialog.**

![](/assets/size-guide/contentful-steps/block_dialog_settings.jpeg)

![](/assets/size-guide/contentful-steps/setting_options.jpeg)

6. **Add Compare Chart Information**:
  - Add a new **Page Sections: Compare Chart** to the **Toggles and References** section on **Block: Dialog**.
  - **Enter a name** for locating within Contentful.
  - **Set the Header** (this will be the Title for the dialog).

![](/assets/size-guide/contentful-steps/compare_chart_reference.jpeg)

7. **Setup Compare Data (columns)**
  - **Create Compare Data** in the **Elements To Compare** section on **Page Sections: Compare Chart**.
  - **Enter a name** for locating within Contentful.
  - **Enter a Description** this will be the second text element in its column.
  - **Add asset** for column's featured product.

![](/assets/size-guide/contentful-steps/compare_data.jpeg)

  - **Create Block: Content** under **Items** section (view layouts below).

#### 2 Row Chart - Block: Content Setup
![2 Row Chart](/assets/size-guide/two-row-sg-chart.jpg)
1. **Block: Content** on Compare Data
  - **Enter a name** for locating within Contentful.
  - **Enter Title** for third text element in column.
  - **Enter Product Name** in **Content** field.
  - **Add media** for Desktop layout in the **Assets** section.
  - **Add media** for Mobile Layout (left aligned image) in the **Mobile Assets** section.

#### 3 Row Chart - Block: Content Setup
![3 Row Chart](/assets/size-guide/three-row-sg-chart.jpg)
1. **Block: Content** on Compare Data
  - **Enter a name** for locating within Contentful.
  - **Enter Title** used for text on second row.
  - **Add media** for Desktop layout in the **Assets** section.
  - **Add media** for Mobile Layout (left aligned image) in the **Mobile Assets** section.
  - **Return to Compare Data**, add third row by following steps in **2 Row Chart**.

#### Add additional Columns by repeating steps.

![](/assets/size-guide/contentful-steps/compare_data.jpeg)

### Linking Product Metadata Item to Call to Action

1. **Navigate** to **Groups** on the **Product** page.
   - Add **Compare Set** from **Add content**.
   - **Enter a name** for locating within Contentful.

![](/assets/size-guide/contentful-steps/product_groups.jpeg)

![](/assets/size-guide/contentful-steps/compare_set.jpeg)

2. Add **Block: Call to Action** to the **Open Dialog** section:
   - **Enter a name** for locating within Contentful.
   - **Text** will appear as the dialog anchor on the PDP.
3. Add the **Block: Dialog** you previously created to **Page section**.

![](/assets/size-guide/contentful-steps/call_to_action_dialog.jpeg)

4. **All grouped products must have Product Metadata updated with Dialog, and Compare Set added to Groups.**
