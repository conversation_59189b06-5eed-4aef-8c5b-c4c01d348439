---
title: Adding Content to the Feature Slider via Contentful
---

# How to Add Content to the Feature Slider

The `FeatureSlider` allows you to create visually rich sections for your website, such as sliders or standalone features, directly from Contentful. By following this guide, you will learn how to fill in the required content in Contentful to power your website's `FeatureSlider`.

## Step 1: Navigate to the Contentful Dashboard

1. Log in to your Contentful account and choose the space where you want to add content.
2. Once inside the Contentful space, navigate to the **Content** tab.
3. Look for an entry type called **Feature Slide**. This is where you'll add content for the `FeatureSlider` on your site.

## Step 2: Create a New Feature Slide Entry

1. In the **Content** section, click **Add Entry**.
2. Select the **Feature Slide** entry type.
3. A form will appear where you can fill in all the details for the slide. Below are the fields you'll need to fill in.

## Step 3: Filling in the Fields

Each **Feature Slide** has a few important sections for you to complete:

### **1. Heading 1 (Main Title)**

This will be the main title or the largest text on the slide.

- **Example:** "Welcome to Our Summer Sale!"
- Make sure this title grabs attention and is short and clear.

### **2. Heading 2 (Subheading)**

The subheading supports the main title and can be used to provide additional context.

- **Example:** "Up to 50% off on all products!"
- This should complement the main heading and encourage users to take action.

### **3. Body Text**

The body text gives more information about the slide.

- **Example:** "Discover our latest collection of summer products with exclusive discounts. Shop now!"
- This text can be a paragraph or two, explaining details about the promotion, product, or feature.

### **4. Button Text**

The button is a call-to-action (CTA) that encourages users to click.

- **Example:** "Shop Now" or "Learn More"
- Keep it short, direct, and action-oriented.

### **5. Button URL**

This is the link where users will be redirected when they click the button.

- **Example:** "/summer-sale" or "https://yourwebsite.com/special-offer"
- Ensure the link is valid and leads to the correct page.

### **6. Button Theme (Optional)**

You can choose a visual style for the button.

- **Primary Button:** Typically used for the main CTA.
- **Secondary Button:** Used for a less emphasized CTA.
- If unsure, you can leave this blank, and the default style will be applied.

### **7. Image**

This field allows you to upload an image that will be displayed on the slide.

- **Example:** Upload an image related to your promotion, product, or feature.
- Ensure the image is high-quality and fits well within the slider layout.

### **8. Slide ID (Optional)**

If required, you can specify a unique ID for the slide. This can be useful for tracking purposes or specific targeting, but it's optional.

### Example of Filled-Out Content:

- **Heading 1 (Main Title):** "Discover Our Latest Collection"
- **Heading 2 (Subheading):** "Shop Trendy Summer Styles"
- **Body Text:** "Our newest summer collection is here! Featuring bold prints, light fabrics, and versatile pieces, perfect for any occasion."
- **Button Text:** "Explore Now"
- **Button URL:** "/collections/summer-2024"
- **Button Theme:** "Primary"
- **Image:** [Upload an image of the collection]
  
## Step 4: Save and Publish

Once you’ve filled in all the fields:

1. Click **Save** to store the entry.
2. Click **Publish** to make the slide live on your website.

## Step 5: Adding Multiple Slides

If you want to create a slider with multiple slides, simply create additional **Feature Slide** entries following the steps above. The website will automatically display the slides in a slider format if more than one slide is available.

## Notes

- **Order of Slides:** The order of slides can be managed by your Contentful configuration or development team. Typically, the latest published slide will appear first unless otherwise specified.
- **Images:** Ensure all images are optimized for web to prevent slow loading times.
- **Character Limits:** It's good practice to keep headings and subheadings concise for better readability.

## Common Questions

### Q: Can I preview my slide before it goes live?
A: Yes, in Contentful you can preview the entry before publishing. Simply click the **Preview** button to see how the slide will look.

### Q: Can I delete a slide if I no longer need it?
A: Yes, you can unpublish or delete a slide entry at any time from the Contentful dashboard. The slide will no longer appear on the website once unpublished.

### Q: What if I want a different button style or layout?
A: The website is built to handle different themes (styles) and layouts, which may be configured by your development team. If you want to modify the appearance, consult your team to make sure the right options are available.

## Conclusion

That's it! You’ve now successfully added a slide to your website's `FeatureSlider` via Contentful. Just fill in the provided fields for each slide, and your content will automatically appear live after publishing. You can add as many slides as you need and make updates any time directly through Contentful.
```````````
