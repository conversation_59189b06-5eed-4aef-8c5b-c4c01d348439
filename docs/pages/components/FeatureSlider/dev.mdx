---
title: Feature Slider Component Documentation
---

# FeatureSlider Component

The `FeatureSlider` component is designed to display a feature section or a slider of features with dynamic content such as headers, subheaders, body text, buttons, and media. It can render either a single feature or multiple features as a slider, depending on the number of provided slides.

## Usage

```tsx
import FeatureSlider from '@components/FeatureSlider';

const slides = [
  {
    id: '1',
    text: {
      json: {
        content: [
          { nodeType: 'heading-1', content: [{ value: 'Main Heading' }] },
          { nodeType: 'heading-2', content: [{ value: 'Sub Heading' }] },
          { nodeType: 'paragraph', content: [{ value: 'Body text here...' }] },
        ],
      },
    },
    button: {
      items: [
        {
          text: 'Click Me',
          customUrl: '/path',
          settings: { theme: 'primary' },
        },
      ],
    },
    media: {
      items: [{ url: '/path/to/image.jpg' }],
    },
  },
];

export default function MyApp() {
  return <FeatureSlider slides={slides} theme="navy" layout="Layout 1" />;
}
```

## Props

### `FeatureSliderProps`

| Name     | Type                    | Description                                                                     | Default |
|----------|-------------------------|---------------------------------------------------------------------------------|---------|
| `slides` | `Slide[]`                | Array of slide objects. Each slide contains text, button, and media information. | `[]`    |
| `theme`  | `ThemeColors`            | Theme color for the component. Inherits from the `ThemeColors` type.            | `'navy'`|
| `layout` | `string`                 | Defines the layout of the component. Options are `"Layout 1"` or `"Layout 2"`.  | `'Layout 1'` |

### `Slide`

The `Slide` type describes the structure of each slide. The following fields are available:

| Name     | Type               | Description                                                                 |
|----------|--------------------|-----------------------------------------------------------------------------|
| `id`     | `string`            | Unique identifier for the slide.                                            |
| `text`   | `{ json: any }`     | Text content of the slide. Supports nested headings and paragraphs in JSON. |
| `button` | `Button`            | Button details including text, URL, and theme settings.                     |
| `media`  | `{ items: { url: string }[] }` | Array of media objects. Currently only supporting a single media item.    |

### `Button`

The `Button` type defines the structure of each button within a slide:

| Name        | Type                | Description                                                  |
|-------------|---------------------|--------------------------------------------------------------|
| `text`      | `string`             | The text that appears on the button.                         |
| `customUrl` | `string`             | The URL to navigate to when the button is clicked.           |
| `settings`  | `{ theme: string }`  | Theme settings for the button (e.g., 'primary', 'secondary').|
| `_id`       | `string`             | (Optional) Unique identifier for the button.                 |

## Processing Logic

### Extracting Text Content

The `extractTextContent` function is responsible for parsing and extracting the relevant content (header, subheader, and body) from the slide's JSON structure.

```ts
const extractTextContent = (contentItem: any): { header: string; subheader: string; body: string } => {
  let header = '';
  let subheader = '';
  let body = '';

  switch (contentItem.nodeType) {
    case 'heading-1':
      header = contentItem.content[0]?.value || '';
      break;
    case 'heading-2':
      subheader = contentItem.content[0]?.value || '';
      break;
    default:
      body += contentItem.content[0]?.value || '';
      break;
  }

  return { header, subheader, body };
};
```

### Button Theme Handling

The button theme is determined by the `getButtonVariant` function. It translates the string-based theme from the slide data into a `VariantTypes` type, which supports `'primary'` and `'secondary'`.

```ts
const getButtonVariant = (themeVariant: string | undefined): VariantTypes => {
  switch (themeVariant?.toLowerCase()) {
    case 'secondary':
      return 'secondary';
    case 'primary':
    default:
      return 'primary';
  }
};
```

### Slide Processing

The `processSlide` function takes in each slide object, processes the text, button, and media data, and returns an object compatible with the `baseProps` type, which is passed to the `Feature` component for rendering.

```ts
const processSlide = (slide: any): baseProps => {
  const id = slide?.id || '';
  let header = '';
  let subheader = '';
  let body = '';

  slide?.text?.json?.content?.forEach((contentItem: any) => {
    const { header: h, subheader: sh, body: b } = extractTextContent(contentItem);
    header = h || header;
    subheader = sh || subheader;
    body += b;
  });

  const mainVariant = getButtonVariant(slide?.button?.items?.[0]?.settings?.theme);

  return {
    id,
    header,
    subheader,
    body,
    button: {
      text: slide?.button?.items?.[0]?.text || '',
      href: slide?.button?.items?.[0]?.customUrl || '',
      variant: mainVariant,
      id: slide?.button?.items?.[0]?._id || '',
      useArrow: true,
    },
    image: slide?.media?.items?.[0]?.url || '',
  };
};
```

## `Feature` Component

The `Feature` component is responsible for rendering either a base feature layout or a slider layout. It accepts the following props:

### `featureProps`

| Name     | Type                   | Description                                                                                       | Default   |
|----------|------------------------|---------------------------------------------------------------------------------------------------|-----------|
| `content`| `baseProps`             | The content data for a single feature, including text, button, and media.                         |           |
| `slides` | `baseProps[]`           | An array of slide content for slider-type layouts.                                                |           |
| `type`   | `'base' \| 'slider'`    | The type of layout to render. `'base'` renders a single feature, while `'slider'` renders multiple features as a slider. | `'base'`   |
| `reverse`| `boolean`               | Whether to reverse the layout or not.                                                             | `false`   |
| `theme`  | `ThemeColors`           | Theme color for the component.                                                                    | `'black'` |
| `size`   | `string`                | Size of the feature layout.                                                                       | `'4'`     |

### Example

```tsx
import Feature from '@components/layout/Feature';

const content = {
  id: '1',
  header: 'Main Heading',
  subheader: 'Sub Heading',
  body: 'Body text here...',
  button: {
    text: 'Click Me',
    href: '/path',
    variant: 'primary',
  },
  image: '/path/to/image.jpg',
};

export default function MyApp() {
  return <Feature content={content} type="base" theme="black" reverse={false} size="4" />;
}
```

### Layout Handling

The `Feature` component will either use the `Base` layout for single content items or the `LayoutWithSlider` for multiple slides.

```tsx
const Feature = ({ content, slides, type, reverse, theme = 'black', size = '4' }: featureProps) => {
  if (type === 'slider') {
    return <LayoutWithSlider slides={slides} reverse={reverse} theme={theme} size={size} />;
  }

  const { id, button, image, ...rest } = content as baseProps;

  return <Base id={id} button={button} image={image} {...rest} reverse={reverse} theme={theme} size={size} />;
};
```

## Conclusion

The `FeatureSlider` and `Feature` components provide a flexible way to display dynamic content as a feature or slider. By passing appropriate slide data and configuring themes and layouts, developers can create rich content sections in their applications.
```````````
