# Hero

For contentful instructions and Screenshots [Here](/components/hero/get-started)

## Import Directions

```tsx
import Rating from '@/components/Content/Hero';
```

## Component props

```tsx

export type HeroProps = {
  props: HeroSliderProps[];
  slideOptions?: EmblaOptionsType;
}

export type HeroSliderProps = {
  id: string;
  media?: AssetsCollectionType | undefined;
  contentAlignment?: 'left' | 'right' | 'center';
  theme?: ThemeColors;
  subHeadline?: string;
  header: string;
  text?: string;
  mediaPlaying?: boolean;
  buttons: AnchorProps[];
  typeFace?: 'primaryFontFamily' | 'secondaryFontFamily';
  onPlayerChangeState?: (playing: boolean) => void;
  videoIconSize?: number;
}


```

## Component Usage

```tsx
 <Hero
      props={[
        {
          id: 'first',
          media: {
            items: [
              {
                url: 'https://videos.ctfassets.net/vitsw3itv0qj/4tEwOmL5aVRiB0tI85e7RS/c57e3de51216ab04217e5d0cf4a6bacc/How_to_Cook_with_Caraway.mp4'
              }
            ]
          },
          contentAlignment: 'left',
          subHeadline: 'Lorem Ipsum Dolor SUB',
          header: 'Lorem Ipsum Dolor  Headline',
          text: 'Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna. Pellentesque sit amet sapien.',
          buttons: [
            {
              children: 'Lorem Ipsum Dolor',
              href: '/demo-page',
              variant: 'primary',
              id: 'cta-1'
            },
            {
              children: 'Lorem Ipsum Dolor',
              href: '/demo-page',
              variant: 'secondary',
              id: 'cta-2'
            }
          ],
        }
      ]}
    />
```