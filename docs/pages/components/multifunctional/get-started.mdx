# Multifunctional

The Multifunctional Module is a versatile component that can display various types of content, such as text, images, or interactive elements, in a flexible layout.

### Design
[Link to Figma design](https://www.figma.com/design/o8aXvUVinp0cG53pl6GWQJ/2024-Full-Site?node-id=2245-299958&node-type=frame&t=AbwxC89JYFW2TUJk-0)

### Screenshots

#### Version 1 - Desktop
![](/assets/multifunctional/v1.png)

#### Version 1 - Desktop
![](/assets/multifunctional/v1-mobile.png)

#### Version 2 - Desktop
![](/assets/multifunctional/v2.png)

#### Version 2 - Desktop
![](/assets/multifunctional/v2-mobile.png)

### Contentful Usage

to be able to use the Multifunctional Module, you need to create a new "Page Section: Content" entry in Contentful with the following fields:

1) Header Field will be used as the title of the module
2) You can add a setting for the module to control the layout, the layout 1 will display the version 1 and the layout 2 will display the version 2.
3) Each Block type content will be the Menu items
    - Block Title for the title of the menu item
    - Block Asset for the image of the menu item (Only V2) ![](/assets/multifunctional/menu-image.png)
    - Block Content for the description of the menu item (Image below)
    - Block Content references with type Call to action for the links at the footer of the each section (Image below)
    - ![](/assets/multifunctional/description.png)
    - Block settings
        - Layout 1 for the slider display the horizontal cards (Default as in the screenshot)
        - Layout 2 for the slider display the two column cards (Image below)
        - ![](/assets/multifunctional/two-column.png)
    - Each Block reference type "Block: Content" will be a slider item
        - Block reference Title for the title of the slider item
        - Block reference Content for the description
        - Block reference Asset for the image or vídeo of the slider item