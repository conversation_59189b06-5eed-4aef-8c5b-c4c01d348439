---
title: "Navigation"
description: "The Navigation components provide a flexible and customizable way to create navigation menus in your application. With NavigationBlock and NavigationLink components, you can easily build responsive and interactive navigation structures."
---

# Navigation

The Navigation components offer a modular approach to building navigation menus. They are designed to be flexible and customizable, allowing you to create various navigation layouts with support for nested menus and callouts.

### Import Directions

```ts filename="From any component"
import NavigationBlock from '@components/layout/Header/NavigationBlock';
import NavigationLink from '@components/layout/Header/NavigationBlock/NavigationLink';
```

### NavigationBlock Usage
The NavigationBlock component accepts the following props:
| **Prop** | **Type**           | **Required** | **Default Value** |
|:---------|:-------------------|:-------------|:------------------|
| `items`  | `NavigationItem[]` | Yes          | N/A               |

```ts
const navItems = [
  { name: 'Home', slug: '/' },
  { name: 'Products', slug: '/products' },
  { name: 'About', slug: '/about' },
];

<NavigationBlock items={navItems} />
```

### NavigationLink Component
The NavigationLink component is used within NavigationBlock to render individual navigation items. It accepts the following props:

| **Prop**   | **Type**        | **Required** | **Default Value** |
|:-----------|:----------------|:-------------|:------------------|
| `slug`     | `string`        | Yes          | N/A               |
| `callout`  | `CalloutProps`  | No           | N/A               |
| `children` | `React.ReactNode` | Yes        | N/A               |

Example usage:

```ts
<NavigationLink slug="/products" callout={{ title: 'New!', settings: { theme: 'navy' } }}>
  Products
</NavigationLink>
```

### Types

```ts
type NavigationItem = {
  name: string;
  slug: string;
  linkCollection?: any | null;
  callout?: CalloutProps;
}

type CalloutProps = {
  title: string,
  settings: {
    theme: ThemeColors
  }
}
```

### Styling
The Navigation components use `stylex` for styling, ensuring consistent and customizable appearance across your application.

### Managing Complex Navigation
With the NavigationBlock and NavigationLink components, you can create complex navigation structures:

- Use nested NavigationBlock components for multi-level menus
- Implement callouts for highlighting specific navigation items
- Create responsive layouts by customizing the NavigationBlock styles

Example of a more complex navigation structure:

```tsx
const navItems = [
  { name: 'Home', slug: '/' },
  { 
    name: 'Products', 
    slug: '/products',
    linkCollection: [
      { name: 'Category 1', slug: '/products/category-1' },
      { name: 'Category 2', slug: '/products/category-2' },
    ]
  },
  { 
    name: 'Sale', 
    slug: '/sale',
    callout: { title: 'Limited Time!', settings: { theme: 'red' } }
  },
];

<NavigationBlock items={navItems} />
```

This structure allows for a main navigation menu with nested subcategories and highlighted items.
```

This documentation provides an overview of the Navigation components, their usage, props, and examples of how to implement more complex navigation structures. You can further customize this template by adding more specific details about styling options, responsive behavior, or any additional features of your Navigation components.