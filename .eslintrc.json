{"extends": ["next/core-web-vitals", "airbnb", "airbnb-typescript"], "parserOptions": {"project": "./tsconfig.json"}, "plugins": ["@stylexjs/eslint-plugin", "newline-destructuring"], "rules": {"@stylexjs/valid-styles": "error", "@typescript-eslint/comma-dangle": "off", "@typescript-eslint/semi": "off", "no-underscore-dangle": "off", "semi": ["error", "never"], "import/order": ["error", {"groups": ["index", "sibling", "parent", "internal", "external", "builtin", "object", "type"], "newlines-between": "always"}], "jsx-a11y/media-has-caption": "off", "react/jsx-one-expression-per-line": "off", "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "react/react-in-jsx-scope": "off", "jsx-a11y/anchor-is-valid": "off", "jsx-a11y/click-events-have-key-events": "off", "no-param-reassign": "off", "jsx-a11y/no-static-element-interactions": "off", "@typescript-eslint/no-extra-parens": "error", "operator-linebreak": ["error", "after", {"overrides": {"?": "before", ":": "before"}}], "line-comment-position": ["error", {"position": "above"}], "react/function-component-definition": "off", "react/jsx-props-no-spreading": "off", "max-len": ["error", {"code": 200}], "no-magic-numbers": ["error", {"ignore": [0, 1, -1]}], "no-else-return": "error", "complexity": ["error", 8], "react/destructuring-assignment": "error", "newline-destructuring/newline": ["error", {"items": 2}], "react/require-default-props": "off", "@typescript-eslint/naming-convention": ["error", {"selector": "variable", "types": ["function"], "format": ["PascalCase", "camelCase"]}, {"selector": "variable", "types": ["boolean", "string", "number", "array"], "format": ["camelCase", "UPPER_CASE"]}]}}