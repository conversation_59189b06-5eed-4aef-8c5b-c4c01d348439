import client from '@lib/shopify/storefront-client'

const getShopifyData = async (id: string) => {
  const res = await fetch(client.getStorefrontApiUrl(), {
    body: JSON.stringify({
      query: `{
        product(id: "gid://shopify/Product/${id}") {
          id
          title
          variants(first: 250) {
            nodes {
              id
              availableForSale
              price {
                amount
                currencyCode
              }
            }
          }
        }
      }
    `,
    }),
    headers: client.getPublicTokenHeaders(),
    method: 'POST',
  })

  if (!res.ok) {
    throw new Error(res.statusText)
  }

  return res.json()
}

export default getShopifyData
