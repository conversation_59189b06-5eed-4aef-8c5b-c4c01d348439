const DOMAIN = process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN
const VERSION = process.env.NEXT_PUBLIC_SHOPIFY_STOREFRONT_API_VERSION
const TOKEN = process.env.NEXT_PUBLIC_SHOPIFY_PUBLIC_STOREFRONT_TOKEN
const API_URL = `${DOMAIN}/api/${VERSION}/graphql.json`

const HEADERS = {
  'content-type': 'application/json',
  'X-SDK-Variant': 'hydrogen-react',
  'X-SDK-Variant-Source': 'react',
  'X-SDK-Version': VERSION || '2024-07',
  'X-Shopify-Storefront-Access-Token': TOKEN || '',
}

const fetchGraphQL = async (query: string) => fetch(API_URL, {
  method: 'POST',
  headers: HEADERS,
  body: JSON.stringify({ query }),
}).then((response) => response.json().then((result) => {
  if (!response.ok || result.errors) {
    throw new Error(
      `GraphQL error: ${
        result.errors?.map((error: any) => error.message).join(', ') ||
            'Unknown error'
      }`
    )
  }
  return result
}))

export default fetchGraphQL
