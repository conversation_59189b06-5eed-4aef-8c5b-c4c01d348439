import fetchGraphQL from './fetchGraphQL'

const AVAILABILITY_FIELDS = `
  availableForSale
  id
`

const PRODUCT_LIMIT = 200

export const fetchAvailability = async (id: number) => {
  const availability = await fetchGraphQL(
    `query getProductById {
      product(id: "gid://shopify/Product/${id}") {
        variants(first: ${PRODUCT_LIMIT}) {
          nodes {
            ${AVAILABILITY_FIELDS}
          }
        }
      }
    }`
  )

  return availability
}

export const fetchVariantAvailability = async (id: number | string) => {
  try {
    const response = await fetchGraphQL(
      `query getVariantAvailability {
        node(id: "gid://shopify/ProductVariant/${id}") {
          ... on ProductVariant {
            ${AVAILABILITY_FIELDS}
          }
        }
      }`
    )

    return response.data.node?.availableForSale === true
  } catch (error) {
    console.warn('Error fetching variant availability', error)

    if (typeof error === 'object' && error !== null && 'errors' in error) {
      console.dir(error.errors, { depth: null })
    }

    return false
  }
}
