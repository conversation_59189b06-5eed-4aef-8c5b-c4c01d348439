import type {
  ProductViewedPayload,
  ProductAddedPayload,
  CheckoutStartedPayload,
} from '@redux/features/events/types'

declare global {
  interface Window {
    axon: (action: string, event_name: string, event_data?: any) => void
  }
}

const checkAxon = () => typeof window.axon === 'function'

const axonItemFormatter = (product: any, variant:any, quantity: number) => ({
  item_id: product?.productId,
  item_name: product?.title,
  price: variant.price,
  image_url: product?.seoMetadata?.image?.url,
  quantity,
  item_variant_id: variant?.variantId,
  item_category_id: 6070
})

const axonLineItemFormatter = (product: any, quantity: number) => ({
  item_id: product?.productId,
  item_name: product?.name,
  price: product?.amount,
  image_url: product?.imageUrl,
  quantity,
  item_variant_id: product?.variantId,
  item_category_id: 6070
})

const trackAxonPageView = () => {
  if (!checkAxon()) return
  window.axon('track', 'page_view')
}

const trackAxonViewItem = (payload: ProductViewedPayload) => {
  if (!checkAxon()) return
  const {
    product,
    variant,
    cart
  } = payload
  const event_data = {
    currency: cart?.currency || 'USD',
    value: variant.price * payload.quantity,
    items: [axonItemFormatter(product, variant, payload.quantity)],
  }
  window.axon('track', 'view_item', event_data)
}

const trackAxonAddToCart = (payload: ProductAddedPayload) => {
  if (!checkAxon()) return
  const {
    product,
    variant,
    cart
  } = payload

  const event_data = {
    currency: cart?.currency || 'USD',
    value: variant.price * payload.quantity,
    items: [axonItemFormatter(product, variant, payload.quantity)],
  }

  window.axon('track', 'add_to_cart', event_data)
}

const trackAxonBeginCheckout = (payload: CheckoutStartedPayload) => {
  if (!checkAxon()) return
  const { cart } = payload

  const event_data = {
    currency: cart?.currency || 'USD',
    value: cart?.value,
    items: cart?.products.map((lineItem: any) => axonLineItemFormatter(lineItem, lineItem.quantity)),
  }

  window.axon('track', 'begin_checkout', event_data)
}

const events = {
  trackAxonAddToCart,
  trackAxonBeginCheckout,
  trackAxonPageView,
  trackAxonViewItem,
}

export default events
