import fetchGraphQL from './fetchGraphQL'
import { pageSectionsContentQuery } from './queries'

const globalPage404 = `
  {
    globalSettingsCollection(where: {name: "Global Settings"}, limit: 1) {
      items {
        page404{
          __typename
          _id
          subtype
          sys {
            id
          }
      }
      }
    }
  }
`

const getPageSectionContentById = async (sectionId: string) => {
  const gqlPageSectionContentQuery = pageSectionsContentQuery(sectionId)

  const graphQlPageSectionContentResponse = await fetchGraphQL(
    gqlPageSectionContentQuery,
    ['pageSectionsContentCollection']
  )
  return graphQlPageSectionContentResponse.data.pageSectionsContent
}

async function fetchGlobalPage404() {
  const { data } = await fetchGraphQL(globalPage404, ['globalPage404'])

  const hero404 = await getPageSectionContentById(data.globalSettingsCollection.items[0].page404.sys.id)
  return hero404
}

export default fetchGlobalPage404
