import gqlmin from 'gqlmin'

const CONTENTFUL_ENVIRONMENT = process.env.CONTENTFUL_ENVIRONMENT || 'master'

// 3 days
const revalidateTime = 259200

const fetchGraphQL = async (
  query: string,
  tag: string[] = ['default'],
  preview = false,
  forceCache = false,
  revalidate = revalidateTime
) => fetch(
  `https://graphql.contentful.com/content/v1/spaces/${process.env.CONTENTFUL_SPACE_ID}/environments/${CONTENTFUL_ENVIRONMENT}`,
  {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${
        preview
          ? process.env.CONTENTFUL_PREVIEW_ACCESS_TOKEN
          : process.env.CONTENTFUL_ACCESS_TOKEN
      }`,
    },
    body: JSON.stringify({ query: gqlmin(query) }),
    next: {
      tags: tag,
      ...forceCache ? {} : { revalidate }
    },
    ...forceCache ? { cache: 'force-cache' } : {}
  }
).then((response) => response.json().then((result) => {
  if (!response.ok || result.errors) {
    throw new Error(
      `GraphQL error: ${result.errors
        ?.map((error: any) => error.message)
        .join(', ') || 'Unknown error'}`
    )
  }
  return result
}))

export default fetchGraphQL
