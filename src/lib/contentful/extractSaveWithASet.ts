import RichTextRenderer from '@/utils/RichTextRenderer'

export const extractSaveWithASetModule = (products: any) => {
  const mainProduct = products.find((product: any) => product.__typename === 'Product')
  let overviewDescription: any
  let keyProps: any[] = []
  let primaryImage: any
  let mainDescription: any
  let blockContentCustomTitle: any

  function handleProduct(product: any) {
    const productAccordions = product.productDetailBlocks?.items.find((item: { type: string }) => item.type === 'ProductAccordions')
    overviewDescription = productAccordions?.blocks?.find((block: { title: string; type: string }) => block.title === 'Overview')
    keyProps = overviewDescription?.referencesCollection.map((item: { reference: string }) => item.reference)
    if (!mainDescription) mainDescription = mainProduct.seoMetadata?.description
    if (!primaryImage) {
      primaryImage = {
        url: product.seoMetadata?.image?.url || product?.variants?.items[0]?.primaryImage?.url,
        title: product.title || product?.variants?.items[0]?.primaryImage?.title
      }
    }
  }

  function handleBlockContent(product: any) {
    blockContentCustomTitle = product.title
    mainDescription = RichTextRenderer({ content: product.content?.json })
    if (product.assetsCollection?.items.length > 0) {
      primaryImage = {
        url: product.assetsCollection?.items[0]?.url,
        title: product.title
      }
    }
    if (product.referencesCollection?.items.length > 0) {
      keyProps = product.referencesCollection?.items.map((item: { content: { json: any } }) => RichTextRenderer({ content: item?.content?.json }))
    }
  }

  products.forEach((product: any) => {
    if (product.__typename === 'Product') {
      handleProduct(product)
    } else if (product.__typename === 'BlockContent') {
      handleBlockContent(product)
    }
  })

  return {
    mainProduct, mainDescription, keyProps, primaryImage, overviewDescription, blockContentCustomTitle
  }
}

export type ExtractSaveWithASetModule = ReturnType<
  typeof extractSaveWithASetModule
>
