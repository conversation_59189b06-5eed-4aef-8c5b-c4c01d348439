import fetchGraphQL from './fetchGraphQL'

export default async function getEntrySlugById(
  id: string
) {
  const productSlug = await fetchGraphQL(
    `query {
      entryCollection(where: {sys: {id: "${id}"}}) {
        items {
          __typename
          ... on Page {
            slug
          }
          ... on Product {
            slug
          }
          ... on Collection {
            slug
          }
          ... on BlogArticle {
            slug
          }
          sys {
            id
          }
        }
      }
    }`,
    ['productSlug']
  )
  return productSlug
}
