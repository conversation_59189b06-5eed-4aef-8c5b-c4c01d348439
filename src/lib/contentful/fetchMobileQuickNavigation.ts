import fetchGraphQL from './fetchGraphQL'

import generateSlug from '@/utils/generateSlug'
import { NavigationItem } from '@/components/layout/Header/Navigation/types'

type LinkDestinationPage = {
  slug: string | null
  __typename: 'Page'
}

type LinkDestinationProduct = {
  slug: string | null
  __typename: 'Product'
}

type LinkCollection = {
  sys: {
    id: string
  }
  name: string
  linkTitle: string
  linkTarget: string | null
  linkDestination: LinkDestinationPage | LinkDestinationProduct
}

type NavigationCollection = {
  type: string
  linkCollectionCollection: {
    items: LinkCollection[]
  }
}

type NavigationLink = {
  sys: {
    id: string
  }
  linkTitle: string
  linkTarget: string | null
  linkDestination: LinkDestinationPage | LinkDestinationProduct
  navigationCollectionsCollection: {
    items: NavigationCollection[]
  }
}

type FooterNavigation = {
  name: string
  navigationLinksCollection: {
    items: NavigationLink[]
  }
}

const FOOTER_QUERY = `
  query {
    globalSettingsCollection(where: {name: "Global Settings"}, limit: 1) {
      items {
        mobileQuickNavigation {
          name
          navigationLinksCollection(limit: 10) {
            items {
              sys {
                id
              }
              linkTitle
              linkDestination {
                ... on Page {
                  __typename
                  slug
                }
                ... on Product {
                  __typename
                  slug
                }
                ... on BlogArticle {
                  __typename
                  slug
                }
              }
            }
          }
        }
      }
    }
  }
`

function extractQuickNavigationLinks(navigationData: FooterNavigation | null): NavigationItem[] {
  if (!navigationData?.navigationLinksCollection?.items) {
    return []
  }

  return navigationData.navigationLinksCollection.items.map((link): NavigationItem => ({
    id: link.sys.id,
    name: link.linkTitle,
    linkTitle: link.linkTitle,
    slug: link.linkDestination?.slug ? generateSlug(link.linkDestination.slug, link.linkDestination.__typename) : '',
    linkTarget: link.linkTarget || '',
  }))
}

export default async function fetchMobileQuickNavigation() {
  const response = await fetchGraphQL(
    FOOTER_QUERY,
    ['mobileQuickNavigation']
  )

  if (!response.data.globalSettingsCollection.items.length) {
    return []
  }

  const navigationData = response.data.globalSettingsCollection.items[0].mobileQuickNavigation
  return extractQuickNavigationLinks(navigationData)
}
