import fetchGraphQL from './fetchGraphQL'

import { NavigationItem } from '@/components/layout/Header/Navigation/types'
import generateSlug from '@/utils/generateSlug'

type LinkDestinationPage = {
  slug: string
  __typename: 'Page'
}

type LinkCollection = {
  _id: string
  name: string
  linkTitle: string
  linkDestination: LinkDestinationPage
}

type NavigationCollection = {
  _id: string
  type: string
  linkCollectionCollection: {
    items: LinkCollection[]
  }
}

type NavigationLink = {
  _id: string
  linkTitle: string
  linkDestination: LinkDestinationPage
  navigationCollectionsCollection: {
    items: NavigationCollection[]
  }
}

type AboutDesktopNavigation = {
  name: string
  navigationLinksCollection: {
    items: NavigationLink[]
  }
}

const NAVIGATION_QUERY = `query {
    globalSettingsCollection(where: {name: "Global Settings"}, limit: 1) {
      items {
        aboutNavigation {
          name
          navigationLinksCollection(limit: 3) {
            items {
              _id
              linkTitle
              linkDestination {
                ... on Page {
                  __typename
                  slug
                }
              }
              navigationCollectionsCollection(limit: 6) {
                items {
                  _id
                  type
                  linkCollectionCollection(limit: 5) {
                    items {
                      _id
                      name
                      linkTitle
                      linkDestination {
                        ... on Page {
                          __typename
                          slug
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }`

function extractNavigationLinks(navigationData: AboutDesktopNavigation): NavigationItem[] {
  return navigationData.navigationLinksCollection.items.map((link): NavigationItem => ({
    name: link.linkTitle,
    linkTitle: link.linkTitle,
    slug: generateSlug(link.linkDestination?.slug || '', link.linkDestination?.__typename || '') || '',
    id: link._id,
    linkTarget: '',
    linkCollection: link.navigationCollectionsCollection.items.map((collection) => ({
      id: collection._id,
      type: collection.type as 'imageLink' | 'textLink' | 'divider' | 'featuredImageLink' | 'interactiveImageLink' | 'interactiveTextLink',
      links: collection.linkCollectionCollection.items.map((item) => ({
        id: item._id,
        name: item.name,
        linkTitle: item.linkTitle,
        slug: generateSlug(item.linkDestination?.slug || '', item.linkDestination?.__typename || '') || '',
        linkTarget: ''
      }))
    }))
  }))
}

export default async function fetchNavigation() {
  const navigationResponse = await fetchGraphQL(
    NAVIGATION_QUERY,
    ['aboutNavigation']
  )

  if (!navigationResponse.data.globalSettingsCollection.items.length) {
    throw new Error('No global settings found')
  }

  const navigationData = navigationResponse.data.globalSettingsCollection.items[0].aboutNavigation
  return extractNavigationLinks(navigationData)
}
