import fetchGraphQL from './fetchGraphQL'

import { ArticleSectionType } from '@components/ArticleSections'
import { equals, notEmpty } from '@utils/checking'

export type ArticleTypeProps = ArticleSectionType & {
  name: string
  image: {
    url: string
  }
  slug: string,
  articleSectionsCollection: {
    sections: ArticleSectionType[]
  }
  authorCollection?: {
    items: {
      name: string
    }[]
  }
  datePublished?: string
  seoMetadata: {
    name: string
    image: {
      url: string
    }
  }
}

export type FetchBlogResponseProps = {
  data: {
    blogArticleCollection: {
      items: ArticleTypeProps[]
    }
  }
}

const blogPageQuery = (slug: string): string => `
  query {
    blogArticleCollection(where:{ slug: "${slug}" }, limit: 1){
      items {
        name,
        image {
          url
        }
        slug,
        datePublished,
        seoMetadata {
          blockSearchIndexing
          name
          keywords
          description
          image {
            url
            width
            height
          }
        }
        authorCollection(limit: 1) {
          items {
            name
          }
        }
        articleSectionsCollection(limit: 8){
          sections: items {
            sectionType: __typename
            sys {
              id
            }
            ... on PageSectionsContent {
              content {
                json
                links {
                  assets {
                    block {
                      sys {
                        id
                      }
                      url
                      title
                      width
                      height
                      description
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`

function extractArticleSections(graphQlResponse:FetchBlogResponseProps) {
  const { data: { blogArticleCollection: { items: [article] } } } = graphQlResponse
  return article
}

export async function getBlogPageBySlug(slug: string) {
  const gqlQuery = blogPageQuery(slug)
  const graphQlResponse = await fetchGraphQL(
    gqlQuery,
    ['blogArticleCollection']
  )

  return extractArticleSections(graphQlResponse)
}

export function getArticleSections(article: ArticleTypeProps) {
  const { articleSectionsCollection } = article
  if (notEmpty(articleSectionsCollection?.sections)) {
    const { sections } = articleSectionsCollection
    return sections.filter((section) => equals(section.sectionType, 'PageSectionsContent'))
  }
  return []
}

export default getBlogPageBySlug
