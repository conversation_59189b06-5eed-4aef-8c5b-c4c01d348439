/* eslint-disable no-tabs */
import fetchGraphQL from './fetchGraphQL'

import { ThemeColors } from '@/app/themeThemes.stylex'
import { toCamelCase } from '@utils/regex'
import RichTextRenderer from '@utils/RichTextRenderer'

import { Document as RichTextNode } from '@contentful/rich-text-types'

type RichTextContent = {
  json: RichTextNode
}

type PromoBarItemResponse = {
  message: RichTextContent
  mobileMessage: RichTextContent
  settings: {
    theme?: ThemeColors
  }
  sys: {
    id: string
  }
  countdownTimer: string
}

type GlobalSettings = {
  promoBarCollection: {
    items: PromoBarItemResponse[]
  }
}

type GlobalSettingsCollection = {
  items: GlobalSettings[]
}

export type PromoBarItem = {
  message: React.ReactNode
  mobileMessage: React.ReactNode
  theme: ThemeColors
  id: string
  countdown: string
}

type PagePromoBarCollection = {
  items: [{
    promoBarCollection: {
      items: {
        message: RichTextContent
        mobileMessage: RichTextContent
        settings: {
          theme: ThemeColors
        }
        sys: {
          id: string
        }
        countdownTimer: string
      }[]
    }
  }]
}

const PROMO_BAR_QUERY = `
  query {
    globalSettingsCollection(where: {name: "Global Settings"}, limit: 1) {
      items {
        promoBarCollection(limit: 3) {
          items {
            message {
              json
            }
            mobileMessage {
              json
            }
            settings {
              theme
            }
            sys {
              id
            }
            countdownTimer
          }
        }
      }
    }
  }
`

function fetchPromoBarPageQuery(slug: string) {
  return `
  query	{
    pageCollection(where: {slug: "${slug}"}, limit: 1) {
      items {
        slug
        promoBarCollection {
          items {
            ... on PromoBar {
              message {
                json
              }
              mobileMessage {
                json
              }
              settings {
                theme
              }
              sys {
                id
              }
              countdownTimer
            }
          }
        }
      }
    }
  }
`
}

function extractPromoBar(
  globalSettingsCollection: GlobalSettingsCollection
): PromoBarItem[] {
  if (!globalSettingsCollection.items.length) {
    throw new Error('No global settings found')
  }

  const settings = {
    typographyTheme: 'bodySmall'
  }

  return globalSettingsCollection.items[0].promoBarCollection.items.map(
    (item): PromoBarItem => ({
      message: item.message?.json ? RichTextRenderer({ content: item.message.json, settings }) : '',
      mobileMessage: item.mobileMessage?.json ? RichTextRenderer({ content: item.mobileMessage.json, settings }) : '',
      theme: item?.settings?.theme ? (toCamelCase(item.settings.theme) as ThemeColors) : 'sage',
      countdown: item?.countdownTimer,
      id: item.sys.id
    })
  )
}

function extractPagePromoBar(
  pagePromoBarCollection: PagePromoBarCollection
): PromoBarItem[] {
  if (!pagePromoBarCollection?.items?.length) {
    return []
  }

  const settings = {
    typographyTheme: 'bodySmall'
  }
  return pagePromoBarCollection.items[0].promoBarCollection.items.map(
    (item) => ({
      message: item.message?.json ? RichTextRenderer({ content: item.message.json, settings }) : '',
      mobileMessage: item.mobileMessage?.json ? RichTextRenderer({ content: item.mobileMessage.json, settings }) : '',
      theme: item?.settings?.theme ? (toCamelCase(item.settings.theme) as ThemeColors) : 'sage',
      countdown: item?.countdownTimer,
      id: item.sys.id
    })
  )
}

export default async function fetchPromoBar(slug?: string) {
  let promoBarResponse = await fetchGraphQL(
    PROMO_BAR_QUERY,
    ['promoBar']
  )

  if (slug) {
    const pagePromoBarResponse = await fetchGraphQL(
      fetchPromoBarPageQuery(slug),
      ['promoBar']
    )

    if (pagePromoBarResponse.data.pageCollection.items.length) {
      promoBarResponse = pagePromoBarResponse
    }

    return extractPagePromoBar(promoBarResponse.data.pageCollection)
  }

  if (!promoBarResponse.data.globalSettingsCollection.items.length) {
    throw new Error('No global settings found')
  }

  return extractPromoBar(promoBarResponse.data.globalSettingsCollection)
}
