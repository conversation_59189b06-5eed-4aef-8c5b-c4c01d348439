const extractTabs = (section: { slides?: any; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        section.slides = section.slides || []
        const slide = {
          title: block.name,
          content: block.content,
          id: block.sys.id,
          settings: block.settings,
        }
        section.slides.push(slide)
      }
    })
  }
}

export default extractTabs
