import RichTextRenderer from '@utils/RichTextRenderer'
import { toCamelCase } from '@/utils/regex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import getMediaType from '@/utils/getMediaType'
import { CallToActionExtractProps } from '@utils/generateCallToActionProps'

import { ReactNode } from 'react'

type media = {
  url: string
  width: number | null
  height: number | null
  type: 'image' | 'video' | 'unknown'
}

type ContentContainerType = {
  title: string;
  content: ReactNode;
  id: string;
  theme?: string;
  fontFamily?: string | null;
  media: media | null;
  callToAction: CallToActionExtractProps | null;
};

type SectionType = {
  settings?: { layout: string };
  containers?: ContentContainerType[];
  blocksCollection?: { items: any[] };
};

function generateMedia(assetsCollection: any) {
  if (!assetsCollection || !assetsCollection.items) return null

  return assetsCollection.items.map((asset: any) => ({
    url: asset.url || '',
    width: asset.width || null,
    height: asset.height || null,
    type: getMediaType(asset.fileName || '')
  }))
}

const extractContentContainer = (section: SectionType) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection || { items: [] }

  section.containers = []
  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent' && block.content) {
        const { theme, fontFamily } = block.settings || {}
        const container: ContentContainerType = {
          title: block.title,
          content: RichTextRenderer({ content: block.content.json, links: block.content.links }),
          id: block.sys.id,
          theme: theme ? toCamelCase(theme) as ThemeColors : undefined,
          fontFamily: fontFamily ?? null,
          media: generateMedia(block.assetsCollection),
          callToAction: block.referencesCollection.items.find((item: any) => item.__typename === 'BlockCallToAction')
        }
        section.containers?.push(container)
      }
    })
  }

  return section.containers
}

export default extractContentContainer
