import generateCallToActionProps from '@/utils/generateCallToActionProps'

const extractFeature = (section: { slides?: any; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent' && block.content) {
        section.slides = section.slides || []
        let subHeader = ''
        let mainContent = ''
        block.content.json.content.map((content: any) => {
          if (content.nodeType === 'paragraph') {
            mainContent += content.content[0].value
          } else {
            subHeader = content.content[0].value
          }
          return content
        })

        let button
        if (block.referencesCollection?.items[0]) {
          button = generateCallToActionProps(block.referencesCollection?.items[0])
        }

        const slide = {
          subheader: subHeader,
          body: mainContent,
          header: block.title,
          id: block.sys.id,
          image: block.assetsCollection?.items[0]?.url,
          button
        }
        section.slides.push(slide)
      }
    })
  }
}

export default extractFeature
