type SlideTextContent = {
  nodeType: string;
  content: { value: string }[];
};

type SlideButtonItem = {
  text?: string;
  customUrl?: string;
  settings: { theme: string };
  _id?: string;
};

type Slide = {
  id?: string;
  text?: { json?: { content?: SlideTextContent[] } };
  button?: { items?: SlideButtonItem[] };
  media?: { items?: { url: string }[] };
};

const extractFeatureSlider = (section: { slides?: Slide[]; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        section.slides = section.slides || []
        const slide = {
          button: block.referencesCollection,
          media: block.assetsCollection,
          text: block.content,
          id: block.sys.id
        }
        section.slides.push(slide)
      }
    })
  }
}

export default extractFeatureSlider
