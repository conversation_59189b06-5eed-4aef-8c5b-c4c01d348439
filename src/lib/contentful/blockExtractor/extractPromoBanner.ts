type SLIDE = {
  assetsCollection: any;
  id: string;
};

const extractPromoBanner = (section: { slides?: SLIDE[]; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        section.slides = section.slides || []
        const slide = {
          assetsCollection: block.assetsCollection,
          id: block.sys.id
        }
        section.slides.push(slide)
      }
    })
  }
}

export default extractPromoBanner
