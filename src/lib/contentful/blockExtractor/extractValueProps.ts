import { toCamelCase } from '@utils/regex'
import generateCallToActionProps from '@/utils/generateCallToActionProps'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

type Block = {
  __typename: string
  text?: string
  anchor?: string
  page?: {
    slug?: string
    __typename?: string
  }
  settings?: {
    theme?: string
  }
  sys: {
    id: string
  }
  assetsCollection?: {
    items: any[]
  }
  title?: string
  content?: any
}

type Section = {
  slides?: any[]
  buttons?: any[]
  blocksCollection?: {
    items: Block[]
  }
}

const addButtonToSection = (section: Section, block: Block) => {
  section.buttons = section.buttons ?? []

  section.buttons.push(
    generateCallToActionProps(block)
  )
}

const addSlideToSection = (section: Section, block: Block) => {
  section.slides = section.slides ?? []
  const slide = {
    theme: block.settings?.theme && toCamelCase(block.settings.theme),
    image: block.assetsCollection?.items,
    title: block.title,
    text: block.content && documentToPlainTextString(block.content?.json),
    id: block.sys.id
  }
  section.slides.push(slide)
}

const extractValueProps = (section: Section) => {
  const blocks = section.blocksCollection?.items

  if (!blocks || blocks.length === 0) {
    return
  }

  blocks.forEach((block) => {
    if (block.__typename === 'BlockCallToAction') {
      addButtonToSection(section, block)
    } else if (block.__typename === 'BlockContent') {
      addSlideToSection(section, block)
    }
  })
}

export default extractValueProps
