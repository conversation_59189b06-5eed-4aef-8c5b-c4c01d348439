import { ContentfulTokenSettings } from '../types/generic'

import RichTextRenderer from '@utils/RichTextRenderer'

import { ReactNode } from 'react'

type VideoModuleProps = {
  header: string,
  subheader: string,
  content: ReactNode,
  assetsCollection: {
    items: {
      fileName: string,
      url: string
    }[],
  },
  mobileAssetsCollection?: {
    items: {
      fileName: string,
      url: string
    }[]
  },
  assets?: {
    video?: { fileName: string, url: string },
    image?: { fileName: string, url: string }
  },
  mobileAssets?: {
    mobileVideo?: { fileName: string, url: string },
    mobileImage?: { fileName: string, url: string }
  },
  settings: ContentfulTokenSettings
}

const extractVideoModule = (section: VideoModuleProps) => {
  const content = (section?.content as any)?.json
  const richTextContent = RichTextRenderer({ content })
  const video = section.assetsCollection.items.filter((asset: any) => asset.url.includes('mp4'))?.[0]
  const image = section.assetsCollection.items.filter((asset: any) => !asset.url.includes('mp4'))?.[0]
  const mobileVideo = section.mobileAssetsCollection?.items.filter((asset: any) => asset.url.includes('mp4'))?.[0]
  const mobileImage = section.mobileAssetsCollection?.items.filter((asset: any) => !asset.url.includes('mp4'))?.[0]

  section.content = richTextContent
  section.assets = { video, image }
  section.mobileAssets = { mobileVideo, mobileImage }
}

export default extractVideoModule
