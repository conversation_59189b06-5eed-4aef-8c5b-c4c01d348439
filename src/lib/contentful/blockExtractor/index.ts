import extractAssetSlider from './extractAssetSlider'
import extractMarquee from './extractMarquee'
import extractFeatureSlider from './extractFeatureSlider'
import extractTestimonials from './extractTestimonials'
import extractZPattern from './extractZPattern'
import extractPress from './extractPress'
import extractHero from './extractHero'
import extractPageCTA from './extractPageCTA'
import extractTabs from './extractTabs'
import extractValueProps from './extractValueProps'
import extractIconLogos from './extractIconLogos'
import extractInstructionsListCircle from './extractInstructionsListCircle'
import extractFaq from './extractFaq'
import extractInformationalText from './extractInformationalText'
import extractFeature from './extractFeature'
import extractProductQuote from './extractProductQuote'
import extractPromoBanner from './extractPromoBanner'
import extractPromotionalBlockSlider from './extractPromotionalBlockSlider'
import extractProgress from './extractProgress'
import extractBlogHeroBanner from './extractBlogHeroBanner'
import extractCategoriesLarge from './extractCategoriesLarge'
import extractAnchorNavigation from './extractAnchorNavigation'
import extractTrade from './extractTrade'
import extractThreeBlockModule from './extractThreeBlockModule'
import extractScrollFeatureSlider from './extractScrollFeatureSlider'
import extractMultfunctional from './extractorMultiFunctional'
import extractTimelineSlider from './extractTimelineSlider'
import extractTetrisModule from './extractTetrisModule'
import extractCategoriesSmall from './extractCategoriesSmall'
import extractAccordionSplit from './extractAccordionSplit'
import extractVideoModule from './extractVideoModule'
import extractMoreArticles from './extractMoreArticles'
import extractWhySlider from './extractWhySlider'
import extractProductTooltips from './extractProductTooltips'
import extractWhatsIncluded from './extractorWhatsIncluded'
import extractFeaturedStories from './extractFeaturedStories'
import extractWeeklyDeals from './extractWeeklyDeals'
import extractCompareChart from './extractCompareChart'
import extractToggle from './extractToggle'
import extractBannerWithImages from './extractBannerWithImages'
import extractCustomHero from './extractCustomHero'
import extractSplitAssetModule from './extractSplitAssetModule'
import extractContentContainer from './extractContentContainer'
import extractSlideCards from './extractSlideCards'
import extractNavPromotional from './extractNavPromotional'

const extractors: { [key: string]: (section: any) => void } = {
  Marquee: extractMarquee,
  PageCTA: extractPageCTA,
  TextBanner: extractPageCTA,
  FeatureSlider: extractFeatureSlider,
  Tabs: extractTabs,
  Testimonials: extractTestimonials,
  ZPattern: extractZPattern,
  Press: extractPress,
  Hero: extractHero,
  ValueProps: extractValueProps,
  IconLogos: extractIconLogos,
  InstructionsListCircle: extractInstructionsListCircle,
  Faq: extractFaq,
  InformationalText: extractInformationalText,
  Feature: extractFeature,
  AssetSlider: extractAssetSlider,
  ProductQuote: extractProductQuote,
  PromoBanner: extractPromoBanner,
  PromotionalBlockSlider: extractPromotionalBlockSlider,
  Progress: extractProgress,
  BlogHeroBanner: extractBlogHeroBanner,
  CategoriesLarge: extractCategoriesLarge,
  AnchorNavigation: extractAnchorNavigation,
  AnchorNavigationCollection: extractAnchorNavigation,
  Trade: extractTrade,
  ThreeBlockModule: extractThreeBlockModule,
  ScrollFeatureSlider: extractScrollFeatureSlider,
  MultiFunctional: extractMultfunctional,
  WhatsIncluded: extractWhatsIncluded,
  TimelineSlider: extractTimelineSlider,
  TetrisModule: extractTetrisModule,
  CategoriesSmall: extractCategoriesSmall,
  AccordionSplit: extractAccordionSplit,
  MoreArticles: extractMoreArticles,
  WhySlider: extractWhySlider,
  ProductTooltips: extractProductTooltips,
  FeaturedStories: extractFeaturedStories,
  WeeklyDeals: extractWeeklyDeals,
  CompareChart: extractCompareChart,
  Toggle: extractToggle,
  BannerWithImages: extractBannerWithImages,
  CustomHero: extractCustomHero,
  SplitAssetModule: extractSplitAssetModule,
  VideoModule: extractVideoModule,
  ContentContainer: extractContentContainer,
  SlideCards: extractSlideCards,
  NavPromotional: extractNavPromotional
}

const Extractor = (subtype: string, section: any) => {
  const extract = extractors[subtype]
  return extract ? extract(section) : null
}

export default Extractor
