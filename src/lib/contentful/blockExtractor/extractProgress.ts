import { notEmpty } from '@/utils/checking'
import generateCallToActionProps, { CallToActionExtractProps } from '@/utils/generateCallToActionProps'
import RichTextRenderer from '@/utils/RichTextRenderer'

const extractProgress = (section: any) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  const buttons: CallToActionExtractProps[] = []
  const steps: any[] = []

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        if (notEmpty(block.content)) {
          steps.push(
            {
              id: block.sys.id,
              content: RichTextRenderer({ content: block.content?.json })
            }
          )
        }
      }

      if (block.__typename === 'BlockCallToAction') {
        buttons.push(
          generateCallToActionProps(block)
        )
      }
    })
  }

  const [callToAction] = buttons

  section.steps = steps
  section.callToAction = callToAction
}

export default extractProgress
