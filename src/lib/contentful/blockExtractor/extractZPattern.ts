import { condition, notEmpty } from '@/utils/checking'
import generateCallToActionProps, { CallToActionExtractProps } from '@/utils/generateCallToActionProps'
import { toCamelCase } from '@/utils/regex'
import RichTextRenderer from '@utils/RichTextRenderer'

type SettingsType = {
  theme: string;
  layout: string;
}

type BlockContentType = {
  name: string;
  content: {
    json: any
  };
  settings: SettingsType;
  assetsCollection: {
    url: string;
  };
  referencesCollection: any;
  sys: {
    id: string;
  };
}

type BlocksCollectionType = {
  items: BlockContentType[];
}

type SlideType = {
  referencesCollection: any;
  settings: SettingsType;
  media: {
    url: string;
  };
  title: string;
  text: {
    json: any
  };
  id: string;
  button?: CallToActionExtractProps;
}

type SectionType = {
  referencesCollection: any;
  settings: SettingsType;
  blocksCollection: BlocksCollectionType;
  media: {
    url: string;
  };
  title: string;
  text: {
    json: any
  };
  id: string;
  button?: CallToActionExtractProps;
  layout: string;
  slides: SlideType[];
}

const extractZPattern = (section: SectionType) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  section.layout = section.settings?.layout
  section.slides = []

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        const callToActionBlocks = block.referencesCollection?.items?.filter(
          (ref: any) => ref.__typename === 'BlockCallToAction'
        )
        const callToActionProps = callToActionBlocks?.map((ctaBlock: any) => generateCallToActionProps(ctaBlock))

        const text = condition<any>(
          notEmpty(block.content),
          RichTextRenderer({ content: block.content?.json }),
          []
        )

        const slide = {
          referencesCollection: block.referencesCollection,
          settings: {
            theme: toCamelCase(block.settings?.theme),
            layout: toCamelCase(block.settings?.layout),
          },
          media: block?.assetsCollection,
          mediaMobile: block?.mobileAssetsCollection,
          title: block.title,
          subtitle: block.subtitle,
          text,
          id: block.sys.id,
          button: notEmpty(callToActionProps) ? callToActionProps : null
        }

        section.slides.push(slide)
      }
    })
  }
  return section.slides
}

export default extractZPattern
