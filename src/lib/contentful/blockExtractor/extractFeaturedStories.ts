import generateSlug from '@/utils/generateSlug'

type articles = {
  image: string,
  category: string,
  title: string,
  id: string,
  url: string
}[]

const extractFeaturedStories = (section: { articles?: articles; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        const mainArticle = block.referencesCollection.items[0]
        if (mainArticle.__typename === 'BlogArticle') {
          section.articles = section.articles || []
          const article = {
            image: mainArticle.seoMetadata.image.url || '',
            category: 'Cooking & Home',
            url: generateSlug(mainArticle.slug, mainArticle.__typename) || '',
            title: mainArticle.name || '',
            id: mainArticle.sys.id
          }
          section.articles.push(article)
        }
      }
    })
  }
}

export default extractFeaturedStories
