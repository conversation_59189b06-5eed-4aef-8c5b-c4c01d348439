import generateCallToActionProps from '@/utils/generateCallToActionProps'
import { CalloutProps } from '@/components/Generic/Callout/types'
import { toCamelCase } from '@/utils/regex'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

interface Block {
  __typename: string;
  sys: { id: string };
  title?: string;
  content: { json: any };
  settings: { layout: string };
  assetsCollection?: { items: { url: string; fileName: string }[] };
  referencesCollection?: { items: any[] };
}

interface Section {
  cards: any[];
  blocksCollection?: { items: Block[] };
}

const extractCTAAndCallouts = (block: Block) => {
  let CTA = ''
  const callouts: CalloutProps[] = []

  block.referencesCollection?.items.forEach((item: any) => {
    if (item.__typename === 'BlockCallToAction') {
      CTA = item
    } else if (item.__typename === 'Callout') {
      callouts.push(item)
    }
  })

  return { CTA, callouts }
}

const determineLayout = (block: Block) => {
  let rounded = false
  let ratio: string | null = null

  if (toCamelCase(block.settings?.layout) === 'layout2') {
    rounded = true
    ratio = '1'
  }

  return { rounded, ratio }
}

const extractImage = (block: Block, index: number) => ({
  url: block.assetsCollection?.items[index]?.url || '',
  title: block.assetsCollection?.items[index]?.fileName || '',
  width: 750,
  height: 898,
})

const createSlide = (block: Block) => {
  const { CTA, callouts } = extractCTAAndCallouts(block)
  const { rounded, ratio } = determineLayout(block)

  return {
    id: block.sys.id,
    title: block.title || '',
    description: documentToPlainTextString(block.content?.json),
    image: extractImage(block, 0),
    hoverImage: extractImage(block, 1),
    link: generateCallToActionProps(CTA).href || '',
    callouts,
    rounded,
    ratio,
  }
}

const extractCategoriesSmall = (section: Section) => {
  const { blocksCollection } = section
  if (!blocksCollection || !blocksCollection.items.length) return

  section.cards = section.cards || []

  blocksCollection.items.forEach((block: Block) => {
    if (block.__typename !== 'BlockContent') return

    const slide = createSlide(block)
    if (section.cards) {
      section.cards.push(slide)
    }
  })
}
export default extractCategoriesSmall
