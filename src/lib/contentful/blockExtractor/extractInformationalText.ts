type slide = Array<{ title: string, content: { json: any } }>;
const extractInformationalText = (section: { slides?: slide; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent' && block.content) {
        section.slides = section.slides || []
        const slide = {
          content: block.content,
          title: block.title,
        }
        section.slides.push(slide)
      }
    })
  }
}

export default extractInformationalText
