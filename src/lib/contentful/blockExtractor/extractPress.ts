import { PressSlide, TypeFaceQuote } from '@/components/Content/Press'
import { RatingProps } from '@/components/Generic/Rating'
import { getStoreReviewsAggregate } from '@/lib/okendo'
import { notEmpty } from '@/utils/checking'
import { getTypeFace } from '@/utils/contentfulSettings'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

type PressSession = {
  slides: PressSlide[]
  settings?: {
    theme?: string
    fontFamily: 'Secondary (Suisse Works, sans-serif)' | 'Primary (Moderat, sans-serif)'
  },
  header: string
  blocksCollection: {
    items: any[]
  }
  quoteTypeFace?: TypeFaceQuote
  ratingProps?: RatingProps
}

const extractPress = async (section: PressSession) => {
  const { blocksCollection, settings } = section
  const { items: blocks } = blocksCollection

  // TODO: to prevent bugs we should not mutate the original object, we need to refactor this extractor way to prevent side effects in the future.

  section.slides = []
  section.quoteTypeFace = getTypeFace(settings?.fontFamily)

  const { rating, count } = await getStoreReviewsAggregate()
  section.ratingProps = {
    reviewCount: count,
    rating,
  }

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      const {
        sys: { id },
        content,
        assetsCollection
      } = block

      const [asset] = assetsCollection.items
      let image = null

      if (asset) {
        const { url, fileName } = asset
        const src = url
        const isSvg = fileName.includes('.svg')
        image = {
          width: 150,
          height: 44,
          src,
          isSvg,
        }
      }

      section.slides.push({
        id,
        text: content && documentToPlainTextString(content.json),
        image,
      })
    })
  }
}

export default extractPress
