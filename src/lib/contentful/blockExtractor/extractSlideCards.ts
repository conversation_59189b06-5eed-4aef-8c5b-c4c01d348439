import { evaluateAsset, getFirstAsset } from '@/utils/asset'
import generateCallToActionProps from '@/utils/generateCallToActionProps'
import { toCamelCase } from '@/utils/regex'
import RichTextRenderer from '@/utils/RichTextRenderer'

const extractMarquee = (section: { slides?: any; blocksCollection?: any; content: any }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  section.slides = []

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent' && block.content) {
        const { referencesCollection } = block

        const ctaRef = referencesCollection.items.find((item: any) => item.__typename === 'BlockCallToAction')

        const callToAction = ctaRef
          ? generateCallToActionProps(ctaRef)
          : null

        const theme = block.settings?.theme
          ? toCamelCase(block.settings?.theme)
          : 'navy'

        const slide = {
          theme,
          asset: evaluateAsset(
            getFirstAsset(block.assetsCollection),
          ),
          mobileAsset: evaluateAsset(
            getFirstAsset(block.mobileAssetsCollection)
          ),
          title: block.title,
          content: block.content && RichTextRenderer({ content: block.content?.json }),
          mobileContent: block.mobileContent && RichTextRenderer({ content: block.mobileContent?.json }),
          id: block.sys.id,
          callToAction
        }
        section.slides.push(slide)
      }
    })
  }

  section.content = section.content && RichTextRenderer({ content: section.content?.json })
}

export default extractMarquee
