import generateCallToActionProps from '@/utils/generateCallToActionProps'

interface Block {
  __typename: string;
  sys: {
    id: string;
  };
}

interface Section {
  slides?: Slide[];
  blocksCollection?: {
    items: Block[];
  };
}

interface Slide {
  text: string;
  href: string;
  id: string;
}

const extractAnchorNavigation = (section: Section) => {
  const { blocksCollection } = section
  if (blocksCollection && blocksCollection.items.length > 0) {
    const slides = blocksCollection.items
      .filter((block) => block.__typename === 'BlockCallToAction')
      .map((block) => {
        const callToActionProps = generateCallToActionProps(block)
        return {
          text: callToActionProps.children || '',
          href: callToActionProps.href || '',
          anchor: callToActionProps.anchor || '',
          id: block.sys.id,
          variant: callToActionProps.variant,
          theme: callToActionProps.theme,
        }
      })

    section.slides = [...section.slides || [], ...slides]
  }
}

export default extractAnchorNavigation
