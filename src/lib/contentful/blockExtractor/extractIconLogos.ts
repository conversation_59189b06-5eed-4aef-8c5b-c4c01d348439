import generateSlug from '@utils/generateSlug'
import RichTextRenderer from '@utils/RichTextRenderer'
import { toCamelCase } from '@/utils/regex'

// TODO: type strongly
type BlockContent = {
  __typename: 'BlockContent'
  assetsCollection: any
  name: string
  content: any
  sys: { id: string }
}

type BlockCallToAction = {
  __typename: 'BlockCallToAction'
  text: string
  anchor: string
  page?: { slug?: string; __typename?: string }
  settings?: { theme?: string }
  sys: { id: string }
}

type Section = {
  buttons?: Button[]
  slides?: Slide[]
  blocksCollection?: { items: (BlockContent | BlockCallToAction)[] }
}

type Slide = {
  image: any
  name: string
  text: any
  id: string
}

type Button = {
  text: string
  anchor: string
  href: string
  variant: string | undefined
  id: string
}

// TODO: extract to be used elsewhere
const createSlide = (block: BlockContent): Slide => ({
  image: block.assetsCollection,
  name: block.name,
  text: block.content && RichTextRenderer({ content: block.content?.json }),
  id: block.sys.id,
})

// TODO: extract to be used elsewhere
const createButton = (block: BlockCallToAction): Button => {
  const href = block.page?.slug && block.page?.__typename
    ? generateSlug(block.page.slug, block.page.__typename)
    : '#'

  return {
    text: block.text,
    anchor: block.anchor,
    href,
    variant: block.settings?.theme && toCamelCase(block.settings.theme),
    id: block.sys.id,
  }
}

const extractIconLogos = (section: Section) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection || { items: [] }

  const { slides, buttons } = blocks.reduce(
    (acc, block) => {
      if (block.__typename === 'BlockContent') {
        acc.slides.push(createSlide(block as BlockContent))
      } else if (block.__typename === 'BlockCallToAction') {
        acc.buttons.push(createButton(block as BlockCallToAction))
      }
      return acc
    },
    { slides: [] as Slide[], buttons: [] as Button[] }
  )

  section.slides = slides.length ? slides : section.slides
  section.buttons = buttons.length ? buttons : section.buttons
}

export default extractIconLogos
