import generateCallToActionProps from '@/utils/generateCallToActionProps'
import generateSlug from '@/utils/generateSlug'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

interface Block {
  __typename: string;
  content: { json: any };
  title: string;
  assetsCollection?: { items: { url: string }[] };
  referencesCollection?: { items: any[] };
  sys: { id: string };
}

interface Section {
  slides?: Slide[];
  blocksCollection?: { items: Block[] };
}

interface Slide {
  subheading: string;
  heading: string;
  media: { src: string | undefined };
  button: { url: string; label: string };
  id: string;
}

const extractBlogHeroBanner = (section: Section): void => {
  const { blocksCollection } = section
  const blocks = blocksCollection?.items || []

  if (blocks.length > 0) {
    blocks.forEach((block) => {
      if (block.__typename === 'BlockContent') {
        section.slides = section.slides || []
        let slide: Slide
        if (block.referencesCollection?.items[0].__typename === 'BlogArticle') {
          slide = {
            subheading: 'Home & Kitchen',
            heading: block.referencesCollection?.items[0].name,
            media: {
              src: block.referencesCollection?.items[0]?.seoMetadata?.image?.url ||
              block.assetsCollection?.items[0]?.url || '',
            },
            button: {
              url: generateSlug(block.referencesCollection?.items[0].slug, 'Blog Article'),
              label: 'Read More',
            },
            id: block.referencesCollection?.items[0].sys?.id,
          }
        } else {
          slide = {
            subheading: documentToPlainTextString(block.content?.json) || '',
            heading: block.title,
            media: { src: block.assetsCollection?.items[0]?.url },
            button: {
              url: generateCallToActionProps(block.referencesCollection?.items[0]).href,
              label: generateCallToActionProps(block.referencesCollection?.items[0]).children,
            },
            id: block.sys?.id,
          }
        }
        section.slides.push(slide)
      }
    })
  }
}

export default extractBlogHeroBanner
