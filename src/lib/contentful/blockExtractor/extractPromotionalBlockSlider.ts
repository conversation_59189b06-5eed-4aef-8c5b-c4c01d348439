import RichTextRender from '@utils/RichTextRenderer'

export const extractSlider = (blocks: any) => {
  const slides:any = []
  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        const slide = {
          title: block.name,
          content: block.content && RichTextRender({
            content: block.content.json
          }),
          settings: block.settings,
          id: block.sys.id,
        }
        slides.push(slide)
      }
    })
  }
  return slides
}

const extractPromotionalBlockSlider = (section: { slides?: any; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection
  section.slides = extractSlider(blocks)
}

export default extractPromotionalBlockSlider
