import RichTextRenderer from '@utils/RichTextRenderer'

const extractMarquee = (section: { slides?: any; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      const settings = block.settings || {}
      if (block.__typename === 'BlockContent' && block.content) {
        section.slides = section.slides || []
        const slide = {
          text: RichTextRenderer({ content: block.content.json, settings }),
          id: block.sys.id
        }
        section.slides.push(slide)
      }
    })
  }
}

export default extractMarquee
