import { RatingProps } from '@/components/Generic/Rating'
import { getStoreReviewsAggregate } from '@/lib/okendo'
import { condition, notEmpty } from '@/utils/checking'
import RichTextRenderer from '@utils/RichTextRenderer'

const extractTestimonials = async (section: { slides?: any; blocksCollection?: any; ratingProps: RatingProps }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  section.slides = []

  const { count, rating: aggregatedRating } = await getStoreReviewsAggregate()

  section.ratingProps = {
    reviewCount: count,
    rating: aggregatedRating
  }

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContentQuote') {
        const { logo, rating } = block
        const content = condition<any>(
          notEmpty(block.quote),
          RichTextRenderer({ content: block.quote?.json }),
          [],
        )
        const { author } = block

        const [firstImage, secondImage] = block.imagesCollection?.items || [
          { url: '' },
          { url: '' },
        ]

        const slides = [
          {
            id: `${block.sys?.id}-firstImage`,
            ...firstImage,
            type: 'image',
          },
          {
            id: `${block.sys?.id}-quote`,
            rating,
            content,
            author,
            logo,
            type: 'quote',
          },
          {
            id: `${block.sys?.id}-secondImage`,
            ...secondImage,
            type: 'image',
          },
        ]
        section.slides = [
          ...section.slides,
          ...slides,
        ]
      }
    })
  }

  return section.slides
}

export default extractTestimonials
