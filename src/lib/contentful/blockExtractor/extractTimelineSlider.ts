import { ContentfulTokenSettings } from '../types/generic'

import RichTextRenderer from '@utils/RichTextRenderer'

interface Block {
  __typename: string;
  settings?: any;
  assetsCollection?: {
    items: { url: string }[];
  };
  title: string;
  content: {
    json: any;
  };
  sys: {
    id: string;
  };
}

interface Section {
  slides?: Slide[];
  blocksCollection?: {
    items: Block[];
  };
  settings?: ContentfulTokenSettings
}

interface Slide {
  media?: string;
  mobileMedia?: string;
  header: string;
  description: React.ReactNode;
  id: string;
}

const extractTimelineSlider = (section: Section): Section => {
  const { blocksCollection, settings: sectionSettings } = section
  const blocks = blocksCollection?.items || []

  const slides = blocks.reduce<Slide[]>((acc, block) => {
    if (block.__typename === 'BlockContent') {
      const settings = block.settings || {}
      const slide: Slide = {
        media: block.assetsCollection?.items[0]?.url,
        mobileMedia: block.assetsCollection?.items[1]?.url,
        header: block.title,
        description: <PERSON>Text<PERSON><PERSON><PERSON>({ content: block.content.json, settings }),
        id: block.sys.id,
      }
      acc.push(slide)
    }
    return acc
  }, [])

  section.slides = slides || []
  section.settings = sectionSettings || {}
  return section
}

export default extractTimelineSlider
