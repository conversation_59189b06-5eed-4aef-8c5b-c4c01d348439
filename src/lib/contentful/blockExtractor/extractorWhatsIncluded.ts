import { AssetType } from '@/components/Content/MediaContainer'
import { AnchorProps } from '@/components/Generic/CallToAction'
import { imageProps } from '@/components/Generic/Card/types'
import { DialogProps } from '@/components/Generic/Dialog/types'
import { getFirstAsset, getLastAsset } from '@/utils/asset'
import { notEmpty } from '@/utils/checking'
import generateCallToActionProps from '@/utils/generateCallToActionProps'
import getMediaType from '@/utils/getMediaType'
import { toCamelCase } from '@/utils/regex'
import RichTextRenderer from '@utils/RichTextRenderer'

const LAYOUTS = {
  'Layout 1': 'horizontal',
  'Layout 2': 'two-column',
}

type WhatsIncludedChildProps = {
  id: string
  title: string
  description: any
  overlay: boolean
  theme: string
  image?: imageProps
  video?: {
    ratio: string
    playOnHover: boolean
    sources: {
      src: string
      type: string
    }[]
  }
  posterImage?: any
}
type WhatsIncludedItemProps = {
  id?: string
  name?: string
  description?: any
  img?: string
  layout?: string
  links?: AnchorProps[],
  items?: WhatsIncludedChildProps[]
  dialog?: DialogProps,
  fixedLink?: AnchorProps | null
}

const buildVideoProps = (asset: AssetType) => (
  {
    ratio: asset.width && asset.height ? `${asset.width} / ${asset.height}` : '1 / 1.2',
    playOnHover: true,
    sources: [
      {
        src: asset.url,
        type: 'video/mp4'
      }
    ]
  }
)

const buildImageProps = (asset: AssetType) => {
  if (notEmpty(asset?.url)) {
    return {
      height: asset.height,
      ratio: `${asset.width} / ${asset.height}`,
      url: asset.url,
      width: asset.width,
      title: asset.description || asset.title || ''
    }
  }
  return undefined
}

const extractWhatsIncluded = (section: any) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  const items: WhatsIncludedItemProps[] = []
  const ctaDialog: { text: string; id: string } = { text: '', id: '' }

  let fixedLink = null as AnchorProps | null

  if (notEmpty(blocks)) {
    // eslint-disable-next-line complexity
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockCallToAction' && block.page.__typename !== 'BlockDialog') {
        fixedLink = generateCallToActionProps(block) as AnchorProps
      }

      if (block.__typename === 'BlockCallToAction' && block.page.__typename === 'BlockDialog') {
        if (!ctaDialog.text) {
          ctaDialog.text = block.text
        }
        if (!ctaDialog.id) {
          ctaDialog.id = block.page.sys.id
        }
      }

      if (block.__typename === 'BlockContent') {
        const { referencesCollection, settings } = block
        const img = getFirstAsset(block.assetsCollection).url

        const layout = LAYOUTS[settings?.layout as keyof typeof LAYOUTS] || 'three-column'
        const theme = toCamelCase(settings?.theme) || 'cream'
        const links = [] as AnchorProps[]
        const childItems = [] as WhatsIncludedChildProps[]

        referencesCollection.items?.forEach((b: any) => {
          if (b.__typename === 'BlockContent') {
            const mainAsset = getLastAsset(b.assetsCollection)
            const src = mainAsset.url
            const isVideo = getMediaType(src) === 'video'

            const video = isVideo ? buildVideoProps(mainAsset) : undefined
            const image = !isVideo ? (buildImageProps(mainAsset) as imageProps) : undefined

            const firstAsset = getFirstAsset(b.assetsCollection)

            const posterImage = firstAsset !== mainAsset ? firstAsset : undefined

            childItems.push({
              id: b.sys.id,
              title: b.title,
              description: b.content?.json && RichTextRenderer({ content: b.content?.json }),
              overlay: true,
              theme: toCamelCase(b.settings?.theme) || theme,
              video,
              image,
              posterImage
            })
          }

          if (b.__typename === 'BlockCallToAction') {
            links.push(generateCallToActionProps(b) as AnchorProps)
          }
        })

        items.push({
          id: block.sys.id,
          name: block.title,
          description: block.content?.json && RichTextRenderer({ content: block.content?.json }),
          links,
          img,
          layout,
          fixedLink,
          items: childItems
        })
      }
    })
  }

  section.fixedLink = fixedLink
  section.items = items
  section.content = { items, ctaDialog }
  section.anchorTargeting = section?.settings?.anchorTargeting || '#'

  // console.log('items wiz khalifa: ', items[0].items)
}

export default extractWhatsIncluded
