import generateCallToActionProps from '@/utils/generateCallToActionProps'

const extractSplitAssetModule = (section: { button?: any; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockCallToAction') {
        section.button = generateCallToActionProps(block)
      }
    })
  }
}

export default extractSplitAssetModule
