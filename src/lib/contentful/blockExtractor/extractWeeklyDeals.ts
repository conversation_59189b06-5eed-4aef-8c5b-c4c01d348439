import { notEmpty } from '@/utils/checking'
import generateCallToActionProps from '@/utils/generateCallToActionProps'
import RichTextRenderer from '@utils/RichTextRenderer'
import { toCamelCase } from '@utils/regex'
import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

import type { WeeklyDealsSection, WeeklyDealsSettings } from '@/components/Content/WeeklyDeals/types'

const styles = stylex.create({
  link: {
    fontWeight: 'normal',
    color: {
      default: colors.navy,
      ':hover': colors.gray500
    },
    cursor: 'pointer',
  },
})
const extractWeeklyDeals = (section: WeeklyDealsSection) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection
  section.deals = []

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        const callToActionBlock = block.referencesCollection?.items?.find(
          (ref: any) => ref.__typename === 'BlockCallToAction'
        )

        const text = block.content
          ? RichTextRenderer({ content: block.content.json, settings: styles.link })
          : []

        const settings: WeeklyDealsSettings = {
          theme: toCamelCase(block.settings?.theme),
          active: block.settings?.layout === 'Active'
        }

        const deal = {
          media: block?.assetsCollection,
          title: block.title,
          text,
          id: block.sys.id,
          button: callToActionBlock && generateCallToActionProps(callToActionBlock),
          settings,
        }

        section.deals.push(deal)
      }
    })
  }

  return section
}

export default extractWeeklyDeals
