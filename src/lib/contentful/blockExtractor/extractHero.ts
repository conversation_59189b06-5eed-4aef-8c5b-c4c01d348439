import RichTextRender from '@/utils/RichTextRenderer'
import { condition, notEmpty } from '@utils/checking'
import { getTypeFace, TypeFaceSettings } from '@utils/contentfulSettings'
import generateCallToActionProps, { CallToActionExtractProps } from '@utils/generateCallToActionProps'
import { toCamelCase } from '@utils/regex'
import { AssetType } from '@/components/Content/MediaContainer'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

const layoutProps: Record<string, {
  contentAlignment?: 'left' | 'right' | 'center' | undefined,
  smallSize?: boolean,
  evenContent?: boolean,
  hideMobileImage?: boolean,
  doNotSplitImage?: boolean,
  inverse?: boolean | undefined
}> = {
  'Layout 1': {
    contentAlignment: 'center',
  },
  'Layout 2': {
    contentAlignment: 'left',
  },
  'Layout 3': {
    contentAlignment: 'right',
  },
  'Layout 4': {
    contentAlignment: 'center',
    smallSize: true
  },
  'Layout 5': {
    contentAlignment: 'left',
    evenContent: true
  },
  'Layout 6': {
    contentAlignment: 'right',
    evenContent: true
  },
  'Layout 7': {
    contentAlignment: 'left',
    hideMobileImage: true
  },
  'Layout 8': {
    contentAlignment: 'left',
    doNotSplitImage: true
  },
  'Layout 9': {
    inverse: false
  },
  'Layout 10': {
    inverse: true
  },
}

type HeroExtractProps = {
  id: string;
  media?: any;
  mobileMedia?: any;
  smallSize?: boolean;
  evenContent?: boolean;
  contentAlignment: 'left' | 'right' | 'center';
  hideMobileImage?: boolean;
  theme: string;
  subHeadline: string | undefined;
  width: string | undefined
  header: string;
  logo?: AssetType[];
  text: string;
  typeFace: TypeFaceSettings;
  buttons: CallToActionExtractProps[];
  layout: string;
}
type LayoutValue = keyof typeof layoutProps

function getSliderProps(
  block: any,
  globalTheme: string,
  globalLayout: LayoutValue
): HeroExtractProps | undefined {
  let id
  let text
  let header
  let subHeadline
  let width
  let theme
  let layout: LayoutValue
  let logo
  let media
  let mobileMedia

  const typeFace = getTypeFace(block.settings?.fontFamily)

  if (block.__typename === 'BlockContent') {
    id = block.sys.id
    text = block.content && RichTextRender({
      content: block.content?.json
    })
    header = block.title
    media = block.assetsCollection
    mobileMedia = block.mobileAssetsCollection

    theme = condition<string>(
      notEmpty(block.settings?.theme),
      block.settings?.theme ? toCamelCase(block.settings.theme) : undefined,
      globalTheme
    )

    width = block.settings?.width

    layout = condition<LayoutValue>(
      notEmpty(block.settings?.layout),
      block.settings?.layout,
      globalLayout
    )

    const buttons: CallToActionExtractProps[] = []

    if (notEmpty(block.referencesCollection?.items)) {
      const { items } = block.referencesCollection
      items.forEach((referBlock: any) => {
        if (referBlock.__typename === 'BlockContent') {
          logo = referBlock.assetsCollection?.items
          subHeadline = referBlock.content && documentToPlainTextString(
            referBlock.content?.json
          )
        }

        if (referBlock.__typename === 'BlockCallToAction') {
          buttons.push(
            generateCallToActionProps(referBlock)
          )
        }
      })
    }

    return {
      id,
      text,
      header,
      subHeadline,
      width,
      buttons,
      media,
      mobileMedia,
      logo,
      theme,
      typeFace,
      layout: toCamelCase(layout),
      ...layoutProps[layout],
    } as HeroExtractProps
  }
  return undefined
}

const extractHero = (
  section: any,
) => {
  const { settings, blocksCollection } = section
  const { items: blocks } = blocksCollection

  const props: HeroExtractProps[] = []

  const globalTheme = settings?.theme ? toCamelCase(settings.theme) : 'navy'
  const globalLayout = settings?.layout
  const anchor = settings?.anchorTargeting

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      const sliderProps = getSliderProps(block, globalTheme, globalLayout)
      if (sliderProps) {
        props.push(sliderProps)
      }
    })
  }
  section.anchor = anchor
  section.props = props
}

export default extractHero
