import { notEmpty } from '@/utils/checking'
import RichTextRender from '@/utils/RichTextRenderer'

const extractScrollFeatureSlider = (section: {
  blocksCollection?: any;
  assetsCollection?: any;
  media?: string;
  body?: {
    id: string;
    title: string;
    content: string;
  }[];
}) => {
  const { blocksCollection, assetsCollection } = section
  const { items: blocks } = blocksCollection

  section.media = assetsCollection?.items[0]?.url || ''
  section.body = []

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        section.body?.push({
          id: block.sys.id,
          title: block.title,
          content: block.content && RichTextRender({ content: block.content?.json })
        })
      }
    })
  }
}

export default extractScrollFeatureSlider
