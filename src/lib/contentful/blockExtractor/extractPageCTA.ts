import generateSlug from '@utils/generateSlug'
import { toCamelCase } from '@utils/regex'

const extractPageCTA = (section: {
  image: any; buttons?: any; blocksCollection?: any; assetsCollection?: any;
}) => {
  const { assetsCollection } = section
  section.image = assetsCollection?.items[0]?.url

  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockCallToAction') {
        section.buttons = section.buttons || []
        const button = {
          settings: block.settings,
          icon: block.icon,
          text: block.text,
          anchor: block.anchor,
          onClick: block.page?.sys?.id,
          href: generateSlug(block.page?.slug, block.page?.__typename) || block.customUrl,
          variant: block.settings?.theme && toCamelCase(block.settings.theme),
          id: block.sys.id
        }
        section.buttons.push(button)
      }
    })
  }
}

export default extractPageCTA
