import { notEmpty } from '@utils/checking'
import generateCallToActionProps, { CallToActionExtractProps } from '@utils/generateCallToActionProps'
import RichTextRender from '@utils/RichTextRenderer'

type SlideProps = {
  id: string;
  name: string;
  media: string;
  content: any;
  callToAction: CallToActionExtractProps | undefined;
}

function getSlideProps(block: any): SlideProps | undefined {
  let name
  let media
  let callToAction
  let content

  if (block.__typename === 'BlockContent') {
    if (notEmpty(block.assetsCollection)) {
      const { items } = block.assetsCollection
      if (notEmpty(items)) {
        const asset = items[0]
        name = asset.title
        media = asset.url
        content = RichTextRender({ content: block.content?.json })
      }
    }

    if (notEmpty(block.referencesCollection?.items)) {
      const { items } = block.referencesCollection
      items.forEach((referBlock: any) => {
        if (referBlock.__typename === 'BlockCallToAction') {
          callToAction = generateCallToActionProps(referBlock)
        }
      })
    }
  }

  return {
    id: block.sys.id,
    content,
    name,
    media,
    callToAction
  }
}

const extractMarquee = (section: { slides?: any; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  const slides: SlideProps[] = []

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      const slide = getSlideProps(block)
      if (slide) {
        slides.push(slide)
      }
    })
  }
  section.slides = slides
}

export default extractMarquee
