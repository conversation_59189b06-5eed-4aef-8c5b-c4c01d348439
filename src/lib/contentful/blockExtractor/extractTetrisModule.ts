import RichTextRenderer from '@utils/RichTextRenderer'

interface Block {
  __typename: string;
  settings?: any;
  assetsCollection?: {
    items: { url: string }[];
  };
  mobileAssetsCollection?: {
    items: { url: string }[];
  }
  content: {
    json: any;
  };
  sys: {
    id: string;
  };
}

interface Section {
  blocks: any[];
  blocksCollection?: {
    items: Block[];
  };
}

const extractTetrisModule = (section: Section) => {
  const blocks = section.blocksCollection?.items ?? []

  if (blocks.length > 0) {
    section.blocks = section.blocks || []
    blocks.forEach((block) => {
      const settings = block.settings || {}
      if (block.__typename === 'BlockContent') {
        const slide = {
          source: block.assetsCollection?.items[0]?.url ?? '',
          mobileSource: block.mobileAssetsCollection?.items[0]?.url ?? null,
          text: RichTextRenderer({ content: block.content.json, settings }),
          id: block.sys.id,
        }
        section.blocks.push(slide)
      }
    })
  }
}

export default extractTetrisModule
