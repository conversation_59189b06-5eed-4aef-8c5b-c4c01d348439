import generateCallToActionProps from '@/utils/generateCallToActionProps'
import generateSlug from '@/utils/generateSlug'

type Article = {
  sys: { id: string };
  name: string;
  slug: string;
  seoMetadata?: { image?: { url: string } };
  __typename: string;
}

type Block = {
  __typename: string;
  referencesCollection?: { items: Article[] };
  text?: string;
  customUrl?: string;
  slug?: string;
  page: { __typename: string; slug: string; }
}

type mainArticles = {
  image?: string;
  category: string;
  title: string;
  id: string;
  url: string;
}

type mainButton = {
  text: string;
  url: string;
}

type Section = {
  articles?: mainArticles[];
  button?: mainButton;
  blocksCollection?: { items: Block[] };
}

const handleBlockContent = (block: Block, section: Section) => {
  if (block.__typename === 'BlockContent') {
    const article = block.referencesCollection?.items[0] || null
    if (article && article.__typename === 'BlogArticle') {
      section.articles = section.articles || []
      const slide: mainArticles = {
        id: article.sys.id,
        title: article.name,
        url: generateSlug(article.slug, article.__typename),
        image: article.seoMetadata?.image?.url,
        category: 'Home | Design',
      }
      section.articles.push(slide)
    }
  }
}

const handleBlockCallToAction = (block: Block, section: Section) => {
  if (block.__typename === 'BlockCallToAction') {
    const callToActionProps = generateCallToActionProps(block)
    section.button = {
      text: callToActionProps.children,
      url: callToActionProps.href
    }
  }
}

const extractMoreArticles = (section: Section) => {
  const blocks = section.blocksCollection?.items || []

  blocks.forEach((block) => {
    handleBlockContent(block, section)
    handleBlockCallToAction(block, section)
  })
}

export default extractMoreArticles
