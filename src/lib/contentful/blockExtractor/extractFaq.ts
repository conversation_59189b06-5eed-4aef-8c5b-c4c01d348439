import { CTAProps } from '@/components/Generic/CallToAction'
import generateCallToActionProps from '@/utils/generateCallToActionProps'
import RichTextRenderer from '@utils/RichTextRenderer'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'
import { ReactNode } from 'react'

type slide = { question: string; answer: ReactNode; id: string; }

const extractFaq = (section: {
  centered?: boolean;
  settings?: { layout: string; };
  slides?: slide[]; button?: CTAProps; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  section.centered = section.settings?.layout === 'Layout 2'

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      const settings = block.settings || {}
      if (block.__typename === 'BlockContent' && block.content) {
        section.slides = section.slides || []
        const slide = {
          question: block.title,
          answer: RichTextRenderer({ content: block.content.json, settings }),
          answerPlainText: documentToPlainTextString(block.content.json),
          id: block.sys.id
        }
        section.slides.push(slide)
      } else if (block.__typename === 'BlockCallToAction') {
        section.button = generateCallToActionProps(block) as CTAProps
      }
    })
  }
}

export default extractFaq
