import RichTextRenderer from '@utils/RichTextRenderer'

const extractInstructionsListCircle = (section: { slides?: any; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        const settings = block.settings || {}
        section.slides = section.slides || []
        const slide = {
          title: block.name,
          text: block.content && RichTextRenderer({ content: block.content?.json, settings }),
          id: block.sys.id
        }
        section.slides.push(slide)
      }
    })
  }
}

export default extractInstructionsListCircle
