import RichTextRenderer from '@utils/RichTextRenderer'

type SLIDES = {
  mainImage: string;
  signatureImage: string;
}

const extractProductQuote = (section: { slides?: SLIDES[]; blocksCollection?: any; assetsCollection?: any; content?: any; }) => {
  const {
    blocksCollection,
    assetsCollection,
    content
  } = section
  const blocks = blocksCollection?.items || []

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        section.slides = section.slides || [] as SLIDES[]
        const slide = {
          mainImage: block.assetsCollection.items[0].url,
          signatureImage: block.assetsCollection.items[1].url,
          id: block.sys.id
        }
        section.slides.push(slide)
      }
    })
  } else if (assetsCollection?.items?.length > 0) {
    section.slides = section.slides || [] as SLIDES[]
    const slide = {
      mainImage: assetsCollection.items[0].url,
      signatureImage: assetsCollection.items[1].url,
    }
    section.slides.push(slide)
  }

  if (content) {
    section.content = RichTextRenderer({ content: content.json }) as React.ReactNode
  }
}

export default extractProductQuote
