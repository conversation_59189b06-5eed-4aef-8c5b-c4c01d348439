import { BlockProps } from '@/components/Content/ThreeBlockModule/Block'
import { notEmpty } from '@/utils/checking'
import generateCallToActionProps, { CallToActionExtractProps } from '@/utils/generateCallToActionProps'

import { ReactNode } from 'react'

type slide = { question: string; answer: ReactNode; id: string; }

const extractThreeBlockModule = (section: { slides?: slide[]; blocksCollection?: any; blocks?: BlockProps[]; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  section.blocks = []

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      const { referencesCollection: { items }, assetsCollection } = block

      const imageSource = assetsCollection.items?.[0]?.url ?? ''
      let callToAction: CallToActionExtractProps | undefined

      items?.forEach((referBlock: any) => {
        if (referBlock.__typename === 'BlockCallToAction') {
          callToAction = generateCallToActionProps(referBlock)
        }
      })

      section.blocks?.push({
        id: block.sys.id,
        title: block.title,
        href: callToAction?.href ?? '',
        imageSource
      })
    })
  }
}

export default extractThreeBlockModule
