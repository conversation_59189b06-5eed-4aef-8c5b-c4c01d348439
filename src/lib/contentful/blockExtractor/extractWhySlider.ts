import { condition, notEmpty } from '@/utils/checking'
import { toCamelCase } from '@/utils/regex'
import RichTextRenderer from '@/utils/RichTextRenderer'

interface Block {
  __typename: string;
  sys: { id: string };
  title?: string;
  content: { json: any };
  settings: { layout: string };
  assetsCollection?: { items: { url: string; fileName: string }[] };
  referencesCollection?: { items: any[] };
}

const extractImage = (block: Block, index: number) => block.assetsCollection?.items[index]?.url || ''

const extractWhySlider = (
  section: any,
) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        section.items = section.items || []

        const slide = {
          text: block.name,
          title: block.title,
          description: condition<any>(
            notEmpty(block.content),
            RichTextRenderer({ content: block.content?.json }),
            []
          ),
          id: block.sys.id,
          image: extractImage(block, 0),
          theme: toCamelCase(block.settings?.theme) || 'cream',
        }

        section.items.push(slide)
      }
    })
  }
}

export default extractWhySlider
