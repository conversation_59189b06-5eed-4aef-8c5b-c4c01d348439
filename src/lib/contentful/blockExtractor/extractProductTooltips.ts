import { isNumber, notEmpty } from '@utils/checking'
import generateCallToActionProps, { CallToActionExtractProps } from '@utils/generateCallToActionProps'
import { HotspotProps } from '@/components/Product/Tooltips'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

function isValidPositionProps(customProps: any) {
  return isNumber(customProps?.desktop?.x) &&
    isNumber(customProps?.desktop?.y) &&
    isNumber(customProps?.mobile?.x) &&
    isNumber(customProps?.mobile?.y)
}

// get the position of the tooltip on the setting JSON field
function getPosition(
  customProps:
  {
    desktop: {
      x: number
      y: number
    }
    mobile: {
      x: number
      y: number
    }
  }
) {
  if (
    isValidPositionProps(customProps)
  ) {
    return customProps
  }

  return {
    desktop: {
      x: 0,
      y: 0
    },
    mobile: {
      x: 0,
      y: 0
    }
  }
}

function getTooltipProp(block: any) {
  const { id } = block.sys
  const { title, settings } = block
  const description = documentToPlainTextString(
    block.content?.json
  )

  const cta = block.referencesCollection?.items?.find((item: any) => item.__typename === 'BlockCallToAction')

  return {
    position: getPosition(settings.customProps),
    popup: {
      id,
      title,
      description,
      cta: cta && generateCallToActionProps(cta as CallToActionExtractProps),
    }
  }
}

const extractProductTooltips = (section: any) => {
  const {
    blocksCollection,
    assetsCollection,
    mobileAssetsCollection
  } = section
  const { items: blocks } = blocksCollection

  const image = assetsCollection.items[0]?.url
  const mobileImage = mobileAssetsCollection.items[0]?.url

  const hotspots: HotspotProps[] = []

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      const spot = getTooltipProp(block)
      // prevent spots without position configuration
      if (spot && isValidPositionProps(block.settings.customProps)) {
        hotspots.push(spot)
      }
    })
  }

  section.image = image
  section.mobileImage = mobileImage
  section.hotspots = hotspots
}

export default extractProductTooltips
