import RichTextRenderer from '@utils/RichTextRenderer'

const extractNavPromotional = (section: { slides?: any; blocksCollection?: any; }) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  if (blocks.length > 0) {
    blocks.forEach((block: any) => {
      const settings = block.settings || undefined
      if (block.__typename === 'BlockContent' && block.content) {
        section.slides = section.slides || []
        const slide = {
          image: block.assetsCollection?.items[0]?.url || null,
          text: RichTextRenderer({ content: block.content.json, settings }),
          settings,
          id: block.sys.id
        }
        section.slides.push(slide)
      }
    })
  }
}

export default extractNavPromotional
