import { CTAProps } from '@/components/Generic/CallToAction'
import generateCallToActionProps from '@/utils/generateCallToActionProps'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

interface Block {
  __typename: string;
  title?: string;
  sys: {
    id: string;
  };
  referencesCollection?: {
    items: ReferenceItem[];
  };
}

interface ReferenceItem {
  __typename: string;
  title?: string;
}

interface Section {
  slides?: Slide[];
  buttons?: CTAProps[];
  blocksCollection?: {
    items: Block[];
  };
}

interface Slide {
  label: string;
  tags: string[];
  id: string;
}

const extractTrade = (section: Section) => {
  const blocks = section.blocksCollection?.items || []

  section.slides = []
  section.buttons = []

  blocks.forEach((block: any) => {
    if (block.__typename === 'BlockContent') {
      const slide: Slide = {
        label: block.title ?? '',
        tags: documentToPlainTextString(block.content?.json).split(','),
        id: block.sys.id,
      }
      section.slides?.push(slide)
    }

    if (block.__typename === 'BlockCallToAction') {
      const ctaProps = generateCallToActionProps(block)
      section.buttons?.push(ctaProps as CTAProps)
    }
  })
}

export default extractTrade
