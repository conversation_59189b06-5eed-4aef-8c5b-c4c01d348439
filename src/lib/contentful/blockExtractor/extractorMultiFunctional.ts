import { AssetType } from '@/components/Content/MediaContainer'
import { AnchorProps } from '@/components/Generic/CallToAction'
import { imageProps } from '@/components/Generic/Card/types'
import { getFirstAsset } from '@/utils/asset'
import { notEmpty } from '@/utils/checking'
import generateCallToActionProps from '@/utils/generateCallToActionProps'
import getMediaType from '@/utils/getMediaType'
import { toCamelCase } from '@/utils/regex'
import RichTextRenderer from '@utils/RichTextRenderer'

type MultifunctionalChildProps = {
  id: string
  title: string
  description: any
  overlay: boolean
  theme: string
  image?: imageProps
  video?: {
    ratio: string
    playOnHover: boolean
    sources: {
      src: string
      type: string
    }[]
  }
}
type MultifunctionalItemProps = {
  id: string
  name: string
  description: any
  img: string
  links: AnchorProps[],
  items: MultifunctionalChildProps[]
}

const buildVideoProps = (asset: AssetType) => (
  {
    ratio: asset.width && asset.height ? `${asset.width} / ${asset.height}` : '549 / 395',
    playOnHover: true,
    sources: [
      {
        src: asset.url,
        type: 'video/mp4'
      }
    ]
  }
)

const buildImageProps = (asset: AssetType) => {
  if (notEmpty(asset?.url)) {
    return {
      height: asset.height,
      ratio: `${asset.width} / ${asset.height}`,
      url: asset.url,
      width: asset.width,
      title: asset.description || asset.title || ''
    }
  }
  return undefined
}

const extractMultfunctional = (section: any) => {
  const { blocksCollection } = section
  const { items: blocks } = blocksCollection

  const items: MultifunctionalItemProps[] = []

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        const { referencesCollection, settings } = block
        const img = getFirstAsset(block.assetsCollection).url

        const theme = toCamelCase(settings?.theme) || 'cream'
        const links = [] as AnchorProps[]
        const childItems = [] as MultifunctionalChildProps[]

        referencesCollection.items?.forEach((b: any) => {
          if (b.__typename === 'BlockContent') {
            const asset = getFirstAsset(b.assetsCollection)
            const src = asset.url
            const isVideo = getMediaType(src) === 'video'

            const video = isVideo ? buildVideoProps(asset) : undefined
            const image = !isVideo ? buildImageProps(asset) as imageProps : undefined

            childItems.push({
              id: b.sys.id,
              title: b.title,
              description: b.content?.json && RichTextRenderer({ content: b.content?.json }),
              overlay: true,
              theme: toCamelCase(b.settings?.theme) || theme,
              video,
              image,
            })
          }

          if (b.__typename === 'BlockCallToAction') {
            links.push(
              generateCallToActionProps(b) as AnchorProps
            )
          }
        })

        items.push({
          id: block.sys.id,
          name: block.title,
          description: block.content?.json && RichTextRenderer({ content: block.content?.json }),
          links,
          img,
          items: childItems
        })
      }
    })
  }

  section.items = items
  section.content = { items }
}

export default extractMultfunctional
