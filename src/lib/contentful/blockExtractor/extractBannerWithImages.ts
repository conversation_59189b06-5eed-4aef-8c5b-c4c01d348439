import { getProductBySlug } from '../fetchProducts'

import { fontSizes } from '@/app/themeTokens.stylex'
import { CTAProps } from '@/components/Generic/CallToAction'
import generateCallToActionProps from '@/utils/generateCallToActionProps'
import RichTextRenderer from '@utils/RichTextRenderer'

import { ReactNode } from 'react'

type Slide = {
  title: string;
  subtitle: string;
  text: ReactNode;
  image: Asset | null;
  total: number;
  id: string;
};

type BlockContent = {
  __typename: 'BlockContent';
  settings?: Record<string, any>;
  title?: string;
  subtitle?: string;
  content?: { json: any };
  assetsCollection?: { items: Asset[] };
  referencesCollection?: { items: Reference[] };
  sys: { id: string };
};

type BlockCallToAction = {
  __typename: 'BlockCallToAction';
  settings?: Record<string, any>;
  sys: { id: string };
};

type Block = BlockContent | BlockCallToAction;

type Asset = {
  url: string;
  title: string;
  description: string;
};

type Reference = {
  __typename: 'Product';
  slug: string;
};

type BlocksCollection = {
  items: Block[];
};

type Section = {
  slides?: Slide[];
  button?: CTAProps;
  blocksCollection?: BlocksCollection;
};

const extractBannerWithImages = async (section: Section) => {
  const { blocksCollection } = section
  const blocks = blocksCollection?.items || []

  if (blocks.length > 0) {
    const processBlockContent = async (block: BlockContent, blockSection: Section) => {
      const settings = block.settings || {}
      section.slides = blockSection.slides || []
      let total = 0

      if (block.referencesCollection?.items) {
        const totals = await Promise.all(
          block.referencesCollection.items.map(async (item) => {
            if (item.__typename === 'Product') {
              const product = await getProductBySlug(item.slug)
              if (product && product.variants && product.variants.items[0]) {
                return product.variants.items[0].compareAtPrice || product.variants.items[0].price || 0
              }
            }
            return 0
          })
        )
        total = totals.reduce((sum, value) => sum + value, 0)
      }

      settings.typographyTheme = fontSizes.bodyLarge
      const slide: Slide = {
        title: block.title || '',
        subtitle: block.subtitle || '',
        text: RichTextRenderer({ content: block.content?.json || {}, settings }),
        image: block.assetsCollection?.items[0] || null,
        total,
        id: block.sys.id,
      }
      section.slides.push(slide)
    }

    const processBlocks = async (mainBlocks: Block[], processSection: Section) => {
      await Promise.all(
        mainBlocks.map(async (block) => {
          if (block.__typename === 'BlockContent') {
            await processBlockContent(block, processSection)
          } else if (block.__typename === 'BlockCallToAction') {
            section.button = generateCallToActionProps(block) as CTAProps
          }
        })
      )
    }

    await processBlocks(blocks, section)
  }
}

export default extractBannerWithImages
