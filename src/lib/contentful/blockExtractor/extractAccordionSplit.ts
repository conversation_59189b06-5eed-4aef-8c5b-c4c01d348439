import { getFirstAsset } from '@/utils/asset'
import { notEmpty } from '@/utils/checking'
import RichTextRender from '@/utils/RichTextRenderer'

type media = {
  url: string
  width: number | null
  height: number | null
}

type AccordionSplitItem = {
  id: string
  title: string
  content: any
  media: media | null,
  mobileMedia: media | null
}

function generateMedia(asset: any) {
  if (!asset || !asset.url) return null
  return {
    url: asset?.url || '',
    width: asset?.width || null,
    height: asset?.height || null,
  }
}

const extractAccordionSplit = (section: any) => {
  const { blocksCollection, settings } = section
  const { items: blocks } = blocksCollection

  const items: AccordionSplitItem[] = []

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      if (block.__typename === 'BlockContent') {
        const asset = getFirstAsset(block.assetsCollection)
        const mobileAsset = getFirstAsset(block.mobileAssetsCollection)
        items.push({
          id: block.sys.id,
          title: block.title,
          content: block.content?.json && RichTextRender({ content: block.content?.json }),
          media: generateMedia(asset),
          mobileMedia: generateMedia(mobileAsset)
        })
      }
    })
  }

  section.title = section.header
  section.items = items
  section.anchorTargeting = settings?.anchorTargeting || '#'
}

export default extractAccordionSplit
