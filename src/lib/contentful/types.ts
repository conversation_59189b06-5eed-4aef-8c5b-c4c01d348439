import { ThemeColors } from '@/app/themeThemes.stylex'

import type { ContentfulProductDetailsBlock } from '@/lib/contentful/types/blocks'

interface ContentfulSys {
  id: string;
}

interface ContentfulProduct {
  __typename: 'Product';
  sys: ContentfulSys;
  slug?: string;
  name?: string;
}

interface ContentfulProductCard {
  __typename: 'ProductCard';
  sys: ContentfulSys;
  name?: string;
}

interface ContentfulBlockContent {
  __typename: 'BlockContent';
  sys: ContentfulSys;
  name?: string;
}

interface ContentfulCollection {
  sys: ContentfulSys;
  name: string;
  productsCollection: {
    total: number;
    items: Array<ContentfulProduct | ContentfulProductCard | ContentfulBlockContent>;
  };
}

interface BasePageSection {
  sectionType: string;
  sys: ContentfulSys;
}

interface PageSectionsContent extends BasePageSection {
  sectionType: 'PageSectionsContent';
}

export interface PageSectionsProduct extends BasePageSection {
  sectionType: 'PageSectionsProduct';
  subtype: string;
  name: string;
  title?: string;
  productsCollection: {
    total: number;
    items: Array<ContentfulProduct | ContentfulProductCard | ContentfulBlockContent | ContentfulCollection>;
  };
  totalProducts: number;
  collections: ContentfulCollection[];
}

interface PageSectionsToggles extends BasePageSection {
  sectionType: 'PageSectionsToggles';
  name: string;
}

interface PageSectionsReadyToUse extends BasePageSection {
  sectionType: 'PageSectionsReadyToUse';
}

interface PageSectionsAppsWidgets extends BasePageSection {
  sectionType: 'PageSectionsAppsWidgets';
}

export type PageSection =
  | PageSectionsContent
  | PageSectionsProduct
  | PageSectionsToggles
  | PageSectionsReadyToUse
  | PageSectionsAppsWidgets;

export interface PageStructure {
  name: string;
  slug: string;
  pageSectionsCollection: {
    items: PageSection[];
  };
}

export interface PlaceholderConfig {
  cardHeight: number;
  gutter: number;
}

export type ProductSlugProps = {
  slug: string
}

export type ProductFieldsProps = {
  title: string
  productId: string
  slug: ProductSlugProps
  variants: {
    items: {
      sku: string
      swatch: {
        slug: string
        presentation: string
        icon: {
          url: string
        }
        style: string
        linkedFrom: {
          swatchCollectionCollection: {
            items: {
              linkedFrom: {
                swatchesCollection: {
                  items: {
                    slug: string
                  }
                }
              }
            }
          }
        }
      }
      variantId: number
      price: number
      compareAtPrice: number
      primaryImage: {
        title: string
        url: string
      }
    }[]
  }
  seoMetadata: {
    name: string
    description: string
    keywords: string
    canonicalUrl: string,
    blockSearchIndexing: boolean,
    image: {
      url: string
    }
  }
  callouts: {
    items: {
      title: string
      settings: {
        theme: ThemeColors
      }
      sys: {
        id: string
      }
    }[]
  }
  groups?: any
  swatches: {
    name: string
    slug: string
    swatchType: string
    presentation: string
  }[]
  reviews?: {
    count: number
    rating: number
  }
  productDetailBlockOverrides: {
    items: ContentfulProductDetailsBlock[]
  }
}

export type ProductCollectionFieldsProps = {
  productCollection: {
    items: ProductFieldsProps[]
  }
}

export type ProductCollectionSlugResponse = {
  data: {
    productCollection: {
      items: ProductSlugProps[]
    }
  }
}

// TODO: clean up unused props or refactor to align with PageStructure/PageSection
export type ProductDetailsProps = {
  productDetailBlocksCollection: {
    items: {
      type: string
      contentfulMetadata: {
        tags: {
          id: string
        }[]
      }
      _id: string
      name: string
      title: string
      description: {
        json: any
      }
      contentCollection: {
        items: {
          name: string
          content: {
            json: any
          }
        }[]
      }
    }[]
  }
}

// TODO: Consider refactoring this to use the PageSection union type.
export type PageSectionsProps = {
  sections: {
    sectionType: string
    name: string
    subtype: string
    header?: string
    subheader?: string
    content?: {
      json: any
    }
    assetsCollection?: {
      items: {
        fileName: string
        url: string
      }[]
    }
    blocksCollection?: {
      items: {
        __typename: string
        name: string
        content?: {
          json: any
        }
        sys: {
          id: string
        }
        settings?: {
          fontFamily?: string
          theme?: ThemeColors
          layout?: string
        }
        text?: string
        page?: {
          __typename: string
          slug: string
        }
        icon?: {
          fileName: string
          url: string
        }
        anchor?: string
      }[]
    }
    settings?: {
      theme?: ThemeColors
    }
    productsCollection?: {
      items: {
        slug: string
      }[]
    }
  }[]
}

// TODO: Consider refactoring raw response types later.
export type PageResponseProps = {
  name: string
  slug: string
  pageCategory: string
  pageSectionsCollection: {
    sections: {
      sys: {
        id: string
      }
      sectionType: string
      PageSectionsContent: {
        name: string
        subtype: string
        header: string
        subheader: string
        content: {
          json: any
        }
        assetsCollection: {
          items: {
            fileName: any
            url: any
          }
        }
        blocksCollection: {
          items: {
            __typename: string
            BlockContent: {
              name: string
              assetsCollection: {
                items: {
                  fileName: any;
                  url: any;
                }
              }
              content: {
                json: any
              }
              settings: {
                fontFamily: string
              }
              BlockCallToAction: {
                icon: {
                  fileName: any
                  url: any
                }
                settings: {
                  theme: string
                  layout: string
                }
              }
            }[]
            BlockCallToAction: {
              icon: {
                fileName: any
                url: any
              }
              settings: {
                theme: string
                layout: string
              }
            }
          }[]
        }
        settings: {
          theme: string
          layout: string
        }
      }
      PageSectionsProduct: {
        name: string
        subtype: string
        productsCollection: {
          items: {
            Product: {
              title: string
              slug: string
              variants: {
                items: {
                  sku: string
                  swatch: {
                    slug: string
                    presentation: string
                    icon: {
                      url: string
                    }
                    style: string
                    linkedFrom: {
                      swatchCollectionCollection: {
                        items: {
                          linkedFrom: {
                            swatchesCollection: {
                              items: {
                                slug: string
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                  variantId: number
                  price: number
                  compareAtPrice: number
                  primaryImage: {
                    title: string
                    url: string
                  }
                }[]
              }
              callouts: {
                items: {
                  title: string
                  settings: {
                    theme: ThemeColors
                  }
                }[]
              }
              swatches: {
                name: string;
                slug: string;
                swatchType: string;
                presentation: string;
              }[]
            }
          }[]
        }
      }
    }[]
  }
  sections: {
    sectionType: string
    PageSectionsContent: {
      name: string
      subtype: string
      header: string
      subheader: string
      content: {
        json: any
      }
      assetsCollection: {
        items: {
          fileName: any
          url: any
        }
      }
      blocksCollection: {
        items: {
          __typename: string
          BlockContent: {
            name: string
            title:string
            assetsCollection: {
              items: {
                fileName: any;
                url: any;
              }
            }
            content: {
              json: any
            }
            settings: {
              fontFamily: string
            }
            BlockCallToAction: {
              icon: {
                fileName: any
                url: any
              }
              settings: {
                theme: string
                layout: string
              }
            }
          }[]
          BlockCallToAction: {
            icon: {
              fileName: any
              url: any
            }
            settings: {
              theme: string
              layout: string
            }
          }
        }[]
      }
      settings: {
        theme: string
        layout: string
      }
    }
    PageSectionsProduct: {
      name: string
      subtype: string
      productsCollection: {
        sections: {
          Product: {
            title: string
            slug: string
            variants: {
              items: {
                sku: string
                swatch: {
                  slug: string
                  presentation: string
                  icon: {
                    url: string
                  }
                  style: string
                  linkedFrom: {
                    swatchCollectionCollection: {
                      items: {
                        linkedFrom: {
                          swatchesCollection: {
                            items: {
                              slug: string
                            }
                          }
                        }
                      }
                    }
                  }
                }
                variantId: number
                price: number
                compareAtPrice: number
                primaryImage: {
                  title: string
                  url: string
                }
              }[]
            }
            callouts: {
              items: {
                title: string
                settings: {
                  theme: ThemeColors
                }
              }[]
            }
            swatches: {
              name: string;
              slug: string;
              swatchType: string;
              presentation: string;
            }[]
          }
        }[]
      }
    }
  }[]
}[]

export type CollectionResponseProps = {
  name: string
  slug: string
  seo: {
    name: string
    description: string
    image: {
      url: string
      width: number
      height: number
    }
  }
  productsCollection: any
  pageSectionsCollection: {
    sections: {
      sectionType: string
      PageSectionsContent: {
        name: string
        subtype: string
        header: string
        subheader: string
        content: {
          json: any
        }
        assetsCollection: {
          items: {
            fileName: any
            url: any
          }
        }
        blocksCollection: {
          items: {
            __typename: string
            BlockContent: {
              name: string
              assetsCollection: {
                items: {
                  fileName: any;
                  url: any;
                }
              }
              content: {
                json: any
              }
              settings: {
                fontFamily: string
              }
              BlockCallToAction: {
                icon: {
                  fileName: any
                  url: any
                }
                settings: {
                  theme: string
                  layout: string
                }
              }
            }[]
            BlockCallToAction: {
              icon: {
                fileName: any
                url: any
              }
              settings: {
                theme: string
                layout: string
              }
            }
          }[]
        }
        settings: {
          theme: string
          layout: string
        }
      }
      PageSectionsProduct: {
        name: string
        subtype: string
        productsCollection: {
          items: {
            Product: {
              slug: string
            }
          }[]
        }
      }
    }[]
  }
}[]

export type FetchCollectionResponseProps = {
  data: {
    collectionCollection: {
      items: CollectionResponseProps
    }
  }
}

export type FetchPageResponseProps = {
  data: {
    pageCollection: {
      __typename: any
      items: PageResponseProps
    }
    redirectCollection: {
      __typename: any
      items: {
        slug: string
      }[]
    }
    globalSettingsCollection: {
      items: {
        logo: {
          url: string
        }
        seoMetadata: {
          name: string
          description: string
          image: {
            url: string
          }
        }
      }[]
    }
  }
}

export type Review = {
  id: string,
  rating: number,
  title: string,
  body: string,
  name: string,
  verified: boolean
}
