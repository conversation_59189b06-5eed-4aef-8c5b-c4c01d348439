import fetchGraphQL from './fetchGraphQL'

import { ThemeColors } from '@/app/themeThemes.stylex'
import { NavigationItem } from '@/components/layout/Header/Navigation/types'
import generateSlug from '@/utils/generateSlug'
import { toLowerCase, removeSpaces, toCamelCase } from '@/utils/regex'

type LinkDestinationPage = {
  slug: string
  __typename: 'Page'
}

type LinkDestinationProduct = {
  slug: string
  __typename: 'Product'
}

type LinkCallout = {
  items: any
  title: string
  settings: {
    theme: ThemeColors
  }
  sys: {
    id: string
  }
}

type LinkImage = {
  url: string
}

type LinkCollection = {
  _id: string
  name: string
  linkTitle: string
  linkTarget: string
  linkImageCollection: {
    items: LinkImage[]
  }
  description: {
    json: any
  }
  linkDestination: LinkDestinationPage | LinkDestinationProduct
  callouts: LinkCallout
  animationType: string
  theme?: {
    theme?: string
  }
}

type NavigationCollection = {
  _id: string
  type: string
  linkCollectionCollection: {
    items: LinkCollection[]
  }
}

type NavigationLink = {
  _id: string
  linkTitle: string
  linkTarget: string
  linkDestination: LinkDestinationPage | LinkDestinationProduct
  callouts: {
    items: LinkCallout[]
  }
  navigationCollectionsCollection: {
    items: NavigationCollection[]
  }
  animationType: string
  theme?: {
    theme?: string
  }
}

type DesktopNavigation = {
  name: string
  navigationLinksCollection: {
    items: NavigationLink[]
  }
}

const NAVIGATION_QUERY = `
 query {
    globalSettingsCollection(where: {name: "Global Settings"}, limit: 1) {
      items {
        desktopNavigation {
          name
          navigationLinksCollection(limit: 10, where:{sys:{id_exists:true}}) {
            items {
              _id
              linkTitle
              linkTarget
              linkDestination {
                ... on Page {
                  __typename
                  slug
                }
                ... on Product {
                  __typename
                  slug
                }
              }
              callouts: calloutsCollection(limit: 5, where:{sys:{id_exists:true}}) {
                items {
                  title
                  settings {
                    theme
                  }
                  sys {
                    id
                  }
                }
              }
              animationType
              theme {
                fontFamily
                theme
              }
              navigationCollectionsCollection(limit: 6, where:{sys:{id_exists:true}}) {
                items {
                  _id
                  type
                  linkCollectionCollection(limit: 9, where:{sys:{id_exists:true}}) {
                    items {
                      _id
                      name
                      linkTitle
                      linkImageCollection(limit: 1) {
                        items {
                          url
                        }
                      }
                      description {
                        json
                      }
                      linkDestination {
                        ... on Page {
                          __typename
                          slug
                        }
                        ... on Product {
                          __typename
                          slug
                        }
                      }
                      linkTarget
                      callouts: calloutsCollection(limit: 5, where:{sys:{id_exists:true}}) {
                        items {
                          title
                          settings {
                            theme
                          }
                          sys {
                            id
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`

function extractNavigationLinks(navigationData: DesktopNavigation): NavigationItem[] {
  return navigationData.navigationLinksCollection.items.map((link): NavigationItem => {
    const linkSlug = generateSlug(link.linkDestination?.slug || '', link.linkDestination?.__typename || '')
    let finalSlug: string

    if (link.linkTarget) {
      // Check if linkTarget is an external URL
      const isExternalURL = link.linkTarget.startsWith('http')
      finalSlug = isExternalURL ? link.linkTarget : `${linkSlug}${link.linkTarget}`
    } else {
      finalSlug = linkSlug
    }

    const processedAnimationType = removeSpaces(toLowerCase(link?.animationType || ''))

    return {
      name: link.linkTitle,
      linkTitle: link.linkTitle,
      slug: finalSlug,
      id: link?._id,
      linkTarget: link.linkTarget,
      callouts: { callouts: link.callouts.items },
      animationType: processedAnimationType,
      theme: link.theme?.theme ? { theme: toCamelCase(removeSpaces(link.theme.theme)) } : undefined,
      linkCollection: link.navigationCollectionsCollection.items.map((collection) => ({
        id: collection._id,
        type: collection.type as 'imageLink' | 'textLink' | 'divider' | 'featuredImageLink' | 'interactiveImageLink' | 'interactiveTextLink',
        links: collection.linkCollectionCollection.items.map((item) => {
          const itemSlug = generateSlug(item.linkDestination?.slug || '', item.linkDestination?.__typename || '')

          let finalItemSlug: string
          if (item.linkTarget) {
            const isExternalURL = item.linkTarget.startsWith('http')

            finalItemSlug = isExternalURL ? item.linkTarget : `${itemSlug}${item.linkTarget}`
          } else {
            finalItemSlug = itemSlug
          }

          return {
            id: item._id,
            name: item.name,
            linkTitle: item.linkTitle,
            slug: finalItemSlug,
            linkTarget: item.linkTarget,
            callouts: { callouts: item.callouts.items },
            description: item.description?.json.content[0]?.content[0]?.value ?? '',
            image: item.linkImageCollection.items.length > 0 ? item.linkImageCollection.items[0].url : '',
            animationType: processedAnimationType,
            theme: item.theme
          }
        })
      }))
    }
  })
}

export default async function fetchNavigation() {
  const navigationResponse = await fetchGraphQL(
    NAVIGATION_QUERY,
    ['globalNav']
  )

  if (!navigationResponse.data.globalSettingsCollection.items.length) {
    throw new Error('No global settings found')
  }

  const navigationData = navigationResponse.data.globalSettingsCollection.items[0].desktopNavigation
  return extractNavigationLinks(navigationData)
}
