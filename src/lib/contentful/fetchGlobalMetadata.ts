import fetchGraphQL from './fetchGraphQL'

const globalMetadata = `
  {
    globalSettingsCollection(limit: 1) {
      items {
        logo {
          url
        }
        seoMetadata {
          blockSearchIndexing
          keywords
          name
          description
          image {
            url
          }
        }
      }
    }
  }
`

async function fetchGlobalMetadata() {
  const { data } = await fetchGraphQL(globalMetadata, ['globalSettingsCollection'])
  return data.globalSettingsCollection.items[0]
}

export default fetchGlobalMetadata
