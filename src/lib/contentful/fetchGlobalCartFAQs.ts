import fetchGraphQL from './fetchGraphQL'

import RichTextRenderer from '@utils/RichTextRenderer'

const globalCartFAQs = `
  {
    globalSettingsCollection(where: {name: "Global Settings"}, limit: 1) {
      items {
        cartFaqBlocksCollection {
          items {
            title
            content {
              json
            }
            sys {
              id
            }
          }
        }
      }
    }
  }
`

const extractCartFAQs = (data: any) => data.globalSettingsCollection.items[0].cartFaqBlocksCollection.items.map((faq: any) => ({
  title: faq?.title,
  content: faq?.content ? RichTextRenderer({ content: faq.content.json }) : '',
  id: faq.sys.id
}))

async function fetchGlobalCartFAQs() {
  const { data } = await fetchGraphQL(globalCartFAQs, ['globalCartFAQs'])

  return extractCartFAQs(data)
}

export default fetchGlobalCartFAQs
