import fetchGraphQL from './fetchGraphQL'

import { ThemeColors } from '@/app/themeThemes.stylex'
import { NavigationItem } from '@/components/layout/Header/Navigation/types'
import generateSlug from '@/utils/generateSlug'
import { toCamelCase, removeSpaces } from '@/utils/regex'

type LinkDestinationPage = {
  slug: string
  __typename: 'Page'
}

type LinkDestinationProduct = {
  slug: string
  __typename: 'Product'
}

type LinkCallout = {
  items: any
  title: string
  settings: {
    theme: ThemeColors
  }
  sys: {
    id: string
  }
}

type LinkImage = {
  url: string
}

type LinkCollection = {
  _id: string
  name: string
  linkTitle: string
  linkTarget: string
  linkImageCollection: {
    items: LinkImage[]
  }
  description: {
    json: any
  }
  linkDestination: LinkDestinationPage | LinkDestinationProduct
  callouts: LinkCallout
  theme: {
    theme: string
  }
}

type NavigationCollection = {
  _id: string
  type: string
  linkCollectionCollection: {
    items: LinkCollection[]
  }
}

type NavigationLink = {
  _id: string
  linkTitle: string
  linkTarget: string
  linkDestination: LinkDestinationPage | LinkDestinationProduct
  callouts: {
    items: LinkCallout[]
  }
  navigationCollectionsCollection: {
    items: NavigationCollection[]
  }
  theme: {
    theme: string
  }
}

type Widget = {
  subtype: string
  name: string
  id: string
  settings: {
    layout: string
    theme: string
  }
}

type MobileNavigation = {
  name: string
  navigationLinksCollection: {
    items: NavigationLink[]
  }
  widgets: Widget[]
}

const LINK_DESTINATION_FIELDS = `
  ... on Page {
    __typename
    slug
  }
  ... on Product {
    __typename
    slug
  }
`

const CALLOUT_FIELDS = `
  title
  settings {
    theme
  }
  sys {
    id
  }
`

const LINK_COLLECTION_FIELDS = `
  _id
  name
  linkTitle
  linkImageCollection(limit: 1) {
    items {
      url
    }
  }
  description {
    json
  }
  linkDestination {
    ${LINK_DESTINATION_FIELDS}
  }
  linkTarget
  callouts: calloutsCollection(limit: 1) {
    items {
      ${CALLOUT_FIELDS}
    }
  }
`

const NAVIGATION_COLLECTION_FIELDS = `
  _id
  type
  linkCollectionCollection(limit: 10) {
    items {
      ${LINK_COLLECTION_FIELDS}
    }
  }
`

const NAVIGATION_LINK_FIELDS = `
  _id
  linkTitle
  linkTarget
  linkDestination {
    ${LINK_DESTINATION_FIELDS}
  }
  callouts: calloutsCollection(limit: 1) {
    items {
      ${CALLOUT_FIELDS}
    }
  }
  navigationCollectionsCollection(limit: 10) {
    items {
      ${NAVIGATION_COLLECTION_FIELDS}
    }
  }
  theme {
    theme
  }
`

async function fetchNavigationLinks() {
  const response = await fetchGraphQL(
    `
    query {
      globalSettingsCollection(where: {name: "Global Settings"}, limit: 1) {
        items {
          mobileNavigation {
            name
            navigationLinksCollection(limit: 20) {
              items {
                ${NAVIGATION_LINK_FIELDS}
              }
            }
            widgets {
              subtype
              name
              id
              settings {
                theme
              }
            }
          }
        }
      }
    }
    `,
    ['mobileNav']
  )

  if (!response.data.globalSettingsCollection.items.length) {
    throw new Error('No global settings found')
  }

  return response.data.globalSettingsCollection.items[0].mobileNavigation
}

function extractNavigationLinks(navigationData: MobileNavigation): NavigationItem[] {
  return navigationData.navigationLinksCollection.items.map((link): NavigationItem => {
    const linkSlug = generateSlug(link.linkDestination?.slug || '', link.linkDestination?.__typename || '')
    let finalSlug: string

    if (link.linkTarget) {
      // Check if linkTarget is an external URL
      const isExternalURL = link.linkTarget.startsWith('http')
      finalSlug = isExternalURL ? link.linkTarget : `${linkSlug}${link.linkTarget}`
    } else {
      finalSlug = linkSlug
    }

    return {
      name: link.linkTitle,
      linkTitle: link.linkTitle,
      slug: finalSlug,
      id: link?._id,
      linkTarget: link.linkTarget,
      callouts: { callouts: link.callouts.items },
      theme: link.theme?.theme ? { theme: toCamelCase(removeSpaces(link.theme.theme)) } : undefined,
      linkCollection: link.navigationCollectionsCollection.items.map((collection) => ({
        id: collection._id,
        type: collection.type as 'imageLink' | 'textLink' | 'divider' | 'featuredImageLink' | 'interactiveImageLink' | 'interactiveTextLink',
        links: collection.linkCollectionCollection.items.map((item) => {
          const itemSlug = generateSlug(item.linkDestination?.slug || '', item.linkDestination?.__typename || '')

          let finalItemSlug: string
          if (item.linkTarget) {
            const isExternalURL = item.linkTarget.startsWith('http')

            finalItemSlug = isExternalURL ? item.linkTarget : `${itemSlug}${item.linkTarget}`
          } else {
            finalItemSlug = itemSlug
          }

          return {
            id: item._id,
            name: item.name,
            linkTitle: item.linkTitle,
            slug: finalItemSlug,
            linkTarget: item.linkTarget,
            callouts: { callouts: item.callouts.items },
            description: item.description?.json.content[0]?.content[0]?.value ?? '',
            image: item.linkImageCollection.items.length > 0 ? item.linkImageCollection.items[0].url : '',
            theme: item.theme
          }
        })
      }))
    }
  })
}

export default async function fetchNavigation() {
  const navigationData = await fetchNavigationLinks()
  return {
    items: extractNavigationLinks(navigationData),
    widgets: navigationData.widgets
  }
}
