import fetchGraphQL from './fetchGraphQL'
import { getProductBySlug } from './fetchProducts'

const SEARCH_MENU_QUERY = `
  query {
    searchMenu(id: "5GTh7RCvzRBYDCRr3WkRem") {
      title
      suggestionsCollection {
        items {
          text
          page {
            ... on Page {
              slug
            }
          }
        }
      }
      bestSellersCollection {
        items {
          ... on Product {
            _id
            __typename
            slug
            title
            productId
          }
        }
      }
    }
  }
`

type SearchMenuType = {
  title: string;
  suggestionsCollection: {
    items: {
      text: string;
      page: {
        slug: string;
      }
    }[]
  }
  bestSellersCollection: {
    items: {
      _item: string;
      __typename: string;
      slug: string;
      title: string;
      productId: string;
    }[]
  }
}

export type BestSellerProductType = Awaited<ReturnType<typeof getProductBySlug>>;
export type BestSellerProductsType = BestSellerProductType[];

export type ExtractedSearchMenuDataType = {
  title: string;
  suggestions: {
    title: string;
    slug: string;
  }[]
  bestSellerProducts: BestSellerProductsType
}

async function extractSearchMenuData(searchMenu: SearchMenuType): Promise<ExtractedSearchMenuDataType> {
  const {
    title,
    suggestionsCollection,
    bestSellersCollection
  } = searchMenu

  const suggestions = suggestionsCollection.items.map((item: SearchMenuType['suggestionsCollection']['items'][number]) => (
    {
      title: item.text,
      slug: item.page.slug
    }
  ))

  const bestSellers = bestSellersCollection.items.map((item: SearchMenuType['bestSellersCollection']['items'][number]) => ({
    id: item.productId,
    slug: item.slug,
    title: item.title
  }))

  const bestSellerProducts = await Promise.all(bestSellers.map(async (bestSeller) => {
    const product = await getProductBySlug(bestSeller.slug)
    return product
  }))

  return {
    title,
    suggestions,
    bestSellerProducts
  }
}

export default async function fetchSearchMenu() {
  const searchMenuResponse = await fetchGraphQL(
    SEARCH_MENU_QUERY
  )

  if (!searchMenuResponse.data.searchMenu?.bestSellersCollection || !searchMenuResponse.data.searchMenu?.suggestionsCollection) {
    console.warn('Search Menu is missing Suggestions or Best Sellers')
  }

  const {
    searchMenu
  } = searchMenuResponse.data

  if (!searchMenu) {
    return {
      title: '',
      suggestions: [],
      bestSellerProducts: []
    }
  }

  return extractSearchMenuData(searchMenu)
}
