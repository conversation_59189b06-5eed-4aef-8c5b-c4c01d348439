import fetchGraphQL from './fetchGraphQL'
import {
  blockCallToActionFragment, getBlockContentByID, getProductCardByID, PRODUCT_FIELDS_QUERY, variantBasicInfoFragment
} from './fetchProducts'
import { FetchCollectionResponseProps, FetchPageResponseProps } from './types'

export const extractPageEntries = (fetchResponse: FetchPageResponseProps) => fetchResponse.data.pageCollection.items.map((page) => ({
  slug: page.slug,
  sections: page.pageSectionsCollection
}))

export const extractCollectionProducts = async (fetchResponse: FetchCollectionResponseProps) => {
  const { data: { collectionCollection: { items: [collection] } } } = fetchResponse
  const {
    name,
    productsCollection: { items },
    pageSectionsCollection: { sections },
    seo
  } = collection

  const products = items
    .filter((product: any) => 'sys' in product)
    .map(async (product: any) => {
      if (product.__typename === 'Product') {
        const response = await fetchGraphQL(
          `
          query {
            product(id: "${product.sys.id}") {
              ${PRODUCT_FIELDS_QUERY}
            }
          }
          ${variantBasicInfoFragment}
          ${blockCallToActionFragment}
        `,
          ['productFields']
        )

        return {
          ...product,
          ...response.data.product,
        }
      }

      if (product.__typename === 'ProductCard') {
        const response = await getProductCardByID(product.sys.id)
        return {
          ...product,
          ...response
        }
      }

      if (product.__typename === 'BlockContent') {
        const response = await getBlockContentByID(product.sys.id)
        return {
          ...product,
          ...response
        }
      }

      return product
    })

  return {
    name,
    products: await Promise.all(products),
    sections,
    seo
  }
}

export const extractPageSections = (graphQlResponse: FetchPageResponseProps) => {
  const {
    data: { pageCollection: { items: [page] } }
  } = graphQlResponse

  const {
    pageSectionsCollection: { sections },
    ...rest
  } = page
  return {
    ...rest,
    sections,
    __typename: graphQlResponse.data.pageCollection.__typename
  }
}

export function extractRedirects(graphQlResponse: FetchPageResponseProps) {
  const { data: { redirectCollection: { items: [redirect] } } } = graphQlResponse
  return {
    ...redirect,
    __typename: graphQlResponse.data.redirectCollection.__typename
  }
}
