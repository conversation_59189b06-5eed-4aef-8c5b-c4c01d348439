import { ContentfulProduct } from './products'

import type { Document } from '@contentful/rich-text-types'
import type {
  ContentfulImage,
  ContentfulRichText,
  ContentfulTokenSettings,
} from './generic'
import type { ContentfulProductVariant } from './variants'

export type ContentfulBlockCallToAction = {
  __typename: 'BlockCallToAction';
  text?: string;
  // TODO: UPDATE TYPES FOR PAGE
  page?: any;
  customURL?: string;
  anchor: string;
  icon?: ContentfulImage;
  iconPosition?: boolean;
  targetBlank?: boolean;
  settings: ContentfulTokenSettings;
};

export type ContentfulBlockQuote = {
  __typename: 'BlockContentQuote';
  author: string;
  quote: ContentfulRichText;
  logo?: ContentfulImage;
  image?: {
    items: ContentfulImage[];
  };
  rating: number;
};

/* eslint-disable line-comment-position */
export type ContentfulProductDetailsBlock = {
  __typename: 'BlockProductDetails';
  type:
  | 'AddtoCartButton' // Add to <PERSON><PERSON>
  | 'AddtoCartButtonOverride' // Add to Cart Button Override
  | 'Disclaimer' // Disclaimer
  | 'PlainText' // Plain text
  | 'ProductAccordions' // Product Accordions
  | 'ProductAddOns' // Product Add-Ons
  | 'ProductByline' // Product Byline
  | 'ProductCrossSells' // Product Cross-Sells
  | 'ProductSwatches' // Product Swatches
  | 'ProductTitle' // Product Title
  | 'ProductToggles' // Product Toggles
  | 'PromotionalTabs' // Promotional Tabs
  | 'TolstoyWidget' // Tolstoy Widget
  | 'BuyWithZest'; // Buy With Zest
  title?: string;
  description?: ContentfulRichText;
  contentCollection?: {
    items: (ContentfulProductVariant | ContentfulContentBlock | ContentfulBlockCallToAction | ContentfulProduct)[];
  };
};

export type ContentfulBlock =
  | ContentfulBlockCallToAction
  | ContentfulBlockQuote
  | ContentfulContentBlock;

export type ContentfulContentBlock = {
  __typename: 'BlockContent'
  sys: { id: string }
  title?: string
  content?: {
    json: Document
  }
  assetsCollection?: {
    items: ContentfulImage[]
  }
  mobileAssetsCollection?: {
    items: ContentfulImage[]
  }
  referencesCollection?: {
    // TODO: UPDATE TYPES FOR REFERENCES COLLECTION OR LIMITING TYPES
    items: (ContentfulBlock | ContentfulProductVariant)[]
  }
  settings?: ContentfulTokenSettings
}
