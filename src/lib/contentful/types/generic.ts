import { ThemeColors } from '@/app/themeThemes.stylex'

export type ContentfulImage = {
  title: string;
  description: string;
  contentType: string;
  fileName: string;
  url: string;
  width: number;
  height: number;
};

export type ContentfulRichText = {
  json: any;
};

export type ContentfulTokenSettingsSpacing =
  | 'tiny'
  | 'xx-small'
  | 'x-small'
  | 'small'
  | 'medium'
  | 'large'
  | 'x-large'
  | 'xx-large'

export type ContentfulTokenSettingsLayout =
  | 'Layout 1'
  | 'Layout 2'
  | 'Layout 3'
  | 'Layout 4'
  | 'Layout 5'
  | 'Layout 6'
  | 'Layout 7'
  | 'Layout 8'
  | 'Layout 9'
  | 'Layout 10'

export type ContentfulTokenSettings = {
  fontFamily?: string
  theme?: ThemeColors
  layout?: ContentfulTokenSettingsLayout
  spacingTop?: ContentfulTokenSettingsSpacing
  spacingBottom?: ContentfulTokenSettingsSpacing
  width?: number
  anchorTargeting?: string
}
