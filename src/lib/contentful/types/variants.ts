import type { ContentfulProductDetailsBlock } from './blocks'
import type { ContentfulCallout } from './callouts'
import type { ContentfulImage } from './generic'
import type { ContentfulProduct, ContentfulProductSwatch } from './products'

export type ContentfulProductVariant = {
  __typename: 'Variant';
  sys: { id: string };
  name: string;
  sku: string;
  swatch: ContentfulProductSwatch;
  swatches?: {
    items: ContentfulProductSwatch[];
  };
  variantId: string;
  price: number;
  compareAtPrice: number;
  regularPriceOverride: number;
  onSale: boolean;
  primaryImage: ContentfulImage;
  secondaryImage?: ContentfulImage;
  tertiaryImage?: ContentfulImage;
  gallery?: {
    items: ContentfulImage[];
  };
  callouts?: {
    items: ContentfulCallout[];
  };
  estimatedShippingDate?: string;
  productDetailBlocksOverrides: {
    items: ContentfulProductDetailsBlock[];
  };
  linkedFromProduct?: {
    productCollection: {
      items: ContentfulProduct[];
    };
  };
  availableForSale: boolean;
  variantAvailability?: 'Notify Me' | 'Sold Out' | 'Notify Me when unavailable' | 'Sold Out when unavailable'
};

export type ContentfulVariantCard = {
  __typename: 'VariantCard';
  image: ContentfulImage;
  swatch: ContentfulProductSwatch;
};
