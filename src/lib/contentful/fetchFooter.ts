import fetchGraphQL from './fetchGraphQL'

import generateSlug from '@/utils/generateSlug'
import { NavigationItem } from '@/components/layout/Header/Navigation/types'

type LinkDestinationPage = {
  slug: string | null
  __typename: string
}

type LinkDestinationProduct = {
  slug: string | null
  __typename: string
}

type LinkImage = {
  url: string | null
  width: number | null
  height: number | null
}

type LinkCollection = {
  _id: string
  name: string
  linkTitle: string
  linkImageCollection: {
    items: LinkImage[]
  }
  linkTarget: string | null
  linkDestination: LinkDestinationPage | LinkDestinationProduct
}

type NavigationCollection = {
  type: string
  linkCollectionCollection: {
    items: LinkCollection[]
  }
}

type NavigationLink = {
  _id: string
  linkTitle: string
  linkTarget: string | null
  linkDestination: LinkDestinationPage | LinkDestinationProduct
  navigationCollectionsCollection: {
    items: NavigationCollection[]
  }
}

type FooterNavigation = {
  name: string
  navigationLinksCollection: {
    items: NavigationLink[]
  }
}

const FOOTER_QUERY = `
  query {
    globalSettingsCollection(where: {name: "Global Settings"}, limit: 1) {
      items {
        footer {
          name
          navigationLinksCollection(limit: 10) {
            items {
              _id
              linkTitle
              linkDestination {
                ... on Page {
                  __typename
                  slug
                }
                ... on Product {
                  __typename
                  slug
                }
                ... on BlogArticle {
                  __typename
                  slug
                }
              }
              navigationCollectionsCollection(limit: 6) {
                items {
                  type
                  linkCollectionCollection(limit: 15) {
                    items {
                      _id
                      name
                      linkTitle
                      linkTarget
                      linkImageCollection(limit: 1) {
                        items {
                          url
                          width
                          height
                        }
                      }
                      linkDestination {
                        ... on Page {
                          __typename
                          slug
                        }
                        ... on Product {
                          __typename
                          slug
                        }
                        ... on BlogArticle {
                          __typename
                          slug
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          widgets {
            subtype
            name
            id
            settings {
              theme
            }
          }
        }
      }
    }
  }
`

function extractFooterLinks(footerData: FooterNavigation): NavigationItem[] {
  return footerData.navigationLinksCollection.items.map((link): NavigationItem => ({
    id: link._id,
    name: link.linkTitle,
    linkTitle: link.linkTitle,
    slug: link.linkDestination?.slug ? generateSlug(link.linkDestination.slug, link.linkDestination.__typename) : '',
    linkTarget: link.linkTarget || '',
    linkCollection: link.navigationCollectionsCollection.items.map((collection) => ({
      id: collection.type,
      type: collection.type as 'imageLink' | 'textLink',
      links: collection.linkCollectionCollection.items.map((item) => {
        const itemSlug = generateSlug(item.linkDestination?.slug || '', item.linkDestination?.__typename || '')

        let finalItemSlug: string
        if (item.linkTarget) {
          const isExternalURL = item.linkTarget.startsWith('http')

          finalItemSlug = isExternalURL ? item.linkTarget : `${itemSlug}${item.linkTarget}`
        } else {
          finalItemSlug = itemSlug
        }

        return {
          id: item._id,
          name: item.name,
          linkTitle: item.linkTitle,
          slug: finalItemSlug,
          linkTarget: item.linkTarget,
          image: item.linkImageCollection.items.length > 0 ? item.linkImageCollection.items[0].url : '',
          imageWidth: item.linkImageCollection.items.length > 0 ? item.linkImageCollection.items[0].width : 0,
          imageHeight: item.linkImageCollection.items.length > 0 ? item.linkImageCollection.items[0].height : 0
        }
      })
    }))
  }))
}

export default async function fetchFooter() {
  const footerResponse = await fetchGraphQL(
    FOOTER_QUERY,
    ['footer']
  )

  if (!footerResponse.data.globalSettingsCollection.items.length) {
    throw new Error('No global settings found')
  }

  const footerData = footerResponse.data.globalSettingsCollection.items[0].footer
  return {
    items: extractFooterLinks(footerData),
    widgets: footerData.widgets
  }
}
