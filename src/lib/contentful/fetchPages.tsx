import fetchGraphQL from './fetchGraphQL'
import propsExtractor from './blockExtractor'
import { extractCollectionProducts, extractPageSections, extractRedirects } from './helpers'
import {
  collectionQuery, pageSectionsContentQuery, pageQuery, redirectQuery, pageSectionsProductQuery,
  toggleSectionCollection,
  compareChartQuery,
  pageIdQuery,
  pageSectionsStructureQuery,
  collectionDetailsQuery
} from './queries'
import { extractSaveWithASetModule } from './extractSaveWithASet'

import { toCamelCase, removeSpaces } from '@utils/regex'
import {
  getBlockContentByID,
  getProductCardByID,
  getProductFieldsBySlug,
} from '@lib/contentful/fetchProducts'
import { getProductReviewsAggregate } from '@lib/okendo'
import { empty } from '@/utils/checking'

const DELAY_MS = 150

function delay(ms?: number) {
  return new Promise((resolve) => { setTimeout(resolve, ms || DELAY_MS) })
}

export async function getToggleSectionsById(id: string) {
  const gqlQuery = toggleSectionCollection(id)
  const graphQlResponse = await fetchGraphQL(
    gqlQuery,
    ['pageSectionsToggleSectionsCollection']
  )
  return graphQlResponse.data.pageSectionsToggles
}

export async function getCollectionBySlug(slug: string) {
  const gqlQuery = collectionQuery(slug)
  const graphQlResponse = await fetchGraphQL(
    gqlQuery,
    ['collectionCollection']
  )

  return extractCollectionProducts(graphQlResponse)
}

const getPageSectionContentById = async (sectionId: string, forceCache = false) => {
  const gqlPageSectionContentQuery = pageSectionsContentQuery(sectionId)

  const graphQlPageSectionContentResponse = await fetchGraphQL(
    gqlPageSectionContentQuery,
    ['pageSectionsContentCollection'],
    false,
    forceCache
  )
  return graphQlPageSectionContentResponse.data.pageSectionsContent
}

const getCompareChartSection = async (sectionId: string) => {
  const gqlCompareChartQuery = compareChartQuery(sectionId)

  const graphQlCompareChartResponse = await fetchGraphQL(
    gqlCompareChartQuery,
    ['compareChartCollection']
  )
  return graphQlCompareChartResponse.data.compareChart
}

const getPageSectionProductById = async (sectionId: string, forceCache = false) => {
  const gqlPageSectionProductQuery = pageSectionsProductQuery(sectionId)

  const graphQlPageSectionProductResponse = await fetchGraphQL(
    gqlPageSectionProductQuery,
    ['pageSectionsProductCollection'],
    false,
    forceCache
  )
  return graphQlPageSectionProductResponse.data.pageSectionsProduct
}

const SECTION_BY_TYPE = {
  PageSectionsContent: getPageSectionContentById,
  PageSectionsProduct: getPageSectionProductById,
  PageSectionsToggles: getToggleSectionsById,
  CompareChart: getCompareChartSection
}

type SectionType = {
  sectionType: string;
  sys: { id: string };
  subtype: string
  productsA?: any;
  productsB?: any;
}

// eslint-disable-next-line complexity
export const fillSectionOperation = async (section:SectionType) => {
  const { sectionType } = section
  const handle = SECTION_BY_TYPE[sectionType as keyof typeof SECTION_BY_TYPE]

  const data = handle
    ? await handle(section.sys.id)
    : null

  if (data) {
    Object.assign(section, data)

    if (section.sectionType === 'PageSectionsToggles') {
      section.subtype = 'Toggle'
    }
    if (section.sectionType === 'CompareChart') {
      section.subtype = 'HowWeCompareChart'
    }
  }
}

const fillSection = (sections: SectionType[]) => Promise.all(
  sections.map(fillSectionOperation)
)

export async function getPageBySlug(slug: string, forceCache = false) {
  const gqlPageQuery = pageQuery(slug)

  const graphQlPageResponse = await fetchGraphQL(
    gqlPageQuery,
    ['pageCollection'],
    false,
    forceCache
  )

  if (!graphQlPageResponse.data.pageCollection.items.length) {
    const gqlRedirectQuery = redirectQuery(slug)
    const graphQlRedirectResponse = await fetchGraphQL(
      gqlRedirectQuery,
      ['redirectCollection'],
    )

    if (empty(graphQlRedirectResponse.data.redirectCollection.items)) {
      return { __typename: 'NotFound' }
    }

    return extractRedirects(graphQlRedirectResponse)
  }

  const { pageSectionsCollection } = graphQlPageResponse.data.pageCollection.items[0]
  const sections = pageSectionsCollection ? pageSectionsCollection.sections : []

  await fillSection(sections)

  return extractPageSections(graphQlPageResponse)
}

export async function getPageIdBySlug(slug: string) {
  const query = pageIdQuery(slug)
  const graphQlResponse = await fetchGraphQL(
    query,
    ['pageCollection']
  )

  return graphQlResponse.data.pageCollection.items[0].sys.id
}

const extractProducts = (data: any[]) => {
  const output: { locator: any; typename: any }[] = []
  const extractFromItem = async (item: any) => {
    if (item.__typename === 'ProductCard' || item.__typename === 'BlockContent') {
      output.push({ locator: item.sys.id, typename: item.__typename })
    }
    if (item.slug) {
      output.push({ locator: item.slug, typename: item.__typename })
    }
    if (item.productsCollection && item.productsCollection.items) {
      item.productsCollection.items.forEach(extractFromItem)
    }
  }
  data.forEach(extractFromItem)
  return output
}

const processProductData = async (item: { typename: string; locator: string }, section: any, forceCache = false) => {
  await delay()

  let productData

  if (item.typename === 'ProductCard') {
    productData = await getProductCardByID(item.locator, forceCache)
  } else if (item.typename === 'Product') {
    productData = await getProductFieldsBySlug(item.locator, true, forceCache)
  } else if (item.typename === 'BlockContent') {
    productData = await getBlockContentByID(item.locator, section.subtype === 'Save With A Set')
  }

  if (item.typename === 'ProductCard' || item.typename === 'Product') {
    const isProductCard = item.typename === 'ProductCard'
    const productId = isProductCard ? productData.product.productId : productData.productId
    const reviews = await getProductReviewsAggregate(productId)
    return { ...productData, reviews }
  }

  return productData
}

const processSectionProducts = async (section: any, forceCache = false) => {
  const { productsCollection } = section

  const { items: products } = productsCollection
  const compiled = extractProducts(products)
  const productsData = await Promise.all(compiled.map((item) => processProductData(item, section, forceCache)))
  if (section.subtype === 'Save With A Set') {
    section.products = extractSaveWithASetModule(productsData)
  } else section.products = productsData
}

const generateSectionDefaultProps = (section: any) => {
  section.anchor = section?.settings?.anchorTargeting
  section.theme = section?.settings?.theme && toCamelCase(section.settings.theme)
  section.layout = section?.settings?.layout && toCamelCase(section.settings.layout)
}

export const processSection = async (section: any, forceCache = false) => {
  const { subtype, sectionType } = section
  const handle = removeSpaces(subtype)

  if (sectionType === 'PageSectionsProduct') {
    await processSectionProducts(section, forceCache)
  } else if (sectionType === 'PageSectionsContent') {
    propsExtractor(handle, section)
  } else if (sectionType === 'PageSectionsAppsWidgets' && subtype === 'Tolstoy') {
    section.shape = 'circle'
  } else if (sectionType === 'PageSectionsToggles') {
    await fillSection(section.pageSectionsCollection.sections)
    await Promise.all(
      section.pageSectionsCollection.sections.map(processSection)
    )
  }

  // TODO: ADD THIS TO EXTRACTOR
  if (handle === 'MediaContainer') {
    const { assetsCollection } = section
    section.asset = { url: assetsCollection?.items[0]?.url }
  }

  generateSectionDefaultProps(section)
}

export const fetchSectionData = async (section: any, forceCache = false) => {
  const { sectionType } = section

  if (sectionType === 'PageSectionsContent') {
    return {
      ...section,
      ...await getPageSectionContentById(section.sys.id, forceCache)
    }
  }

  if (sectionType === 'PageSectionsProduct') {
    return {
      ...section,
      ...await getPageSectionProductById(section.sys.id, forceCache)
    }
  }

  return section
}

export const getPageSections = async (page: any, forceCache = false) => {
  const { sections } = page
  await Promise.all(sections.map((section: any) => processSection(section, forceCache)))
  return sections
}

export const getPageSectionsData = async (sections: any, forceCache = false) => {
  const sectionsData = await Promise.all(sections.map((section: any) => fetchSectionData(section, forceCache)))

  const page = {
    sections: sectionsData
  }

  return getPageSections(page, forceCache)
}

export const getPageGenerator = async (slug: string) => {
  const data = await getPageBySlug(slug)
  const hydrated = await getPageSections(data)

  return hydrated
}

async function fetchCollectionDetails(collectionId: string) {
  const query = collectionDetailsQuery(collectionId)
  const response = await fetchGraphQL(query)
  return response.data.collection
}

export const getPageSectionsStructure = async (id: string) => {
  const query = pageSectionsStructureQuery(id)
  const graphQlResponse = await fetchGraphQL(query)
  const pageStructure = graphQlResponse.data.page.pageSectionsCollection.items || []

  const collectionIds = new Set<string>()
  pageStructure.forEach((section: any) => {
    if (section.sectionType === 'PageSectionsProduct' && section.productsCollection) {
      section.productsCollection.items.forEach((item: any) => {
        if (item.__typename === 'Collection') {
          collectionIds.add(item.sys.id)
        }
      })
    }
  })

  const collectionDetails = await Promise.all(
    Array.from(collectionIds).map(fetchCollectionDetails)
  )

  const collectionsMap = new Map(
    collectionDetails.map((collection) => [collection.sys.id, collection])
  )

  const enhancedStructure = pageStructure.map((section: any) => {
    if (section.sectionType === 'PageSectionsProduct' && section.productsCollection) {
      const totalProducts = section.productsCollection.items.reduce((acc: number, item: any) => {
        if (item.__typename === 'Collection') {
          const collection = collectionsMap.get(item.sys.id)
          return acc + (collection?.productsCollection?.total || 0)
        }
        return acc + 1
      }, 0)

      return {
        ...section,
        totalProducts,
        collections: section.productsCollection.items
          .filter((item: any) => item.__typename === 'Collection')
          .map((item: any) => collectionsMap.get(item.sys.id))
      }
    }
    return section
  })

  return enhancedStructure
}
