import fetchGraphQL from './fetchGraphQL'
import { fillSectionOperation, processSection } from './fetchPages'

import { toCamelCase } from '@utils/regex'
import RichTextRenderer from '@utils/RichTextRenderer'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

const globalDialogs = `
  {
    globalSettingsCollection(where: {name: "Global Settings"}, limit: 1) {
      items {
        dialogsCollection(limit: 4) {
          items {
            header
            subheader
            content {
              json
            }
            mobileContent {
              json
            }
            footerContent {
              json
            }
            asset {
              url
            }
            settings {
              theme
              layout
            }
            sys {
              id
            }
            dialogTogglesCollection {
              items {
                __typename
                ... on PageSectionsContent {
                  sectionType: __typename
                  subtype
                  sys { id } 
                }
              }
            }
          }
        }
      }
    }
  }
`
export const extractDialogs = (data: any) => data.map((dialog: any) => ({
  header: dialog?.header,
  subheader: dialog?.subheader,
  content: dialog?.content ? RichTextRenderer({ content: dialog.content.json }) : '',
  mobileContent: dialog?.mobileContent ? RichTextRenderer({ content: dialog.mobileContent.json }) : '',
  footerContent: dialog?.footerContent ? RichTextRenderer({ content: dialog.footerContent.json }) : '',
  asset: dialog?.asset?.url,
  theme: (dialog?.settings?.theme && toCamelCase(dialog.settings.theme)) || 'cream',
  layout: dialog?.settings?.layout || 'Layout 5',
  id: dialog.sys.id,
  sections: dialog.dialogTogglesCollection
}))

const extractGlobalDialogs = (data: any) => extractDialogs(data.globalSettingsCollection.items[0].dialogsCollection.items || [])

export async function feedDialogsWithSection(dialogsCollection: any) {
  const sectionPromises: Promise<void>[] = []

  dialogsCollection.items.forEach(({ dialogTogglesCollection }: any) => {
    dialogTogglesCollection.items.forEach((item: any) => {
      if (item.__typename === 'PageSectionsContent') {
        sectionPromises.push(
          fillSectionOperation(item)
        )
      }
    })
  })

  await Promise.all(sectionPromises)

  dialogsCollection.items.forEach(({ dialogTogglesCollection }: any) => {
    dialogTogglesCollection.items.forEach((item: any) => {
      if (item.__typename === 'PageSectionsContent') {
        processSection(item)
      }
    })
  })
}

export async function extractProductDialogs(product: any) {
  const dialogsMetadata = product?.productMetadata?.items?.find(
    (dialog: any) => dialog.type === 'Dialogs'
  )

  if (!dialogsMetadata) return []

  await feedDialogsWithSection(dialogsMetadata?.references)

  return dialogsMetadata?.references?.items?.map((dialog: any) => {
    const d = {
      ...dialog,
      content: dialog?.content?.json && RichTextRenderer({ content: dialog.content.json }),
      mobileContent: dialog?.mobileContent?.json && RichTextRenderer({ content: dialog.mobileContent.json }),
      sections: dialog?.dialogTogglesCollection,
      // TODO: update dialogToggles to togglesAndReferences in Contentful and in Code, this should accept Page Section: Compare Chart //
      togglesAndReferences: dialog?.dialogTogglesCollection?.items ? dialog.dialogTogglesCollection.items.map((item: any) => ({
        compareChart: item?.__typename === 'CompareChart' && {
          header: item.header,
          elementsToCompare: item?.elementsToCompareCollection && {
            items: item.elementsToCompareCollection.items.map((element: any) => ({
              name: element.name,
              description: element.description,
              selectedAsset: Object.keys(element.selectedAsset) && {
                url: element.selectedAsset.url,
                description: element.selectedAsset?.description
              },
              pairedWith: element?.itemsCollection?.items && element.itemsCollection.items.map((pair: any) => ({
                content: pair?.content?.json && documentToPlainTextString(pair.content.json),
                title: pair?.title,
                asset: pair?.assetsCollection?.items && {
                  description: pair.assetsCollection.items[0].description,
                  url: pair.assetsCollection.items[0].url
                },
                mobileAsset: pair?.mobileAssetsCollection?.items && {
                  description: pair.mobileAssetsCollection.items[0].description,
                  url: pair.mobileAssetsCollection.items[0].url
                }
              })),
              callToAction: element?.callToAction && {
                text: element.callToAction.text,
                page: element.callToAction?.page && {
                  __typename: element.callToAction.page.__typename,
                  slug: element.callToAction.page.slug
                }
              }
            }))
          }
        },
        toggles: item?.__typename === 'BlockContent' ? [
          {
            title: item?.title,
            content: item?.content?.json && RichTextRenderer({
              content: item?.content?.json,
              settings: {
                id: dialog?.settings?.layout === 'Layout 3' && 'storage'
              }
            }),
            mobileContent: item?.mobileContent?.json && RichTextRenderer({
              content: item?.mobileContent?.json,
              settings: {
                id: dialog?.settings?.layout === 'Layout 3' && 'storage'
              }
            }),
            asset: {
              url: item?.assetsCollection?.items[0]?.url,
              fileName: item?.assetsCollection?.items[0]?.fileName
            }
          }
        ] : []
      }))[0] : [],
    }

    delete d.dialogTogglesCollection

    return d
  })
}

async function fetchGlobalDialogs() {
  const { data } = await fetchGraphQL(globalDialogs, ['globalDialogs'])
  const [{ dialogsCollection }] = data.globalSettingsCollection.items

  await feedDialogsWithSection(dialogsCollection)

  return extractGlobalDialogs(data)
}

export default fetchGlobalDialogs
