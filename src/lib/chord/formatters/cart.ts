import lineItemFormatter from './lineItem'

import type { CartFormatter } from '@chordcommerce/analytics'
import type { CartStateCart } from '@redux/features/cart/types'

const cartFormatter: CartFormatter<CartStateCart> = (props) => {
  const { cart } = props

  return {
    cart_id: cart?.cartId,
    currency: cart?.currency,
    products: cart?.products?.map((lineItem, i) => {
      const item = lineItemFormatter({ lineItem })
      return {
        ...item,
        position: i + 1,
      }
    }),
    value: cart?.value
  }
}

export default cartFormatter
