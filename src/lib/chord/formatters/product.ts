import type { ProductFormatter } from '@chordcommerce/analytics'
import type { ProductCardFieldsProps } from '@components/Product/types'

const productFormatter: ProductFormatter<ProductCardFieldsProps> = (props) => {
  const {
    position,
    product,
    variantId
  } = props

  const matchingVariant =
    variantId &&
    product?.variants?.items.find((variant) => variant?.variantId === variantId)

  const variant = matchingVariant || product?.variants?.items[0]

  return {
    description: product?.seoMetadata?.description,
    image_url: variant?.primaryImage?.url,
    name: product?.title,
    position,
    price: Number(variant?.price) || 0,
    product_id: product?.productId,
    sku: variant?.sku,
    slug: product?.slug,
    variant: variant?.swatch?.presentation,
    variant_id: variant?.variantId,
  }
}

export default productFormatter
