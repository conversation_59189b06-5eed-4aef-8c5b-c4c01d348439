import lineItemFormatter from './lineItem'

import type { CheckoutFormatter } from '@chordcommerce/analytics'
import type { CartStateCart } from '@redux/features/cart/types'

const checkoutFormatter: CheckoutFormatter<CartStateCart> = (props) => {
  const { checkout: cart } = props

  return {
    checkout_type: 'Shopify',
    currency: cart?.currency,
    products: cart?.products?.map((lineItem, i) => {
      const item = lineItemFormatter({ lineItem })
      return {
        ...item,
        position: i + 1,
      }
    }),
    value: cart?.value
  }
}

export default checkoutFormatter
