import type { LineItemFormatter } from '@chordcommerce/analytics'
import type { CartStateLineItem } from '@redux/features/cart/types'

const lineItemFormatter: LineItemFormatter<CartStateLineItem> = (props) => {
  const { lineItem } = props

  return {
    image_url: lineItem?.imageUrl,
    line_item_id: lineItem.lineItemId,
    name: lineItem.name,
    option_values: lineItem?.options?.length ? [...lineItem.options] : undefined,
    price: lineItem?.amount || 0,
    product_id: lineItem.productId,
    quantity: lineItem.quantity,
    sku: lineItem?.sku ?? '',
    slug: lineItem?.slug,
    variant: lineItem?.variant
  }
}

export default lineItemFormatter
