import cartFormatter from './formatters/cart'
import checkoutFormatter from './formatters/checkout'
import lineItemFormatter from './formatters/lineItem'
import productFormatter from './formatters/product'

import { ChordAnalytics, ChordAnalyticsOptions } from '@chordcommerce/analytics'
import { AnalyticsBrowser } from '@segment/analytics-next'

const options: ChordAnalyticsOptions = {
  formatters: {
    objects: {
      cart: cartFormatter,
      checkout: checkoutFormatter,
      lineItem: lineItemFormatter,
      product: productFormatter
    },
  },
  metadata: {
    i18n: {
      // TODO: Make Chord currency and locale dynamic
      currency: '',
      locale: '',
    },
    ownership: {
      // TODO: Add Chord environment variables
      omsId: process.env.NEXT_PUBLIC_CHORD_OMS_ID ?? '',
      storeId: process.env.NEXT_PUBLIC_CHORD_STORE_ID ?? '',
      tenantId: process.env.NEXT_PUBLIC_CHORD_TENANT_ID ?? '',
    },
    platform: {
      name: 'Shopify',
      type: 'web'
    },
    store: {
      domain: process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN ?? '',
    },
  },
}

const createChordClient = (cdp: AnalyticsBrowser) => new ChordAnalytics({ ...options, cdp })

export default createChordClient
