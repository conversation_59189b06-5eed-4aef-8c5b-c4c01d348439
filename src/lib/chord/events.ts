import { addFBTrackingParams } from './utils'

import createChordClient from '@lib/chord/chord'
import analytics from '@lib/segment/segment'

import type {
  CartViewedPayload,
  CheckoutStartedPayload,
  ProductAddedPayload,
  ProductRemovedPayload,
  ProductViewedPayload,
  ProductListViewedPayload,
  ProductClickedPayload,
  ProductsSearchedPayload,
  NavigationClickedPayload,
  VariantClickedPayload,
  CouponDeniedPayload,
  CouponEnteredPayload,
  CouponAppliedPayload,
  CouponRemovedPayload,
} from '@redux/features/events/types'

export const chord = createChordClient(analytics)

const trackCartViewed = (payload: CartViewedPayload) => {
  chord.trackCartViewed({ cart: payload.cart }, {
    traits: addFBTrackingParams()
  })
}

const trackCheckoutStarted = (payload: CheckoutStartedPayload) => {
  chord.trackCheckoutStarted({ checkout: payload.cart }, {
    traits: addFBTrackingParams()
  })
}

const trackPageViewed = () => {
  chord.page()
}

const filterPayload = (payload: ProductListViewedPayload) => ({
  ...payload,
  products: payload.products
    .filter((product) => product && product.__typename === 'Product'),
})

const trackProductAdded = (payload: ProductAddedPayload) => {
  chord.trackProductAdded({
    cart: payload.cart,
    product: {
      product: payload.product,
      quantity: payload.quantity,
      variantId: payload.variant.variantId
    }
  }, { traits: addFBTrackingParams() })
}

const trackProductRemoved = (payload: ProductRemovedPayload) => {
  chord.trackProductRemoved({
    cart: payload.cart,
    lineItem: payload.lineItem,
  }, { traits: addFBTrackingParams() })
}

const trackProductViewed = (payload: ProductViewedPayload) => {
  chord.trackProductViewed({
    cart: payload.cart,
    product: {
      product: payload.product,
      quantity: payload.quantity,
      variantId: payload?.variant?.variantId
    }
  }, { traits: addFBTrackingParams() })
}

const trackProductListViewed = (payload: ProductListViewedPayload) => {
  const filteredPayload = filterPayload(payload)
  chord.trackProductListViewed({
    listId: filteredPayload.id,
    listName: filteredPayload.title,
    products: filteredPayload.products.map((product) => ({
      product,
      quantity: 1,
      variantId: product.variants.items[0].variantId,
    })),
  }, { traits: addFBTrackingParams() })
}

const trackProductClicked = (payload: ProductClickedPayload) => {
  chord.trackProductClicked({
    cart: payload.cart,
    listId: payload.listId,
    listName: payload.listName,
    product: {
      position: payload?.position,
      product: payload.product,
      quantity: payload.quantity,
      variantId: payload?.variant?.variantId
    }
  }, { traits: addFBTrackingParams() })
}

const trackProductsSearched = (payload: ProductsSearchedPayload) => {
  chord.trackProductsSearched({
    query: payload.query,
  }, { traits: addFBTrackingParams() })
}

const trackNavigationClicked = (payload: NavigationClickedPayload) => {
  chord.trackNavigationClicked({
    navigationUrl: payload.navigationUrl,
    navigationTitle: payload.navigationTitle,
    label: payload.label,
    category: payload.category,
    navigationPlacement: payload.navigationPlacement
  }, { traits: addFBTrackingParams() })
}

const trackVariantClicked = (payload: VariantClickedPayload) => {
  chord.trackVariantClicked({
    cart: payload.cart,
    product: {
      product: payload.product,
      quantity: payload.quantity,
      variantId: payload.variant.variantId,
    }
  }, { traits: addFBTrackingParams() })
}

const trackDiscountDenied = (payload: CouponDeniedPayload) => {
  chord.trackCouponDenied({
    cartId: payload?.cartId,
    couponName: payload?.couponName,
    reason: payload?.reason,
  }, { traits: addFBTrackingParams() })
}

const trackDiscountEntered = (payload: CouponEnteredPayload) => {
  chord.trackCouponEntered({
    cartId: payload?.cartId,
    couponName: payload?.couponName,
  }, { traits: addFBTrackingParams() })
}

const trackDiscountApplied = (payload: CouponAppliedPayload) => {
  chord.trackCouponApplied({
    cartId: payload?.cartId,
    couponName: payload?.couponName,
  })
}

const trackDiscountRemoved = (payload: CouponRemovedPayload) => {
  chord.trackCouponRemoved({
    cartId: payload?.cartId,
    couponName: payload?.couponName,
  }, { traits: addFBTrackingParams() })
}

const events = {
  trackCartViewed,
  trackCheckoutStarted,
  trackDiscountApplied,
  trackDiscountDenied,
  trackDiscountEntered,
  trackDiscountRemoved,
  trackNavigationClicked,
  trackPageViewed,
  trackProductAdded,
  trackProductClicked,
  trackProductListViewed,
  trackProductRemoved,
  trackProductViewed,
  trackProductsSearched,
  trackVariantClicked,
}

export default events
