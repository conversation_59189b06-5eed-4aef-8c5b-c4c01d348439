import analytics from '@lib/segment/segment'

type EventPayload = {
  [key: string]: any
}

type PageViewedPayload = EventPayload & {
  pageName: string
  url: string
}

type ProductPayload = EventPayload & {
  title: string
  slug: string
  productId: string
  quantity?: number
  currency?: string
}

type VariantPayload = EventPayload & {
  title: string
  variantId: number
  price: number
}

type CartPayload = EventPayload & {
  items: ProductPayload[]
  totalValue: number
}

type DiscountPayload = EventPayload & {
  discountCode: string
  discountValue: number
}

type CheckoutPayload = EventPayload & {
  revenue: number
  products: ProductPayload[]
}

type EventName =
  | 'pageViewed'
  | 'productViewed'
  | 'productClicked'
  | 'productAdded'
  | 'productRemoved'
  | 'variantClicked'
  | 'cartViewed'
  | 'discountEntered'
  | 'discountApplied'
  | 'discountDenied'
  | 'discountRemoved'
  | 'checkoutStarted'
  | 'productsSearched'
  | 'subscribed'
  | 'navigationClicked'
  | 'productListClicked'
  | 'promotionClicked'
  | 'promotionViewed'
  | 'productListViewed'
  | 'experimentViewed'
  | 'elementInteraction'

const trackEvent = (eventName: EventName, payload: EventPayload) => {
  // eslint-disable-next-line no-console
  console.log(`Tracking ${eventName} Event`, payload)
  analytics.track(eventName, payload)
}

const trackPageViewed = (payload: PageViewedPayload) => {
  trackEvent('pageViewed', payload)
}

const trackProductViewed = (payload: ProductPayload) => {
  trackEvent('productViewed', payload)
}

const trackProductAdded = (payload: ProductPayload) => {
  trackEvent('productAdded', payload)
}

const trackProductClicked = (payload: ProductPayload) => {
  trackEvent('productClicked', payload)
}

const trackProductRemoved = (payload: ProductPayload) => {
  trackEvent('productRemoved', payload)
}

const trackVariantClicked = (payload: VariantPayload) => {
  trackEvent('variantClicked', payload)
}

const trackCartViewed = (payload: CartPayload) => {
  trackEvent('cartViewed', payload)
}

const trackDiscountEntered = (payload: DiscountPayload) => {
  trackEvent('discountEntered', payload)
}

const trackDiscountApplied = (payload: DiscountPayload) => {
  trackEvent('discountApplied', payload)
}

const trackDiscountDenied = (payload: DiscountPayload) => {
  trackEvent('discountDenied', payload)
}

const trackDiscountRemoved = (payload: DiscountPayload) => {
  trackEvent('discountRemoved', payload)
}

const trackCheckoutStarted = (payload: CheckoutPayload) => {
  trackEvent('checkoutStarted', payload)
}

const trackProductsSearched = (payload: EventPayload) => {
  trackEvent('productsSearched', payload)
}

const trackSubscribed = (payload: EventPayload) => {
  trackEvent('subscribed', payload)
}

const trackNavigationClicked = (payload: EventPayload) => {
  trackEvent('navigationClicked', payload)
}

const trackProductListClicked = (payload: EventPayload) => {
  trackEvent('productListClicked', payload)
}

const trackPromotionClicked = (payload: EventPayload) => {
  trackEvent('promotionClicked', payload)
}

const trackPromotionViewed = (payload: EventPayload) => {
  trackEvent('promotionViewed', payload)
}

const trackProductListViewed = (payload: EventPayload) => {
  trackEvent('productListViewed', payload)
}

const trackExperimentViewed = (payload: EventPayload) => {
  trackEvent('experimentViewed', payload)
}

const trackElementInteraction = (payload: EventPayload) => {
  trackEvent('elementInteraction', payload)
}

const events = {
  trackPageViewed,
  trackProductViewed,
  trackProductClicked,
  trackVariantClicked,
  trackProductAdded,
  trackProductRemoved,
  trackCartViewed,
  trackDiscountEntered,
  trackDiscountApplied,
  trackDiscountDenied,
  trackDiscountRemoved,
  trackCheckoutStarted,
  trackProductsSearched,
  trackSubscribed,
  trackNavigationClicked,
  trackProductListClicked,
  trackPromotionClicked,
  trackPromotionViewed,
  trackProductListViewed,
  trackExperimentViewed,
  trackElementInteraction
}

export default events
