/**
 * Custom image loader for Next.js that standardizes query parameters for media assets
 */
export default function customLoader({ src, width }: { src: string; width?: string }): string {
  const contentfulHostnames = ['images.ctfassets.net', 'videos.ctfassets.net']
  const shopifyHostname = 'cdn.shopify.com'

  const baseUrl = src.split('?')[0]

  const isAllowedHost = contentfulHostnames.some((hostname) => baseUrl.includes(hostname)) || baseUrl.includes(shopifyHostname)

  if (isAllowedHost) {
    const url = new URL(baseUrl)

    if (baseUrl.endsWith('.svg')) {
      return baseUrl
    }

    url.searchParams.set('fm', 'webp')

    if (width) {
      url.searchParams.set('w', width)
    }

    return url.toString()
  }

  const params = new URLSearchParams()

  if (width) {
    params.set('w', width)
  }

  const paramsString = params.toString()

  return `${baseUrl}${paramsString ? `?${paramsString}` : ''}`
}
