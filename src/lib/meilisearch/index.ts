import getCookie from '@/utils/getCookie'

import { Mei<PERSON><PERSON>earch } from 'meilisearch'

const client = new MeiliSearch({
  host: process.env.NEXT_PUBLIC_MEILISEARCH_HOST as string,
  apiKey: process.env.NEXT_PUBLIC_MEILISEARCH_API_KEY as string,
})

export const getFrontendClient = (): MeiliSearch => {
  const userId = getCookie('ajs_anonymous_id')
  return new MeiliSearch({
    host: process.env.NEXT_PUBLIC_MEILISEARCH_HOST as string,
    apiKey: process.env.NEXT_PUBLIC_MEILISEARCH_API_KEY as string,
    requestConfig: {
      headers: userId ? { 'X-MS-USER-ID': userId } : {}
    }
  })
}

export default client
