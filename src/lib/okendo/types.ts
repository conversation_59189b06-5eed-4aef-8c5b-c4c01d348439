export type OkendoAggregateReviewsResponse = {
  reviewAggregate?: {
    reviewRatingValuesTotal: number,
    reviewCount: number
  },
  error?: {
    description: string
  }
}

export type OkendoStoreReviewAggregateError = {
  error: {
    description?: string
  }
}

export type OkendoReviewResponse = {
  reviewId: string;
  body: string;
  // ISO 8601 date string
  dateCreated: string;
  rating: number;
  reviewer: {
    displayName: string;
    isVerified: boolean;
  };
  title: string;
}

export type ReviewAggregate = {
  count: number,
  rating: number,
}

export type Review = {
  id: string,
  rating: number,
  title: string,
  body: string,
  datePublished?: string,
  name: string,
  verified: boolean
}

export type ProductLatestReviews = {
  reviews: Review[]
  aggregated: boolean
}
