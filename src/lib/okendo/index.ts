import {
  OkendoAggregateReviewsResponse, OkendoReviewResponse, ReviewAggregate, ProductLatestReviews
} from './types'

const BASE_URL = 'https://api.okendo.io/v1'
export const STORES_URL = `${BASE_URL}/stores/${process.env.NEXT_PUBLIC_OKENDO_SUBSCRIBER_ID}`
export const getProductUrl = (productId: string) => `${STORES_URL}/products/shopify-${productId}`

export const getStoreReviewsAggregate = async (): Promise<ReviewAggregate> => {
  try {
    const url = `${STORES_URL}/review_aggregate`
    const response = await fetch(url)
    const data: OkendoAggregateReviewsResponse = await response.json()

    if (!response.ok || data.error || !data.reviewAggregate) {
      throw new Error('Failed to fetch review aggregate data')
    }

    const { reviewAggregate } = data
    const { reviewRatingValuesTotal, reviewCount } = reviewAggregate

    if (reviewCount === 0 || reviewRatingValuesTotal === 0) {
      throw new Error('No reviews found')
    }

    // Calculate the rating to the nearest tenth
    const average = reviewRatingValuesTotal / reviewCount
    // eslint-disable-next-line no-magic-numbers
    const rating = Math.round(average * 10) / 10

    return {
      count: reviewCount,
      rating
    }
  } catch (error) {
    return {
      rating: 0,
      count: 0,
    }
  }
}

export async function getProductReviewsAggregate(productId: string): Promise<ReviewAggregate> {
  try {
    const productUrl = getProductUrl(productId)
    const response = await fetch(`${productUrl}/review_aggregate`)

    const data: OkendoAggregateReviewsResponse = await response.json()

    if (!response.ok || data.error || !data.reviewAggregate) {
      throw new Error('Failed to fetch review aggregate data')
    }

    const { reviewAggregate } = data
    const { reviewRatingValuesTotal, reviewCount } = reviewAggregate

    return {
      rating: reviewRatingValuesTotal / reviewCount,
      count: reviewCount,
    }
  } catch (error) {
    return {
      rating: 0,
      count: 0,
    }
  }
}

export async function getProductLatestReviews(productId: string): Promise<ProductLatestReviews> {
  try {
    const productUrl = getProductUrl(productId)
    const response = await fetch(
      `${productUrl}/reviews?limit=5&orderBy=rating%20desc`
    )

    const data: {
      areReviewsGrouped: boolean,
      nextUrl: string,
      reviews: OkendoReviewResponse[]
    } | {
      error: {
        description: string
      }
    } = await response.json()

    if (!response.ok) {
      throw new Error('Failed to fetch latest reviews')
    }

    if ('error' in data) {
      throw new Error(data.error.description)
    }

    const { reviews, areReviewsGrouped } = data

    return {
      reviews: reviews.map((review: OkendoReviewResponse) => ({
        id: review.reviewId,
        rating: review.rating,
        title: review.title,
        body: review.body,
        datePublished: review.dateCreated,
        name: review.reviewer.displayName,
        verified: review.reviewer.isVerified,
      })),
      aggregated: areReviewsGrouped,
    }
  } catch (error) {
    return {
      reviews: [],
      aggregated: false,
    }
  }
}
