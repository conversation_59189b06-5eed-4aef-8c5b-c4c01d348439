{"all_products": "All Products", "not_found": "Page not found.", "your_email": "Your email", "cart": {"page_title": "<PERSON><PERSON>", "empty": "You have no items in your cart", "shop_button": "Shop Our Collections", "subtotal": "Subtotal", "estimated_total": "Estimated Total:", "total": "Total:", "tax": "Tax", "shipping": "Shipping", "calculated_at_checkout": "Shipping (calculated at checkout)", "apply_promo_code": "+ Apply promo code", "apply_promo_code_button": "apply", "remove_promo_code_button": "remove", "checkout": "Checkout", "promo_code": "Promo Code", "gift_card": "Gift Card", "apply_gift_card": "+ Apply gift card", "apply_gift_card_button": "apply", "remove_gift_card_button": "remove", "cart_title": "Your Cart", "order_summary": "Order Summary", "your_items": "Your Items", "free_shipping": "Free", "shipping_banner_not_free": "Spend {amount} more for free shipping.", "shipping_banner_free": "You've qualified for free shipping!"}, "confirmation": {"page_title": "Thanks for your order", "title": "Thanks for your order", "text": "Order No. ", "error": "This page has expired. Please check your email for your order summary.", "contact": {"title": "Something not right?", "text": "Contact our customer service team at"}, "details": {"title": "confirmation", "subtitle": "You should receive it in the next few minutes. If you don't see it, try checking your spam folder.", "text": "An email is on its way to ", "shipping": "shipping to", "payment": "payment method"}}, "referral": {"title": "Refer a friend?", "subtitle": "15% off their first order", "text": "Share this link and your friends get a 15% discount on their order. To sweeten the deal, you'll receive a credit when they make their first purchase too.", "copy_link_input_label": "Your referral link", "copy_link_button": "copy link", "copy_link_button_clicked": "copied"}, "product": {"related_products": "More Great Products to Love", "filter_collections": "Filter by collection", "all_collections": "All", "singular": "product", "plural": "products", "ingredients_list": "View Full Ingredients List", "add_to_cart": "Add to cart", "reserve_now": "Reserve Now", "coming_soon": "Coming Soon", "sold_out": "Sold Out", "notify_when_available": "Enter your email below to be notified when this product becomes available again.", "notify_me": "Notify me", "notify_when_available_thank_you": "You're all set, we will email you when this product is back in stock and available for purchase.", "included_products": "Included Products:", "how_to_use": "How to use", "choose_variant": "Choose a variant", "choose_kit": "Choose a kit"}, "validation": {"required_field": "This field is required", "invalid_email": "Hmm, looks like that email address isn’t valid. Please try again."}, "collections": {"title": "COLLECTIONS", "shop_button": "EXPLORE"}, "footer": {"newsletter_signup": {"title": "Stay posted", "text": "Subscribe to our newsletter to receive updates on new launches, learnings, events and more.", "input_label": "Your email", "button": "submit", "confirmation": "Thanks for signing up!"}, "contact": {"title": "Contact", "general_inquiries": "General inquiries", "press_inquiries": "Press inquiries"}, "shop": {"title": "Shop", "shop_all": "All Products"}, "company": {"title": "Company"}}, "navigation": {"cart": "<PERSON><PERSON>", "menu": "<PERSON><PERSON>"}, "error": {"not_found": {"page_title": "404 Not found", "title": "This page is cooling off somewhere", "text": "Hit the button below to do the same or head back to our ", "text_homepage_link": "homepage", "cta": "Shop Products"}, "api": {"default": "An unexpected error occurred."}}, "articles": {"author_by": "By"}, "account": {"addresses": {"page_title": "My Account", "default_address_label": "default", "edit_button": "Edit", "delete_button": "Delete", "add_button": "Add a new address", "first_name_input_label": "First Name", "last_name_input_label": "Last Name", "company_input_label": "Company", "address_1_input_label": "Address 1", "address_2_input_label": "Address 2", "city_input_label": "City", "country_input_label": "Country/Region", "province_input_label": "Province/State", "zip_input_label": "Postal/Zip Code", "phone_input_label": "Phone", "default_address_checkbox_label": "Set as default address", "submit_button": "Submit", "addresses_list_title": "Your Addresses", "empty_list": "No saved addresses", "add_address_title": "Add address"}, "forgot_password": {"page_title": "Forgot password", "email_input_label": "Email", "confirmation": "Check your email for a link to reset your password.", "button": "Email me"}, "login": {"page_title": "Log in", "email_input_label": "Email", "password_input_label": "Password", "button": "Log in", "forgot_link": "Forgot your password?", "register_link": "Register for an account"}, "profile": {"page_title": "Profile", "logout": "Log out", "account_details_title": "Account Details", "view_addresses_link": "View Addresses", "order_history_title": "Order History", "column_date": "Date", "column_number": "Order Number", "column_status": "Order Status"}, "register": {"page_title": "Register", "email_input_label": "Email", "password_input_label": "Password", "button": "Register", "login_link": "Already have an account? Log in."}, "reset_password": {"page_title": "Reset password", "password_input_label": "Password", "confirm_password_input_label": "Confirm Password", "button": "Log in"}}}