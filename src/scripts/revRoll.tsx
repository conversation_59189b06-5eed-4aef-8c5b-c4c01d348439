'use client'

import Script from 'next/script'

declare global {
  interface Window {
    dataLayer: any[]
  }
}

const RevRoll = () => {
  const onRevRoll = () => {
    const l = 'dataLayer'
    window[l] = window[l] || []
    window[l]?.push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' })
  }

  return (
    <Script
      src="https://ss.carawayhome.com/pqbftcll.js?id=GTM-TTZRC24"
      onLoad={() => onRevRoll()}
      strategy="lazyOnload"
    />
  )
}
export default RevRoll
