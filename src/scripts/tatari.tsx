'use client'

import tracking from '@utils/trackingId'

import { useEffect } from 'react'

declare global {
  interface Window {
    tatari: any
  }
}

const Tatari = () => {
  useEffect(() => {
    try {
      (function tatariInit(doc, i) {
        if (!i.version) {
          window.tatari = i
          i.init = function init(config: any, n: any) {
            const e = function e(events: any[][], eventName: string) {
              i[eventName] = function eventNameFunction(...args: any[]) {
                i.push([eventName].concat(Array.prototype.slice.call(args, 0)))
              }
            }
            'track pageview identify'.split(' ').forEach((event) => {
              e(i, event)
            })
            i._i = config
            i.config = n
            i.pageview()
          }
          i.version = '1.2.1'
          const n = doc.createElement('script')
          n.type = 'text/javascript'
          n.async = true
          n.src =
            'https://d2hrivdxn8ekm8.cloudfront.net/tag-manager/a7db7bfb-92da-40cf-979c-3274f71dddc5-latest.js'
          const e = doc.getElementsByTagName('script')[0]
          if (e.parentNode) {
            e.parentNode.insertBefore(n, e)
          }
        }
      }(document, window.tatari || []))
    } catch (t) {
      // eslint-disable-next-line no-console
      console.error('Tatari initialization error:', t)
    } finally {
      if (window.tatari) {
        window.tatari.init('a7db7bfb-92da-40cf-979c-3274f71dddc5')
        window.tatari.identify(tracking())
      }
    }
  }, [])

  return null
}

export default Tatari
