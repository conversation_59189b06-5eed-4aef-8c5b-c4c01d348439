import Script from 'next/script'

declare global {
  interface Window {
    parcelLabTrackAndTrace?: {
      initialize: (config: {
        plUserId: number
        show_searchForm: boolean
        show_zipCodeInput: boolean
        show_articleList: boolean
        use_campaign_banners: boolean
      }) => void
    };
  }
}

const plTrackAndTraceStart = () => {
  window?.parcelLabTrackAndTrace?.initialize({
    plUserId: 1620800,
    show_searchForm: true,
    show_zipCodeInput: true,
    show_articleList: true,
    use_campaign_banners: true,
  })
  const linkTag = document.createElement('link')
  linkTag.rel = 'stylesheet'
  linkTag.href = 'https://cdn.parcellab.com/css/v5/main.min.css'
  document.getElementsByTagName('head')[0].appendChild(linkTag)
}

const ParcelLab = () => (
  <Script
    src="https://cdn.parcellab.com/js/v5/main.min.js"
    onLoad={() => plTrackAndTraceStart()}
  />
)

export default ParcelLab
