'use client'

import useIsMobile from '@/hooks/isMobile'
import { isClient } from '@/utils/checking'

import Script from 'next/script'
import { useEffect } from 'react'

declare global {
  interface Window {
    DG_CHAT_WIDGET_CONFIG: {
      widgetId: string;
      env: string;
    };
    dgchat?: {
      init: () => void;
    };
  }
}

const DigitalGenius = () => {
  const onDigitalGeniusLoad = () => {
    window.DG_CHAT_WIDGET_CONFIG = {
      widgetId: '1ea28e32-4892-4394-8301-c8d57886f134',
      env: 'us',
    }
    if (window.dgchat) window.dgchat.init()
  }

  const { isMobile } = useIsMobile()

  useEffect(() => {
    if (!isMobile || !isClient()) return

    const observer = new MutationObserver(() => {
      const dgChat = document.getElementById('dg-chat')
      const dgChatContainer = document.getElementById('dg-chat-widget-container')

      if (dgChat && dgChatContainer) {
        const widgetObserver = new MutationObserver(() => {
          if (dgChatContainer.children.length > 0) {
            dgChat.style.setProperty('z-index', '55', 'important')
          } else {
            dgChat.style.setProperty('z-index', '40', 'important')
          }
        })

        widgetObserver.observe(dgChatContainer, {
          childList: true,
          subtree: false,
        })

        if (dgChatContainer.children.length > 0) {
          dgChat.style.setProperty('z-index', '55', 'important')
        }

        observer.disconnect()
      }
    })

    observer.observe(document.body, { childList: true, subtree: true })
  }, [isMobile])

  return (
    <Script
      src="https://chat.digitalgenius.com/init.js"
      onLoad={onDigitalGeniusLoad}
      strategy="lazyOnload"
    />
  )
}

export default DigitalGenius
