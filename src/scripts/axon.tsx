/* eslint-disable max-len */

'use client'

import getCookie from '@/utils/getCookie'

import Script from 'next/script'
import { useEffect } from 'react'

const INTERVAL_MS = 100

const Axon = () => {
  useEffect(() => {
    const interval = setInterval(() => {
      const axonCookie = getCookie('_axwrt')

      if (axonCookie) {
        clearInterval(interval)
        document.cookie = `axwrt=${axonCookie}; max-age=31536000; path=/; domain=.carawayhome.com`
      }
    }, INTERVAL_MS)

    return () => clearInterval(interval)
  }, [])

  return (
    <Script id="axon-script">{`
      const AXON_EVENT_KEY = '47197a38-b7f8-4c47-b906-d084e53de34d';
      !function(e,r){var t=["https://s.axon.ai/pixel.js","https://c.albss.com/p/l/loader.iife.js"];if(!e.axon){var a=e.axon=function(){a.performOperation?a.performOperation.apply(a,arguments):a.operationQueue.push(arguments)};a.operationQueue=[],a.ts=Date.now(),a.eventKey=AXON_EVENT_KEY;for(var n=r.getElementsByTagName("script")[0],o=0;o<t.length;o++){var i=r.createElement("script");i.async=!0,i.src=t[o],n.parentNode.insertBefore(i,n)}}}(window,document);
      axon('init');
    `}
    </Script>
  )
}

export default Axon
