'use client'

import { useEffect } from 'react'

declare global {
  type ClarityFunction = (command: string, ...args: unknown[]) => void;

  interface Window {
    clarity?: ClarityFunction & { q?: unknown[] } & { [key: string]: any };
  }
}

const MicrosoftClarity = () => {
  useEffect(() => {
    (function clarityInit(
      c: Window,
      l: Document,
      a: 'clarity',
      r: string,
      i: string,
      t?: HTMLScriptElement,
      y?: Element
    ) {
      c[a] = c[a] || function clarityFunction(...args: unknown[]) {
        (c[a]!.q = c[a]!.q || []).push(args)
      }

      t = l.createElement(r) as HTMLScriptElement
      t.async = true
      t.src = `https://www.clarity.ms/tag/${i}`
      const elements = Array.from(l.getElementsByTagName(r));
      [y] = elements
      y?.parentNode?.insertBefore(t, y)
    }(window, document, 'clarity', 'script', 'n48bq10gx5'))
  }, [])

  return null
}

export default MicrosoftClarity
