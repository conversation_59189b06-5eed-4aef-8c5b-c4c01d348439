import { useDispatch, useSelector, useStore } from 'react-redux'

import type { TypedUseSelectorHook } from 'react-redux'
import type { RootState, AppDispatch } from './store'
import type { Action, EnhancedStore } from '@reduxjs/toolkit'

export const useAppDispatch: () => AppDispatch = useDispatch
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
export const useAppStore: () => EnhancedStore<RootState, Action> = useStore
