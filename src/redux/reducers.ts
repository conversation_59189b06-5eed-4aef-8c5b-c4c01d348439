import cartReducer from '@redux/features/cart/cartSlice'
import productReducer from '@redux/features/product/productSlice'
import eventsReducer from '@redux/features/events/eventsSlice'
import meilisearchEventsReducer from '@redux/features/events/meilisearchSlice'
import gwpReducer from '@redux/features/promo/gwp'
import { availabilityApi } from '@redux/features/api/availabilitySlice'

const rootReducer = {
  cart: cartReducer,
  product: productReducer,
  events: eventsReducer,
  meilisearchEvents: meilisearchEventsReducer,
  gwp: gwpReducer,

  [availabilityApi.reducerPath]: availabilityApi.reducer,
}

export default rootReducer
