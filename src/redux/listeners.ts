/* eslint-disable import/order  */
/* eslint-disable */

// The cartSlice handles actions related to shopping cart.
import { addToCart, removeFromCart, updateCart } from '@redux/features/cart/cartSlice'
// The productSlice manages actions related to products.
import { selectProduct, selectVariant } from '@redux/features/product/productSlice'
import { productAdded as meilisearchProductAdded } from '@redux/features/events/meilisearchSlice'
import { chord } from '@lib/chord/events'
import getCookie from '@utils/getCookie'

// The eventsSlice manages all the events related to tracking events which are side effects of actions.
import {
  productAdded,
  productClicked,
  variantClicked,
  productRemoved
} from '@redux/features/events/eventsSlice'

import getCollections from '@utils/productData'

import { createListenerMiddleware } from '@reduxjs/toolkit'

import type { RootState } from './store'
import generateTtkTrackingId from '@/utils/trackingId'
import { getSizeByVariantId, mdGWPSizes } from '@/components/Cart/utils/mdGWP'
import { rejectOffer, selectSize } from './features/promo/gwp'

const listenerMiddleware = createListenerMiddleware()

listenerMiddleware.startListening({
  actionCreator: addToCart,
  effect: (action, listenerApi) => {

    const state = listenerApi.getState() as RootState;

    listenerApi.dispatch(
      productAdded({
        ...action.payload,
        cart: state.cart.cart
      })
    )

    // the following code handles non standard add to cart events, this is temporary and should be deprecated
    const eventID = generateTtkTrackingId()

    const { product, variant } = action.payload

    const collections = getCollections(product)
    const firstCollection = collections.length > 0 ? collections[0] : null

    const productAddedData = {
      content_ids: variant.variantId,
      content_name: product.title,
      content_category: firstCollection,
      currency: 'USD',
      value: variant.price,
      product_id: product.productId,
      category: firstCollection,
      contents: [{
          id: variant.variantId,
          quantity: 1,
          value: variant.price,
        }],
      fbp: getCookie('_fbp'),
      fbc: getCookie('_fbc'),
      event_id: eventID
    }

    const ttqData = {
      "contents": [
        {
          content_id: product?.productId?.toString(),
          content_name: product?.title,
          content_category: collections,
          brand: "Caraway",
          quantity: 1,
          value: variant?.price,
          price: variant?.price
        }
      ],
      value: variant?.price,
      content_type: "product",
      currency: "USD",
    }

    window?.dataLayer?.push({
      event: "rr_add_to_cart",
      rr_cart_id: state.cart.cart.cartId,
      ecommerce: {
      currencyCode: "USD",
        add: {
          products: [{
          name: product.title,
          id: product.productId.toString(),
          sku: variant.sku,
          variant_id: variant.variantId,
          price: variant.price,
          brand: "Caraway",
          category: firstCollection,
          image: variant?.primaryImage?.url,
          quantity: 1
          }]
        }
      },
    })

    window?.postscript?.event('add_to_cart', {
      "shop_id": "4845",
      "url": window.location.href,
      "page_type": "product",
      "resource": {
        // "category": product.collection[0]?.slug,
        "name": product.title,
        "price_in_cents": variant.price * 100,
        "resource_id": product.productId,
        "resource_type": "product",
        "sku": variant.sku,
        "variant_id": variant.variantId,
        "vendor": "Caraway"
      }
    })

    window?.ttq?.track('AddToCart', ttqData, {event_id: eventID})
    chord.track('AddToCart', productAddedData)

    listenerApi.dispatch(
      meilisearchProductAdded({ slug: product.slug })
    )

  }
})

listenerMiddleware.startListening({
  actionCreator: removeFromCart,
  effect: (action, listenerApi) => {
    const state = listenerApi.getState() as RootState;
     listenerApi.dispatch(
      productRemoved({
        ...action.payload,
        cart: state.cart.cart
      })
    )
  }
})

listenerMiddleware.startListening({
  actionCreator: selectProduct,
  effect: (action, listenerApi) => {
    const state = listenerApi.getState() as RootState;
    listenerApi.dispatch(
      productClicked({
        ...action.payload,
        cart: state.cart.cart,
      })
    )
  }
})


listenerMiddleware.startListening({
  actionCreator: selectVariant,
  effect: (action, listenerApi) => {
    const state = listenerApi.getState() as RootState;
    listenerApi.dispatch(
      variantClicked({
        ...action.payload,
        cart: state.cart.cart,
      })
    )
  }
})

listenerMiddleware.startListening({
  actionCreator: updateCart,
  effect: (action, listenerApi) => {
    const gwpVariantIds = mdGWPSizes.map((size) => size.variantId)

    const gwpProduct = action.payload.products.find((product) => {
      return gwpVariantIds.includes(product.variantId)
    })

    if (gwpProduct) {
      const size = getSizeByVariantId(gwpProduct.variantId)
      
      if (size) {
        listenerApi.dispatch(selectSize(size));
      }
    }

    const isRejected = localStorage.getItem('gwpOfferRejected') === 'true'

    if (isRejected) {
      listenerApi.dispatch(rejectOffer())
    }
  },
})

export default listenerMiddleware
