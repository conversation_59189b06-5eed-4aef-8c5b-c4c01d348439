/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  AddToCartAction,
  CartState,
  RemoveFromCartAction,
  UpdateCartAction,
} from './types'

import { createSlice, PayloadAction } from '@reduxjs/toolkit'

type incrementByAmountProps = {
  variantId: number
  quantity: number
}

const initialState: CartState = {
  cart: {
    cartId: '',
    currency: '',
    products: [],
    value: 0
  },
  open: false,
  testing: 0,
  error: {}
}

export const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    updateCart: (state, action: UpdateCartAction) => {
      state.cart = action.payload
    },
    increment: (state) => {
      state.testing += 1
    },
    decrement: (state) => {
      state.testing -= 1
    },
    incrementByAmount: (state, action: PayloadAction<incrementByAmountProps>) => {
      // can also attach the payload to a listener and grab the rest of the cart item details if we need it
    },
    addToCart: (state, action: AddToCartAction) => {
      // TODO: Implement the logic to add the item to the cart
    },
    removeFromCart: (state, action: RemoveFromCartAction) => {
      // const { currentProduct, remove } = action.payload

      // // TODO: Refactor this as it's being used above too.
      // const existingLine = state.cart.find((line) => line.productId === currentProduct.productId)

      // if (existingLine) {
      //   existingLine.quantity -= 1
      //   if (existingLine.quantity === 0) {
      //     // TODO: Refactor this as it's being used below again
      //     state.cart = state.cart.filter((line) => line.productId !== currentProduct.productId)
      //   }
      // }

      // if (remove) {
      //   state.cart = state.cart.filter((line) => line.productId !== currentProduct.productId)
      // }
    },
    signalError: (state, action: PayloadAction<string>) => {
      state.error = {
        message: action.payload
      }
    },
    open: (state) => {
      state.open = true
    },
    close: (state) => {
      state.open = false
    }
  },
})

export const {
  updateCart,
  addToCart,
  removeFromCart,
  increment,
  decrement,
  incrementByAmount,
  open,
  close,
  signalError
} = cartSlice.actions

export default cartSlice.reducer
