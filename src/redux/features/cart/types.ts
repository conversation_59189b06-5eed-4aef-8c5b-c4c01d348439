import { PayloadAction } from '@reduxjs/toolkit'

import type { ProductCardFieldsProps } from '@components/Product/types'

export type AddToCartAction = PayloadAction<AddToCartPayload>

export type CartViewedAction = PayloadAction<boolean>

export type RemoveFromCartAction = PayloadAction<RemoveFromCartPayload>

export type UpdateCartAction = PayloadAction<CartStateCart>

export type AddToCartPayload = {
  product: ProductCardFieldsProps
  quantity: number
  variant: {
    variantId: number,
    price: number,
    sku?: string,
    primaryImage?: {
      title: string
      url: string
    }
  }
}

export type RemoveFromCartPayload = {
  lineItem: CartStateLineItem
}

export type CartState = {
  cart: CartStateCart
  open: boolean
  testing: number
  error?: {
    message?: string
  }
}

export type CartStateCart = {
  cartId?: string
  currency: string
  products: CartStateLineItem[]
  value: number
}

export type CartStateLineItem = {
  amount: number
  currency: string
  imageUrl?: string
  lineItemId?: string
  name: string
  options?: string[]
  productId: string
  quantity: number
  sku?: string
  slug?: string
  variant: string
  variantId: string
}
