import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export type Size = {
  label: null | 'XS/S' | 'M/L' | 'XL/XXL';
  variantId: string;
};

type GWPState = {
  isEligible: boolean;
  selectedSize: Size | null;
  offerRejected: boolean;
};

const initialState: GWPState = {
  isEligible: false,
  selectedSize: null,
  offerRejected: false,
}

export const gwpSlice = createSlice({
  name: 'gwp',
  initialState,
  reducers: {
    setIsEligible: (state, action: PayloadAction<boolean>) => {
      state.isEligible = action.payload
    },
    selectSize: (state, action: PayloadAction<Size>) => {
      const size = {
        label: action.payload.label,
        variantId: `gid://shopify/ProductVariant/${action.payload.variantId}`,
      }
      state.selectedSize = size
      state.offerRejected = false
    },
    rejectOffer: (state) => {
      state.offerRejected = true
      state.selectedSize = null
    },
    resetOffer: (state) => {
      state.offerRejected = false
      state.selectedSize = null
    },
  },
})

export const {
  setIsEligible,
  selectSize,
  rejectOffer,
  resetOffer
} = gwpSlice.actions

export default gwpSlice.reducer
