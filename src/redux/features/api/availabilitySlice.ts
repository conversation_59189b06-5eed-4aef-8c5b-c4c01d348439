import {
  storeDomain,
  publicStorefrontToken,
  storefrontApiVersion
} from '@/config/storefront'
import { encodeShopifyVariantID } from '@utils/shopifyBase64'

import { createApi } from '@reduxjs/toolkit/query/react'
import { graphqlRequestBaseQuery } from '@rtk-query/graphql-request-base-query'
import { gql } from 'graphql-request'

const AVAILABILITY_FIELDS = `
  availableForSale
`

type VariantAvailability = {
  node: {
    availableForSale: boolean
  }
}

type ProductAvailability = {
  product: {
    availableForSale: boolean
  }
}

const gid = (id: number) => encodeShopifyVariantID(id)

export const availabilityApi = createApi({
  reducerPath: 'availabilityApi',
  baseQuery: graphqlRequestBaseQuery({
    url: `${storeDomain}/api/${storefrontApiVersion}/graphql.json/`,
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json')
      headers.set('X-Shopify-Storefront-Access-Token', `${publicStorefrontToken}`)
      return headers
    }
  }),
  refetchOnFocus: true,
  endpoints: (builder) => ({
    getVariantAvailability: builder.query<{}, number>({
      query: (id) => ({
        document: gql`
        query getVariantAvailability {
          node(id: "${gid(id)}") {
            ... on ProductVariant {
              ${AVAILABILITY_FIELDS}
            }
          }
        }
        `,
      }),
      transformResponse: async (response: VariantAvailability) => Promise.resolve(response).then((res) => res?.node?.availableForSale),
    }),
    getProductAvailability: builder.query<{}, number>({
      query: (id) => ({
        document: gql`
        query getProductById {
          product(id: "gid://shopify/Product/${id}") {
            ${AVAILABILITY_FIELDS}
          }
        }
        `,
      }),
      transformResponse: async (response: ProductAvailability) => Promise.resolve(response).then((res) => res?.product?.availableForSale),
    }),
  }),
})

export const { useGetVariantAvailabilityQuery, useGetProductAvailabilityQuery } = availabilityApi
