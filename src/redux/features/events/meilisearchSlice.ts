// import { trackMlAddToCartEvent, trackMlClickEvent } from '@/utils/milisearch'

import { createSlice, PayloadAction } from '@reduxjs/toolkit'

type MeilisearchEventPayload = {
  objectId: string,
  position: number,
  slug: string,
}

// ATENTION: We are temporarily disabling analytics events as meilisearch is experiencing issues with data collection.
export const meilisearchEventsSlice = createSlice({
  name: 'meilisearchEvents',
  initialState: {
    objectId: '',
    position: -1,
    slug: '',
  },
  reducers: {
    productClicked: (state, action: PayloadAction<MeilisearchEventPayload>) => {
      // trackMlClickEvent(action.payload)

      state.objectId = action.payload.objectId
      state.position = action.payload.position
      state.slug = action.payload.slug
    },
    productAdded: (state, action: PayloadAction<{ slug: string }>) => {
      if (action.payload.slug === state.slug) {
        /*  trackMlAddToCartEvent({
          objectId: state.objectId,
          position: state.position,
        }) */
        state.objectId = ''
        state.position = -1
      }
    },
  },
})

export const { productClicked, productAdded } = meilisearchEventsSlice.actions

export default meilisearchEventsSlice.reducer
