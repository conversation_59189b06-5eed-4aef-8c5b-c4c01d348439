/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  CartViewedAction,
  CheckoutStartedAction,
  DiscountAppliedAction,
  DiscountDeniedAction,
  DiscountEnteredAction,
  DiscountRemovedAction,
  NavigationClickedAction,
  ProductAddedAction,
  ProductClickedAction,
  ProductListViewedAction,
  ProductRemovedAction,
  ProductViewedAction,
  ProductsSearchedAction,
  VariantClickedAction,
} from './types'

import axonEvents from '@lib/axon/events'
import chordEvents from '@lib/chord/events'
import segmentEvents from '@lib/segment/events'

import { createSlice, PayloadAction } from '@reduxjs/toolkit'

const {
  trackCartViewed,
  trackCheckoutStarted,
  trackDiscountApplied,
  trackDiscountDenied,
  trackDiscountEntered,
  trackDiscountRemoved,
  trackNavigationClicked,
  trackPageViewed,
  trackProductAdded,
  trackProductClicked,
  trackProductListViewed,
  trackProductRemoved,
  trackProductViewed,
  trackProductsSearched,
  trackVariantClicked,
} = chordEvents

const {
  trackAxonAddToCart,
  trackAxonBeginCheckout,
  trackAxonPageView,
  trackAxonViewItem,
} = axonEvents

const {
  trackSubscribed,
  trackProductListClicked,
  trackPromotionClicked,
  trackPromotionViewed,
  trackExperimentViewed,
  trackElementInteraction
} = segmentEvents

export const eventsSlice = createSlice({
  name: 'events',
  initialState: {
  },
  reducers: {
    pageViewed: (state, action: PayloadAction<any>) => {
      trackPageViewed()
      window?.tatari?.pageview()
      trackAxonPageView()
    },
    productViewed: (state, action: ProductViewedAction) => {
      trackProductViewed(action.payload)
      trackAxonViewItem(action.payload)
    },
    productClicked: (state, action: ProductClickedAction) => {
      trackProductClicked(action.payload)
    },
    productAdded: (state, action: ProductAddedAction) => {
      trackProductAdded(action.payload)
      trackAxonAddToCart(action.payload)
    },
    productRemoved: (state, action: ProductRemovedAction) => {
      trackProductRemoved(action.payload)
    },
    variantClicked: (state, action: VariantClickedAction) => {
      trackVariantClicked(action.payload)
    },
    cartViewed: (state, action: CartViewedAction) => {
      trackCartViewed(action.payload)
    },
    discountEntered: (state, action: DiscountEnteredAction) => {
      trackDiscountEntered(action.payload)
    },
    discountApplied: (state, action: DiscountAppliedAction) => {
      trackDiscountApplied(action.payload)
    },
    discountDenied: (state, action: DiscountDeniedAction) => {
      trackDiscountDenied(action.payload)
    },
    discountRemoved: (state, action: DiscountRemovedAction) => {
      trackDiscountRemoved(action.payload)
    },
    checkoutStarted: (state, action: CheckoutStartedAction) => {
      trackCheckoutStarted(action.payload)
      trackAxonBeginCheckout(action.payload)
    },
    productsSearched: (state, action: ProductsSearchedAction) => {
      trackProductsSearched(action.payload)
    },
    subscribed: (state, action: PayloadAction<any>) => {
      // trackSubscribed(action.payload)
    },
    navigationClicked: (state, action: NavigationClickedAction) => {
      trackNavigationClicked(action.payload)
    },
    productListClicked: (state, action: PayloadAction<any>) => {
      // trackProductListClicked(action.payload)
    },
    promotionClicked: (state, action: PayloadAction<any>) => {
      // trackPromotionClicked(action.payload)
    },
    promotionViewed: (state, action: PayloadAction<any>) => {
      // trackPromotionViewed(action.payload)
    },
    productListViewed: (state, action: ProductListViewedAction) => {
      trackProductListViewed(action.payload)
    },
    experimentViewed: (state, action: PayloadAction<any>) => {
      // trackExperimentViewed(action.payload)
    },
    elementInteraction: (state, action: PayloadAction<any>) => {
      trackElementInteraction(action.payload)
    }
  },
})

export const {
  pageViewed,
  productViewed,
  productClicked,
  productAdded,
  productRemoved,
  variantClicked,
  cartViewed,
  discountEntered,
  discountApplied,
  discountDenied,
  discountRemoved,
  checkoutStarted,
  productsSearched,
  subscribed,
  navigationClicked,
  productListClicked,
  promotionClicked,
  promotionViewed,
  productListViewed,
  experimentViewed,
  elementInteraction
} = eventsSlice.actions

export default eventsSlice.reducer
