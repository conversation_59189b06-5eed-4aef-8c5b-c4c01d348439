import { PayloadAction } from '@reduxjs/toolkit'

import type { ProductCardFieldsProps, VariantProps } from '@/components/Product/types'
import type { ContentfulProduct } from '@lib/contentful/types/products'
import type {
  AddToCartPayload, CartStateCart, RemoveFromCartPayload
} from '@redux/features/cart/types'

export type CartViewedAction = PayloadAction<CartViewedPayload>
export type CheckoutStartedAction = PayloadAction<CheckoutStartedPayload>
export type DiscountAppliedAction = PayloadAction<CouponEnteredPayload>
export type DiscountDeniedAction = PayloadAction<CouponDeniedPayload>
export type DiscountEnteredAction = PayloadAction<CouponEnteredPayload>
export type DiscountRemovedAction = PayloadAction<CouponEnteredPayload>
export type NavigationClickedAction = PayloadAction<NavigationClickedPayload>
export type ProductAddedAction = PayloadAction<ProductAddedPayload>
export type ProductClickedAction = PayloadAction<ProductClickedPayload>
export type ProductListViewedAction = PayloadAction<ProductListViewedPayload>
export type ProductRemovedAction = PayloadAction<ProductRemovedPayload>
export type ProductViewedAction = PayloadAction<ProductViewedPayload>
export type ProductsSearchedAction = PayloadAction<ProductsSearchedPayload>
export type VariantClickedAction = PayloadAction<VariantClickedPayload>

export type CartViewedPayload = {
  cart: CartStateCart
}

export type CheckoutStartedPayload = {
  cart: CartStateCart
}

export type CouponAppliedPayload = CouponEnteredPayload

export type CouponDeniedPayload = {
  cartId?: string,
  couponName?: string,
  reason?: string,
}

export type CouponEnteredPayload = {
  cartId?: string,
  couponName?: string,
}

export type CouponRemovedPayload = CouponEnteredPayload

export type NavigationClickedPayload = {
  navigationUrl?: string,
  navigationTitle?: string,
  label?: string,
  category?: string,
  navigationPlacement?: string,
}

export type ProductAddedPayload = AddToCartPayload & {
  cart: CartStateCart
}

export type ProductClickedPayload = {
  listId?: string,
  listName?: string,
  position?: number,
  cart: CartStateCart,
  product: ProductCardFieldsProps,
  quantity: number,
  variant: VariantProps
}

export type ProductListViewedPayload = {
  id?: string
  title?: string
  products: ContentfulProduct[]
}

export type ProductRemovedPayload = RemoveFromCartPayload & {
  cart: CartStateCart
}

export type ProductViewedPayload = {
  cart: CartStateCart
  product: ProductCardFieldsProps
  quantity: number
  variant: VariantProps
}

export type ProductsSearchedPayload = {
  query: string
}

export type VariantClickedPayload = ProductClickedPayload
