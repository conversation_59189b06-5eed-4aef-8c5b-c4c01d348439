'use client'

import {
  useEffect, useState, useRef, DependencyList
} from 'react'

const DEFAULT_THRESHOLD = 0.1

interface EnhancedIntersectionObserverOptions {
  root?: Element | Document | null
  rootMargin?: string
  threshold?: number | number[]
  dependencies?: DependencyList
}

/**
 * Enhanced version of useIntersectionObserver with
 * dependency-based reconnection capabilities
 */
function useEnhancedIntersectionObserver({
  root = null,
  rootMargin = '0px',
  threshold = DEFAULT_THRESHOLD,
  dependencies = []
}: EnhancedIntersectionObserverOptions = {}) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [intersectionRatio, setIntersectionRatio] = useState(0)
  const targetRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!targetRef.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        setIntersectionRatio(entry.intersectionRatio)
      },
      { root, rootMargin, threshold }
    )

    const currentTarget = targetRef.current
    observer.observe(currentTarget)

    // eslint-disable-next-line consistent-return
    return function cleanupObserver() {
      observer.unobserve(currentTarget)
    }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [root, rootMargin, threshold, ...dependencies])

  return {
    isIntersecting,
    intersectionRatio,
    ref: targetRef
  }
}

export default useEnhancedIntersectionObserver
