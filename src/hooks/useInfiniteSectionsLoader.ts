'use client'

import { PageSection } from '@/lib/contentful/types'

import {
  useCallback,
  useState,
  useMemo
} from 'react'

async function loadSection(pageId: string, sectionId: string) {
  const response = await fetch('/api/collections/sections', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      pageId,
      sectionIds: [sectionId],
      sectionType: 'PageSectionsProduct'
    })
  })

  const data = await response.json()

  if (!response.ok) {
    throw new Error(data.error || 'Failed to fetch section')
  }

  return data
}

export default function useInfiniteSectionsLoader({
  pageId,
  initialSections,
  pageStructure
}: {
  pageId: string
  initialSections: PageSection[]
  pageStructure: PageSection[]
}) {
  const [loadedSections, setLoadedSections] = useState<Map<string, PageSection>>(
    new Map(initialSections.map((section) => [section.sys.id, section]))
  )
  const [loadingSections, setLoadingSections] = useState<Set<string>>(new Set())
  const [error, setError] = useState<string | null>(null)

  const initialProductSectionIds = useMemo(() => new Set(initialSections
    .filter((section) => section.sectionType === 'PageSectionsProduct')
    .map((section) => section.sys.id)), [initialSections])

  const hasMore = useMemo(() => {
    const productSections = pageStructure.filter(
      (section) => section.sectionType === 'PageSectionsProduct'
    )
    return productSections.some((section) => !loadedSections.has(section.sys.id))
  }, [pageStructure, loadedSections])

  const topObserverOptions = useMemo(() => ({
    rootMargin: '0px 0px 1000px 0px',
    threshold: 0.1,
    dependencies: [loadedSections, loadingSections]
  }), [loadedSections, loadingSections])

  const bottomObserverOptions = useMemo(() => ({
    rootMargin: '1000px 0px 0px 0px',
    threshold: 0.1,
    dependencies: [loadedSections, loadingSections]
  }), [loadedSections, loadingSections])

  const fetchSection = useCallback(async (sectionId: string) => {
    if (loadingSections.has(sectionId) || loadedSections.has(sectionId)) return

    setLoadingSections((prev) => {
      const updated = new Set(prev)
      updated.add(sectionId)
      return updated
    })

    try {
      const data = await loadSection(pageId, sectionId)

      if (data.sections && data.sections.length > 0) {
        setLoadedSections((prev) => {
          const updated = new Map(prev)
          data.sections.forEach((section: PageSection) => {
            updated.set(section.sys.id, section)
          })
          return updated
        })
      }

      setError(null)
    } catch (fetchError: any) {
      console.error(`Error fetching section ${sectionId}:`, fetchError)
      setError(fetchError?.message || 'Failed to load section')
    } finally {
      setLoadingSections((prev) => {
        const updated = new Set(prev)
        updated.delete(sectionId)
        return updated
      })
    }
  }, [loadingSections, loadedSections, pageId])

  const shouldHaveSentinel = useCallback((section: PageSection) => section.sectionType === 'PageSectionsProduct' &&
           !loadedSections.has(section.sys.id) &&
           !initialProductSectionIds.has(section.sys.id), [loadedSections, initialProductSectionIds])

  return {
    loadedSections,
    loadingSections,
    hasMore,
    error,
    fetchSection,
    shouldHaveSentinel,
    topObserverOptions,
    bottomObserverOptions
  }
}
