import { useCallback, useEffect, useState } from 'react'

export function useMatchMedia(media: string = '') {
  const [matches, setMatches] = useState(false)

  const checkMedia = useCallback(() => {
    const mediaQuery = window.matchMedia(media)
    setMatches(mediaQuery.matches)
  }, [media])

  useEffect(() => {
    checkMedia()
    window.addEventListener('resize', checkMedia)

    return () => {
      window.removeEventListener('resize', checkMedia)
    }
  }, [media, checkMedia])

  return matches
}

function useIsMobile(mobileMedia: string = '(max-width: 1024px)') {
  const isMobile = useMatchMedia(mobileMedia)
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    setIsReady(true)
  }, [])

  return { isMobile, isReady }
}

export default useIsMobile
