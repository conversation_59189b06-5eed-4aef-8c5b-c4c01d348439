'use client'

import { UseFormSetError } from 'react-hook-form'

type FormFields = {
  email: string;
}

type UseKlaviyoSubscribeProps = {
  formId: string;
  setError: UseFormSetError<FormFields>;
}

const useKlaviyoSubscribe = ({ formId, setError }: UseKlaviyoSubscribeProps) => {
  const subscribe = async (data: FormFields) => {
    const requestPayload = {
      data: {
        type: 'subscription',
        attributes: {
          profile: {
            data: {
              type: 'profile',
              attributes: {
                email: data.email,
                properties: {
                  source: 'Website Footer'
                },
                subscriptions: {
                  email: {
                    marketing: {
                      consent: 'SUBSCRIBED'
                    }
                  }
                }
              }
            }
          }
        },
        relationships: {
          list: {
            data: {
              type: 'list',
              id: formId
            }
          }
        }
      }
    }

    const options = {
      method: 'POST',
      headers: {
        revision: '2024-10-15.pre',
        'content-type': 'application/vnd.api+json',
        Authorization: process.env.NEXT_PUBLIC_KLAVIYO_API_KEY || ''
      },
      body: JSON.stringify(requestPayload)
    }

    const SUCCESS_STATUS = 202
    try {
      const response = await fetch(
        `https://a.klaviyo.com/client/subscriptions?company_id=${process.env.NEXT_PUBLIC_KLAVIYO_API_KEY}`,
        options
      )
      const responseData = await response.text()
      if (response.status === SUCCESS_STATUS) {
        return
      }
      if (!response.ok) {
        throw new Error(responseData)
      }
    } catch (err) {
      setError('email', {
        type: 'submit',
        message: 'Failed to subscribe. Please try again.',
      })
      throw new Error('Failed to subscribe. Please try again.')
    }
  }

  return {
    subscribe
  }
}

export default useKlaviyoSubscribe
