import useIsMobile from './isMobile'

import { RefObject, useState } from 'react'

type AccordionProps = {
  title: string
  ref: RefObject<HTMLElement>
}

export type useSingleAccordionReturn = {
  isOpenedId: string | null
  handleToggle: (e: MouseE<PERSON>, { title, ref }: AccordionProps,) => void
}

export function useSingleAccordion(defaultOpenedId: string = '', closeAll: Boolean = false): useSingleAccordionReturn {
  const [isOpenedId, setIsOpenedId] = useState<string | null>(defaultOpenedId)
  const { isMobile } = useIsMobile()
  const HUNDRED = 100

  const scrollToAccordion = (ref: RefObject<HTMLElement>, isOpenedIdMain: string): void => {
    const scrolled = window.scrollY
    const { top } = ref.current!.getBoundingClientRect()

    const previousElement = document.getElementById(isOpenedIdMain)
    const previousDistance = previousElement?.getBoundingClientRect().top || 0
    const previousHeight = previousElement?.clientHeight || 0

    const isBelow = previousDistance < top
    let scrollDistance = top + scrolled - HUNDRED

    if (isBelow) {
      scrollDistance -= previousHeight
    }

    window.scrollTo({ top: scrollDistance })
  }

  const handleToggle = (e: MouseEvent, { title, ref }: AccordionProps): void => {
    if (title === isOpenedId && closeAll) {
      setIsOpenedId(null)
    } else {
      setIsOpenedId(title)
      if (ref.current && isMobile && isOpenedId) {
        scrollToAccordion(ref, isOpenedId)
      }
    }
  }

  return {
    isOpenedId,
    handleToggle,
  }
}

export default useSingleAccordion
