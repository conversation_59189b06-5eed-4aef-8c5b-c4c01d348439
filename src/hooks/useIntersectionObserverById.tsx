/* eslint-disable consistent-return */
import { useState, useEffect } from 'react'

type IntersectionObserverOptions = {
  root?: Element | null
  rootMargin?: string
  threshold?: number | number[]
  distance?: number
}

export default function useIntersectionObserverById(
  elementId: string,
  options: IntersectionObserverOptions = {}
): boolean {
  const [isVisible, setIsVisible] = useState<boolean>(false)

  useEffect(() => {
    const element = document.getElementById(elementId)
    if (!element) {
      return
    }

    const rootMargin = options.distance ? `0px 0px -${options.distance}px 0px` : options.rootMargin

    const observer = new IntersectionObserver(([entry]) => {
      if (entry.boundingClientRect.top >= 0) {
        setIsVisible(entry.boundingClientRect.top <= window.innerHeight && entry.isIntersecting)
      }
    }, { ...options, rootMargin, threshold: 0 })

    observer.observe(element)

    return () => {
      if (element) {
        observer.unobserve(element)
      }
    }
  }, [elementId, options])

  return isVisible
}
