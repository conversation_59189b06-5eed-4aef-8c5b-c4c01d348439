'use client'

import { findUrlExperimentById } from '@/providers/GrowthBook'

import { useExperiment as useExperimentGb } from '@growthbook/growthbook-react'

type ExperimentData = {
  key: string
  variations: [string, string, ...string[]];
  forceVariation?: number
}

export function useExperiment(exp: ExperimentData): { value: string | number | boolean | object } {
  const experimentByUrl = findUrlExperimentById(exp.key)
  const variantToForce = exp.variations.findIndex((variant) => variant === experimentByUrl?.variationId)

  const { value } = useExperimentGb({
    key: exp.key,
    variations: exp.variations,
    force: variantToForce ?? exp.forceVariation
  })

  return { value }
}

export default useExperiment

// testing-footer
