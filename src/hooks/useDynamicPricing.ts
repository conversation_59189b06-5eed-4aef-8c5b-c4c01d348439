import { CalloutProps } from '@components/Generic/Callout/types'
import formatCurrency from '@utils/formatCurrency'

import { useMemo } from 'react'
import { useCart } from '@shopify/hydrogen-react'

type Tier = {
  threshold: number
  callouts: CalloutProps[]
  discount: number
}

const tierList: Tier[] = [
  {
    threshold: 85,
    discount: 0.1,
    callouts: [
      {
        title: '10% Unlocked',
        settings: { theme: 'red700' },
        sys: { id: '10off' }
      }
    ]
  },
  {
    threshold: 425,
    discount: 0.15,
    callouts: [
      {
        title: '15% Unlocked',
        settings: { theme: 'red700' },
        sys: { id: '15off' }
      }
    ]
  },
  {
    threshold: 525,
    discount: 0.2,
    callouts: [
      {
        title: '20% Unlocked',
        settings: { theme: 'red700' },
        sys: { id: '20off' }
      }
    ]
  },
]

type UseDynamicPricingResult = {
  finalPrice: number
  callouts: CalloutProps[]
}

type UseDynamicPricingParams = {
  price: number
  compareAtPrice?: number
  enabled?: boolean
  showDiscountValue?: boolean
}

const UseDynamicPricing = ({
  price,
  compareAtPrice,
  enabled = true,
  showDiscountValue = false
}: UseDynamicPricingParams): UseDynamicPricingResult => {
  const { cost } = useCart()
  const cartTotal = cost?.totalAmount?.amount ? parseFloat(cost.totalAmount.amount) : 0

  return useMemo(() => {
    if (!enabled) {
      return { finalPrice: price, callouts: [] }
    }
    let finalPrice = price
    let calloutSelected: CalloutProps[] = []

    const applicableTier = [...tierList].reverse().find((tier) => {
      const updatedCartTotal = cartTotal * (1 + tier.discount)
      return (
        updatedCartTotal >= tier.threshold || price >= tier.threshold
      )
    })

    if (applicableTier) {
      calloutSelected = applicableTier.callouts.map((callout) => {
        const discountAmount = compareAtPrice ? compareAtPrice - (price - (price * applicableTier.discount)) : price * applicableTier.discount
        return {
          ...callout,
          title: showDiscountValue ? `Save ${formatCurrency(discountAmount)}` : callout.title
        }
      })
      finalPrice = price - (price * applicableTier.discount)
    }

    return { finalPrice, callouts: calloutSelected }
  }, [enabled, price, cartTotal, compareAtPrice, showDiscountValue])
}

export default UseDynamicPricing
