import { useState } from 'react'

const useFormFields = <T extends object>(initialState: T) => {
  const [fields, setFields] = useState(initialState)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setFields((prev) => ({
      ...prev,
      [id]: value,
    }))
  }

  return [fields, handleChange, setFields] as const
}

export default useFormFields
