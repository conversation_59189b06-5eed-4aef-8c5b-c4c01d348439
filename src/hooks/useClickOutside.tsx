import { RefObject, useEffect } from 'react'

export default function useClickOutside(
  ref: RefObject<HTMLElement>,
  callback: () => void,
  deps: any[] = []
) {
  useEffect(() => {
    let mouseDownInside = false

    const handleMouseDown = (event: MouseEvent) => {
      // Check if mousedown occurred inside the element
      mouseDownInside = !!ref.current?.contains(event.target as Node)
    }

    const handleMouseUp = (event: MouseEvent) => {
      // Only trigger if mousedown started outside the element
      if (
        !mouseDownInside &&
        ref.current &&
        !ref.current.contains(event.target as Node)
      ) {
        callback()
      }
      // Reset the flag
      mouseDownInside = false
    }

    document.addEventListener('mousedown', handleMouseDown)
    document.addEventListener('mouseup', handleMouseUp)

    return () => {
      document.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [ref, callback, ...deps])
}
