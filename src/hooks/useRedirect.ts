'use client'

import { useEffect } from 'react'
import { useSearchParams, redirect } from 'next/navigation'

const useRedirect = (ready?: boolean) => {
  const searchParams = useSearchParams()
  useEffect(() => {
    if (!ready) {
      return
    }
    const redirectParam = searchParams.get('redirect') || '/'
    const utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term']
      .map((param) => {
        const value = searchParams.get(param)
        return value ? `${param}=${value}` : null
      })
      .filter(Boolean)
    const newUrl = `${redirectParam}${utmParams.length > 0 ? `?${utmParams.join('&')}` : ''}`
    redirect(newUrl)
  }, [ready, searchParams])
}

export default useRedirect
