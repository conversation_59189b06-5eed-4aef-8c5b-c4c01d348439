import { useCart } from '@shopify/hydrogen-react'

export const NOTIFY_ME_PAGE_URL = '/caraway-5-years'

// TODO: change the specialVariants to the correct variant IDs
/* eslint-disable line-comment-position */
const limitedVariantIds = [
  '41312872038482', // Petite Cooker - Sky Blue - CW-PTCK-203
  '41312872136786', // Petite Cooker - Stone - CW-PTCK-209
  '41312872235090', // Petite Cooker - Brick Red - CW-PTCK-206
  '41312872202322', // Petite Cooker - Midnight - CW-PTCK-208
  '41312872071250', // Petite Cooker - Lavender - CW-PTCK-204
  '41312872169554', // Petite Cooker - Emerald - CW-PTCK-207
  '41312872104018', // Petite Cooker - Peach - CW-PTCK-205
]

export function isVariantLimited(variantId?: string) {
  if (!variantId) {
    return false
  }

  return limitedVariantIds.includes(variantId)
}

const MAX_LIMITED_ITEMS = 2

export default function usePurchaseLimit(variantId?: string) {
  const normalizedVariantId = variantId?.startsWith('gid://shopify/ProductVariant/')
    ? variantId
    : `gid://shopify/ProductVariant/${variantId}`

  const limitedVariants = limitedVariantIds.map((id) => `gid://shopify/ProductVariant/${id}`)

  const cart = useCart()
  const { lines } = cart

  const isPurchaseLimited = false

  if (!variantId) {
    return { isPurchaseLimited }
  }

  let totalLimitedVariantsInCart = 0

  if (!lines) {
    return { isPurchaseLimited }
  }

  lines.forEach((line) => {
    if (limitedVariants.find((id) => id === line?.merchandise?.id)) {
      totalLimitedVariantsInCart += line?.quantity ?? 0
    }
  })

  const isLimitedItemsLimitReached = totalLimitedVariantsInCart >= MAX_LIMITED_ITEMS
  const isVariantPurchaseLimited = limitedVariants.includes(normalizedVariantId)

  return {
    isPurchaseLimited: isLimitedItemsLimitReached && isVariantPurchaseLimited,
  }
}
