'use client'

import { useEffect, useState, useRef } from 'react'

const DEFAIULT_THRESHOLD = 0.1

function useIntersectionObserver({
  root = null,
  rootMargin = '0px',
  threshold = DEFAIULT_THRESHOLD,
} = {}) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const targetRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!targetRef.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
      },
      { root, rootMargin, threshold }
    )

    const currentTarget = targetRef.current
    observer.observe(currentTarget)
    // eslint-disable-next-line consistent-return
    return () => {
      observer.unobserve(currentTarget)
    }
  }, [root, rootMargin, threshold])

  return { isIntersecting, ref: targetRef }
}

export default useIntersectionObserver
