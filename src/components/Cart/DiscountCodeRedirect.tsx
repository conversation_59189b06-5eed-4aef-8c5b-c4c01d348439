'use client'

// import { SITEWIDE_DISCOUNT_CODE } from '@/utils/coupons'

import { useCart } from '@shopify/hydrogen-react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

type DiscountCodeProps = {
  code?: string;
  redirect?: string;
  children?: React.ReactNode;
}

const DiscountCodeRedirect = ({
  code,
  redirect = '/',
  children = null
}: DiscountCodeProps) => {
  const router = useRouter()
  const {
    id,
    discountCodesUpdate,
    cartCreate,
    cartReady,
    discountCodes
  } = useCart()

  // eslint-disable-next-line complexity
  useEffect(() => {
    if (!id || !cartReady) {
      cartCreate({})
    }

    // if the discount code is not the sitewide discount code
    // and the cart is ready, apply the discount code
    if (id && cartReady && code) {
      discountCodesUpdate([code])
      router.push(redirect)
    }

    // // if the discount code is the sitewide discount code
    // // and the cart is ready, and there are no discount codes
    // // apply the site-wide discount code
    // if (
    //   cartReady &&
    //   code === SITEWIDE_DISCOUNT_CODE &&
    //   discountCodes &&
    //   discountCodes.length === 0
    // ) {
    //   discountCodesUpdate([code])
    // }

    // `cartCreate`, `discountCodesUpdate`, `router` are stable
    // so no need to include them in the dependencies array
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, code, cartReady, discountCodes])

  return children
}

export default DiscountCodeRedirect
