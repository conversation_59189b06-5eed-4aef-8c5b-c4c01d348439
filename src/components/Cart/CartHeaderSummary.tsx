'use client'

import {
  globalTokens as $g,
  fontSizes,
  spacing,
  colors
} from '@/app/themeTokens.stylex'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'

import * as stylex from '@stylexjs/stylex'
import { useCart } from '@shopify/hydrogen-react'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  wrapperYourCartMain: {
    margin: '0 auto',
    maxWidth: $g.maxWidth,
    flexWrap: 'nowrap',
    alignItems: {
      default: 'center',
      [DESKTOP]: 'flex-start',
    },
    justifyContent: {
      default: 'center',
      [DESKTOP]: 'flex-start',
    },
  },
  wrapperYourCart: {
    width: '100%',
    maxWidth: '550px',
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: 'initial',
    },
  },
  circle: {
    display: 'flex',
    fontSize: fontSizes.xs,
    justifyContent: 'center',
    alignItems: 'center',
    width: 27,
    height: 27,
    borderRadius: '50%',
    borderColor: colors.navy,
    borderWidth: 2,
    borderStyle: 'solid',
    color: colors.navy,
    marginLeft: spacing.xs,
  },
})

const CartHeaderSummary = () => {
  const { cost, totalQuantity } = useCart()

  const amount = Number(cost?.totalAmount?.amount) || 0

  if (amount === 0) return null
  return (
    <Container as="div" flex flexRow alignCentered styleProp={[styles.wrapperYourCartMain]}>
      <Container as="div" flex flexRow alignCentered paddingBlock="2" styleProp={[styles.wrapperYourCart]}>
        <Typography as="h2" typographyTheme="h4Secondary" fontBold>
          Your Cart
        </Typography>
        <Typography as="span" styleProp={[styles.circle]}>
          <Typography as="span" fontBold typographyTheme="subheading">
            {totalQuantity}
          </Typography>
        </Typography>
      </Container>
    </Container>
  )
}

export default CartHeaderSummary
