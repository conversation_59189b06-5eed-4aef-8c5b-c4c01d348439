'use client'

import AfterpayLogo from '@assets/afterpay/afterpayLogo.svg'
import formatCurrency from '@utils/formatCurrency'
import Container from '@components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing } from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import AfterpayScript from '@/scripts/afterpay'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

type AfterpayInfoProps = {
  totalAmount: number;
  theme?: ThemeColors;
}

const styles = stylex.create({
  wrapper: {
    borderRadius: spacing.xs,
    marginBlock: spacing.sm,
    containerType: 'inline-size',
    alignItems: 'flex-end',
    rowGap: spacing.xxs,
    columnGap: spacing.xxs,

  },
  afterPayCopy: {
    textAlign: 'center',
    flexBasis: {
      default: 'unset',
      '@container (max-width: 475px)': '100%'
    },
  }
})

const AfterpayInfo = ({ totalAmount, theme = 'gray200' }: AfterpayInfoProps) => {
  const installments = 4
  const installmentAmount = totalAmount / installments

  return (
    <Container flex flexRow flexCentered flexWrap styleProp={styles.wrapper} paddingBlock="md" theme={theme}>
      <Typography as="span" size="bodySmall" styleProp={styles.afterPayCopy}>
        Pay 4 interest-free installments of {formatCurrency(installmentAmount)} by
      </Typography>
      <Image src={AfterpayLogo} alt="Afterpay Logo" width={72} height={14} />
      <button type="button" data-afterpay-modal="en_US">
        <Typography as="span" size="bodySmall" underline>More Info</Typography>
      </button>
      <AfterpayScript />
    </Container>
  )
}

export default AfterpayInfo
