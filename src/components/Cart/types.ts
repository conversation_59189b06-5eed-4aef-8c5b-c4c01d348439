export type MerchandiseProps = {
  availableForSale: boolean;
  compareAtPrice?: {
    amount: number;
  };
  id: string;
  image: {
    altText?: string;
    height: number;
    id: string;
    url: string;
    width: number;
  };
  price: {
    currencyCode: string;
    amount: string;
  };
  product: {
    handle: string;
    id: string;
    title: string;
  };
  requiresShipping: true;
  selectedOptions: {
    name: string;
    value: string;
  }[];
  title: string;
};

export type LineItemProp = {
  id: string;
  quantity: number;
  merchandise: MerchandiseProps;
  cost: any
  attributes?: {
    key: string
    value: string
  }[]
}
