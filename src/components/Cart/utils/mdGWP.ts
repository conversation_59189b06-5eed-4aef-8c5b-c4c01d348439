import { LineItemProp } from '../types'

import { rejectOffer, Size } from '@/redux/features/promo/gwp'
import { useAppDispatch, useAppSelector } from '@/redux/hooks'

import { Cart, useCart } from '@shopify/hydrogen-react'
import { CartLineInput } from '@shopify/hydrogen-react/storefront-api-types'

import type { RootState } from '@/redux/store'

export const mdGWPSizes: Size[] = [
  { label: 'XS/S', variantId: '42011844837458' },
  { label: 'M/L', variantId: '42011844870226' },
  { label: 'XL/XXL', variantId: '42011844902994' },
]

export const getSizeByVariantId = (variantId: string) => {
  const selectedSize = mdGWPSizes.find((s) => s.variantId === variantId)
  return selectedSize || null
}

export function getVariantIdFromSize(size: Size['label']) {
  const selectedSize = mdGWPSizes.find((s) => s.label === size)
  return selectedSize ? selectedSize.variantId : ''
}

export async function removePromoLineItems(
  lines: Cart['lines'],
  linesRemove: (lines: string[]) => void,
  newVariantId?: string,
) {
  if (!lines) return

  const gwpItems = lines.filter((item) => {
    const variantId = item?.merchandise?.id?.split('/').pop()
    return variantId
      ? mdGWPSizes.some((s) => s.variantId === variantId)
      : false
  })

  if (gwpItems.length > 0) {
    const linesToRemove = gwpItems
      .filter(
        (item) => item?.merchandise?.id !==
          `gid://shopify/ProductVariant/${newVariantId}`
      )
      .map((item) => item?.id)
      .filter((item) => item !== undefined)

    linesRemove(linesToRemove)
  }
}

export async function updatePromoLineItem(
  lines: Cart['lines'],
  linesRemove: (lines: string[]) => void,
  linesAdd: (lines: CartLineInput[]) => void,
  newVariantId: string,
) {
  if (!lines) return

  await removePromoLineItems(lines, linesRemove)

  await new Promise((resolve) => {
    const SECOND = 1000
    setTimeout(resolve, SECOND)
  })

  linesAdd([
    {
      merchandiseId: `gid://shopify/ProductVariant/${newVariantId}`,
    },
  ])
}

export function useMDGWP(lineItems: LineItemProp[]) {
  const selectedSize = useAppSelector((state: RootState) => state.gwp.selectedSize)
  const isGWPEligible = useAppSelector((state: RootState) => state.gwp.isEligible)
  const isGWPRejected = useAppSelector((state: RootState) => state.gwp.offerRejected)
  const gwpSizes = mdGWPSizes.map((size) => `gid://shopify/ProductVariant/${size.variantId}`)
  const lines = lineItems.filter((line) => !gwpSizes.includes(line?.merchandise?.id))
  const gwpLines = lineItems.filter((line) => gwpSizes.includes(line?.merchandise?.id)) as Cart['lines']
  const dispatch = useAppDispatch()
  const { linesRemove } = useCart()

  function rejectMDGWPOffer() {
    dispatch(rejectOffer())
    localStorage.setItem('gwpOfferRejected', 'true')
    removePromoLineItems(gwpLines, linesRemove)
  }

  return {
    selectedSize,
    isGWPEligible,
    isGWPRejected,
    lines,
    rejectMDGWPOffer,
  }
}
