import { useMDGWP } from './mdGWP'

import LineItemFreeWithSizeSelection from '../LineItemFreeWithSizeSelection'
import { LineItemProp } from '../types'
import LineItemRedeemGift from '../LineItemRedeemGift'

import CallToAction from '@/components/Generic/CallToAction'
import { colors } from '@/app/themeTokens.stylex'
import { useDialogContext } from '@/providers/config/DialogContext'
import { resetOffer } from '@/redux/features/promo/gwp'
import { useAppDispatch } from '@/redux/hooks'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

const styles = stylex.create({
  redeemGiftCTA: {
    display: 'inline-block',
    textAlign: 'left',
    width: 'auto',
  },
  selectSizeCta: {
    width: 'min-content',
    whiteSpace: 'nowrap',
    fontWeight: 400,
    color: colors.perracotta,
  },
  editSizeCta: {
    width: 'min-content',
    whiteSpace: 'nowrap',
    fontWeight: 400,
    color: colors.gray,
  },
  freeLineItemImage: {
    objectFit: 'contain',
    width: {
      default: '90px',
      '@media (min-width: 768px)': '145px',
    },
    height: {
      default: '90px',
      '@media (min-width: 768px)': '100px',
    },
  },
})

export function SelectSizeLineItem({ lines } : { lines: LineItemProp[] }) {
  const { selectedSize, rejectMDGWPOffer } = useMDGWP(lines)
  const { triggerDialog } = useDialogContext()

  return (
    <LineItemFreeWithSizeSelection
      price={138}
      image={(
        <Image
          src="https://cdn.shopify.com/s/files/1/0258/6273/3906/files/sweatshirt_430x.jpg?v=1743513532#"
          alt="Free Sweatshirt"
          width={145}
          height={100}
          {...stylex.props([styles.freeLineItemImage])}
        />
      )}
      customMessage="Gift with Purchase Unlocked"
      productName="Home Cook Sweatshirt"
      selectedSize={selectedSize?.label || null}
      selectSizeCTA={(
        <CallToAction
          variant="underlined"
          onClick={() => triggerDialog('SweatshirtSizeSelection')}
          styleProp={
            selectedSize
              ? styles.editSizeCta
              : styles.selectSizeCta
          }
        >
          {selectedSize ? 'Edit Size' : 'Select Size'}
        </CallToAction>
      )}
      onRemoveItem={rejectMDGWPOffer}
    />
  )
}

export function RedeemGiftLineItemCard() {
  const { triggerDialog } = useDialogContext()
  const dispatch = useAppDispatch()

  return (
    <LineItemRedeemGift
      icon={(
        <Image
          src="/assets/large-gift-circle-icon.svg"
          alt=""
          width={61}
          height={60}
        />
      )}
      text="You’ve unlocked a free gift with purchase! Claim your 100% organic cotton sweatshirt by Caraway x MATE."
      callToAction={(
        <CallToAction
          variant="underlined"
          size="small"
          onClick={() => {
            triggerDialog('SweatshirtSizeSelection')
            dispatch(resetOffer())
            localStorage.removeItem('gwpOfferRejected')
          }}
          useArrow
          styleProp={styles.redeemGiftCTA}
        >
          Redeem Free Gift
        </CallToAction>
      )}
    />
  )
}
