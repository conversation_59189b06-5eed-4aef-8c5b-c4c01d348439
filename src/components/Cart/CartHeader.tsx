import {
  colors,
  fontSizes,
  spacing,
} from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import { useAppDispatch } from '@redux/hooks'
import { close } from '@redux/features/cart/cartSlice'
import CallToAction from '@components/Generic/CallToAction'
import Close from '@components/Generic/Icon/lib/Close'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  wrapper: {
    paddingBlock: spacing.sm,
    paddingInline: spacing.sm,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  circle: {
    display: 'flex',
    fontSize: fontSizes.xs,
    justifyContent: 'center',
    alignItems: 'center',
    width: 32,
    height: 32,
    borderRadius: '50%',
    borderColor: colors.navy,
    borderWidth: 1,
    borderStyle: 'solid',
    color: colors.navy,
    marginLeft: 'auto'
  },
})

const CartHeader = ({ totalQuantity }: { totalQuantity: number }) => {
  const dispatch = useAppDispatch()
  return (
    <Container
      as="header"
      flex
      flexRow
      alignCentered
      gap="2"
      paddingBlock="2"
      contentGap
      theme="white"
      boxShadow
    >
      <CallToAction
        onClick={() => dispatch(close())}
        size="inline"
        variant="transparent"
      >
        <Close size="small" />
      </CallToAction>
      <Typography
        as="span"
        fontSecondary
        fontBold
        size="md"
      >
        Your Cart
      </Typography>
      <span {...stylex.props(styles.circle)}>
        <Typography
          as="span"
          fontBold
          size="md"
          styleProp={{ marginTop: '-2px' }}
        >
          {totalQuantity}
        </Typography>
      </span>
    </Container>
  )
}

export default CartHeader
