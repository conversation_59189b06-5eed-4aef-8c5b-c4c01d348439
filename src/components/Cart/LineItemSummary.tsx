'use client'

import { LineItemProp } from './types'
import ProductRecommender from './ProductRecommender'

import { spacing, colors } from '@/app/themeTokens.stylex'
import Container from '@components/layout/Container'
import LineItem from '@components/Cart/LineItem'

import * as stylex from '@stylexjs/stylex'
import { useCart } from '@shopify/hydrogen-react'
import { useMemo } from 'react'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  wrapperItems: {
    flex: '1',
    overflow: 'hidden',
    width: '100%',
    maxWidth: {
      default: '100%',
      '@media (min-width: 550px) and (max-width: 1023px)': '550px',
      [DESKTOP]: '100%',
    },
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: 'initial',
    },
  },
  line: {
    height: '1px',
    width: '100%',
    marginTop: spacing.xl,
    marginBottom: spacing.sm,
    backgroundColor: colors.gray300,
  }
})

type LineItemSummaryProps = {
  productsWithAnIncludedItem: {
    name: string,
    productId: string,
    productMetadataCollection: {
      items: {
        type: string
      }[]
    }[]
  }[]
}

const LineItemSummary = ({ productsWithAnIncludedItem }: LineItemSummaryProps) => {
  const {
    lines,
    status,
    cost
  } = useCart()

  const amount = Number(cost?.totalAmount?.amount) || 0

  const renderLineItems = useMemo(
    () => lines?.map((line) => (
      <LineItem
        key={line?.id}
        line={line as LineItemProp}
        status={status}
        productsWithAnIncludedItem={productsWithAnIncludedItem}
      />
    )),
    [lines, status]
  )

  if (amount === 0) return null

  return (
    <Container as="div" styleProp={[styles.wrapperItems]}>
      <Container as="div" flex gap="3">
        {renderLineItems}
      </Container>
      <hr {...stylex.props(styles.line)} />
      {lines && <ProductRecommender lineItems={lines as any} layout="double" />}
    </Container>
  )
}

export default LineItemSummary
