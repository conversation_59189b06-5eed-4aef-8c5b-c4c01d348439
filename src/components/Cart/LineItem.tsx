import { LineItemProp } from './types'

import RenderIf from '../Generic/RenderIf'
import RightArrow from '../Generic/Icon/lib/RightArrow'

import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import { incrementByAmount, removeFromCart } from '@redux/features/cart/cartSlice'
import {
  spacing,
  colors,
  globalTokens as $
} from '@/app/themeTokens.stylex'
import { useAppDispatch } from '@redux/hooks'
import formatCurrency from '@utils/formatCurrency'
import Skeleton from '@components/Generic/Skeleton'
import CallToAction from '@components/Generic/CallToAction'
import Close from '@components/Generic/Icon/lib/Close'
import { empty } from '@/utils/checking'
import usePurchaseLimit from '@/hooks/usePurchaseLimit'
import { MARCH_PROMO_VARIANT_IDS } from '@/utils/marchPromo'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import {
  useCart,
  parseGid
} from '@shopify/hydrogen-react'
import Link from 'next/link'
import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'
import { useState } from 'react'

const styles = stylex.create({
  lineItemWrapper: {
    backgroundColor: colors.offWhite,
    borderRadius: $.borderRadiusLarge,
    overflow: 'hidden',
    padding: {
      default: spacing.xxs,
      '@media (min-width: 768px)': spacing.sm
    }
  },
  lineItem: {
    display: 'flex',
    alignItems: 'start',
    justifyContent: 'space-between',
    padding: {
      default: spacing.sm,
      '@media (min-width: 768px)': spacing.md
    },
    position: 'relative',
    width: '100%',
  },
  removeButton: {
    position: 'absolute',
    top: spacing.xs,
    left: spacing.xs,
    cursor: 'pointer',
  },
  freeIconSize: {
    width: {
      default: '18px',
      '@media (min-width: 768px)': '34px'
    },
    height: {
      default: '18px',
      '@media (min-width: 768px)': '34px'
    },
  },
  lineCartImage: {
    marginRight: spacing.xxs
  },
  lineCartImageSize: {
    objectFit: 'contain',
    width: {
      default: '90px',
      '@media (min-width: 768px)': '145px'
    },
    height: {
      default: '90px',
      '@media (min-width: 768px)': '100px'
    },
  },
  dl: {
    width: 'clamp(45px, 30vw, 165px)',
    display: 'flex',
    marginRight: 'auto',
    marginLeft: {
      default: spacing.md,
      '@media (max-width: 768px)': spacing.xxs
    },
    flexDirection: 'column',
    gap: spacing.xs,
  },
  quantityControl: {
    display: 'flex',
    justifyContent: 'space-around',
    alignItems: 'center',
    gap: spacing.xs,
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.gray300,
    borderRadius: '18px',
    width: '85px',
    height: '24px',
    marginTop: spacing.xxs,
    padding: spacing.xs,
  },
  quantityControlColor: {
    color: colors.navy
  },
  quantityControlColorDisabled: {
    color: colors.gray300,
    cursor: 'not-allowed',
    pointerEvents: 'none',
  },
  highlight: {
    color: colors.perracotta,
  },
  lineThrough: {
    textDecoration: 'line-through'
  },
  prices: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'end',
  },
  priceContent: {
    display: 'flex',
    justifyContent: 'flex-end',
  },
  lineItemDetails: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: '0.25rem',
    marginTop: '5px',
  },
  lineItemDetail: {
    display: 'flex',
    alignItems: 'center',
    gap: '0.2rem',
    textAlign: 'right',
  },
  lineItemCopy: {
    fontSize: {
      default: '12px',
      '@media (max-width: 768px)': '10px'
    },
  },
  lineItemDetailRed: {
    color: colors.red700
  },
  shopifyDiscountCopy: {
    color: colors.red700
  },
  redCustomMessage: { color: colors.red700 },
})

const marchPromoStyles = stylex.create({
  toggleHeader: {
    paddingBlock: '11px',
    paddingInline: {
      default: '14px',
      '@media (min-width: 768px)': '24px',
    },
    cursor: 'pointer',
    backgroundColor: colors.skyBlue,
    gap: '5px',
  },
  toggleText: {
    fontWeight: 700,
  },
  toggleContent: {
    padding: '20px 24px',
  },
  toggleArrow: {
    transform: 'rotate(90deg)',
  },
  toggleArrowExpanded: {
    transform: 'rotate(-90deg)',
  },
})

type LineItemProps = {
  line: LineItemProp;
  status: string;
  productsWithAnIncludedItem?: {
    name: string,
    productId: string,
    productMetadataCollection: {
      items: {
        type: string
      }[]
    }[]
  }[];
  productsCartItemsDetails?: any;
};

const getCustomMsgEntries = (filteredProductDetails: any, line: any) => {
  if (filteredProductDetails.length > 0) {
    const productMetadata = filteredProductDetails[0].productMetadataCollection?.items || []

    const customMsgEntries = productMetadata.reduce((acc: any, meta: any) => {
      const {
        type,
        references,
        description
      } = meta
      if (type.includes('Variant Custom Message')) {
        references.items.forEach(({ slug }: { slug: string }) => {
          if (empty(acc[slug])) {
            acc[slug] = []
          }

          acc[slug].push({
            type,
            description,
          })
        })
      }
      return acc
    }, {})

    return customMsgEntries[line?.merchandise?.selectedOptions?.[0]?.value.toLocaleLowerCase() as string] || null
  }
  return null
}

// eslint-disable-next-line complexity
const LineItem = ({
  line,
  status,
  productsWithAnIncludedItem,
  productsCartItemsDetails,
}: LineItemProps) => {
  const {
    linesRemove, linesUpdate
  } = useCart()
  const dispatch = useAppDispatch()
  const { attributes } = line
  const { isPurchaseLimited } = usePurchaseLimit(line?.merchandise?.id)
  const [toggleExpanded, setToggleExpanded] = useState(false)

  const filteredProductDetails = productsCartItemsDetails?.filter((item: any) => item.slug === line.merchandise.product.handle) || []

  const selectedCustomMsg = getCustomMsgEntries(filteredProductDetails, line)

  // TODO: Optimize and refactor the dispatch object since it's being reused. Can we use a custom hook?
  const handleIncrementQuantity = (item: any) => {
    linesUpdate([{ id: item?.id, quantity: item.quantity + 1 }])
    dispatch(incrementByAmount({ variantId: Number(parseGid(item?.merchandise?.id).id), quantity: 1 }))
  }

  const handleDecrementQuantity = (item: any, remove = false) => {
    if (remove) {
      linesRemove([item?.id ?? ''])
      dispatch(removeFromCart({
        lineItem: {
          imageUrl: item?.merchandise?.image?.url,
          quantity: item.quantity,
          productId: item?.merchandise?.product?.id,
          lineItemId: item?.id,
          name: item?.merchandise?.product?.title,
          variant: item?.merchandise?.selectedOptions?.[0]?.value,
          variantId: item?.merchandise?.id,
          currency: item?.cost?.totalAmount?.currencyCode,
          amount: item?.cost?.totalAmount?.amount,
        }
      }))
    } else {
      linesUpdate([{ id: item?.id, quantity: item.quantity - 1 }])
    }
  }

  const loading = status === 'updating' && true

  const price = Number(line?.cost?.totalAmount?.amount || 0)
  const compareAtPrice = line.cost.compareAtAmountPerQuantity ? Number(line.cost.compareAtAmountPerQuantity.amount * line.quantity) : 0
  const isDiscounted = price < compareAtPrice

  const ONE_HUNDRED = 100
  const setSavingsDiscount = (Number(line?.merchandise?.compareAtPrice?.amount) - Number(line?.merchandise?.price?.amount)) * (line?.quantity ?? 0)

  const hasIncludedItem = productsWithAnIncludedItem?.find((productWithAnIncludedItem) => line.merchandise.product.id.includes(productWithAnIncludedItem.productId))

  const shopifyDiscountApplied = Math.round(ONE_HUNDRED * (1 - Number(line?.cost?.totalAmount?.amount) / (Number(line?.merchandise?.price?.amount) * (line?.quantity ?? 0))))

  const isPromoItem = 'gid://shopify/ProductVariant/41258519593042'
  const PROMO_ITEM_MESSAGE = 'Hooray! Your redemption code will be emailed to you in 7 days after purchase.'

  const estimatedShippingDate = attributes?.find((attribute) => attribute?.key === 'Est. Ship Date')?.value

  const AVOID_REROUTE_SLUGS = ['kitchen-gadgets-promo']

  const IS_MARCH_PROMO_ITEM = MARCH_PROMO_VARIANT_IDS.includes(line?.merchandise?.id)

  const selectedOptionsValue = line?.merchandise?.selectedOptions?.[0]?.value
  return (
    <Container styleProp={styles.lineItemWrapper}>
      <div key={line?.id} {...stylex.props(styles.lineItem)}>
        {line?.merchandise?.id !== isPromoItem ? (
          <CallToAction
            onClick={() => line?.merchandise?.product?.handle !== 'kitchen-gadgets-promo' &&
              handleDecrementQuantity(line, true)}
            size="small"
            variant="secondary"
            {...stylex.props(styles.removeButton)}
          >
            <Close size="small" />
          </CallToAction>
        ) : (
          <Image
            src="/assets/Free-Gift-Icon.svg"
            alt="Free Kitchen Gadget Promo Item"
            width={18}
            height={18}
            {...stylex.props([styles.removeButton, styles.freeIconSize])}
          />
        )}

        <Link
          href={
            !AVOID_REROUTE_SLUGS.includes(line?.merchandise?.product?.handle)
              ? `/products/${line?.merchandise?.product?.handle}`
              : '#'
          }
        >
          <Image
            src={line?.merchandise?.image?.url ?? ''}
            alt={line?.merchandise?.product?.title ?? ''}
            width={145}
            height={100}
            {...stylex.props([styles.lineCartImage, styles.lineCartImageSize])}
            priority
          />
        </Link>
        <dl {...stylex.props(styles.dl)}>
          <Typography as="h2" size="xs" fontBold>
            {line?.merchandise?.product?.title}
          </Typography>
          {line?.merchandise?.id === isPromoItem ? (
            <Typography as="p" typographyTheme="captionSmall">
              {PROMO_ITEM_MESSAGE}
            </Typography>
          )
            : selectedOptionsValue !== 'Default Title' && (
              <Typography
                as="p"
                typographyTheme="bodySmall"
                typographyThemeMobile="captionLarge"
              >
                {selectedOptionsValue}
              </Typography>
            )}
          {estimatedShippingDate ? (
            <Typography
              as="p"
              typographyTheme="captionLarge"
              typographyThemeMobile="captionSmall"
            >
              Est. Ship Date: {estimatedShippingDate}
            </Typography>
          ) : (
            <RenderIf condition={selectedCustomMsg}>
              {selectedCustomMsg?.map((msg: any) => (
                <Typography
                  as="span"
                  typographyTheme="captionSmall"
                  styleProp={[
                    msg?.type?.includes('Alert') && styles.redCustomMessage,
                  ]}
                >
                  {documentToPlainTextString(msg?.description?.json)}
                </Typography>
              ))}
            </RenderIf>
          )}
          {line?.merchandise?.id !== isPromoItem && (
            <div
              {...stylex.props([
                styles.quantityControl,
                styles.quantityControlColor,
              ])}
            >
              <button
                type="button"
                onClick={() => handleDecrementQuantity(line, line?.quantity === 1)}
                {...stylex.props(styles.quantityControlColor)}
              >
                -
              </button>
              <Typography as="p" size="xs">
                {line?.quantity}
              </Typography>
              <button
                type="button"
                onClick={() => handleIncrementQuantity(line)}
                disabled={isPurchaseLimited}
                {...stylex.props(
                  styles.quantityControlColor,
                  isPurchaseLimited && styles.quantityControlColorDisabled
                )}
              >
                +
              </button>
            </div>
          )}
        </dl>
        <div {...stylex.props(styles.prices)}>
          <Container as="div" flex flexRow gap="xxs" styleProp={styles.priceContent}>
            <Typography
              as="h2"
              size="sm"
              fontBold
              styleProp={isDiscounted && styles.highlight}
            >
              <Skeleton
                isLoaded={!loading}
                animation="shimmer"
                width={65}
                height={18}
              >
                {price === 0 ? 'FREE' : formatCurrency(price)}
              </Skeleton>
            </Typography>
            {isDiscounted && (
              <Typography as="p" size="xs" styleProp={styles.lineThrough}>
                <Skeleton
                  isLoaded={!loading}
                  animation="shimmer"
                  width={75}
                  height={18}
                >
                  {formatCurrency(compareAtPrice)}
                </Skeleton>
              </Typography>
            )}
          </Container>

          <div {...stylex.props(styles.lineItemDetails)}>
            {setSavingsDiscount > 0 && status === 'idle' && (
              <div {...stylex.props(styles.lineItemDetail)}>
                <Typography as="p" size="xxs" styleProp={styles.lineItemCopy}>
                  ${setSavingsDiscount} Set Savings
                </Typography>
                <Image
                  src="/assets/black-check-mark.svg"
                  alt="Free Organizer Check Mark"
                  width={12}
                  height={11}
                />
              </div>
            )}
            {hasIncludedItem && status === 'idle' && (
              <div {...stylex.props(styles.lineItemDetail)}>
                <Typography as="p" size="xxs" styleProp={styles.lineItemCopy}>
                  Free Organizer
                </Typography>
                <Image
                  src="/assets/black-check-mark.svg"
                  alt="Free Organizer Check Mark"
                  width={12}
                  height={11}
                />
              </div>
            )}
            {!!shopifyDiscountApplied && status === 'idle' && (
              <div {...stylex.props(styles.lineItemDetail)}>
                <Typography
                  as="p"
                  size="xxs"
                  styleProp={[styles.shopifyDiscountCopy, styles.lineItemCopy]}
                >
                  {shopifyDiscountApplied}% Off Applied
                </Typography>
                <Image
                  src="/assets/red-check-mark.svg"
                  alt="Free Organizer Check Mark"
                  width={12}
                  height={11}
                />
              </div>
            )}
            {isPurchaseLimited && (
              <div
                {...stylex.props(
                  styles.lineItemDetail,
                  styles.lineItemDetailRed
                )}
              >
                <Typography as="p" size="xxs" styleProp={styles.lineItemCopy}>
                  Limited to 2 per person
                </Typography>
                <svg
                  width="11"
                  height="12"
                  viewBox="0 0 11 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0.6875 5.84375L3.64904 9.125L10.3125 2.5625"
                    stroke="currentColor"
                    strokeWidth="1.2375"
                    strokeLinecap="round"
                  />
                </svg>
              </div>
            )}
          </div>
        </div>
      </div>
      {IS_MARCH_PROMO_ITEM && (
        <Container flex>
          <Container
            styleProp={marchPromoStyles.toggleHeader}
            flex
            flexRow
            noWrap
            alignCentered
            spaceBetween
            gap="2"
            onClick={() => setToggleExpanded((prev) => !prev)}
          >
            <Container flex flexRow noWrap alignCentered gap="1">
              <Image src="/eco-friendly.svg" alt="" width={24} height={24} />
              <Typography
                as="p"
                styleProp={marchPromoStyles.toggleText}
                typographyTheme="bodySmall"
                typographyThemeMobile="bodyXSmall"
              >
                A Healthier Choice for You and the Planet
              </Typography>
            </Container>
            <RightArrow
              dimensions="16"
              styleProp={[
                marchPromoStyles.toggleArrow,
                toggleExpanded && marchPromoStyles.toggleArrowExpanded,
              ]}
            />
          </Container>
          {toggleExpanded && (
            <Container styleProp={marchPromoStyles.toggleContent}>
              <Typography as="p" typographyTheme="bodySmall">
                Congratulations! You’re one step closer to a microplastic-free
                home. This simple swap avoids thousands of microplastics every
                time you cook.
              </Typography>
            </Container>
          )}
        </Container>
      )}
    </Container>
  )
}

export default LineItem
