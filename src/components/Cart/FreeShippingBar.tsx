import {
  colors,
  spacing
} from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'

import { useCart } from '@shopify/hydrogen-react'
import stylex from '@stylexjs/stylex'

type FreeShippingBarProps = {
  barWidth?: string;
  contentAlign?: 'center' | 'left' | 'right';
};

const styles = stylex.create({
  wrapper: {
    paddingBlockStart: spacing.md,
  },
  wrapperCenter: {
    alignItems: 'center',
  },
  wrapperLeft: {
    alignItems: 'flex-start',
  },
  wrapperRight: {
    alignItems: 'flex-end',
  },
})

const FreeShippingBar = (
  { barWidth = '360px', contentAlign = 'center' }: FreeShippingBarProps
) => {
  const {
    cost,
  } = useCart()
  let amount = 0

  if (cost?.subtotalAmount) {
    amount = Number(cost.subtotalAmount.amount)
  }

  const freeShippingThreshold = 90
  const MAX_PROGRESS = 100
  const DECIMAL_PLACES = 2
  const progressPercentage = Math.min((amount / freeShippingThreshold) * MAX_PROGRESS, MAX_PROGRESS)
  const wrapperStyles = [
    styles.wrapper,
    contentAlign === 'center' && styles.wrapperCenter,
    contentAlign === 'left' && styles.wrapperLeft,
    contentAlign === 'right' && styles.wrapperRight,
  ]

  return (
    <Container
      as="div"
      flexCentered
      styleProp={wrapperStyles}
    >
      <Typography as="p" size="sm" marginBottom="sm">
        {amount < freeShippingThreshold
          ? `Almost there! You're $${(freeShippingThreshold - amount).toFixed(DECIMAL_PLACES)} away from free shipping`
          : 'Voila! Free shipping unlocked!'}
      </Typography>
      <div style={{
        width: barWidth, backgroundColor: colors.gray300, borderRadius: '8px', overflow: 'hidden'
      }}
      >
        <div style={{
          width: `${progressPercentage}%`, backgroundColor: colors.sage, height: '10px', transition: 'width 0.3s ease-in-out'
        }}
        />
      </div>
    </Container>
  )
}

export default FreeShippingBar
