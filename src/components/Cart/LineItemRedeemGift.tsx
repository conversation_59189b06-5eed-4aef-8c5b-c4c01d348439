import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import {
  spacing,
  colors,
  globalTokens as $
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import { ReactNode } from 'react'

const styles = stylex.create({
  lineItemWrapper: {
    backgroundColor: colors.offWhite,
    borderRadius: $.borderRadiusLarge,
    overflow: 'hidden',
  },
  lineItem: {
    padding: {
      default: spacing.sm,
      '@media (min-width: 768px)': spacing.md
    },
    position: 'relative',
    width: '100%',
  },
  content: {
    alignItems: 'flex-start',
  },
  text: {
    lineHeight: '22px',
  },
})

type LineItemRedeemGiftProps = {
  text?: string;
  icon?: ReactNode;
  callToAction?: ReactNode;
};

const LineItemRedeemGift = ({
  text,
  icon,
  callToAction,
}: LineItemRedeemGiftProps) => {
  if (!icon && !text && !callToAction) {
    return null
  }

  return (
    <Container theme="cream" styleProp={styles.lineItemWrapper}>
      <Container flex flexRow noWrap gap="2" styleProp={styles.lineItem}>
        {icon}
        {(text || callToAction) && (
          <Container flex gap="1" styleProp={styles.content}>
            {text && (
              <Typography as="p" size="bodySmall" styleProp={styles.text}>
                {text}
              </Typography>
            )}
            {callToAction}
          </Container>
        )}
      </Container>
    </Container>
  )
}

export default LineItemRedeemGift
