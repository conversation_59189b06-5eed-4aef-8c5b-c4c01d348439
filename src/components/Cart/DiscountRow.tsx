import { colors, spacing } from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import CallToAction from '@components/Generic/CallToAction'
import Close from '@components/Generic/Icon/lib/Close'
import { useAppDispatch } from '@redux/hooks'
import { discountDenied, discountApplied, discountRemoved } from '@redux/features/events/eventsSlice'
import { RootState } from '@redux/store'

import { useSelector } from 'react-redux'
import { useEffect, useState, useCallback } from 'react'
import { useCart } from '@shopify/hydrogen-react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  removeFormWrapper: {
    paddingTop: spacing.sm,
  },
  discountLabel: {
    backgroundColor: colors.gray200,
    textTransform: 'uppercase',
    paddingBlock: spacing.xxs,
    paddingInline: spacing.sm,
    borderRadius: spacing.sm,
    display: 'flex',
    width: 'fit-content',
    gap: spacing.sm,
    alignItems: 'center',
  },
  discountLabelError: {
    color: colors.perracotta,
  },
  removeForm: {
    display: 'flex',
  },
  removeButton: {
    cursor: 'pointer',
  },
})

const DiscountRow = () => {
  const { discountCodesUpdate, discountCodes = [] } = useCart()
  const [discountError, setDiscountError] = useState('')
  const dispatch = useAppDispatch()
  const cartState = useSelector((state: RootState) => state.cart)
  const code = discountCodes[0]?.code

  useEffect(() => {
    setDiscountError('')
    if (discountCodes.length && !discountCodes[0]?.applicable) {
      const errorMessage = `This discount code: "${code}" is not applicable`
      setDiscountError(errorMessage)
      dispatch(discountDenied({ couponName: code, reason: errorMessage, cartId: cartState.cart.cartId }))
    } else if (discountCodes.length && discountCodes[0]?.applicable) {
      dispatch(discountApplied({ couponName: code, cartId: cartState.cart.cartId }))
    }
  }, [cartState.cart.cartId, code, discountCodes, dispatch])

  const handleRemoveDiscount = useCallback(
    (e: { preventDefault: () => void }) => {
      e.preventDefault()
      discountCodesUpdate([])
      dispatch(discountRemoved({ couponName: code, cartId: cartState.cart.cartId }))
    },
    [cartState.cart.cartId, code, discountCodesUpdate, dispatch]
  )

  if (!discountCodes.length || !code) {
    return null
  }

  return (
    <Container as="div" styleProp={styles.removeFormWrapper} theme="white">
      {!discountError ? (
        <Typography as="span" typographyTheme="captionLarge" styleProp={[styles.discountLabel]}>
          {code}
          <form onSubmit={handleRemoveDiscount} {...stylex.props(styles.removeForm)}>
            <CallToAction
              submit
              size="small"
              variant="secondary"
              {...stylex.props(styles.removeButton)}
            >
              <Close size="small" />
            </CallToAction>
          </form>
        </Typography>
      ) : (
        <Typography as="span" fontBold typographyTheme="captionLarge" styleProp={[styles.discountLabelError]}>
          {discountError}
        </Typography>
      )}
    </Container>
  )
}

export default DiscountRow
