'use client'

import <PERSON><PERSON><PERSON>ooter from './CartFooter'
import Car<PERSON><PERSON>eader from './CartHeader'
import Cart<PERSON>ain from './CartMain'
import { responsive } from './token.stylex'
import { LineItemProp } from './types'

import CartViewedEvent from '@/app/cart/CartViewedEvent'
import {
  globalTokens as $,
  colors
} from '@/app/themeTokens.stylex'

import { useEffect } from 'react'
import { useCart } from '@shopify/hydrogen-react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  wrapper: {
    display: 'flex',
    position: 'fixed',
    overflow: 'hidden',
    right: 0,
    width: responsive.miniCartWidth,
    flexDirection: 'column',
    height: '100dvh',
    backgroundColor: colors.mist200,
    boxShadow: $.boxShadow,
    transform: `translateX(${responsive.miniCartWidth})`,
    transition: $.transitionSmooth,
    pointerEvents: 'visible',
  },
  open: {
    transform: 'translateX(0)'
  },
})

type CartPanelProps = {
  open: boolean;
  productsWithAnIncludedItem: {
    name: string,
    productId: string,
    productMetadataCollection: {
      items: {
        type: string
      }[]
    }[]
  }[];
  productsCartItemsDetails?: any;
}

const CartWrapper = ({ children, open }: {
  children: React.ReactNode;
  open: boolean;
}) => (
  <div
    {...stylex.props(
      styles.wrapper,
      open && styles.open
    )}
  >
    { children }
  </div>
)

const compareAtPriceTotals = (lines: LineItemProp[]) => lines.reduce((acc, line) => acc +
      (Number(line?.merchandise?.compareAtPrice?.amount) ||
        Number(line?.merchandise?.price?.amount)) *
        line.quantity, 0)

const CartPanel = ({
  open,
  productsWithAnIncludedItem,
  productsCartItemsDetails
}: CartPanelProps) => {
  const {
    cost,
    totalQuantity,
    lines,
    status,
    checkoutUrl,
    discountCodes,
  } = useCart()

  const amount = Number(cost?.subtotalAmount?.amount) || 0
  const compareAtPriceTotal = compareAtPriceTotals(lines as LineItemProp[])

  useEffect(() => {
    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted && status === 'uninitialized' && !checkoutUrl) {
        window.location.reload()
      }
    }

    window.addEventListener('pageshow', handlePageShow)

    return () => {
      window.removeEventListener('pageshow', handlePageShow)
    }
  }, [])

  return (
    <CartWrapper open={open}>
      <CartViewedEvent />
      <CartHeader totalQuantity={totalQuantity ?? 0} />
      <CartMain
        lines={lines as LineItemProp[]}
        status={status}
        productsCartItemsDetails={productsCartItemsDetails}
        productsWithAnIncludedItem={productsWithAnIncludedItem}
      />
      {amount > 0 && (
        <CartFooter
          totalQuantity={totalQuantity ?? 0}
          status={status}
          amount={amount}
          discountCodes={discountCodes as any}
          compareAtPriceTotal={compareAtPriceTotal}
        />
      )}
    </CartWrapper>
  )
}

export default CartPanel
