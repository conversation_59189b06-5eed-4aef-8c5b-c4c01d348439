'use client'

import Slide from './Slide'

import Container from '../../layout/Container'
import Typography from '../../Typography'
import { LineItemProp } from '../types'

import { spacing } from '@/app/themeTokens.stylex'
import LeftArrow from '@/components/Generic/Icon/lib/LeftArrow'
import RightArrow from '@/components/Generic/Icon/lib/RightArrow'
import Skeleton from '@/components/Generic/Skeleton'
import usePrevNextButtons from '@/hooks/prevNextButtons'
import getRecommendations from '@/lib/shopify/getRecommendations'

import * as stylex from '@stylexjs/stylex'
import useEmblaCarousel from 'embla-carousel-react'
import {
  useEffect,
  useReducer,
  useState
} from 'react'

const styles = stylex.create({
  container: {
    width: '100%',
    gap: '16px',
    paddingBlockStart: spacing.md,
    paddingBlockEnd: spacing.md,
  },
  containerDouble: {
    paddingInline: 0,
  },
  header: {
    paddingLeft: {
      default: 0,
      '@media (min-width: 768px)': '26px',
    }
  },
  sliderWrapper: {
    width: '100%',
    opacity: 0,
  },
  sliderWrapperLoaded: {
    opacity: 1,
  },
  sliderRef: {
    width: '100%',
    overflowX: 'hidden',
    flex: '1',
  },
  slidesWrapper: {
    width: '100%',
    touchAction: 'pan-y pinch-zoom',
    userSelect: 'none',
    containerType: 'inline-size',
  },
  buttonDisabled: {
    opacity: 0.2,
    cursor: 'not-allowed',
  },
})

type ProductRecommenderProps = {
  lineItems: LineItemProp[];
  layout?: 'single' | 'double';
};

const initialState = {
  recommendations: [],
  loading: false,
}

const recommendationsReducer = (state: typeof initialState, action: { type: string, payload?: any }) => {
  switch (action.type) {
    case 'SET_RECOMMENDATIONS':
      return { ...state, recommendations: action.payload, loading: false }
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    default:
      return state
  }
}

// eslint-disable-next-line complexity
const ProductRecommender = ({ lineItems, layout = 'single' }: ProductRecommenderProps) => {
  const [watchDrag, setWatchDrag] = useState(true)
  const [emblaRef, emblaApi] = useEmblaCarousel({
    watchDrag,
    align: 'start',
  })
  const {
    onPrevButtonClick,
    onNextButtonClick,
    prevBtnDisabled,
    nextBtnDisabled,
  } = usePrevNextButtons(emblaApi)

  const toggleSliderDrag = () => {
    setWatchDrag((prev) => !prev)
  }

  const [{ recommendations, loading }, dispatch] = useReducer(recommendationsReducer, initialState)

  const setRecommendations = (newRecommendations: any[]) => {
    dispatch({ type: 'SET_RECOMMENDATIONS', payload: newRecommendations })
  }

  const setLoading = (isLoading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: isLoading })
  }

  // Cookware Set is the default base product for recommendations
  let baseProductId = 'gid://shopify/Product/7250150064210'

  // If there are items in the cart, use the first item as the base product
  if (lineItems.length > 0) {
    baseProductId = lineItems[0].merchandise.product.id
  }

  useEffect(() => {
    async function fetchRecommendations() {
      setLoading(true)

      const response = await getRecommendations(baseProductId)
      const productRecommendations = response.data.productRecommendations.map(
        (product: any) => product.handle
      )

      const params = new URLSearchParams({
        slugs: productRecommendations,
      })
      const r = await fetch(`/api/recommended-products?${params}`)
      const data = await r.json()

      const bannedProducts = [
        'gift-card',
        'gift-card-promo',
        'tea-kettle-promo',
        'square-grill-pan-promo',
        '10-cashback',
        '10-cashback-b',
        '15-cashback',
        '40-cashback',
        '60-cashback',
        'linens-set',
        'linen-apron',
        'routeins',
        'sustainability-coverage',
        'stainless-steel-knife-set-promo',
      ]

      const slugsInCart = lineItems
        ? lineItems.map((lineItem) => lineItem.merchandise.product.handle)
        : []

      const relatedProducts = data
        .filter((item: any) => !slugsInCart.includes(item.slug))
        .filter((item: any) => !bannedProducts.includes(item.slug))

      setRecommendations(relatedProducts || [])
      setLoading(false)
    }

    fetchRecommendations()
  }, [baseProductId])

  if (recommendations.length === 0) {
    return null
  }

  return (
    <Container
      flex
      contentGap
      styleProp={[
        styles.container,
        layout === 'double' && styles.containerDouble,
      ]}
    >
      {/* TODO: I have to make this font size 16px mobile, 18px dekstop. I couldn figure it out */}
      <Container flex flexRow noWrap alignCentered spaceBetween gap="1">
        <Typography
          as="h6"
          typographyTheme="h6Secondary"
          size="sm"
          styleProp={[
            styles.header,
            layout === 'double' && { paddingLeft: 0 },
          ]}
        >
          Complete your kitchen:
        </Typography>
        {layout === 'double' && (
          <Container flex flexRow flexCentered gap="2">
            <button
              type="button"
              onClick={onPrevButtonClick}
              {...stylex.props(prevBtnDisabled && styles.buttonDisabled)}
            >
              <LeftArrow dimensions="19" />
            </button>
            <button
              type="button"
              onClick={onNextButtonClick}
              {...stylex.props(nextBtnDisabled && styles.buttonDisabled)}
            >
              <RightArrow dimensions="19" />
            </button>
          </Container>
        )}
      </Container>
      {loading && (
        <Container flex flexRow noWrap alignCentered gap="1">
          <Skeleton
            width={15}
            height={15}
            animation="shimmer"
            isLoaded={false}
          />
          <Skeleton height={170} animation="shimmer" isLoaded={false} />
          <Skeleton
            width={15}
            height={15}
            animation="shimmer"
            isLoaded={false}
          />
        </Container>
      )}
      <Container
        flex
        flexRow
        noWrap
        alignCentered
        gap="1"
        styleProp={[
          styles.sliderWrapper,
          !loading && styles.sliderWrapperLoaded,
        ]}
      >
        {layout === 'single' && (
          <button
            type="button"
            onClick={onPrevButtonClick}
            {...stylex.props(prevBtnDisabled && styles.buttonDisabled)}
          >
            <LeftArrow dimensions="19" />
          </button>
        )}
        <div ref={emblaRef} {...stylex.props(styles.sliderRef)}>
          <Container
            flex
            flexRow
            noWrap
            gap="2"
            styleProp={styles.slidesWrapper}
          >
            {recommendations.map((product: any) => (
              <Slide
                key={product.productId}
                product={product}
                toggleSliderDrag={toggleSliderDrag}
                layout={layout}
              />
            ))}
          </Container>
        </div>
        {layout === 'single' && (
          <button
            type="button"
            onClick={onNextButtonClick}
            {...stylex.props(nextBtnDisabled && styles.buttonDisabled)}
          >
            <RightArrow dimensions="19" />
          </button>
        )}
      </Container>
    </Container>
  )
}

export default ProductRecommender
