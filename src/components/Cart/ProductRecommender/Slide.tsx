'use client'

import SwatchesDropdown from '@components/Generic/SwatchesDropdown'
import { colors } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import AddToCart from '@/components/Product/AddToCart'
import Typography from '@/components/Typography'
import { ContentfulProduct } from '@/lib/contentful/types/products'
import getShopifyData from '@/lib/shopify/getShopifyData'
import ProductPrice from '@/components/Product/ProductPrice'
import { getProductCardDescription } from '@/components/Product/ProductCard/utils'
import buttonStyles from '@components/Generic/CallToAction/tokens.stylex'
import { ContentfulRichText } from '@/lib/contentful/types/generic'
import ProductLink from '@/components/Product/ProductLink'
import { close } from '@redux/features/cart/cartSlice'
import { useAppDispatch } from '@/redux/hooks'

import { flattenConnection, ProductProvider } from '@shopify/hydrogen-react'
import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

const styles = stylex.create({
  sliderItem: {
    width: '100%',
    height: 'auto',
    flexShrink: 0,
    backgroundColor: colors.white,
    borderRadius: '10px',
    padding: '16px',
    justifyContent: 'center',
  },
  sliderItemDouble: {
    width: {
      default: '100%',
      '@container (min-width: 640px)': 'calc(50% - 8px)',
    },
    justifyContent: {
      default: 'center',
      '@container (min-width: 640px)': 'space-between',
    }
  },
  data: {
    flex: '1',
  },
  slideHeaderData: {
    flexDirection: {
      default: 'column',
      '@media (min-width: 568px)': 'row',
    },
  },
  cta: {
    flex: '1',
  },
  ctaButton: {
    display: 'block',
    width: '100%',
    height: '100%',
    color: 'inherit',
  },
  toggleWrapper: {
    position: 'relative',
    width: '80px',
    flexShrink: 0,
  },
  toggle: {
    position: 'absolute',
    bottom: '0',
    borderRadius: '20px',
    borderWidth: '1px',
    borderColor: colors.navy,
    borderStyle: 'solid',
    width: '100%',
    height: '42px',
    backgroundColor: colors.white,
    overflow: 'hidden',
  },
  toggleOpen: {
    width: '100%',
    height: '142px',
  },
  toggleArrow: {
    position: 'absolute',
    top: '50%',
    right: '12px',
    transform: 'translateY(-50%) rotate(90deg)',
  },
  toggleArrowDisabled: {
    opacity: 0.2,
  },
  sliderRef: {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
  },
  sliderContainer: {
    height: '100%',
    width: '100%',
  },
  swatchItem: {
    cursor: 'pointer',
    padding: '8px 12px',
    height: '40px',
    width: '100%',
  },
  swatchItemDisabled: {
    cursor: 'not-allowed',
  },
  swatchImage: {
    borderRadius: '50%',
  },
  swatchImageSquare: {
    borderRadius: '4px',
  },
  swatch: {
    outline: '1px solid #1f3438',
    outlineOffset: '2px',
  },
})

type SlideProps = {
  product?: ContentfulProduct
  toggleSliderDrag?: () => void
  layout?: 'single' | 'double'
};

// eslint-disable-next-line complexity
const Slide = ({
  product,
  toggleSliderDrag,
  layout = 'single'
}: SlideProps) => {
  const [shopifyData, setShopifyData] = useState<any>(null)

  const [availableVariants, setAvailableVariants] = useState(product?.variants.items || [])
  const [currentVariant, setCurrentVariant] = useState(availableVariants?.[0])
  const dispatch = useAppDispatch()

  useEffect(() => {
    const fetchShopifyData = async () => {
      if (!product?.productId) {
        return
      }

      const { data } = await getShopifyData(product.productId)
      setShopifyData(data)
    }

    fetchShopifyData()
  }, [product?.productId])

  useEffect(() => {
    if (availableVariants && availableVariants.length > 0) {
      if (!availableVariants.includes(currentVariant)) {
        setCurrentVariant(availableVariants[0])
      }
    }
  }, [availableVariants, currentVariant])

  if (!product || !currentVariant) {
    return null
  }

  const description = getProductCardDescription(product)

  const shopifyProduct = shopifyData && flattenConnection(shopifyData.product)

  if (!shopifyProduct || !currentVariant) {
    return null
  }

  return (
    <Container
      flex
      gap="2"
      styleProp={[
        styles.sliderItem,
        layout === 'double' && styles.sliderItemDouble,
      ]}
    >
      <Container flex flexRow gap="1" noWrap>
        {currentVariant.primaryImage?.url && (
          <ProductLink
            product={product}
            variant={currentVariant}
            onClick={() => {
              dispatch(close())
            }}
          >
            <Image
              src={currentVariant.primaryImage.url}
              alt={currentVariant.primaryImage.title ?? product.title ?? 'Product image'}
              width={90}
              height={62}
            />
          </ProductLink>
        )}
        <Container flex gap="1" styleProp={styles.data}>
          <Container
            flex
            spaceBetween
            gap="1"
            styleProp={styles.slideHeaderData}
          >
            <Typography as="h5" typographyTheme="bodySmall" fontBold>
              {product.title}
            </Typography>
            <Typography as="p" typographyTheme="bodySmall">
              <ProductPrice
                size="xs"
                discountedSize="xs"
                slug={product.slug}
                price={currentVariant?.price ?? null}
                compareAtPrice={currentVariant?.compareAtPrice ?? null}
              />
            </Typography>
          </Container>
          {description && (
            <Typography
              as="p"
              typographyTheme="bodyLarge"
              typographyThemeMobile="bodyXSmall"
            >
              {documentToPlainTextString((description as ContentfulRichText)?.json)}
            </Typography>
          )}
        </Container>
      </Container>
      <Container flex flexRow gap="1" noWrap theme="offWhite">
        <SwatchesDropdown
          product={product}
          setAvailableVariants={setAvailableVariants}
          setCurrentVariant={setCurrentVariant}
          currentVariant={currentVariant}
          onOpenToggle={toggleSliderDrag}
        />
        <ProductProvider data={shopifyProduct}>
          <AddToCart
            product={product as any}
            variant={currentVariant as any}
            styleProp={buttonStyles.primary}
          />
        </ProductProvider>
      </Container>
    </Container>
  )
}

export default Slide
