import CallToAction from '@components/Generic/CallToAction'
import { colors, spacing, globalTokens as $ } from '@/app/themeTokens.stylex'
import { discountEntered } from '@/redux/features/events/eventsSlice'
import { useAppDispatch } from '@redux/hooks'
import { RootState } from '@redux/store'

import { useSelector } from 'react-redux'
import { useState, useCallback, SetStateAction } from 'react'
import * as stylex from '@stylexjs/stylex'
import { useCart } from '@shopify/hydrogen-react'

const styles = stylex.create({
  base: {
    position: 'relative',
    display: 'flex',
    justifyContent: 'space-between',
    marginLeft: 'auto',
    gap: '1rem',
  },
  discountInput: {
    width: '100%',
    paddingBlock: spacing.sm,
    paddingInline: spacing.sm,
    borderColor: {
      default: colors.gray300,
      ':active': colors.babyBlue300,
    },
    borderWidth: '1px',
    borderStyle: 'solid',
    borderRadius: $.borderRadius,
  },
  discountBTN: {
    position: 'absolute',
    top: '10px',
    right: spacing.md,
    fontWeight: $.fontWeightBold,
  }
})

const DiscountForm = () => {
  const { discountCodesUpdate } = useCart()
  const [discountCode, setDiscountCode] = useState('')
  const dispatch = useAppDispatch()
  const cartState = useSelector((state: RootState) => state.cart)

  const handleSubmit = useCallback(
    (e: { preventDefault: () => void }) => {
      e.preventDefault()
      if (discountCode) {
        discountCodesUpdate([discountCode])
        dispatch(discountEntered({ couponName: discountCode, cartId: cartState.cart.cartId }))
        setDiscountCode('')
      }
    },
    [cartState.cart.cartId, discountCode, discountCodesUpdate, dispatch]
  )

  const handleChange = useCallback((e: { target: { value: SetStateAction<string> } }) => {
    setDiscountCode(e.target.value)
  }, [])

  return (
    <form {...stylex.props(styles.base)} onSubmit={handleSubmit}>
      <input
        id="discountCode"
        placeholder="Enter Code"
        value={discountCode}
        onChange={handleChange}
        {...stylex.props(styles.discountInput)}
      />
      <CallToAction
        submit
        size="small"
        disabled={!discountCode}
        {...stylex.props([
          styles.discountBTN
        ])}
        variant="underlined"
      >
        Apply Code
      </CallToAction>
    </form>
  )
}

export default DiscountForm
