import LineItem from './LineItem'
import { LineItemProp } from './types'
import CartPerks from './CartPerks'
import ProductRecommender from './ProductRecommender'
import FreeShippingBar from './FreeShippingBar'

import Container from '@components/layout/Container'
import Spacer from '@components/layout/Spacer'
import EmptyCartState from '@components/Cart/EmptyCartState'
import { ThemeColors } from '@/app/themeThemes.stylex'
import CallToAction from '@components/Generic/CallToAction'
import Divider from '@/components/ui/Divider'
import EmptyStateData from '@/data/emptyCartStateData.json'
import { spacing } from '@/app/themeTokens.stylex'
import { useAppDispatch, useAppSelector } from '@/redux/hooks'
import { close } from '@redux/features/cart/cartSlice'

import { useCart } from '@shopify/hydrogen-react'
import * as stylex from '@stylexjs/stylex'
import { useRouter } from 'next/navigation'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  background: {
    overflowY: 'auto',
    alignItems: 'unset',
    flex: '1',
    paddingBlockEnd: spacing.md,
    paddingLeft: {
      default: 'initial',
      [DESKTOP]: spacing.md
    },
    paddingRight: {
      default: 'initial',
      [DESKTOP]: spacing.md,
    }
  },
  backgroundFixedH: {
    justifyContent: 'start',
  },
  mainCartWrapper: {
    flex: '1',
    width: '100%',
    paddingInline: 'initial',
  },
  lineItemWrapper: {
    paddingInline: spacing.md,
  },
  viewFullCartBtn: {
    width: '100%',
    borderWidth: '1px',
  },
  crossSells: {
    width: '100%',
  },
  emptyStateMain: {
    flex: '1',
  },
  emptyStateFooter: {
    width: '100%',
  },
  progressBarContainer: {
    paddingBlockEnd: spacing.xxs,
    paddingLeft: spacing.md,
    paddingRight: spacing.md,
  },
  perkContainer: {
    background: 'transparent',
  }
})

type CartMainProps = {
  lines: LineItemProp[];
  status: string;
  productsWithAnIncludedItem?: {
    name: string,
    productId: string,
    productMetadataCollection: {
      items: {
        type: string
      }[]
    }[]
  }[];
  productsCartItemsDetails?: any;
};

// eslint-disable-next-line complexity
const CartMain = ({
  lines,
  status,
  productsWithAnIncludedItem,
  productsCartItemsDetails,
}: CartMainProps) => {
  const isCartOpen = useAppSelector((state) => state.cart.open)
  const dispatch = useAppDispatch()
  const router = useRouter()
  const { cost } = useCart()
  const amount = Number(cost?.totalAmount?.amount) || 0

  const handleViewCartClick = (e?: React.MouseEvent) => {
    e?.preventDefault()
    dispatch(close())
    router.push('/cart')
  }

  return (
    <Container
      as="main"
      theme="warmGrayExtraLight"
      flex
      flexCentered
      start={amount > 0}
      styleProp={[styles.background]}
    >
      <Container
        as="div"
        gap="2"
        flex
        contentGap
        styleProp={styles.mainCartWrapper}
      >
        {amount > 0 && (
          <Container paddingBlock="md" styleProp={[styles.progressBarContainer]}>
            <FreeShippingBar barWidth="100%" />
            <Spacer size="xs" key="spacer-xs" />
          </Container>
        )}
        <Container
          as="div"
          flex
          gap="2"
          styleProp={styles.lineItemWrapper}
          id="cart-line-items-wrapper"
        >
          {lines && lines.map((line) => (
            <LineItem
              productsCartItemsDetails={productsCartItemsDetails}
              key={line?.id}
              line={line}
              status={status}
              productsWithAnIncludedItem={productsWithAnIncludedItem}
            />
          ))}
        </Container>
        {amount > 0 && (
          <>
            <Container theme="warmGrayExtraLight" paddingInline="md">
              <CartPerks
                containerStyle={styles.perkContainer}
                layout="2"
                perks={[
                  {
                    text: 'Free Shipping On Orders $90+',
                    media: 'free-shipping',
                  },
                  {
                    text: 'Free Returns',
                    media: 'returns',
                  },
                  {
                    text: '30-Day Trial',
                    media: 'trial',
                  },
                ]}
              />
            </Container>
            <Container flexCentered paddingInline="md">
              <Divider orientation="horizontal" />
            </Container>
            {isCartOpen && (
              <Container styleProp={styles.crossSells}>
                <ProductRecommender lineItems={lines} />
              </Container>
            )}
            <Container contentGap>
              <CallToAction
                onClick={handleViewCartClick}
                variant="transparentBorder"
                styleProp={[styles.viewFullCartBtn]}
                theme="navy300"
              >
                View Full Cart
              </CallToAction>
            </Container>
          </>
        )}
        {amount === 0 && (
          <>
            <Container
              as="section"
              flex
              flexCentered
              gap="4"
              styleProp={styles.emptyStateMain}
            >
              <EmptyCartState
                header={EmptyStateData.header}
                button={{
                  text: EmptyStateData.button.text,
                  customUrl: EmptyStateData.button.customUrl,
                  _id: EmptyStateData.button._id,
                }}
                theme={EmptyStateData.theme as ThemeColors}
              />
              <CartPerks
                containerStyle={styles.perkContainer}
                layout="2"
                perks={[
                  {
                    text: 'Free Shipping On Orders $90+',
                    media: 'free-shipping',
                  },
                  {
                    text: 'Free Returns',
                    media: 'returns',
                  },
                  {
                    text: '30-Day Trial',
                    media: 'trial',
                  },
                ]}
              />
            </Container>
            {isCartOpen && (
              <Container as="footer" styleProp={styles.emptyStateFooter}>
                <ProductRecommender lineItems={lines} />
              </Container>
            )}
          </>
        )}
      </Container>
    </Container>
  )
}
export default CartMain
