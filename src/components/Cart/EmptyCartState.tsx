'use client'

import { ThemeColors } from '@/app/themeThemes.stylex'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import CallToAction from '@components/Generic/CallToAction'

import stylex from '@stylexjs/stylex'

type EmptyCartStateProps = {
  header?: string;
  button?: ButtonItem;
  theme?: ThemeColors
};

type ButtonItem = {
  text?: string;
  customUrl?: string;
  _id?: string;
};

const styles = stylex.create({
  header: {
    width: 'min(290px, 100%)',
  },
  headerContainer: {
    backgroundColor: 'transparent',
  }
})

const EmptyCartState = ({
  header,
  button,
  theme = 'white'
}: EmptyCartStateProps) => {
  if (!header && !button) {
    return null
  }

  return (
    <Container
      as="div"
      flexCentered
      paddingBlock="1"
      theme={theme}
      styleProp={styles.headerContainer}
    >
      {header && (
        <Typography
          as="h2"
          size="h6"
          fontBold
          textCentered
          marginBottom="md"
          styleProp={styles.header}
        >
          {header}
        </Typography>
      )}
      {button && (
        <CallToAction
          href={button.customUrl}
          variant="primary"
          id={button._id}
        >
          {button.text}
        </CallToAction>
      )}
    </Container>
  )
}

export default EmptyCartState
