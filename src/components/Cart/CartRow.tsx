import {
  colors,
  spacing,
  globalTokens as $
} from '@/app/themeTokens.stylex'
import Skeleton from '@components/Generic/Skeleton'
import Typography from '@components/Typography'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  dl: {
    display: 'flex',
    flexDirection: 'column',
    paddingInline: spacing.md,
  },
  row: {
    display: 'grid',
    gap: spacing.sm,
    gridTemplateColumns: '2fr 1fr',
    paddingBlock: spacing.xxs,
  },
  dd: {
    textAlign: 'right',
    fontWeight: $.fontWeightBold,
  },
  lineThrough: {
    textDecoration: 'line-through'
  },
  highlight: {
    color: colors.navy,
    fontWeight: 'bold',
  },
  sale: {
    color: colors.red700,
  },
  subtitleLabel: {
    marginLeft: spacing.xxs,
    color: colors.black,
    fontWeight: 'normal',
  },
})

type CartRowProps = {
  title: string,
  subtitle?: string,
  lineThrough?: boolean,
  highlight?: boolean,
  value: string,
  discounted?: boolean,
  status?: 'idle' | 'updating' | 'creating' | 'uninitialized' | 'fetching' | 'creating'
}

// eslint-disable-next-line complexity
const CartRow = ({
  title,
  subtitle,
  lineThrough,
  highlight,
  discounted,
  value,
  status
}: CartRowProps) => {
  const loading = status === 'updating' && true

  const rowStyle = stylex.props(
    styles.row,
    highlight && !subtitle && styles.highlight
  )

  const titleStyle = stylex.props(highlight && styles.highlight)

  const valueStyle = stylex.props(
    styles.dd,
    highlight && styles.highlight,
    lineThrough && styles.lineThrough,
    discounted && styles.sale
  )

  return (
    <div {...rowStyle}>
      <dt>
        <span {...titleStyle}>
          {title}:
          {subtitle && <Typography as="span" styleProp={[styles.subtitleLabel]}><small>{subtitle}</small></Typography>}
        </span>
      </dt>
      <dd {...valueStyle}>
        <Skeleton isLoaded={!loading} animation="shimmer" width={65} height={14}>
          {value}
        </Skeleton>
      </dd>
    </div>
  )
}

export default CartRow
