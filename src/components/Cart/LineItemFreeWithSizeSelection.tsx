import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import {
  spacing,
  colors,
  globalTokens as $
} from '@/app/themeTokens.stylex'
import formatCurrency from '@utils/formatCurrency'
import CallToAction from '@components/Generic/CallToAction'
import Close from '@components/Generic/Icon/lib/Close'
import { Size } from '@/redux/features/promo/gwp'

import * as stylex from '@stylexjs/stylex'
import { ReactNode } from 'react'

const styles = stylex.create({
  lineItemWrapper: {
    backgroundColor: colors.offWhite,
    borderRadius: $.borderRadiusLarge,
    overflow: 'hidden',
  },
  lineItem: {
    display: 'flex',
    alignItems: 'start',
    justifyContent: 'space-between',
    padding: {
      default: spacing.sm,
      '@media (min-width: 768px)': spacing.md
    },
    position: 'relative',
    width: '100%',
  },
  removeButton: {
    position: 'absolute',
    top: spacing.xs,
    left: spacing.xs,
    cursor: 'pointer',
  },
  dl: {
    width: 'clamp(45px, 30vw, 165px)',
    display: 'flex',
    marginRight: 'auto',
    marginLeft: {
      default: spacing.md,
      '@media (max-width: 768px)': spacing.xxs
    },
    flexDirection: 'column',
    gap: spacing.xs,
  },
  highlight: {
    color: colors.perracotta,
  },
  lineThrough: {
    textDecoration: 'line-through'
  },
  prices: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'end',
  },
  customMessage: {
    whiteSpace: 'nowrap',
    textAlign: 'left',
  },
})

type LineItemFreeWithSizeSelectionProps = {
  price: number,
  image: ReactNode,
  customMessage: string,
  productName: string,
  selectedSize: Size['label'],
  selectSizeCTA: ReactNode,
  onRemoveItem: () => void,
}

// eslint-disable-next-line complexity
const LineItemFreeWithSizeSelection = ({
  price,
  image,
  customMessage,
  productName,
  selectedSize,
  selectSizeCTA,
  onRemoveItem,
}: LineItemFreeWithSizeSelectionProps) => (
  <Container styleProp={styles.lineItemWrapper}>
    <Container styleProp={styles.lineItem}>
      <CallToAction
        onClick={onRemoveItem}
        size="small"
        variant="secondary"
        {...stylex.props(styles.removeButton)}
      >
        <Close size="small" />
      </CallToAction>

      {image}

      <dl {...stylex.props(styles.dl)}>
        <Typography
          as="p"
          size="captionSmall"
          styleProp={styles.customMessage}
        >
          {customMessage}
        </Typography>
        <Typography as="h2" size="xs" fontBold>
          {productName}
        </Typography>
        <Typography
          as="p"
          typographyTheme="bodySmall"
          typographyThemeMobile="captionLarge"
        >
          Size: {selectedSize || 'Not Selected'}
        </Typography>
        {selectSizeCTA}
      </dl>
      <Container styleProp={styles.prices}>
        <Container as="div" flex flexRow gap="xxs">
          <Typography as="h2" size="sm" fontBold styleProp={styles.highlight}>
            FREE
          </Typography>
          <Typography as="p" size="xs" styleProp={styles.lineThrough}>
            {formatCurrency(price)}
          </Typography>
        </Container>
      </Container>
    </Container>
  </Container>
)

export default LineItemFreeWithSizeSelection
