'use client'

import AfterpayInfo from './AfterpayBlock'

import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import FreeShippingBar from '@components/Cart/FreeShippingBar'
import Spacer from '@components/layout/Spacer'
import DiscountForm from '@components/Cart/DiscountForm'
import DiscountRow from '@components/Cart/DiscountRow'
import ShopPayButton from '@components/Cart/ShopPayButton'
import ArrowUpIcon from '@components/Generic/Icon/lib/ArrowUp'
import { colors, fontSizes, spacing } from '@/app/themeTokens.stylex'
import themes, { ThemeColors } from '@/app/themeThemes.stylex'
import formatCurrency from '@utils/formatCurrency'
import EmptyCartState from '@components/Cart/EmptyCartState'
import EmptyStateData from '@/data/emptyCartStateData.json'

import React, { useState } from 'react'
import { CartCheckoutButton, useCart } from '@shopify/hydrogen-react'
import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  hrBar: {
    height: '1px',
    borderWidth: 0,
    background: colors.gray300,
  },
  textColor: { color: colors.sage },
  disabledText: { color: 'colors.beige300' },
  textPadding: { marginLeft: spacing.lg },
  orderWrapper: {
    paddingBlock: '28px',
    paddingInline: '24px',
    backgroundColor: colors.white,
  },
  breakDownPrice: {
    alignItems: 'end',
  },
  breakdownBTN: {
    color: colors.sage,
    fontSize: fontSizes.bodySmall,
    paddingInline: spacing.xs,
    fontWeight: 400,
    textDecoration: 'underline',
  },
  breakdownSVG: { marginLeft: spacing.xs },
  wraperCTAs: {
    position: { default: 'fixed', [DESKTOP]: 'initial' },
    bottom: 0,
    left: 0,
    width: '100%',
    zIndex: 1,
    backgroundColor: { default: colors.white, [DESKTOP]: 'initial' },
    padding: { default: spacing.md, [DESKTOP]: 'initial' },
  },
})

const checkout = stylex.create({
  base: {
    fontSize: 14,
    lineHeight: 1.5,
    display: 'inline-block',
    borderRadius: 40,
    textAlign: 'center',
    fontWeight: 600,
    textTransform: 'uppercase',
    padding: '12px 24px',
    backgroundColor: { default: colors.navy, ':disabled': colors.gray },
    cursor: { default: 'pointer', ':disabled': 'not-allowed' },
    color: { default: colors.white, ':disabled': colors.gray500 },
  },
  fullWidth: { width: '100%' },
})

type OrderSummaryProps = { theme?: ThemeColors };

const OrderSummaryHeader = () => (
  <header>
    <Typography as="h4" fontBold marginBottom="sm" size="h6">
      Order Summary
    </Typography>
  </header>
)

const BreakdownDetails = ({ breakdown }: any) => (
  <Container
    as="div"
    styleProp={{
      maxHeight: breakdown ? '100px' : '0',
      opacity: breakdown ? 1 : 0,
      overflow: 'hidden',
      transition: 'all 0.5s ease-in-out',
    }}
  >
    {breakdown && (
      <Container as="div" flex gap="1" paddingBlock="1">
        <Typography as="span" styleProp={[styles.disabledText, styles.textPadding]}>
          Set Savings
        </Typography>
        <Typography as="span" styleProp={[styles.disabledText, styles.textPadding]}>
          Discount Savings
        </Typography>
      </Container>
    )}
  </Container>
)

const BreakdownSavings = ({
  breakdown,
  totalSetSavings,
  totalDiscountSavings
}: any) => (
  <Container
    as="div"
    styleProp={{
      maxHeight: breakdown ? '100px' : '0',
      opacity: breakdown ? 1 : 0,
      overflow: 'hidden',
      transition: 'all 0.5s ease-in-out',
    }}
  >
    {breakdown && (
      <Container as="div" flex gap="1" paddingBlock="1">
        <Typography size="bodySmall" as="span" styleProp={[styles.disabledText]}>
          {formatCurrency(totalSetSavings)}
        </Typography>
        <Typography size="bodySmall" as="span" styleProp={[styles.disabledText]}>
          {formatCurrency(totalDiscountSavings)}
        </Typography>
      </Container>
    )}
  </Container>
)

const Subtotal = ({ cost }: any) => (
  <Container as="div" flex flexRow spaceBetween paddingBlock="1">
    <Typography as="span" fontBold>
      Subtotal:
      <br />
      <Typography as="span" size="bodySmall" styleProp={[styles.disabledText]}>
        (Taxes calculated at checkout)
      </Typography>
    </Typography>
    <Typography as="span" fontBold>
      {formatCurrency(parseFloat(cost?.subtotalAmount?.amount ?? '0'))}
    </Typography>
  </Container>
)

const OrderSummaryDetails = ({
  breakdown,
  setBreakdown,
  totalSavings,
  totalSetSavings,
  totalDiscountSavings,
  cost,
  lines,
  amount
}: any) => (
  <Container as="div" paddingBlock="2">
    <Container as="div" flex flexRow spaceBetween paddingBlock="1">
      <Container as="div">
        <Typography as="span" fontBold styleProp={[styles.textColor]}>
          Total Savings
          <button
            type="button"
            {...stylex.props(styles.breakdownBTN)}
            onClick={() => setBreakdown(!breakdown)}
          >
            Breakdown
            <ArrowUpIcon
              styleProp={[
                styles.breakdownSVG,
                !breakdown && { transform: 'rotate(180deg)' },
              ]}
            />
          </button>
        </Typography>
        <BreakdownDetails breakdown={breakdown} totalSavings={totalSavings} />
      </Container>
      <Container as="div" flex styleProp={[styles.breakDownPrice]}>
        <Typography as="span" fontBold styleProp={[styles.textColor]}>
          {formatCurrency(totalSavings)}
        </Typography>
        <BreakdownSavings
          breakdown={breakdown}
          totalSavings={totalSavings}
          totalSetSavings={totalSetSavings}
          totalDiscountSavings={totalDiscountSavings}
          lines={lines}
        />
      </Container>
    </Container>
    <Subtotal cost={cost} />
    <AfterpayInfo totalAmount={amount} />
  </Container>
)

const OrderSummaryCTAs = ({ lines }: any) => (
  <Container as="div" styleProp={[styles.wraperCTAs]}>
    <CartCheckoutButton id="cart-checkout-button" {...stylex.props(checkout.base, checkout.fullWidth)}>
      Checkout &rarr;
    </CartCheckoutButton>
    <div id="cart-corso-checkout-disable" />
    <Spacer size="sm" />
    <ShopPayButton lineItems={lines} />
  </Container>
)

const OrderSummary = ({ theme = 'navy' }: OrderSummaryProps) => {
  const themeStyles = themes[theme]
  const [breakdown, setBreakdown] = useState(false)
  const {
    totalQuantity,
    cost,
    lines
  } = useCart()

  const amount = Number(cost?.totalAmount?.amount) || 0

  let totalSetSavings = 0
  let totalDiscountSavings = 0
  let totalSavings = 0

  lines?.forEach((line: any) => {
    if (line?.merchandise?.compareAtPrice?.amount) {
      totalSetSavings += (Number(line?.merchandise?.compareAtPrice?.amount) * line.quantity) - (Number(line?.merchandise?.price?.amount) * line.quantity)
    }

    totalDiscountSavings += (Number(line?.merchandise?.price?.amount) * line.quantity) - Number(line?.cost?.totalAmount?.amount)
  })

  totalSavings += totalSetSavings + totalDiscountSavings

  if (!amount) {
    return (
      <EmptyCartState
        header={EmptyStateData.header}
        button={{
          text: EmptyStateData.button.text,
          customUrl: EmptyStateData.button.customUrl,
          _id: EmptyStateData.button._id,
        }}
        theme="beige300"
      />
    )
  }

  return (
    <Container as="section" styleProp={[themeStyles, styles.orderWrapper]}>
      <OrderSummaryHeader />
      <Spacer size="md" />
      <hr {...stylex.props(styles.hrBar)} />
      <Spacer size="sm" />
      <FreeShippingBar contentAlign="left" barWidth="100%" />
      {(totalQuantity ?? 0) > 0 && (
        <OrderSummaryDetails
          breakdown={breakdown}
          setBreakdown={setBreakdown}
          totalSavings={totalSavings}
          totalSetSavings={totalSetSavings}
          totalDiscountSavings={totalDiscountSavings}
          cost={cost}
          lines={lines}
          amount={amount}
        />
      )}
      <DiscountForm />
      <DiscountRow />
      <Spacer size="md" />
      <OrderSummaryCTAs lines={lines} />
    </Container>
  )
}

export default OrderSummary
