import FreeShippingIcon from '../Generic/Icon/lib/FreeShipping'
import ReturnsIcon from '../Generic/Icon/lib/Returns'
import TrialIcon from '../Generic/Icon/lib/Trial'
import PriceMatchIcon from '../Generic/Icon/lib/PriceMatch'
import CustomerSupportIcon from '../Generic/Icon/lib/CustomerSupportIcon'
import Container from '../layout/Container'
import Typography from '../Typography'

import {
  fontSizes,
  defaultTheme as $T,
  spacing,
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  wrapper: {
    justifyContent: 'initial',
  },
  wrapperV2: {
    paddingInline: 'initial',
  },
  header: {
    width: 'min(290px, 100%)',
  },
  perkContainer: {
    width: '100%',
    justifyContent: 'space-evenly',
    alignItems: 'baseline',
    marginTop: 0,
    flexWrap: {
      default: 'nowrap',
      [DESKTOP]: 'wrap',
    },
    gap: {
      default: spacing.xs,
      [DESKTOP]: spacing.sm,
    },
    backgroundColor: $T.secondarySurface,
    padding: {
      default: spacing.md,
      [DESKTOP]: `${spacing.md} ${spacing.xl}`,
    },
    borderRadius: spacing.md,
  },
  perkContainerV2: {
    padding: {
      default: spacing.md,
      [DESKTOP]: `${spacing.sm} 0`,
    },
    alignItems: {
      default: 'baseline',
      [DESKTOP]: 'center',
    },
    borderRadius: 0,
    width: '100%',
    marginTop: 0,
  },
  perkContent: {
    alignItems: 'center',
    maxWidth: '115px',
    width: '100%',
  },
  perkContentV2: {
    flexDirection: {
      default: 'column',
      [DESKTOP]: 'row',
    },
    flexWrap: 'nowrap',
    gap: {
      default: '0',
      [DESKTOP]: spacing.sm,
    },
    maxWidth: '148px',
  },
  perkText: {
    fontSize: {
      default: fontSizes.xxs,
      [DESKTOP]: fontSizes.bodySmall,
    },
    display: 'flex',
    alignItems: 'center',
    minHeight: spacing.lg,
  },
  perkTextV2: {
    fontSize: fontSizes.xxs,
    textAlign: {
      default: 'center',
      [DESKTOP]: 'left',
    },
    minWidth: {
      default: 'initial',
      [DESKTOP]: '95px',
    },
  },
  perkImage: {
    width: '57px',
    height: 'auto',
    objectFit: 'contain',
  },
})

type Perk = {
  text?: string;
  media?: string;
};

type CartPerksProps = {
  perks: Perk[];
  layout?: '1' | '2';
  containerStyle?: React.CSSProperties;
};

function PerkComponent({ media }: { media: Perk['media'] }) {
  switch (media) {
    case 'free-shipping':
      return <FreeShippingIcon />
    case 'returns':
      return <ReturnsIcon />
    case 'trial':
      return <TrialIcon />
    case 'price-match':
      return <PriceMatchIcon />
    case 'customer-support':
      return <CustomerSupportIcon />
    default:
      return null
  }
}

const CartPerks = ({
  perks,
  layout = '1',
  containerStyle
}: CartPerksProps) => (
  <Container
    as="div"
    flexCentered
    flexRow
    styleProp={[
      styles.perkContainer,
      layout === '2' && styles.perkContainerV2,
      containerStyle,
    ]}
  >
    {perks.map((perk) => (
      <Container
        as="div"
        key={perk.text}
        flex
        styleProp={[
          styles.perkContent,
          layout === '2' && styles.perkContentV2,
        ]}
      >
        {perk.media && (
          <div {...stylex.props(styles.perkImage)}>
            <PerkComponent media={perk.media} />
          </div>
        )}

        <Typography
          as="p"
          size="sm"
          textCentered
          styleProp={[styles.perkText, layout === '2' && styles.perkTextV2]}
        >
          {perk.text}
        </Typography>
      </Container>
    ))}
  </Container>
)

export default CartPerks
