'use client'

import { open } from '@redux/features/cart/cartSlice'
import { useAppDispatch } from '@redux/hooks'
import Container from '@components/layout/Container'
import Typography from '@/components/Typography'
import { colors } from '@/app/themeTokens.stylex'

import { useCart } from '@shopify/hydrogen-react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  bag: {
    cursor: 'pointer',
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center'
  },
  icon: {
    gridArea: '1/1',
    width: {
      default: '38px',
      '@media (min-width: 1024px)': '42px',
    },
  },
  quantity: {
    gridArea: '1/1',
    alignSelf: 'center',
    justifySelf: 'center',
    marginBottom: '-5px',
    color: colors.navy
  }
})

const CartToggle = () => {
  const { totalQuantity } = useCart()
  const dispatch = useAppDispatch()

  return (
    <Container
      grid
      styleProp={styles.bag}
      aria-label="Open cart"
      role="button"
      onClick={() => {
        dispatch(open())
      }}
    >
      <svg
        viewBox="0 0 42 36"
        {...stylex.props(styles.icon)}
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g stroke={colors.navy} strokeWidth="1px" strokeMiterlimit="2">
          <path
            d="M2.5 8.9h5c.6 0 1.1.6 1.1 1.3v.5c0 .7-.5 1.3-1.1 1.3h-6c-.3
            0-.5-.3-.5-.6V9.5c0-.3.2-.6.5-.6h1ZM39.9 12h-5c-.6
            0-1.1-.6-1.1-1.3v-.5c0-.7.5-1.3 1.1-1.3h6c.3 0 .5.3.5.6v1.9c0
            .3-.2.6-.5.6h-1ZM37.8 12.3v15.8c0 4.2-4.6 6.9-8.9 6.9H12.8c-4.3
            0-8.9-2.7-8.9-6.9V12.3M37.9 8.5V6.6c0-.2-.2-.4-.4-.4H4.9c-.2
            0-.4.2-.4.4v1.9M7.6 5.6h2.9L14 3.3c4.6-3.1 10-3.1 14.5 0l1.8 1.2 1.8 1.2H35"
          />
        </g>
      </svg>
      <Typography styleProp={styles.quantity} as="span" typographyTheme="bodyLarge">{totalQuantity}</Typography>
    </Container>
  )
}

export default CartToggle
