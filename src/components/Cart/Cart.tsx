'use client'

import CartPanel from '@components/Cart/CartPanel'
import { close } from '@redux/features/cart/cartSlice'
import { useAppSelector, useAppDispatch } from '@redux/hooks'

import * as stylex from '@stylexjs/stylex'

import type { RootState } from '@redux/store'

const styles = stylex.create({
  root: {
    zIndex: 50,
    position: 'fixed',
    top: 0,
    left: 0,
    pointerEvents: 'none',
  },
  backdrop: {
    position: 'fixed',
    top: 0,
    width: '100%',
    height: '100%',
    opacity: 0,
    transition: 'opacity 0.25s ease-in-out',
    backgroundColor: 'rgba(0, 0, 0, 0)'
  },
  backdropVisible: {
    pointerEvents: 'auto',
    backgroundColor: 'rgba(0, 0, 0, 0.35)',
    opacity: 1,
  },
})

type CartProps = {
  productsWithAnIncludedItem: {
    name: string,
    productId: string,
    productMetadataCollection: {
      items: {
        type: string
      }[]
    }[]
  }[];
  productsCartItemsDetails?: any
}

const Cart = ({ productsWithAnIncludedItem, productsCartItemsDetails }: CartProps) => {
  const openStatus = useAppSelector((state: RootState) => state.cart.open)
  const dispatch = useAppDispatch()

  return (
    <div
      {...stylex.props(
        styles.root,
      )}
    >
      <div
        aria-hidden="true"
        {...stylex.props(
          styles.backdrop,
          openStatus && styles.backdropVisible
        )}
        onClick={() => {
          dispatch(close())
        }}
      />
      <CartPanel
        open={openStatus}
        productsCartItemsDetails={productsCartItemsDetails}
        productsWithAnIncludedItem={productsWithAnIncludedItem}
      />
    </div>
  )
}

export default Cart
