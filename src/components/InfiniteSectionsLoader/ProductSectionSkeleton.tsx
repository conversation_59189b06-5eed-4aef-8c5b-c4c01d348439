/* eslint-disable react/prop-types */
import ProductCardSkeleton from './ProductCardSkeleton'

import ProductGridTitle from '../Product/ProductGridTitle'

import { spacing } from '@/app/themeTokens.stylex'
import Wrapper from '@/components/layout/Wrapper'
import Container from '@/components/layout/Container'
import { PageSectionsProduct } from '@/lib/contentful/types'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const UI_CONSTANTS = {
  SECTION: {
    VERTICAL_PADDING: 80,
    PADDING_DIVISOR: 2,
  },
  HEADING: {
    WIDTH: '80%',
    HEIGHT: 80,
  },
  SKELETON: {
    MIN_COUNT: 4
  }
} as const

const styles = stylex.create({
  wrapper: {
    paddingBlock: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    }
  },
  productGrid: {
    display: 'grid',
    gridTemplateColumns: {
      default: 'auto auto',
      [DESKTOP]: 'repeat(auto-fill, minmax(min(20%, 100%), 1fr))',
    },
    rowGap: {
      default: spacing.lg,
      [DESKTOP]: spacing.xl,
    },
    minHeight: '300px'
  }
})

export interface ProductSectionSkeletonGridProps {
  itemCount: number
}

export const ProductSectionSkeletonGrid = ({ itemCount }: ProductSectionSkeletonGridProps) => (
  <>
    {Array.from({ length: Math.max(itemCount, UI_CONSTANTS.SKELETON.MIN_COUNT) }, (_, i) => (
      <ProductCardSkeleton key={`product-skeleton-${i}`} />
    ))}
  </>
)

export interface ProductSectionSkeletonProps {
  section: PageSectionsProduct
}

export const ProductSectionSkeleton = ({ section }: ProductSectionSkeletonProps) => {
  const sectionTitle = section.title || section.name
  const itemCount = Math.max(section.totalProducts || UI_CONSTANTS.SKELETON.MIN_COUNT, UI_CONSTANTS.SKELETON.MIN_COUNT)

  return (
    <Wrapper as="section" size="5" pageGap styleProp={styles.wrapper}>
      {sectionTitle && <ProductGridTitle title={sectionTitle} />}
      <Container as="div" gap="3" styleProp={styles.productGrid}>
        <ProductSectionSkeletonGrid
          itemCount={itemCount}
        />
      </Container>
    </Wrapper>
  )
}
