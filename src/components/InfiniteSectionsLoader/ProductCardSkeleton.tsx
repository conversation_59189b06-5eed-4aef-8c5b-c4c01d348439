'use client'

import Skeleton from '@/components/Generic/Skeleton'
import useIsMobile from '@/hooks/isMobile'

const CARD_HEIGHT = {
  DESKTOP: 622,
  MOBILE: 504,
} as const

export default function ProductCardSkeleton() {
  const { isMobile } = useIsMobile()

  return (
    <Skeleton
      as="div"
      isLoaded={false}
      animation="shimmer"
      width="100%"
      height={isMobile ? CARD_HEIGHT.MOBILE : CARD_HEIGHT.DESKTOP}
    />
  )
}
