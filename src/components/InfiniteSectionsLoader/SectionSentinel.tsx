'use client'

import useEnhancedIntersectionObserver from '@/hooks/useEnhancedIntersectionObserver'

import * as stylex from '@stylexjs/stylex'
import React, { useEffect } from 'react'

const styles = stylex.create({
  sentinel: {
    height: 1,
    width: '100%',
    visibility: 'hidden',
  },
  topSentinel: {
    marginBottom: -1,
  },
  bottomSentinel: {
    marginTop: -1,
  }
})

export type SentinelPosition = 'top' | 'bottom'

interface SectionSentinelProps {
  sectionId: string
  fetchSection: (sectionId: string) => Promise<void>
  observerOptions: {
    rootMargin: string
    threshold: number
    dependencies: any[]
  }
  position: SentinelPosition
}

const SectionSentinel = ({
  sectionId,
  fetchSection,
  observerOptions,
  position
}: SectionSentinelProps) => {
  const { isIntersecting, ref } = useEnhancedIntersectionObserver(observerOptions)

  useEffect(() => {
    if (isIntersecting) {
      fetchSection(sectionId)
    }
  }, [isIntersecting, fetchSection, sectionId])

  return (
    <div
      data-section-sentinel={sectionId}
      data-sentinel-position={position}
      ref={ref}
      {...stylex.props(
        styles.sentinel,
        position === 'top' ? styles.topSentinel : styles.bottomSentinel
      )}
    />
  )
}

export default SectionSentinel
