import { selectProduct } from '@/redux/features/product/productSlice'

const subscribeToTolstoyProductCardClick = (dispatch: any) => {
  const myCallback = async (payload: any) => {
    try {
      const res = await fetch(`/data/json-products/${payload.productId}.json`)

      const productJson = await res.json()

      const productObject = {
        productId: productJson.productId,
        slug: productJson.slug,
        title: productJson.name,
        variants: productJson.variants,
      }

      dispatch(selectProduct({
        product: productObject,
        quantity: 1
      }))
    } catch (err) {
      console.log('Issue dispatching tolstoy product card clicked event', err)
    }
  }

  const options = {
    disableProductModal: false
  }

  window.tolstoyWidget.subscribe(
    'tolstoy_product_card_click',
    myCallback,
    options
  )
}

export default subscribeToTolstoyProductCardClick
