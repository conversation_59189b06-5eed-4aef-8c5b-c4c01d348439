'use client'

import subscribeToTolstoyProductCardClick from './utils'

import { useAppDispatch } from '@/redux/hooks'
import Tolstoy from '@/scripts/tolstoy'

import { useCart } from '@shopify/hydrogen-react'
import React, { useEffect } from 'react'

type TolstoyProps = {
  id: string
  shape?: string
}

const TolstoySection = ({ id, shape = 'rectangle' }: TolstoyProps) => {
  const { linesAdd } = useCart()
  const dispatch = useAppDispatch()

  const COMPONENT = shape === 'rectangle' ? 'tolstoy-stories' : 'tolstoy-carousel'

  useEffect(() => {
    const handleAddToCart = async (payload: any) => {
      try {
        const result: any = await linesAdd([{
          merchandiseId: `gid://shopify/ProductVariant/${payload.variantId}`,
          quantity: 1
        }])

        if (result && result.errors && result.errors.length) {
          window.tolstoyWidget.postMessage({
            ...payload,
            eventName: 'tolstoy_add_to_cart_error',
          })

          throw new Error('Tolstoy ATC unsuccessful')
        } else {
          window.tolstoyWidget.postMessage({
            ...payload,
            eventName: 'tolstoy_add_to_cart_success',
          })
        }
      } catch (error) {
        window.tolstoyWidget.postMessage({
          ...payload,
          eventName: 'tolstoy_add_to_cart_error',
        })
      }
    }

    const callback = async (payload: any) => {
      await handleAddToCart(payload)
    }

    const subscribeToTolstoyAddToCartClick = () => {
      window.tolstoyWidget.subscribe('tolstoy_add_to_cart', callback, {})
    }

    if (window.tolstoyWidget) {
      subscribeToTolstoyProductCardClick(dispatch)
      subscribeToTolstoyAddToCartClick()
    } else {
      window.addEventListener('tolstoyWidgetReady', () => {
        subscribeToTolstoyProductCardClick(dispatch)
        subscribeToTolstoyAddToCartClick()
      })
    }
  }, [dispatch, linesAdd])

  return (
    <>
      <Tolstoy />
      {React.createElement(COMPONENT, {
        class: COMPONENT,
        'data-publish-id': id
      })}
    </>
  )
}

export default TolstoySection
