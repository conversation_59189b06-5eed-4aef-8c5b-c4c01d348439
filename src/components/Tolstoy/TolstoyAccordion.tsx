import Accordion from '@components/Generic/Accordion'
import Tolstoy from '@/scripts/tolstoy'

import React from 'react'
import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

type AccordionProps = {
  block: {
    title: string,
    content: {
      json: any
    }
  }
}

const TolstoyAccordion = ({ block }: AccordionProps) => {
  const id = documentToPlainTextString(block.content.json)
  const shape = 'rectangle'
  const COMPONENT = shape === 'rectangle' || shape === 'circle' ? 'tolstoy-stories' : 'tolstoy-carousel'

  return (
    <>
      <Tolstoy />
      <Accordion
        key={id}
        title={block.title}
        isLast
        open
      >
        {React.createElement(COMPONENT, {
          class: COMPONENT,
          'data-publish-id': id
        })}
      </Accordion>
    </>
  )
}

export default TolstoyAccordion
