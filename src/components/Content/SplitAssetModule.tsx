'use client'

import {
  defaultTheme as $T,
  globalTokens as $,
  spacing
} from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import MediaContainer from '@components/Content/MediaContainer'
import RenderIf from '@/utils/renderIf'
import RichTextRenderer from '@/utils/RichTextRenderer'
import RenderCTAS from '@components/Generic/RenderCTAS'
import useIsMobile from '@/hooks/isMobile'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  root: {
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    },
    paddingBlock: {
      default: spacing.md,
      [DESKTOP]: '32px',
    },
  },
  mainWrapper: {
    maxWidth: $.maxWidth,
    margin: '0 auto',
    borderRadius: $.borderRadius,
    overflow: 'hidden',
    flexDirection: {
      default: 'column',
      [DESKTOP]: 'row',
    },
  },
  mediaWrapper: {
    width: '100%',
    minHeight: '256px',
    height: {
      default: '256px',
      [DESKTOP]: 'initial',
    }
  },
  mainContentWrapper: {
    paddingInline: {
      default: '15px',
      [DESKTOP]: spacing.xl,
    },
    paddingBlock: {
      default: '36px',
      [DESKTOP]: spacing.xxl,
    },
    width: '100%',
  },
  contentWrapper: {
    maxWidth: '405px',
    margin: '0 auto',
  },
  subheader: {
    borderWidth: '1px',
    borderRadius: $.borderRadiusSmall,
    borderStyle: 'solid',
    borderColor: $T.primaryText,
    paddingInline: spacing.xs,
    paddingBlock: spacing.xxs,
    width: 'fit-content',
  },
  cta: {
    width: {
      default: '100%',
      [DESKTOP]: 'fit-content',
    }
  },
})

type SplitAssetModuleProps = {
  header?: string;
  subheader?: string;
  content?: any;
  assetsCollection?: any;
  mobileAssetsCollection?: any;
  button: any;
  theme?: ThemeColors;
};

const SplitAssetModule = ({
  header,
  subheader,
  content,
  assetsCollection,
  mobileAssetsCollection,
  button,
  theme = 'navy'
}: SplitAssetModuleProps) => {
  const { items } = assetsCollection
  const { items: mobileItems } = mobileAssetsCollection || {}
  const { isMobile } = useIsMobile()

  return (
    <Container as="section" styleProp={styles.root}>
      <Container
        theme={theme}
        as="div"
        flex
        noWrap
        spaceBetween
        styleProp={styles.mainWrapper}
      >
        <Container as="div" styleProp={styles.mainContentWrapper}>
          <Container as="div" styleProp={styles.contentWrapper}>
            <RenderIf condition={subheader !== ''}>
              <Typography
                as="p"
                fontBold
                typographyTheme="captionSmall"
                marginBottom="md"
                styleProp={styles.subheader}
              >
                {subheader}
              </Typography>
            </RenderIf>

            <RenderIf condition={header !== ''}>
              <Typography
                as="h4"
                fontBold
                typographyTheme="h4Secondary"
              >
                {header}
              </Typography>
            </RenderIf>

            <RenderIf condition={content.json}>
              <Container as="div" paddingBlock="md">
                <RichTextRenderer content={content.json} />
              </Container>
            </RenderIf>

            <RenderIf condition={button}>
              <RenderCTAS
                buttons={Array.isArray(button) ? button : [button]}
                styleProp={styles.cta}
              />
            </RenderIf>
          </Container>
        </Container>

        <RenderIf condition={items}>
          {isMobile && mobileItems?.length > 0 && (
            <MediaContainer
              styleProps={styles.mediaWrapper}
              asset={mobileItems[0]}
            />
          )}
          {(!isMobile || mobileItems?.length === 0) && (
            <MediaContainer
              styleProps={styles.mediaWrapper}
              asset={items[0]}
            />
          )}
        </RenderIf>
      </Container>
    </Container>
  )
}

export default SplitAssetModule
