'use client'

import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { spacing } from '@/app/themeTokens.stylex'

import stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  container: {
    containerName: 'card',
    containerType: 'inline-size',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.sm,
    padding: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    textAlign: 'center',
    paddingBlock: spacing.lg,
    borderRadius: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    aspectRatio: {
      default: '16 / 9',
      [DESKTOP]: '566 / 420',
    },
    gridColumn: {
      default: 'span 2',
      [DESKTOP]: 'span 1',
    },
  },
  body: {
    paddingInline: spacing.md,
    maxWidth: {
      default: '35ch',
      '@container card (width > 600px)': '80ch',
    },
  },
  isFirstChild: {
    aspectRatio: {
      default: 'unset',
      [DESKTOP]: '1160 / 420',
    },
    gridColumn: 'span 2',
    minHeight: '100%',
  },
})

function GridLayout3({ container, child }: { container: any, child?: number }) {
  const isFirstChild = child === 0
  return (
    <Container
      styleProp={[styles.container, isFirstChild && styles.isFirstChild]}
      key={container.id}
      theme={container.theme}
      size="5"
    >
      <Typography as="h2" typographyTheme="bodyLarge">{container.title}</Typography>
      <Container styleProp={styles.body}>{container.content}</Container>
    </Container>
  )
}

export default GridLayout3
