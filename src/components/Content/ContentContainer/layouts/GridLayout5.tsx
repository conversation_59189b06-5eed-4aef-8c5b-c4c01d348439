/* eslint-disable complexity */

import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { spacing } from '@/app/themeTokens.stylex'
import MediaImage from '@/components/Content/ContentContainer/media/MediaImage'
import MediaVideo from '@/components/Content/ContentContainer/media/MediaVideo'
import DialogTrigger from '@/components/Generic/Dialog/DialogTrigger'

import stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  container: {
    containerName: 'card',
    containerType: 'inline-size',
    display: 'grid',
    placeContent: 'center',
    gap: spacing.sm,
    padding: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    textAlign: 'center',
    borderRadius: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    paddingBlock: spacing.xl,
  },
  body: {
    paddingInline: spacing.md,
    maxWidth: {
      default: '35ch',
      '@container card (width > 600px)': '60ch',
    },

  },
  isFirstChild: {
    gridColumn: {
      default: 'span 1',
      [DESKTOP]: 'span 2',
    },
    minHeight: '100%',
  },
  supportsMedia: {
    padding: 'unset',
    borderRadius: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    overflow: 'hidden',
  },
  hasMedia: {
    gridArea: '1/1',
  },
  contentWithMedia: {
    maxWidth: 'unset',
    marginBottom: 130,
    alignSelf: 'end',
  },
  callToAction: {
    width: 'fit-content',
    justifySelf: 'center',
  },
  callToActionVideoIcon: {
    gridArea: '1/1',
    alignSelf: 'end',
    marginBottom: spacing.xxl,
  }
})

const FIRST_CHILD = 0

function GridLayout5({ container, child }: { container: any, child?: number }) {
  const isFirstChild = child === FIRST_CHILD
  const hasMedia = container.media && container.media.length > 0
  const mediaItem = hasMedia ? container.media[0] : null
  const { callToAction } = container
  return (
    <Container
      styleProp={[
        styles.container,
        isFirstChild && styles.isFirstChild,
        hasMedia && styles.supportsMedia,
      ]}
      key={container.id}
      theme={container.theme}
      size="5"
    >
      {mediaItem && mediaItem.type === 'image' && !isFirstChild && (
        <Container styleProp={styles.hasMedia}>
          <MediaImage
            url={mediaItem.url}
            alt={container.title}
            width={mediaItem.width}
            height={mediaItem.height}
          />
        </Container>
      )}
      {mediaItem && mediaItem.type === 'video' && !isFirstChild && (
        <Container styleProp={styles.hasMedia}>
          <MediaVideo
            url={mediaItem.url}
            width={mediaItem.width}
            height={mediaItem.height}
          />
        </Container>
      )}
      {container.title && (
        <Typography as="h2" typographyTheme="bodyLarge">{container.title}</Typography>
      )}
      <Container styleProp={[styles.body, hasMedia && styles.hasMedia, hasMedia && !isFirstChild && styles.contentWithMedia]}>{container.content}</Container>

      {callToAction && (
        <DialogTrigger
          styleProp={[styles.callToAction, hasMedia && styles.callToActionVideoIcon]}
          id={callToAction.page.sys.id}
          text={callToAction.text}
          variant="transparent"
          theme="transparent"
        >
          <MediaImage
            url={callToAction.icon.url}
            alt={callToAction.icon.fileName}
            width={36}
            height={36}
            layout="intrinsic"
          />
        </DialogTrigger>
      )}
    </Container>
  )
}

export default GridLayout5
