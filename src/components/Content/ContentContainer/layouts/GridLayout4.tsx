import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { spacing } from '@/app/themeTokens.stylex'

import stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  container: {
    containerName: 'card',
    containerType: 'inline-size',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.sm,
    padding: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    textAlign: 'center',
    borderRadius: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    aspectRatio: '368 / 420',
    paddingBlock: spacing.md,
  },
  body: {
    paddingInline: spacing.md,
    maxWidth: {
      default: '35ch',
      '@container card (width > 600px)': '60ch',
    },
  },
  isFirstChild: {
    gridColumn: {
      default: 'span 1',
      [DESKTOP]: 'span 2',
    },
    aspectRatio: {
      default: 'unset',
      [DESKTOP]: '764 / 420',
    },
    minHeight: '100%',
  },
  isLastChild: {
    gridColumn: {
      default: 'span 1',
      [DESKTOP]: 'span 2',
    },
    aspectRatio: '764 / 420',
    minHeight: '100%',
  },
})

const FIRST_CHILD = 0
const LAST_CHILD = 3

function GridLayout4({ container, child }: { container: any, child?: number }) {
  const isFirstChild = child === FIRST_CHILD
  const isLastChild = child === LAST_CHILD
  return (
    <Container
      styleProp={[
        styles.container,
        isFirstChild && styles.isFirstChild,
        isLastChild && styles.isLastChild
      ]}
      key={container.id}
      theme={container.theme}
      size="5"
    >
      <Typography as="h2" typographyTheme="bodyLarge">{container.title}</Typography>
      <Container styleProp={styles.body}>{container.content}</Container>
    </Container>
  )
}

export default GridLayout4
