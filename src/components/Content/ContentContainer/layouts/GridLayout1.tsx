import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { spacing } from '@/app/themeTokens.stylex'

import stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  container: {
    containerName: 'card',
    containerType: 'inline-size',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.sm,
    padding: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    textAlign: 'center',
    paddingBlock: spacing.lg,
    borderRadius: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    aspectRatio: '16 / 9',
  },
  body: {
    paddingInline: spacing.md,
    maxWidth: {
      default: '35ch',
      '@container card (width > 600px)': '80ch',
    },
  },
})

function GridLayout1({ container }: { container: any }) {
  return (
    <Container
      styleProp={styles.container}
      key={container.id}
      theme={container.theme}
      size="5"
    >
      <Typography as="h2" typographyTheme="captionLarge">{container.title}</Typography>
      <Container styleProp={styles.body}>{container.content}</Container>
    </Container>
  )
}

export default GridLayout1
