import Image from 'next/image'

type MediaImageProps = {
  url: string
  alt: string
  width: number
  height: number
  layout?: 'responsive' | 'fill' | 'intrinsic'
}

const MediaImage = ({
  url,
  alt,
  width,
  height,
  layout = 'responsive'
}: MediaImageProps) => (
  <Image
    style={{ display: 'block' }}
    src={url}
    alt={alt}
    width={width}
    height={height}
    layout={layout}
  />
)

export default MediaImage
