import GridLayout1 from './layouts/GridLayout1'
import GridLayout2 from './layouts/GridLayout2'
import GridLayout3 from './layouts/GridLayout3'
import GridLayout4 from './layouts/GridLayout4'
import GridLayout5 from './layouts/GridLayout5'
import GridLayout6 from './layouts/GridLayout6'

import Container from '@/components/layout/Container'
import { spacing } from '@/app/themeTokens.stylex'

import stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const gridLayouts = [
  GridLayout1,
  GridLayout2,
  GridLayout3,
  <PERSON>ridLayout4,
  <PERSON><PERSON><PERSON>ayout5,
  <PERSON>ridLayout6,
]

const ONE_COLUMN = 1
const TWO_COLUMNS = 2
const THREE_COLUMNS = 3

const gridColumns = [ONE_COLUMN, TWO_COLUMNS, TWO_COLUMNS, THREE_COLUMNS, THREE_COLUMNS, THREE_COLUMNS]

const styles = stylex.create({
  wrapper: {
    containerType: 'inline-size',
    display: 'grid',
    gridTemplateColumns: {
      default: '1fr',
      [DESKTOP]: 'repeat(var(--cols, 1), 1fr)',
    },
    gridTemplateRows: 'auto',
    placeItems: 'center',
    gap: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    padding: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    paddingBlock: {
      default: 0,
      [DESKTOP]: spacing.xxl,
    },
    marginInline: 'auto',
    alignItems: {
      default: 'center',
      [DESKTOP]: 'stretch',
    },
  },
})

function ContentContainer({ containers }: { containers: any[] }) {
  if (containers.length === 0 || containers.length > gridLayouts.length) return null

  const LayoutComponent = gridLayouts[containers.length - 1]
  const cols = gridColumns[containers.length - 1]

  return (
    <Container styleProp={styles.wrapper} style={{ '--cols': cols }} size="5">
      {containers.map((container, index) => (
        <LayoutComponent key={container.id} container={container} child={index} />
      ))}
    </Container>
  )
}

export default ContentContainer
