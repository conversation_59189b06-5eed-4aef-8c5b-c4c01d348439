import { baseProps } from '@components/layout/Feature/types'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Feature from '@components/layout/Feature'
import generateSlug from '@/utils/generateSlug'

import { FC } from 'react'

type VariantTypes = 'primary' | 'secondary'

type SlideTextContent = {
  nodeType: string;
  content: { value: string }[];
};

type SlideButtonItem = {
  text?: string;
  customUrl?: string;
  settings: { theme: string };
  _id?: string;
};

type Slide = {
  id?: string;
  text?: { json?: { content?: SlideTextContent[] } };
  button?: { items?: SlideButtonItem[] };
  media?: { items?: { url: string }[] };
};

type FeatureSliderProps = {
  slides: Slide[];
  theme?: ThemeColors;
  layout?: string;
};

const FeatureSlider: FC<FeatureSliderProps> = ({
  slides,
  theme = 'navy',
  layout = 'Layout 1',
}) => {
  const reverseTemplate = layout === 'layout2'
  const currSlideData: baseProps[] = []

  const extractTextContent = (contentItem: any): { header: string; subheader: string; body: string } => {
    let header = ''
    let subheader = ''
    let body = ''

    switch (contentItem.nodeType) {
      case 'heading-1':
        header = contentItem.content[0]?.value || ''
        break
      case 'heading-2':
        subheader = contentItem.content[0]?.value || ''
        break
      default:
        body += contentItem.content[0]?.value || ''
        break
    }

    return { header, subheader, body }
  }

  const getButtonVariant = (themeVariant: string | undefined): VariantTypes => {
    switch (themeVariant?.toLowerCase()) {
      case 'secondary':
        return 'secondary'
      case 'primary':
      default:
        return 'primary'
    }
  }

  const processSlide = (slide: any): baseProps => {
    const id = slide?.id || ''
    let header = ''
    let subheader = ''
    let body = ''

    slide?.text?.json?.content?.forEach((contentItem: any) => {
      const {
        header: h,
        subheader: sh,
        body: b
      } = extractTextContent(contentItem)
      header = h || header
      subheader = sh || subheader
      body += b
    })

    const mainVariant = getButtonVariant(slide?.button?.items?.[0]?.settings?.theme)
    const ctaURL = slide?.button?.items?.[0]?.customUrl || generateSlug(slide?.button?.items?.[0]?.page.slug, slide?.button?.items?.[0]?.page.__typename) || ''
    const ctaAnchor = slide?.button?.items?.[0]?.anchor || null

    return {
      id,
      header,
      subheader,
      body,
      button: {
        anchor: ctaAnchor,
        children: slide?.button?.items?.[0]?.text || '',
        href: ctaURL,
        variant: mainVariant,
        id: slide?.button?.items?.[0]?._id || '',
        useArrow: true,
      },
      image: slide?.media?.items?.[0]?.url || '',
    }
  }
  if (slides.length === 0) {
    return null
  }

  slides.forEach((slide) => {
    const mainSlide = processSlide(slide)
    currSlideData.push(mainSlide)
  })

  return (
    <>
      {slides.length === 1 && (
        <Feature slides={[]} content={currSlideData[0]} theme={theme} type="base" reverse={reverseTemplate} />
      )}
      {slides.length > 1 && (
        <Feature slides={currSlideData} theme={theme} type="slider" reverse={reverseTemplate} />
      )}
    </>
  )
}

export default FeatureSlider
