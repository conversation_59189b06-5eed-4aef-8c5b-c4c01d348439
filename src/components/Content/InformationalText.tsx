import Typography from '../Typography'

import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing, breakpoints } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import RichTextRender from '@/utils/RichTextRenderer'

import * as stylex from '@stylexjs/stylex'

type InformationalTextProps = {
  theme: ThemeColors;
  header: string;
  content?: { json: any };
  slides: Array<{ title: string, content: { json: any } }>;
};

const styles = stylex.create({
  container: {
    padding: {
      default: spacing.sm,
      '@media (min-width: 640px)': spacing.xl,
    }
  },
  wrapper: {
    padding: spacing.md,
    gap: {
      default: spacing.sm,
      '@media (min-width: 640px)': spacing.md,
    },
    maxWidth: breakpoints.xxl,
  },
  bodyWrapper: {
    display: 'flex',
    flexDirection: {
      default: 'column',
      '@media (min-width: 640px)': 'row',
    },
  },
  body: {
    maxWidth: {
      default: 'auto',
      '@media (min-width: 640px)': '40%'
    },
  },
  rightWrapper: {
    maxWidth: {
      default: '100%',
      '@media (min-width: 640px)': '400px'
    },
  },
  inlineStyle: {
    color: 'inherit',
    textAlign: 'left'
  }
})

const settingsMainTextSection = {
  typographyTheme: 'h3Primary'
}

const settingsBlockContentSection = {
  inlineStyle: styles.inlineStyle
}

const InformationalText = ({
  theme,
  header,
  content,
  slides,
}: InformationalTextProps) => (
  <Container as="section" theme={theme} styleProp={styles.container}>
    <Container as="section" theme={theme} flex align="center" styleProp={styles.wrapper}>
      <Typography as="h2" fontBold fontSecondary>
        {header}
      </Typography>
      <Container as="div" styleProp={styles.bodyWrapper} gap="8">
        <Container as="div" styleProp={styles.body}>
          <RichTextRender content={content?.json} settings={settingsMainTextSection} />
        </Container>
        {slides?.map((slide) => (
          <Container as="div" gap="2" flex styleProp={styles.rightWrapper}>
            <Typography as="h2" typographyTheme="h2Secondary" fontBold>
              {slide.title}
            </Typography>
            <Container as="div">
              <RichTextRender content={slide.content?.json} settings={settingsBlockContentSection?.inlineStyle} />
            </Container>
          </Container>
        ))}
      </Container>
    </Container>
  </Container>
)

export default InformationalText
