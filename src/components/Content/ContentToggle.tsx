import AnchorNavigation from './AnchorNavigation'
import AnchorNavigationCollection from './AnchorNavigationCollection'
import AssetSlider from './AssetSlider'
import AtomicLevelTooltip from './AtomicLevelTooltip'
import BlogHeroBanner from './BlogHeroBanner'
import FeatureSlider from './FeatureSlider'
import Hero from './Hero'
import IconLogos from './IconLogos'
import InformationalText from './InformationalText'
import InstructionsListCircle from './InstructionsListCircle'
import Marquee from './Marquee'
import MediaContainer from './MediaContainerWrapper'
import PageCTA from './PageCTA'
import Press from './Press'
import ProductQuote from './ProductQuote'
import Progress from './Progress'
import PromotionalBlockSlider from './PromotionalBlockSlider/PromotionalBlockSlider'
import RichText from './RichText'
import RichTextWithMedia from './RichTextWithMedia'
import Separator from './Separator'
import Tabs from './Tabs'
import Testimonials from './Testimonials'
import TextBanner from './TextBanner'
import Trade from './Trade'
import ValueProps from './ValueProps'
import ZPattern from './ZPattern'
import ThreeBlockModule from './ThreeBlockModule'
import ScrollFeatureSlider from './ScrollFeatureSlider'
import TetrisModule from './TetrisModule'
import MoreArticles from './MoreArticles'
import WhySlider from './WhySlider'
import FeaturedStories from './FeaturedStories'
import WeeklyDeals from './WeeklyDeals'
import HowWeCompareChart from './CompareChart/Chart'
import HowWeCompareItems from './CompareChart/Items'

import TimelineSlider from '../Generic/TimelineSlider'
import CategoriesLarge from '../Generic/CategoriesLarge'
import PromoBanner from '../Generic/PromoBanner'
import Feature from '../layout/Feature'
import Faq from '../layout/Faq'
import CategoriesSmall from '../Generic/CategoriesSmall'
import AccordionSplit from '../Product/AccordionSplit'
import ProductTooltips from '../Product/Tooltips'

export default {
  AnchorNavigation,
  AnchorNavigationCollection,
  AssetSlider,
  AtomicLevelTooltip,
  BlogHeroBanner,
  CategoriesLarge,
  Faq,
  Feature,
  FeatureSlider,
  Hero,
  IconLogos,
  InformationalText,
  InstructionsListCircle,
  Marquee,
  MediaContainer,
  PageCTA,
  Press,
  ProductQuote,
  Progress,
  PromoBanner,
  PromotionalBlockSlider,
  RichText,
  RichTextWithMedia,
  Separator,
  Tabs,
  Testimonials,
  TextBanner,
  Trade,
  ValueProps,
  ZPattern,
  ThreeBlockModule,
  ScrollFeatureSlider,
  TimelineSlider,
  TetrisModule,
  CategoriesSmall,
  AccordionSplit,
  MoreArticles,
  WhySlider,
  ProductTooltips,
  FeaturedStories,
  WeeklyDeals,
  HowWeCompareChart,
  HowWeCompareItems
}
