import Container from '../layout/Container'

import { ThemeColors } from '@/app/themeThemes.stylex'
import { colors, spacing } from '@/app/themeTokens.stylex'
import CallToAction from '@components/Generic/CallToAction'
import Typography from '@components/Typography'
import Wrapper from '@components/layout/Wrapper'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'
import Link from 'next/link'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  root: {
    paddingBlock: spacing.lg,
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: spacing.sm,
    },
  },
  grid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    rowGap: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    },
    columnGap: {
      default: spacing.md,
      [DESKTOP]: spacing.xxl,
    },
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: spacing.sm,
    },
    paddingBlock: spacing.lg,
  },
  card: {
    overflow: 'hidden',
    display: {
      default: 'block',
      [DESKTOP]: 'flex'
    },
    flexDirection: {
      default: 'column',
      [DESKTOP]: 'row'
    },
    gap: spacing.md,
  },
  headerContainer: {
    display: {
      default: 'block',
      [DESKTOP]: 'flex'
    },
  },
  headerBorder: {
    flex: '1',
    height: spacing.xxs,
    borderBlockWidth: 1,
    borderInlineWidth: 0,
    borderColor: colors.gray300,
    borderStyle: 'solid',
    width: '100%'
  },
  imageContainer: {
    flex: '1',
    position: 'relative',
    width: '100%',
    height: 'auto',
    aspectRatio: '16/9',
    marginBottom: {
      default: spacing.md,
      [DESKTOP]: 0
    },
  },
  detailsContainer: {
    flex: '1',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    gap: spacing.xs,
  },
  category: {
    textTransform: 'uppercase'
  },
  actionContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
  },
})

type MoreArticlesProps = {
  header?: string
  articles: {
    image: string,
    category: string,
    title: string,
    id: string,
    url: string
  }[]
  button?: { text: string, url: string }
  theme?: ThemeColors
};

const MoreArticles = ({
  header = 'More Articles',
  articles,
  button,
  theme = 'offWhite',
}: MoreArticlesProps) => (
  <Wrapper theme={theme} as="section" size="xl" styleProp={styles.root}>
    <Container flexRow alignCentered gap="xs" styleProp={styles.headerContainer}>
      <Typography as="h4" typographyTheme="h4Secondary" fontBold>
        {header}
      </Typography>
      <div {...stylex.props(styles.headerBorder)} />
    </Container>
    <Container styleProp={styles.grid}>
      {articles.map((article) => (
        <Link href={article.url} key={`article-${article.id}`}>
          <Container {...stylex.props(styles.card)}>
            <Container styleProp={styles.imageContainer}>
              <Image fill src={article.image} alt={article.title} />
            </Container>
            <Container styleProp={styles.detailsContainer}>
              <Typography
                as="span"
                typographyTheme="subheading"
                styleProp={styles.category}
              >
                {article.category}
              </Typography>
              <Typography as="h6" typographyTheme="h6Secondary" fontBold>
                {article.title}
              </Typography>
            </Container>
          </Container>
        </Link>
      ))}
    </Container>
    {button && (
      <Container flex flexRow flexCentered>
        <CallToAction
          theme="navy"
          useArrow
          href={button.url}
          fullWidthMobile
          variant="secondary"
        >
          {button.text}
        </CallToAction>
      </Container>
    )}
  </Wrapper>
)

export default MoreArticles
