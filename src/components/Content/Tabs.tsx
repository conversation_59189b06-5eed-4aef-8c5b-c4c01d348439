'use client'

import TextBlock from '@components/Generic/TextBlock'
import {
  spacing,
  fontSizes,
  defaultTheme as $T
} from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@components/layout/Container'

import { useState } from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  mainContainer: {
    maxWidth: '1024px',
    margin: '0 auto',
    paddingInline: spacing.md,
  },
  tabContent: {
    display: 'none',
  },
  activeTabContent: {
    display: 'block',
  },
  tabTitle: {
    color: $T.primaryText,
    cursor: 'pointer',
    fontSize: fontSizes.xs,
    paddingBlock: spacing.xxs,
  },
  activeTabTitle: {
    fontWeight: 'bold',
    borderBottomStyle: 'solid',
    borderBottomWidth: '2px',
  },
})

type TabsProps = {
  slides: Array<{ title: string, content: { json: any }, settings: { theme: ThemeColors }, id: string }>;
  theme?: ThemeColors
};

const Tabs = ({ slides, theme = 'white' }: TabsProps) => {
  const [activeTab, setActiveTab] = useState(slides[0].id)

  return (
    <Container
      as="section"
      paddingBlock="4"
      theme={theme}
    >
      <Container
        as="div"
      >
        <Container
          as="div"
          flex
          flexRow
          gap="3"
        >
          {slides.map((slide) => (
            <button
              type="button"
              key={slide.id}
              {...stylex.props(
                styles.tabTitle,
                activeTab === slide.id && styles.activeTabTitle
              )}
              onClick={() => setActiveTab(slide.id)}
            >
              {slide.title}
            </button>
          ))}
        </Container>
        <Container
          as="div"
        >
          {slides?.length > 0 && slides.map((slide) => (
            <Container
              as="div"
              styleProp={[
                styles.tabContent,
                activeTab === slide.id && styles.activeTabContent,
              ]}
            >
              <TextBlock
                theme={slide?.settings?.theme}
              >
                {slide?.content?.json || slide?.content }
              </TextBlock>
            </Container>
          ))}
        </Container>
      </Container>
    </Container>
  )
}

export default Tabs
