import {
  globalTokens as $G,
  spacing,
  fontSizes,
} from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@components/layout/Container'
import RenderIf from '@components/Generic/RenderIf'
import Typography from '@components/Typography'
import RenderCTAS from '@components/Generic/RenderCTAS'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  wrapperContainer: {
    maxWidth: $G.maxWidth,
    paddingBlock: spacing.lg,
    paddingInline: spacing.md,
    flexDirection: {
      default: 'column',
      [DESKTOP]: 'row',
    },
    margin: '0 auto',
    gap: {
      default: '30px',
      [DESKTOP]: '32px',
    }
  },
  headerWrapper: {
    maxWidth: {
      default: null,
      [DESKTOP]: '215px',
    },
    textAlign: {
      default: 'center',
      [DESKTOP]: 'left',
    },
  },
  slideContainer: {
    maxWidth: '380px',
    position: 'relative',
  },
  slideValueWrapper: {
    position: 'absolute',
    top: '0',
    left: '0',
    background: '#E2DCEC',
    paddingBlock: spacing.sm,
    paddingInline: spacing.sm,
    borderRadius: '43px',
  },
  slideTitleWrapper: {
    flexDirection: {
      default: 'row',
      [DESKTOP]: 'column',
    },
    gap: {
      default: spacing.xxs,
      [DESKTOP]: null
    },
    alignItems: {
      default: 'center',
      [DESKTOP]: 'flex-start',
    }
  },
  slideSubtitle: {
    textTransform: {
      default: 'none',
      [DESKTOP]: 'uppercase',
    }
  },
  cta: {
    padding: null,
    fontWeight: 'normal',
    fontSize: fontSizes.captionLarge,
    textDecoration: 'underline',
    marginTop: spacing.sm,
    width: 'fit-content',
  },
  desktopCTA: {
    display: {
      default: 'none',
      [DESKTOP]: 'block',
    }
  },
  mobileCTA: {
    display: {
      default: 'block',
      [DESKTOP]: 'none',
    },
    marginTop: null,
  }
})

  type BannerWithImagesProps = {
    header?: string;
    slides: Array<any>;
    button?: any;
    theme?: ThemeColors;
  };

const BannerWithImages = ({
  slides,
  header,
  button,
  theme = 'navy'
}: BannerWithImagesProps) => {
  const updatedButton = { ...button, variant: 'transparent' }
  return (
    <Container as="section" theme={theme}>
      <Container
        flex
        flexCentered
        alignCentered
        noWrap
        as="div"
        styleProp={styles.wrapperContainer}
      >
        <Container as="div" styleProp={styles.headerWrapper}>
          <RenderIf condition={header !== ''}>
            <Typography as="h4" typographyTheme="h5Secondary">
              {header}
            </Typography>
          </RenderIf>
          <RenderIf condition={button}>
            <RenderCTAS
              buttons={Array.isArray(updatedButton) ? updatedButton : [updatedButton]}
              styleProp={[styles.cta, styles.desktopCTA]}
            />
          </RenderIf>
        </Container>
        <RenderIf condition={slides.length > 0}>
          <Container as="div" justifyContentCenter flex flexRow gap="4">
            {slides.map((slide: any) => (
              <Container
                key={slide.id}
                as="div"
                flex
                flexRow
                alignCentered
                noWrap
                gap="2"
                styleProp={styles.slideContainer}
              >
                <RenderIf condition={slide.total}>
                  <Container
                    as="div"
                    flex
                    alignCentered
                    styleProp={styles.slideValueWrapper}
                  >
                    <Typography as="p" fontBold typographyTheme="captionSmall">
                      ${slide.total}
                    </Typography>
                    <Typography as="span" typographyTheme="captionSmall">
                      Value
                    </Typography>
                  </Container>
                </RenderIf>
                <RenderIf condition={slide.image}>
                  <Image
                    src={slide.image.url}
                    alt={slide.image.title}
                    width={slide.image.width}
                    height={slide.image.height}
                  />
                </RenderIf>
                <Container as="div" flex>
                  <Container as="div" flex styleProp={styles.slideTitleWrapper}>
                    <RenderIf condition={slide.subtitle}>
                      <Typography
                        as="p"
                        typographyTheme="captionLarge"
                        typographyThemeMobile="h6Primary"
                        styleProp={styles.slideSubtitle}
                      >
                        {slide.subtitle}
                      </Typography>
                    </RenderIf>
                    <RenderIf condition={slide.title}>
                      <Typography as="h5" typographyTheme="h6Primary">
                        {slide.title}
                      </Typography>
                    </RenderIf>
                  </Container>
                  <RenderIf condition={slide.text}>
                    <Container as="div">
                      {slide.text}
                    </Container>
                  </RenderIf>
                </Container>
              </Container>
            ))}
          </Container>
        </RenderIf>
        <RenderIf condition={button}>
          <RenderCTAS
            buttons={Array.isArray(updatedButton) ? updatedButton : [updatedButton]}
            styleProp={[styles.cta, styles.mobileCTA]}
          />
        </RenderIf>
      </Container>
    </Container>
  )
}

export default BannerWithImages
