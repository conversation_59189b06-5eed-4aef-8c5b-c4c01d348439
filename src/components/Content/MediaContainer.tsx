'use client'

import Container from '@components/layout/Container'
import MediaVideo from '@components/Generic/MediaVideo'
import getMediaType from '@/utils/getMediaType'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  container: {
    position: 'relative',
    maxHeight: {
      default: '600px',
      '@media screen and (max-width: 768px)': '300px',
    }
  },
})

const DEFAULT_ICON_SIZE = 19

export type AssetType = {
  url: string;
  width?: number;
  height?: number;
  description?: string;
  title?: string;
};

type MediaContainerProps = {
  iconOnTopCorner?: boolean;
  objectFit?: 'cover' | 'contain' | 'fill' | 'initial';
  objectPosition?: 'bottom' | 'center' | 'left' | 'right' | 'top' | 'initial';
  position?: 'absolute' | 'relative' | 'fixed';
  header?: string;
  asset: AssetType;
  imageHeight?: string;
  imageHeightMobile?: string;
  autoPlay?: boolean;
  setIsPlaying?: boolean,
  styleProps?: {};
  stylePropMedia?: {};
  onPlayerChangeState?: (playing: boolean) => void;
  iconSize?: number;
  fixedIcon?: boolean;
  soundIcon?: boolean;
  playIcon?: boolean;
};

const MediaContainer = ({
  iconOnTopCorner = false,
  objectFit = 'cover',
  objectPosition = 'initial',
  position = 'absolute',
  header,
  imageHeight,
  imageHeightMobile,
  asset = {
    url: '',
  },
  styleProps = {},
  setIsPlaying = false,
  autoPlay = true,
  onPlayerChangeState = () => {},
  iconSize = DEFAULT_ICON_SIZE,
  fixedIcon = false,
  soundIcon = false,
  playIcon = true,
  stylePropMedia = {},
}: MediaContainerProps) => {
  const source = asset?.url || ''

  const hasAudio = getMediaType(source) === 'video'
  const displaySoundIcon = soundIcon && hasAudio

  return (
    <Container
      as="section"
      styleProp={[styles.container, styleProps]}
    >
      <MediaVideo
        iconOnTopCorner={iconOnTopCorner}
        name={header}
        media={source}
        setPlaying={setIsPlaying}
        autoPlay={autoPlay}
        setIsPlaying={setIsPlaying}
        soundIcon={displaySoundIcon}
        imageHeight={imageHeight}
        imageHeightMobile={imageHeightMobile}
        onPlayerChangeState={onPlayerChangeState}
        iconSize={iconSize}
        fixedIcon={fixedIcon}
        objectFit={objectFit}
        objectPosition={objectPosition}
        playIcon={playIcon}
        position={position}
        styleProp={stylePropMedia}
      />
    </Container>
  )
}

export default MediaContainer
