'use client'

import useStickyNav from '@components/layout/Header/hooks'
import PillFilter from '@components/Generic/PillFilter'
import Container from '@components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { globalTokens as $ } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

type PillProps = {
  text:string
  href: string
};

const styles = stylex.create({
  base: {
    boxSizing: 'border-box',
    margin: 0,
    minWidth: 0,
    width: '100%',
    maxWidth: 'unset',
    padding: {
      default: 0,
      '@media (min-width: 1024px)': '0 20px',
    },
    overflow: 'hidden',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  swiper: {
    overflowX: 'scroll',
    scrollbarWidth: 'none',
    margin: '1rem 0',
    maxWidth: '100%',
    listStyle: 'none',
    marginRight: 'auto',
    position: 'relative',
    display: 'flex',
    justifyContent: {
      default: 'flex-start',
      '@media (min-width: 1024px)': 'center',
    },
    zIndex: 1,
    gap: {
      default: '10px',
      '@media (min-width: 1024px)': '30px',
    },
    boxSizing: 'border-box',
    height: '100%',
    transitionProperty: 'transform',
    width: '100%',
    transform: 'translate3d(0px, 0px, 0px)',
    transitionDuration: '0ms',
    transitionTimingFunction: 'ease-out',
    paddingInline: {
      default: '20px',
      '@media (min-width: 1024px)': '0',
    }
  },
  stickyNav: {
    position: 'sticky',
    top: {
      default: `calc(${$.promoBarHeight} * 1.48)`,
      '@media (min-width: 1024px)': `calc(${$.promoBarHeight} * 1.85)`,
    },
    zIndex: 10,
    boxShadow: $.boxShadow,
    transition: $.transitionSmooth,
    transform: 'translateY(0)',
  },
  stickyNavActive: {
    top: {
      default: `calc(${$.promoBarHeight} * 2.48)`,
      '@media (min-width: 1024px)': `calc(${$.promoBarHeight} * 4.52)`,
    },
  },
})

const AnchorNavigation = ({
  theme = 'rustDark',
  slides,
  layout
}: {
  theme: ThemeColors
  slides: PillProps[]
  layout?: string
}) => {
  const { isSticky } = useStickyNav()
  return (
    <Container
      as="div"
      theme={theme}
      flex
      styleProp={[
        styles.base,
        layout === 'layout2' && styles.stickyNav,
        layout === 'layout2' && !isSticky && styles.stickyNavActive
      ]}
    >
      <Container as="div" flex flexRow noWrap styleProp={styles.swiper}>
        {
          slides.length > 0 && slides.map((item) => (
            <PillFilter key={item.href} theme={theme} pill={item} />
          ))
        }
      </Container>
    </Container>
  )
}

export default AnchorNavigation
