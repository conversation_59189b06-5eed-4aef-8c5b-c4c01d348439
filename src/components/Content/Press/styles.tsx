import {
  defaultTheme as $T,
  spacing,
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  container: {
    display: 'block',
    padding: {
      default: `${spacing.lg} calc(${spacing.xs} * 4)`,
      '@media (min-width: 1024px)': `${spacing.xl}`,
    }
  },
  grid: {
    margin: '0 auto',
    maxWidth: '997px',
    display: {
      default: 'block',
      '@media (min-width: 1024px)': 'grid',
    }
  },
  simpliedGrid: {
    margin: '0 auto',
    maxWidth: '600px'
  },
  gridWithBorder: {
    gridTemplateColumns: {
      default: '1fr',
      '@media (min-width: 1024px)': '309px 1px 1fr',
    },
    gap: spacing.xl,
  },
  gridWithoutBorder: {
    gridTemplateColumns: {
      default: '1fr',
      '@media (min-width: 1024px)': '309px 1fr',
    },
    gap: {
      default: spacing.xl,
      '@media (min-width: 1024px)': spacing.lg,
    }
  },
  leftContainer: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    alignItems: {
      default: 'center',
      '@media (min-width: 1024px)': 'flex-start',
    },
    marginBottom: {
      default: spacing.xl,
      '@media (min-width: 1024px)': '0',
    },
    gap: spacing.sm,
  },
  rightContainer: {
    margin: {
      default: '0',
      '@media (min-width: 1024px)': `${spacing.sm} 0`,
    }
  },
  verticalBorder: {
    borderLeftColor: $T.primaryText,
    borderLeftWidth: '1px',
    borderLeftStyle: 'solid',
  },
  horizontalBorder: {
    width: '184px',
    height: '0px',
    borderBottomColor: $T.primaryText,
    borderBottomWidth: '1px',
    borderBottomStyle: 'solid',
    margin: `${spacing.sm} auto`,
  },
  title: {
    textAlign: {
      default: 'center',
      '@media (min-width: 1024px)': 'left',
    },
  },
  seAllLink: {
    display: 'inline-block',
    paddingTop: spacing.xxs,
  },
  slide: {
    textAlign: 'center',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  sliderPagination: {
    marginTop: '32px',
    display: 'flex',
    gap: spacing.lg,
  },
  paginationOpacity: {
    opacity: {
      default: '0.5',
      ':hover': '1',
    },
  },
  mobileImage: {
    display: 'block',
    margin: '32px auto',
    color: $T.primaryText,
  },
  icon: {
    color: $T.primaryText,
    maxWidth: '100%',
  },
  arrows: {
    position: 'absolute',
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
  },
  simpleDotbase: {
    display: 'inline-block',
    width: spacing.xs,
    height: spacing.xs,
    borderRadius: '50%',
    textIndent: '-9999px',
    cursor: 'pointer',
    backgroundColor: $T.indicatorInactive,
  },
  simpleSelectedDots: {
    backgroundColor: $T.indicatorActive,
  },
  simplesArrows: {
    display: 'flex',
    justifyContent: 'space-between',
    minHeight: spacing.lg,
    margin: '32px 0'
  },
})

export default styles
