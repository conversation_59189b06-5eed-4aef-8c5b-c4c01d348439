'use client'

import styles from './styles'

import Icon from '@/components/Generic/Icon'
import Rating, { RatingProps } from '@/components/Generic/Rating'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import Slider, { RenderDotsFunction, RenderPrevNextButtons } from '@/components/Generic/CustomSlider'
import {
  condition, empty, equals, notEmpty
} from '@/utils/checking'
import useIsMobile from '@/hooks/isMobile'
import RenderIf from '@/utils/renderIf'

import * as stylex from '@stylexjs/stylex'
import Link from 'next/link'
import { EmblaOptionsType, EmblaPluginType } from 'embla-carousel'
import Autoplay from 'embla-carousel-autoplay'
import Image, { ImageProps } from 'next/image'
import { ReactNode } from 'react'

export type PressSlide = {
  id: string;
  text: string;
  image: {
    width: number;
    height: number;
    src: string;
    isSvg: boolean;
  } | null;
}

export type TypeFaceQuote = 'primaryFontFamily' | 'secondaryFontFamily';

type SliderImage = (props: ImageProps) => ReactNode

type PressProps = {
  withBorder?: boolean;
  quoteTypeFace?: TypeFaceQuote;
  theme?: ThemeColors;
  slides: PressSlide[];
  slideOptions?: EmblaOptionsType;
  sliderDelay?: number;
  sliderAutoPlay?: boolean;
  header: string;
  ratingProps: RatingProps;
  linkToReviews: string;
  linkLabel?: string;
}

const FIVE_SECONDS = 5000

/* eslint-disable-next-line complexity */
const Press = ({
  withBorder = false,
  quoteTypeFace = 'primaryFontFamily',
  theme = 'cream',
  slides = [],
  slideOptions = { slidesToScroll: 'auto' },
  sliderDelay = FIVE_SECONDS,
  sliderAutoPlay = true,
  header = '',
  ratingProps = {},
  linkToReviews = '/review',
  linkLabel = 'See All →',
}:PressProps) => {
  const {
    textContentOn = 'right',
    rating,
    reviewCount,
    iconColor = 'black',
    size = 'small',
    textSize = 'xxs',
  } = ratingProps as RatingProps
  const { isMobile, isReady } = useIsMobile()

  const borderStyle = condition<{}>(isMobile, styles.horizontalBorder, styles.verticalBorder)

  const mainContentBorderStyle = condition<{}>(withBorder, styles.gridWithBorder, styles.gridWithoutBorder)

  const sliderPlugins = condition<EmblaPluginType[]>(
    sliderAutoPlay,
    [Autoplay({ playOnInit: true, delay: sliderDelay })],
    [],
  )

  const safeRatingContentTextOn = textContentOn || condition<'left' | 'right'>(isMobile, 'right', 'left')
  const simplyfiedVersion = condition<boolean>(empty(header), true, false)
  const gridStyles = condition<{}>(simplyfiedVersion, [styles.simpliedGrid], [styles.grid, mainContentBorderStyle])
  const dotsStyles = condition<{}>(simplyfiedVersion, [styles.simpleDotbase], undefined)
  const selectedDotsStyles = condition<{}>(simplyfiedVersion, [styles.simpleDotbase, styles.simpleSelectedDots], undefined)

  const renderDesktopDots: RenderDotsFunction = ({
    onDotButtonClick,
    scrollSnaps,
    selectedIndex
  }) => (
    <Container as="nav" styleProp={styles.sliderPagination}>
      {scrollSnaps.map((_, i) => {
        const {
          id,
          image,
          text
        } = slides[i]
        const isSelected = equals(i, selectedIndex)
        const ImageComponent = condition<'img' | React.FC<ImageProps>>(Boolean(image?.isSvg), 'img', Image,)

        return (
          <button
            {
            ...stylex.props(
              !isSelected && styles.paginationOpacity,
            )
          }
            type="button"
            onClick={() => onDotButtonClick(i)}
            key={id}
          >

            <RenderIf condition={notEmpty(image)}>
              <ImageComponent
                src={condition<string>(Boolean(image?.src), image?.src, '')}
                width={image?.width}
                height={image?.height}
                alt={text}
                {...stylex.props(styles.icon)}
              />{' '}
            </RenderIf>
          </button>
        )
      })}

    </Container>
  )

  const renderSimplifiedArrows: RenderPrevNextButtons = ({
    onPrevButtonClick,
    prevBtnDisabled,
    nextBtnDisabled,
    onNextButtonClick,
    selectedIndex
  }) => {
    const { text, image } = slides[selectedIndex]

    const ImageComponent = condition<'img' | React.FC<ImageProps>>(Boolean(image?.isSvg), 'img', Image,)
    return (
      <Container as="div" styleProp={styles.simplesArrows}>
        <button onClick={onPrevButtonClick} disabled={prevBtnDisabled} type="button" aria-label="Go to previus slide">
          <Icon name="LeftArrow" size="large" />
        </button>
        <RenderIf condition={notEmpty(image)}>
          <ImageComponent
            src={condition<string>(Boolean(image?.src), image?.src, '')}
            width={image?.width}
            height={image?.height}
            alt={text}
            {...stylex.props(styles.icon)}
          />
        </RenderIf>
        <button onClick={onNextButtonClick} disabled={nextBtnDisabled} type="button" aria-label="Go to next slide">
          <Icon name="RightArrow" size="large" />
        </button>
      </Container>
    )
  }

  const renderArrows = condition<any>(simplyfiedVersion && isMobile, renderSimplifiedArrows, () => null)
  const renderDotsButtons = condition<any>(isMobile, undefined, renderDesktopDots)
  const quoteTypefaceSecondary = condition<boolean>(equals(quoteTypeFace, 'secondaryFontFamily'), true, false)

  return (
    <Container as="section" theme={theme} styleProp={styles.container}>
      <Container as="div" styleProp={gridStyles}>
        <RenderIf condition={!simplyfiedVersion}>
          <Container as="div" styleProp={styles.leftContainer}>
            <Typography as="h2" typographyTheme="h3Primary" styleProp={styles.title}>
              {header}
            </Typography>
            <RenderIf condition={isReady}>
              <Rating
                withoutReviews={!reviewCount}
                withoutRating={!rating}
                textContentOn={safeRatingContentTextOn}
                rating={rating}
                reviewCount={reviewCount}
                iconColor={iconColor}
                size={size}
                textSize={textSize}
                isAggregated
              />
            </RenderIf>
            <Link href={linkToReviews}>
              <Typography as="span" size="sm" underline styleProp={styles.seAllLink}>
                { linkLabel }
              </Typography>
            </Link>
          </Container>
        </RenderIf>
        <RenderIf condition={withBorder && isReady}>
          <div {...stylex.props(borderStyle)} />
        </RenderIf>
        <Container as="div" styleProp={styles.rightContainer}>
          <Slider
            plugins={sliderPlugins}
            dotsStyles={dotsStyles}
            selectedDotsStyles={selectedDotsStyles}
            extractSlideKey={({ id }) => id}
            renderDotsButtons={renderDotsButtons}
            renderPrevNextButtons={renderArrows}
            renderSlide={
              ({
                text,
                id,
                image
              }: PressSlide) => {
                const ImageComponent = condition<'img' | SliderImage>(Boolean(image?.isSvg), 'img', Image,)
                return (
                  <Container
                    align="center"
                    as="div"
                    key={id}
                    styleProp={styles.slide}
                  >
                    <Typography fontSecondary={quoteTypefaceSecondary} as="span" size="h6" lineHeight="md" textCentered>
                      {text}
                    </Typography>
                    <RenderIf condition={isMobile}>
                      <RenderIf condition={!simplyfiedVersion && notEmpty(image)}>
                        <ImageComponent
                          src={condition<string>(Boolean(image?.src), image?.src, '')}
                          width={image?.width}
                          height={image?.height}
                          alt={text}
                          {...stylex.props(styles.mobileImage)}
                        />
                      </RenderIf>
                    </RenderIf>
                  </Container>
                )
              }
            }
            slidesPerPage={1}
            slides={slides}
            options={slideOptions}
          />
        </Container>
      </Container>
    </Container>
  )
}

export default Press
