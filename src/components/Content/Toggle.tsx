'use client'

import PageSections from '../PageSectionsToggle'

import ToggleSwitcher from '@/components/Generic/Toggle'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import { useState } from 'react'

type PageSectionsCollection = {
  sections: {
    header?: string;
    title?: string;
  }[];
};

type ToggleProps = {
  theme: ThemeColors;
  pageSectionsCollection: PageSectionsCollection;
  header?: string;
};

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  wrapper: {
    position: 'relative',
    width: '100%',
    alignSelf: 'center',
    padding: {
      default: '40px 0px',
      [DESKTOP]: '60px 0px',
    },
  },
  toggleContainer: {
    margin: {
      default: `calc(${spacing.md} + ${spacing.sm}) ${spacing.sm} ${spacing.xl} `,
      [DESKTOP]: `${spacing.lg} 0 ${spacing.xl}`,
    }
  },
  toggle: {
    width: '100%',
    height: {
      default: 'auto',
      '@media (min-width: 400px)': '46px',
    },
  },
  title: {
    paddingInline: spacing.sm,
    textAlign: {
      default: 'left',
      [DESKTOP]: 'center',
    },
  },
})

function normalizeProps(section: any) {
  return {
    ...section, header: null, title: null, containerWithouPadding: true
  }
}

const Toggle = ({
  theme = 'gray200',
  pageSectionsCollection = { sections: [] },
  header
}: ToggleProps) => {
  const { sections } = pageSectionsCollection
  const [activeOptionIndex, setActiveOptionIndex] = useState(0)

  const handleSwitchOption = (index: number) => {
    setActiveOptionIndex(index)
  }
  const getSectionHeader = (section: any) => section.header || section.title

  const opts = sections.map((sec) => ({
    title: getSectionHeader(sec),
    id: getSectionHeader(sec),
  }))

  const currentSection = sections[activeOptionIndex]
  const curentHeader = getSectionHeader(currentSection)

  return (
    <Container as="section" theme={theme} styleProp={styles.wrapper}>
      {header && <Typography as="h2" typographyTheme="h2Secondary" typographyThemeMobile="h3Secondary" styleProp={styles.title} fontBold marginBottom="md">{header}</Typography>}
      <Container as="div" styleProp={styles.toggleContainer}>
        <ToggleSwitcher
          theme="white"
          optionsList={opts}
          handleChange={handleSwitchOption}
          activeOption={curentHeader}
          styleProp={styles.toggle}
          outlineWhite
        />
      </Container>

      <PageSections sections={[normalizeProps(sections[activeOptionIndex])]} />
    </Container>
  )
}

export default Toggle
