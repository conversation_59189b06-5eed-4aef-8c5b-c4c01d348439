'use client'

import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { colors, spacing, globalTokens as $ } from '@/app/themeTokens.stylex'
import RenderIf from '@/utils/renderIf'
import { notEmpty } from '@/utils/checking'
import CallToAction from '@/components/Generic/CallToAction'
import useIsMobile from '@/hooks/isMobile'
import { getPriceInstallments } from '@/utils/installments'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Icon from '@/components/Generic/Icon'
import RichTextRenderer from '@/utils/RichTextRenderer'
import { toCamelCase } from '@/utils/regex'
import SimpleCallout from '@/components/Generic/Callout/SimpleCallout'
import debounce from '@/utils/debounce'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import {
  BaseSyntheticEvent, ForwardedRef, useCallback, useEffect, useRef, useState
} from 'react'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  container: {
    padding: {
      default: `${spacing.lg} ${spacing.md}`,
      [DESKTOP]: `${spacing.xxl} 0`,
    },
  },
  product: {
    scrollSnapAlign: 'center',
    position: 'relative',
    display: {
      default: 'initial',
      [DESKTOP]: 'grid',
    },
    gridTemplateColumns: {
      default: 'initial',
      [DESKTOP]: '1fr',
    },
    gridTemplateRows: {
      default: 'initial',
      [DESKTOP]: 'subgrid',
    },
    justifyContent: {
      default: 'center',
    },
    gridRow: {
      default: 'initial',
      [DESKTOP]: 'span 12',
    },
    padding: {
      default: `${spacing.lg} ${spacing.sm} ${spacing.sm} ${spacing.sm}`,
      [DESKTOP]: `calc(${spacing.lg} + ${spacing.xxs}) ${spacing.md} ${spacing.xl} ${spacing.md}`,
    },
    textAlign: {
      default: 'left',
      [DESKTOP]: 'center',
    },
    maxWidth: {
      default: '165px',
      [DESKTOP]: '320px',
    },
    minWidth: '165px',
  },
  currentBorder: {
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.navy,
    backgroundColor: colors.offWhite,
    borderRadius: $.borderRadius,
  },
  currentLabel: {
    position: 'absolute',
    padding: {
      default: `${spacing.xxs} ${spacing.xs}`,
      [DESKTOP]: `${spacing.xxs} ${spacing.sm}`,
    },
    top: '-14px',
    left: '0',
    right: '0',
    width: 'fit-content',
    margin: '0 auto'
  },
  compareItem: {
    paddingTop: {
      default: `calc(${spacing.md} + ${spacing.xs})`,
      [DESKTOP]: spacing.lg,
    },
  },
  compareItemFirst: {
    paddingTop: {
      default: `calc(${spacing.md} + ${spacing.xs})`,
      [DESKTOP]: spacing.md,
    },
  },
  icon: {
    display: 'block',
    margin: {
      default: `0 0 ${spacing.xxs}`,
      [DESKTOP]: `0 auto ${spacing.md} auto`,
    },
  },
  row: {
    maxWidth: '230px',
    margin: '0 auto',
  },
  shopNow: {
    marginBottom: spacing.md
  },
  minHeight: {
    minHeight: '19px'
  },
  productImage: {
    maxWidth: '100%',
    height: 'auto',
    paddingBottom: {
      default: spacing.sm,
      [DESKTOP]: '0',
    },
    margin: 'auto',
  },
  scrollContainer: {
    position: 'relative',

  },
  mobileScroll: {
    position: 'relative',
    display: 'grid',
    gridAutoFlow: 'column',
    scrollSnapType: 'x mandatory',
    overflowX: 'auto'
  },
  price: {
    display: 'block',
    margin: '0 auto',
    maxWidth: '150px',
    minHeight: {
      default: '60px',
      [DESKTOP]: 'initial',
    },
  },
  textWrap: {
    maxWidth: '170px',
    margin: '0 auto'
  },
  arrowsContainer: {
    position: 'absolute',
    left: '165px',
    right: '0',
    bottom: '0',
    height: '10px',
  },
  arrow: {
    display: {
      default: 'none',
      '@media (max-width: 419px)': 'block'
    },
    position: 'absolute',
    bottom: '0',
  },
  arrowRight: {
    right: '0',
  },
  arrowLeft: {
    left: spacing.md,
  },
  title: {
    margin: '0',
  },
  description: {
    padding: {
      default: `${spacing.md} 0`,
      [DESKTOP]: `${spacing.lg} 0`,
    }
  },
  installments: {
    margin: '0 auto',
    paddingBottom: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    }
  },
  separator: {
    width: '100%',
    maxWidth: '235px',
    margin: `${spacing.md} auto`,
    backgroundColor: colors.navy,
    height: '1px',
    opacity: 0.2,
  },
  gridWrapper: {
    display: 'flex',
    justifyContent: {
      default: 'flex-start',
      [DESKTOP]: 'center',
    },
  },
})

type Product = {
  title: string;
  productId: string;
  compare: {
    title?: string;
    description: string;
    callout: {
      title: string;
      settings: {
        theme: string;
      }
    },
    itemsCollection: {
      items: {
        title: string;
        content: {
          json: any;
        };
        assetsCollection: {
          items: {
            url: string;
            width: number;
            height: number;
            title: string;
          }[]
        }
      }[]
    };
    selectedAsset: {
      url: string;
      width: number;
      height: number;
      title: string;
    };
    notSelectedAsset: {
      url: string;
      width: number;
      height: number;
      title: string;
    };
  }
  variants: {
    items: {
      price: number;
    }[]
  }
  slug: string;
}

type HowWeCompareItemsProps = {
  products: Product[];
  title?: string;
  theme: ThemeColors;
  containerWithouPadding?: boolean;
  settings?: {
    anchorTargeting?: string
  }
}

type SyncRowProps = {
  children: React.ReactNode;
  onReady: (ref: HTMLDivElement, key: string) => void;
  indentifier: string;
  height: number;
  styleProp?: {};
}

const SyncRow = ({
  children,
  onReady,
  indentifier,
  height,
  styleProp
}: SyncRowProps) => {
  const ref = useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (ref.current) {
      onReady(ref.current, indentifier)
    }
  }, [])
  return (
    <div ref={ref} {...stylex.props(styleProp)} style={{ height }}>
      {children}
    </div>
  )
}

const DEBOUNCE_TIME = 500
const RENDER_DELAY = 250
const MIN_PRODUCTS_TO_SCROLL = 2
const HowWeCompareItems = (props: HowWeCompareItemsProps) => {
  const {
    products,
    title: header,
    theme = 'gray200',
    containerWithouPadding = false,
    settings
  } = props

  const rowsRef = useRef<{ [key: string]: HTMLDivElement | null }>({})
  const [rowsConfig, setRowsConfig] = useState<{ [key: string]: number }>({})

  const firstChildRef = useRef<HTMLDivElement>(null)
  const scrollRef = useRef<HTMLDivElement>(null)
  const [displayArrowRight, setDisplayArrowRight] = useState(
    products.length > MIN_PRODUCTS_TO_SCROLL
  )
  const [displayArrowLeft, setDisplayArrowLeft] = useState(false)
  const { isMobile } = useIsMobile()

  const registerRow = (ref: HTMLDivElement, key: string) => {
    rowsRef.current[key] = ref
  }

  const onResize = useCallback(debounce(() => {
    if (!isMobile) {
      return
    }

    setRowsConfig({})

    setTimeout(() => {
      const rowKeys = Object.keys(rowsRef.current)
      const rowsInColumns = rowKeys.length / products.length

      const rowsNames = rowKeys.reduce(
        (acc: any, key) => {
          const name = key.split('-')[1]
          if (!acc[name]) {
            acc[name] = true
          }

          return acc
        },
        {}
      )

      const rowNamesAsArray = Object.keys(rowsNames)
      const maxHeightsPerRowTipe = rowNamesAsArray.reduce((acc: { [key: string]: number }, rowType) => {
        for (let rowIndex = 0; rowIndex < rowsInColumns; rowIndex += 1) {
          const key = `${rowIndex}-${rowType}`
          const row = rowsRef.current[key]
          if (row) {
            const height = row.offsetHeight
            acc[rowType] = acc[rowType] ? Math.max(acc[rowType], height) : height
          }
        }

        return acc
      }, { })

      setRowsConfig(maxHeightsPerRowTipe)
    }, RENDER_DELAY)
  }, DEBOUNCE_TIME), [isMobile])

  useEffect(() => {
    onResize()
    window.addEventListener('resize', onResize)

    return () => {
      window.removeEventListener('resize', onResize)
    }
  }, [isMobile, onResize])

  useEffect(() => {
    if (!isMobile) {
      setRowsConfig({})
    }
  }, [isMobile])

  const renderProduct = (product: Product, index: number, ref: ForwardedRef<HTMLDivElement>) => {
    const isCurrentProduct = index === 0
    const compareItems = product.compare?.itemsCollection.items

    if (product.compare) {
      const asset = isCurrentProduct ? product.compare.selectedAsset : product.compare.notSelectedAsset
      const { price } = product.variants.items[0]

      const installments = getPriceInstallments(
        product.variants.items
      )

      const { callout } = product.compare
      const callOutTheme = notEmpty(callout?.settings?.theme)
        ? toCamelCase(callout.settings.theme) as keyof typeof colors
        : 'navy'

      return (
        <div
          ref={ref}
          key={product.productId}
          {...stylex.props(
            styles.product,
            isCurrentProduct && styles.currentBorder
          )}
        >

          <RenderIf condition={notEmpty(asset)}>
            <Image src={asset?.url} alt={asset?.title} width="212" height="130" {...stylex.props(styles.productImage)} />
          </RenderIf>
          <Container styleProp={styles.minHeight}>
            {callout?.title && (
            <SimpleCallout
              theme={callOutTheme}
              title={callout.title}
            />
            )}
          </Container>

          <SyncRow onReady={registerRow} indentifier={`${index}-title`} height={rowsConfig.title} styleProp={styles.title}>
            <Typography as="h6" typographyTheme="h6Secondary" typographyThemeMobile="h6Secondary" fontSecondary fontBold>
              { product.compare.title || product.title }
            </Typography>
          </SyncRow>

          <Container styleProp={[styles.row, styles.description]}>
            <SyncRow onReady={registerRow} indentifier={`${index}-description`} height={rowsConfig.description}>
              <Typography as="p" typographyTheme="bodyLarge" typographyThemeMobile="captionLarge">
                {product.compare.description}
              </Typography>
            </SyncRow>
          </Container>

          <Container styleProp={[styles.row, styles.installments]}>
            <SyncRow onReady={registerRow} indentifier={`${index}-installments`} height={rowsConfig.installments}>
              <Typography as="span" typographyTheme="bodyLarge" typographyThemeMobile="captionLarge" fontSecondary styleProp={[styles.price]}>
                <RenderIf condition={notEmpty(installments)}>
                  From ${installments?.price} or ${installments?.periodicPrice}/{installments?.periodLabel}
                </RenderIf>
                <RenderIf condition={!installments}>
                  ${price}
                </RenderIf>
              </Typography>
            </SyncRow>
          </Container>

          <Container styleProp={[styles.row, styles.shopNow]}>
            <CallToAction variant="tertiary" href={`/products/${product.slug}`}>
              Shop Now
            </CallToAction>
          </Container>
          <div {...stylex.props(styles.separator)} />
          {
              compareItems.length && compareItems.map(({
                assetsCollection,
                content,
                title
              }, i) => {
                const rowType = `dynamic_${i}`
                const rowKey = `${index}-${rowType}`

                return (
                  <Container styleProp={[styles.compareItem, i === 0 && styles.compareItemFirst]} key={title}>
                    <SyncRow onReady={registerRow} indentifier={`${rowKey}`} height={rowsConfig[rowType]}>

                      <Container as="div" styleProp={styles.textWrap}>
                        {assetsCollection.items[0] && (
                        <Image
                          src={assetsCollection.items[0].url}
                          alt={assetsCollection.items[0].title}
                          width={assetsCollection.items[0].width}
                          height={assetsCollection.items[0].height}
                          {...stylex.props(styles.icon)}
                        />
                        )}
                      </Container>
                      <RenderIf condition={notEmpty(title)}>
                        <Typography as="span" typographyTheme="h5Primary" typographyThemeMobile="h6Primary" styleProp={styles.textWrap}>
                          {title}
                        </Typography>
                      </RenderIf>
                      <RenderIf condition={notEmpty(content)}>
                        <RichTextRenderer
                          content={
                          content?.json?.nodeType === 'document' ? content.json : { nodeType: 'document', content: [content.json] }
                        }
                          settings={{
                            typographyTheme: 'bodyLarge',
                            typographyThemeMobile: 'captionLarge',
                            styleProp: styles.textWrap
                          }}
                        />
                      </RenderIf>
                    </SyncRow>
                  </Container>
                )
              })
            }

        </div>
      )
    }

    return null
  }

  const firstProduct = products[0]
  const otherProducts = products.slice(1)
  const otherProductsRendered = otherProducts.map((product, index) => renderProduct(product, index + 1, null))

  const onScroll = ({ target }: BaseSyntheticEvent) => {
    const {
      scrollWidth,
      scrollLeft,
      clientWidth
    } = target
    const scrollOffset = scrollWidth - scrollLeft
    const isEnd = clientWidth >= scrollOffset

    setDisplayArrowRight(!isEnd)
    setDisplayArrowLeft(isEnd)
  }

  const handleNextScroll = () => {
    const elementWidth = firstChildRef.current?.offsetWidth || 0
    const left = elementWidth
    scrollRef.current?.scrollBy({ left, behavior: 'smooth' })
    onScroll({ target: scrollRef.current } as BaseSyntheticEvent)
  }

  const handlePrevScroll = () => {
    const elementWidth = firstChildRef.current?.offsetWidth || 0
    const left = elementWidth
    scrollRef.current?.scrollBy({ left: -left, behavior: 'smooth' })
    onScroll({ target: scrollRef.current } as BaseSyntheticEvent)
  }

  return (
    <Container as="section" theme={theme} paddingInline="2" styleProp={!containerWithouPadding && styles.container} gap="4">
      <span id={settings?.anchorTargeting || '#'} />
      {header && <Typography as="h4" typographyTheme="h4Secondary" textCentered fontBold marginBottom="md">{header}</Typography>}

      <Container styleProp={styles.gridWrapper}>

        <RenderIf condition={!isMobile}>
          <Container as="div" flex gridColumns>
            { products.map((product, index) => renderProduct(product, index, null)) }
          </Container>
        </RenderIf>

        <RenderIf condition={isMobile}>
          <Container as="div" flex gridColumns styleProp={styles.scrollContainer}>
            { renderProduct(firstProduct, 0, firstChildRef) }
            <div ref={scrollRef} {...stylex.props(styles.mobileScroll)} onScroll={onScroll}>
              { otherProductsRendered }
            </div>
            <Container styleProp={styles.arrowsContainer}>
              <RenderIf condition={displayArrowRight}>
                <button type="button" onClick={handleNextScroll} {...stylex.props(styles.arrow, styles.arrowRight)}>
                  <Icon name="RightLongArrow" fill={colors.navy} />
                </button>
              </RenderIf>
              <RenderIf condition={displayArrowLeft}>
                <button type="button" onClick={handlePrevScroll} {...stylex.props(styles.arrow, styles.arrowLeft)}>
                  <Icon name="LeftLongArrow" fill={colors.navy} />
                </button>
              </RenderIf>
            </Container>
          </Container>
        </RenderIf>
      </Container>
    </Container>
  )
}

export default HowWeCompareItems
