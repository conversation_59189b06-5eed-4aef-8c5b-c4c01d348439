'use client'

import {
  colors,
  spacing,
  globalTokens as $,
  fontSizes,
} from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import CallToAction, { CTAProps } from '@/components/Generic/CallToAction'
import generateCallToActionProps, { CallToActionExtractProps } from '@/utils/generateCallToActionProps'
import RenderIf from '@/components/Generic/RenderIf'
import { DesktopOnly, MobileOnly } from '@/utils/responsiveDisplay'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { notEmpty } from '@/utils/checking'
import Icon from '@/components/Generic/Icon'
import useIsMobile from '@/hooks/isMobile'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'
import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'
import {
  BaseSyntheticEvent, ForwardedRef, forwardRef, useCallback, useEffect, useRef, useState
} from 'react'

const DESKTOP = '@media (min-width: 1024px)'
const TABLET = '@media (min-width: 861px) and (max-width: 1023px)'
const MIDDLE = '@media (min-width: 550px) and (max-width: 860px)'
const MOBILE = '@media (max-width: 550px)'

const styles = stylex.create({
  wrapper: {
    position: 'relative',
    width: '100%',
    alignSelf: 'center',
    padding: {
      default: `${spacing.lg} ${spacing.md}`,
      [DESKTOP]: `${spacing.xxl} 0`,
    },
    margin: '0 auto',
    maxWidth: {
      default: '100%',
      [DESKTOP]: '900px',
    }
  },
  noPadding: {
    paddingBlock: '0'
  },
  headerWrapper: {
    display: 'grid',
  },
  img: {
    display: 'block',
    margin: '0 auto',
    maxWidth: '210px',
    width: '100%',
    height: 'auto',
  },
  compareItem: {
    padding: {
      default: `${spacing.md} ${spacing.sm}`,
      [DESKTOP]: `${spacing.md}`,
    },
  },
  scrollSnap: {
    scrollSnapAlign: 'center',
  },
  headerHeight2Items: {
    height: {
      [MOBILE]: '156px',
      [TABLET]: '220px',
      [MIDDLE]: '210px',
      [DESKTOP]: '259px',
    }
  },
  headerHeight3Items: {
    height: {
      [MOBILE]: '160px',
      [MIDDLE]: '185px',
      [TABLET]: '220px',
      [DESKTOP]: '259px',
    }
  },
  headerHeight4Items: {
    height: {
      [MOBILE]: '160px',
      [TABLET]: '215px',
      [MIDDLE]: '180px',
      [DESKTOP]: '230px',
    }
  },
  compareItemTitle: {
    marginTop: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    },
    fontSize: {
      [MOBILE]: fontSizes.xxs,
      [MIDDLE]: fontSizes.xs,
      [DESKTOP]: fontSizes.md,
    },
    lineHeight: {
      [MOBILE]: fontSizes.sm,
    }
  },
  activeItemHeader: {
    backgroundColor: colors.offWhite,

    borderTopWidth: '1px',
    borderTopStyle: 'solid',
    borderTopColor: colors.navy,

    borderLeftWidth: '1px',
    borderLeftStyle: 'solid',
    borderLeftColor: colors.navy,

    borderRightWidth: '1px',
    borderRightStyle: 'solid',
    borderRightColor: colors.navy,

    borderTopLeftRadius: $.borderRadius,
    borderTopRightRadius: $.borderRadius

  },
  itemValue: {
    position: 'relative',
    borderTopWidth: '1px',
    borderTopStyle: 'solid',
    borderTopColor: colors.gray300,

    display: 'flex',
    placeContent: 'center',
    alignItems: 'center',

  },
  activeItemValue: {
    backgroundColor: colors.offWhite,
    borderLeftWidth: '1px',
    borderLeftStyle: 'solid',
    borderLeftColor: colors.navy,

    borderRightWidth: '1px',
    borderRightStyle: 'solid',
    borderRightColor: colors.navy,

  },
  criteriaRow: {
    display: 'flex',
  },
  criteriaRowLast: {
    paddingBottom: {
      default: spacing.md,
      [DESKTOP]: spacing.xxl,
    },
  },
  compareCriteria: {
    borderTopWidth: '1px',
    borderTopStyle: 'solid',
    borderTopColor: colors.gray300,
    padding: {
      default: `${spacing.md} ${spacing.sm} ${spacing.sm} 0`,
      [DESKTOP]: spacing.md,
    },
  },
  textCenter: {
    textAlign: 'center',
  },
  criteriaWrapper: {
    position: 'relative',
    display: 'flex',
  },
  criteriaHidden: {
    position: 'relative',
    zIndex: '-1',
  },
  loader: {
    zIndex: '10',
    position: 'absolute',
    top: '0',
    left: '0',
    width: '100%',
    height: '100%',
    transition: 'opacity 0.5s',
  },
  loaderFadeout: {
    opacity: '0',
    pointerEvents: 'none',
  },
  callToAction: {
    position: 'absolute',
    top: '95%',
    left: '-1px',
    right: '-1px',
    padding: `0 ${spacing.md} ${spacing.lg}`,
    justifyContent: 'center',
    display: {
      default: 'none',
      [DESKTOP]: 'flex',
    }
  },
  cta: {
    maxWidth: '160px',
  },
  borderBottomRound: {
    borderBottomWidth: '1px',
    borderBottomStyle: 'solid',
    borderBottomColor: colors.navy,
    borderBottomLeftRadius: $.borderRadius,
    borderBottomRightRadius: $.borderRadius,
  },
  borderBottomRoundMobileOnly: {
    borderBottomLeftRadius: {
      default: $.borderRadius,
      [DESKTOP]: '0',
    },
    borderBottomRightRadius: {
      default: $.borderRadius,
      [DESKTOP]: '0',
    },
  },
  visibilityNone: {
    visibility: 'hidden',
    width: '1px',
  },
  horintalOverflow: {
    position: 'relative',
    overflowX: 'auto',
    display: 'grid',
    gridAutoFlow: 'column',
    scrollSnapType: 'x mandatory',
  },
  overflowContent: {},
  firstItemHeaderWrapper: {
    display: 'flex',
    justifyContent: 'flex-end'
  },
  arrowsContainer: {
    position: 'absolute',
    bottom: '0',
    left: '220px',
    right: '0',
    height: '10px',
  },
  arrow: {
    display: {
      default: 'none',
      '@media (max-width: 419px)': 'block',
    },
    position: 'absolute',
    bottom: spacing.md,
    cursor: 'pointer',
    zIndex: '10',
  },
  arrowRight: {
    right: '0',
  },
  arrowLeft: {
    left: spacing.md,
  }
})

type ElementsToCompare = {
  items: {
    name: string
    selectedAsset: {
      url: string
      width: number
      height: number
      title: string
    }
    itemsCollection: {
      items: {
        assetsCollection: {
          items: {
            url: string
            width: number
            height: number
            title: string
          }[]
        }
      }[]
    }
    sys: {
      id: string
    }
    callToAction: CallToActionExtractProps
  }[]
}

type CompareCriteriaCollection = {
  items: {
    title: string
    content: {
      json: any
    }
  }[]
}

type HowWeCompareChartProps = {
  theme: ThemeColors
  header?: string
  elementsToCompareCollection: ElementsToCompare
  compareCriteriaCollection: CompareCriteriaCollection
  containerWithouPadding?: boolean
}

type ElementHeaderProps = {
  name: string
  selectedAsset: {
    url: string
    width: number
    height: number
    title: string
  }
  width: number
  index: number
  styleProp: {}
}

type ElementValueProps = {
  sys: {
    id: string
  }
  callToAction: CallToActionExtractProps
  itemsCollection: {
    items: {
      assetsCollection: {
        items: {
          url: string
          width: number
          height: number
          title: string
        }[]
      }
    }[]
  }
  width: number
  parentIndex: number
  isLastCriteria: boolean
  i: number
}

function getCompareStyles({ isFirst, activeClass } : { isFirst: boolean, activeClass: boolean }) {
  return [
    styles.itemValue,
    isFirst && styles.activeItemValue,
    activeClass && styles.borderBottomRound,
    activeClass && styles.borderBottomRoundMobileOnly,
  ]
}

function getGridStyles(length: number) {
  const TWO_ELEMENTS = 2
  const THREE_ELEMENTS = 3
  const FOUR_ELEMENTS = 4
  switch (length) {
    case TWO_ELEMENTS:
      return styles.headerHeight2Items
    case THREE_ELEMENTS:
      return styles.headerHeight3Items
    case FOUR_ELEMENTS:
      return styles.headerHeight4Items
    default:
      return styles.headerHeight2Items
  }
}

const CompareElementHeader = forwardRef(({
  name,
  selectedAsset,
  width,
  index,
  styleProp,
}: ElementHeaderProps, ref: ForwardedRef<HTMLDivElement>) => (
  <div ref={ref} style={{ width: `${width}px` }} {...stylex.props(styles.compareItem, index === 0 && styles.activeItemHeader, styleProp)}>
    <Container>
      <RenderIf condition={notEmpty(selectedAsset)}>
        <Image
          src={selectedAsset.url}
          alt={selectedAsset.title}
          width={selectedAsset.width}
          height={selectedAsset.height}
          {...stylex.props(styles.img)}
        />
      </RenderIf>
    </Container>
    <Container styleProp={styles.textCenter}>
      <Typography as="span" typographyTheme="h6Secondary" textCentered styleProp={styles.compareItemTitle}>
        {name}
      </Typography>
    </Container>
  </div>
))

const CompareElementValue = ({
  callToAction,
  itemsCollection,
  width,
  parentIndex,
  isLastCriteria,
  i
}: ElementValueProps) => {
  const { assetsCollection } = itemsCollection.items[parentIndex]
  const isFirst = i === 0
  const activeClass = isLastCriteria && isFirst
  const icon = assetsCollection.items[0]

  const displayCallToAction = Boolean(isLastCriteria && isFirst && notEmpty(callToAction))

  return (
    <div
      {...stylex.props(getCompareStyles({ isFirst, activeClass }))}
      style={{ width: `${width}px` }}
    >
      <Image src={icon.url} alt={icon.title} width={icon.width} height={icon.height} />

      <RenderIf condition={Boolean(isLastCriteria && callToAction)}>
        {
          displayCallToAction && (
            <Container as="div" styleProp={[styles.callToAction, isFirst && styles.activeItemValue, isFirst && styles.borderBottomRound]}>
              <CallToAction {...generateCallToActionProps(callToAction) as CTAProps} fullWidth styleProp={styles.cta} />
            </Container>
          )
        }

      </RenderIf>
    </div>
  )
}

const minWidth = 110

const HowWeCompareChart = ({
  theme = 'gray200',
  header,
  elementsToCompareCollection = { items: [] },
  compareCriteriaCollection = { items: [] },
  containerWithouPadding = false,
}: HowWeCompareChartProps) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const firstChildRef = useRef<HTMLDivElement>(null)

  const scrollRef = useRef<HTMLDivElement>(null)

  const [displayArrowRight, setDisplayArrowRight] = useState(true)
  const [displayArrowLeft, setDisplayArrowLeft] = useState(false)

  const { isMobile } = useIsMobile()
  const [itemsWidth, setItemsWidth] = useState(0)
  const firstElementToCompare = elementsToCompareCollection.items[0]
  const othersToCompare = elementsToCompareCollection.items.slice(1)

  const CTA = firstElementToCompare.callToAction

  const rowIndexStyle = elementsToCompareCollection.items.length
  const headerClass = getGridStyles(rowIndexStyle)

  function calcWidth() {
    const containerWidth = containerRef.current?.clientWidth
    const width = (containerWidth || 0) / (rowIndexStyle + 1)
    const w = width > minWidth ? width : minWidth

    setItemsWidth(w)
  }

  const checkScroll = useCallback(() => {
    const { scrollWidth = 0, clientWidth = 0 } = scrollRef.current || {}
    const isScrollDisplaying = scrollWidth > clientWidth
    setDisplayArrowRight(isScrollDisplaying)
    setDisplayArrowLeft(false)
  }, [])

  useEffect(() => {
    const listenerHandler = () => {
      calcWidth()
      checkScroll()
    }

    const FIFTY_MILLISECONDS = 50
    setTimeout(listenerHandler, FIFTY_MILLISECONDS)

    window.addEventListener('resize', listenerHandler)
    return () => {
      window.removeEventListener('resize', listenerHandler)
    }
  }, [])

  const onScroll = ({ target }: BaseSyntheticEvent) => {
    const {
      scrollWidth,
      scrollLeft,
      clientWidth
    } = target
    const scrollOffset = scrollWidth - scrollLeft
    const isEnd = clientWidth >= scrollOffset
    setDisplayArrowRight(!isEnd)
    setDisplayArrowLeft(isEnd)
  }

  const handleNextScroll = () => {
    const left = itemsWidth
    scrollRef.current?.scrollBy({ left, behavior: 'smooth' })
    onScroll({ target: scrollRef.current } as BaseSyntheticEvent)
  }

  const handlePrevScroll = () => {
    const left = itemsWidth
    scrollRef.current?.scrollBy({ left: -left, behavior: 'smooth' })
    onScroll({ target: scrollRef.current } as BaseSyntheticEvent)
  }

  const hasElementsToPaginate = othersToCompare.length > 1
  const wrapperStyles = () => [styles.wrapper, containerWithouPadding && styles.noPadding]

  return (
    <Container as="section" theme={theme}>
      <Container as="div" styleProp={wrapperStyles()}>
        {header && <Typography as="h2" typographyTheme="h2Secondary" textCentered marginBottom="lg">{header}</Typography>}
        <div ref={containerRef} />
        <Container as="div" styleProp={[styles.criteriaWrapper]}>
          <Container styleProp={[styles.loader, itemsWidth > 0 && styles.loaderFadeout]} theme={theme}>
            <span />
          </Container>
          <Container as="div">
            <Container as="div" styleProp={styles.firstItemHeaderWrapper}>
              <CompareElementHeader {...firstElementToCompare} index={0} width={itemsWidth} styleProp={headerClass} ref={firstChildRef} />
            </Container>
            {
              compareCriteriaCollection.items.map(({ title, content }, index) => {
                const isLastCriteria = index === compareCriteriaCollection.items.length - 1

                return (
                  <Container
                    as="div"
                    styleProp={[
                      styles.criteriaRow,
                      isLastCriteria && styles.criteriaRowLast]}
                    key={title}
                  >
                    <div {...stylex.props(styles.compareCriteria)} style={{ width: `${itemsWidth}px` }}>
                      <Typography as="h3" typographyTheme="h6Primary" typographyThemeMobile="bodySmall" fontBold marginBottom="xs">{title}</Typography>
                      <DesktopOnly>
                        <Typography as="p" typographyTheme="bodySmall">{documentToPlainTextString(content.json)}</Typography>
                      </DesktopOnly>
                    </div>
                    <CompareElementValue {...firstElementToCompare} parentIndex={index} i={0} isLastCriteria={isLastCriteria} width={itemsWidth} />
                  </Container>
                )
              })
            }
          </Container>

          <div ref={scrollRef} {...stylex.props(styles.horintalOverflow)} onScroll={onScroll} dir="ltr">
            {
                  othersToCompare.map((item, i) => (
                    <Container as="div" styleProp={styles.scrollSnap} key={item.sys.id}>
                      <CompareElementHeader {...item} index={i + 1} width={itemsWidth} styleProp={headerClass} />
                      {compareCriteriaCollection.items.map(({ title, content }, index) => {
                        const isLastCriteria = index === compareCriteriaCollection.items.length - 1
                        return (
                          <Container as="div" styleProp={[styles.criteriaRow]}>
                            <div {...stylex.props(styles.compareCriteria, styles.criteriaHidden)} style={{ width: `${itemsWidth}px`, marginLeft: `-${itemsWidth}px` }}>
                              <Typography as="h3" typographyTheme="h6Primary" typographyThemeMobile="bodySmall" fontBold marginBottom="xs">{title}</Typography>
                              <DesktopOnly>
                                <Typography as="p" typographyTheme="bodySmall">{documentToPlainTextString(content.json)}</Typography>
                              </DesktopOnly>
                            </div>
                            <CompareElementValue {...item} parentIndex={index} i={i + 1} isLastCriteria={isLastCriteria} width={itemsWidth} key={item.sys.id} />
                          </Container>
                        )
                      })}
                    </Container>
                  ))
                }

          </div>
          <Container styleProp={styles.arrowsContainer}>
            <RenderIf condition={displayArrowRight && isMobile && hasElementsToPaginate}>
              <button type="button" onClick={handleNextScroll} {...stylex.props(styles.arrow, styles.arrowRight)}>
                <Icon name="RightLongArrow" fill={colors.navy} />
              </button>
            </RenderIf>
            <RenderIf condition={displayArrowLeft && isMobile && hasElementsToPaginate}>
              <button type="button" onClick={handlePrevScroll} {...stylex.props(styles.arrow, styles.arrowLeft)}>
                <Icon name="LeftLongArrow" fill={colors.navy} />
              </button>
            </RenderIf>
          </Container>
        </Container>
        <MobileOnly>
          { CTA && <CallToAction {...generateCallToActionProps(CTA) as CTAProps} fullWidth /> }
        </MobileOnly>
      </Container>
    </Container>
  )
}

export default HowWeCompareChart
