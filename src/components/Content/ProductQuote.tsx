import Typography from '../Typography'

import { fontSizes, spacing } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

type ProductQuoteProps = {
  theme?: ThemeColors
  content?: React.ReactNode
  header?: string
  slides: {
    mainImage: string
    signatureImage: string
    id: string
  }[];
};

const DESKTOP = '@media (min-width: 776px)'

const styles = stylex.create({
  wrapper: {
    gridTemplateColumns: {
      default: '1fr',
      [DESKTOP]: '1fr 2fr',
    },
    gridAutoFlow: {
      default: 'row',
      [DESKTOP]: 'column',
    },
    maxWidth: {
      default: '400px',
      [DESKTOP]: ` calc(75rem + ${spacing.md} * 2)`,
    }
  },
  contentWrapper: {
    flexDirection: {
      default: 'row',
      [DESKTOP]: 'column',
    },
    justifyContent: 'center',
  },
  header: {
    textAlign: {
      default: 'center',
      [DESKTOP]: 'left',
    },
  },
  quote: {
    fontSize: {
      default: fontSizes.md,
      [DESKTOP]: fontSizes.lg,
    },
  },
  signatureImage: {
    maxWidth: {
      default: '180px',
      [DESKTOP]: '280px',
    },
    marginBlockStart: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    },
    marginBlockEnd: {
      default: spacing.xs,
      [DESKTOP]: spacing.xxs,
    },
  },
})

const ProductQuote = ({
  theme = 'black',
  content,
  header,
  slides,
}: ProductQuoteProps) => {
  const { mainImage, signatureImage } = slides ? slides[0] : { mainImage: null, signatureImage: null }
  return (
    <Container as="section" theme={theme} flex flexCentered>
      <Container
        as="div"
        grid
        gap="4"
        size="5"
        theme={theme}
        styleProp={styles.wrapper}
        paddingBlock="5"
        paddingInline="4"
      >
        {mainImage && (
          <Image
            src={mainImage}
            alt="Product Image"
            layout="responsive"
            width={332}
            height={332}
          />
        )}
        <Container
          as="div"
          styleProp={styles.contentWrapper}
          flex
          flexRow
        >
          <Typography as="h2" typographyTheme="h5Secondary" styleProp={styles.header}>
            {header}
          </Typography>
          {signatureImage && (
            <Image
              src={signatureImage}
              alt="Product Image"
              layout="responsive"
              {...stylex.props(styles.signatureImage)}
              width={180}
              height={50}
            />
          )}
          {content && <div>{content}</div>}
        </Container>
      </Container>
    </Container>
  )
}

export default ProductQuote
