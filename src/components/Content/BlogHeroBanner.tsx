// TODO: Add types for slides
// TODO: Connect to CMS

import themes, { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@components/layout/Container'
import EmblaCarousel from '@components/layout/BlogHeroBanner/Embla/EmblaCarousel'
import EmblaCarouselMobile from '@components/layout/BlogHeroBanner/Embla/EmblaCarouselMobile'

import { EmblaOptionsType } from 'embla-carousel'

type BlogHeroBannerProps = {
  slides: any;
  theme?: ThemeColors
};

const BlogHeroBanner = ({ slides, theme = 'navy' }: BlogHeroBannerProps) => {
  const themeStyles = themes[theme]

  const OPTIONS: EmblaOptionsType = {}
  const SLIDES = Array.from(Array(slides?.length).keys())

  return (
    <Container
      as="section"
      theme={theme}
      styleProp={[themeStyles]}
    >
      <EmblaCarousel
        slidesData={slides}
        slides={SLIDES}
        options={OPTIONS}
      />

      <EmblaCarouselMobile
        slidesData={slides}
        slides={SLIDES}
        options={OPTIONS}
      />

    </Container>
  )
}

export default BlogHeroBanner
