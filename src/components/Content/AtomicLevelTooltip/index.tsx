'use client'

import Tooltip from './Tooltip'

import { ThemeColors } from '@/app/themeThemes.stylex'
import { colors } from '@/app/themeTokens.stylex'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import { useState } from 'react'

type AtomicLevelTooltipProps = {
  header: string;
  content: { json: any };
  theme: ThemeColors;
};

const styles = stylex.create({
  wrapper: {
    position: 'absolute',
    top: '5px',
    left: '5px',
    isolation: 'isolate',
    zIndex: 1
  },
  button: {
    width: '24px',
    height: '24px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    cursor: 'pointer',
    backgroundColor: {
      default: colors.navy,
      ':hover': colors.navy300,
    },
    position: 'relative',
  },
  icon: {
    position: 'relative',
    width: '12px',
    height: '2px',
    backgroundColor: '#fff',
    transition: '.2s all ease-out',
    '::before': {
      content: '""',
      position: 'absolute',
      width: '2px',
      height: '12px',
      backgroundColor: '#fff',
      top: '-5px',
      left: '5px',
      transition: 'transform .2s ease-out',
    },
  },
  rotatePlus: {
    '::before': {
      transform: 'rotate(90deg)',
    },
  },
  hidden: {
    opacity: '0',
    transition: '.1s all ease-out',
  },
  visible: {
    display: 'flex',
    transition: '.1s all ease-out',
  },
})

const AtomicLevelTooltip = ({
  header,
  content,
  theme,
}: AtomicLevelTooltipProps) => {
  const [isVisible, setIsVisible] = useState(false)

  return header && content && theme ? (
    <Container as="div" styleProp={[styles.wrapper]}>
      <button
        {...stylex.props(styles.button)}
        type="button"
        onClick={() => setIsVisible(!isVisible)}
      >
        {/* CSS Plus/Minus Icon */}
        <div
          {...stylex.props(
            styles.icon,
            isVisible && styles.rotatePlus
          )}
        />
      </button>
      <div
        {...stylex.props(
          isVisible ? styles.visible : styles.hidden
        )}
      >
        <Tooltip title={header} content={content} theme={theme} />
      </div>
    </Container>
  ) : null
}

export default AtomicLevelTooltip
