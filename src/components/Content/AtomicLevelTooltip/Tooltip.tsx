import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import RichTextRender from '@/utils/RichTextRenderer'

import * as stylex from '@stylexjs/stylex'

type TooltipProps = {
  title: string;
  content: { json: any };
  theme: ThemeColors;
};

const styles = stylex.create({
  wrapper: {
    position: 'absolute',
    width: '100%',
    maxWidth: '280px',
    padding: '15px',
    borderRadius: '16px',
    top: '35px',
    zIndex: '1',
    minWidth: '280px'
  },
})

const Tooltip = ({
  title,
  content,
  theme,
}: TooltipProps) => (
  <Container as="div" styleProp={styles.wrapper} theme={theme}>
    <Typography as="h2" size="xs" marginBottom="xs">
      {title}
    </Typography>
    <Typography as="p" size="xs">
      <RichTextRender content={content?.json} />
    </Typography>
  </Container>
)

export default Tooltip
