import { ThemeColors } from '@/app/themeThemes.stylex'
import { colors, spacing } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'

import * as stylex from '@stylexjs/stylex'

type ProductDetailsTooltipProp = {
  currentProductValueCopy?: string
  customLaunchDiscountCopy?: string
  regularPriceOverride?: number
  onSale?: boolean
  compareAtPrice?: number
  price?: number
  theme: ThemeColors
  isHovered: boolean
};

const styles = stylex.create({
  wrapper: {
    position: 'absolute',
    width: '100%',
    padding: '20px 16px',
    borderRadius: '4px',
    top: '35px',
    right: '-10px',
    zIndex: '2',
    minWidth: '250px',
    display: 'none',
  },
  active: {
    display: 'block'
  },
  horizontal: {
    marginBottom: spacing.xs,
    width: '100%',
    height: '1px',
    backgroundColor: colors.black,
  },
  finalPayment: {
    color: colors.navy
  }
})

const marchPromoStyles = stylex.create({
  discountMessage: {
    color: colors.red700
  }
})

const DECIMAL_PLACES = 2

const formatNumber = (num: number) => (num % 1 === 0 ? num.toString() : num.toFixed(DECIMAL_PLACES))

// eslint-disable-next-line complexity
const ProductDetailsTooltip = ({
  currentProductValueCopy = 'Product Value',
  customLaunchDiscountCopy,
  onSale,
  regularPriceOverride = 0,
  compareAtPrice,
  price,
  theme,
  isHovered
}: ProductDetailsTooltipProp) => {
  const isSetProduct = !!compareAtPrice && !!price

  let finalPrice
  let setSavings

  if (price) {
    finalPrice = formatNumber(price)
  }

  if (compareAtPrice && price) {
    setSavings = formatNumber(compareAtPrice - price)
  }

  return (
    <Container as="div" styleProp={[styles.wrapper, isHovered && styles.active]} theme={theme}>
      <Container as="div" flex flexRow spaceBetween>
        <Typography as="p" size="xs" marginBottom="xs">
          {isSetProduct ? 'Set Value: ' : currentProductValueCopy }
        </Typography>
        <Typography as="p" size="xs" marginBottom="xs">
          ${isSetProduct ? compareAtPrice : price }
        </Typography>
      </Container>

      {setSavings && (
        <Container as="div" flex flexRow spaceBetween>
          <Typography as="p" size="xs" marginBottom="xs">
            Set Savings:
          </Typography>
          <Typography as="p" size="xs" marginBottom="xs">
            -${setSavings}
          </Typography>
        </Container>
      )}

      {(onSale && regularPriceOverride) && (
        <Container as="div" flex flexRow spaceBetween>
          <Typography as="p" size="xs" marginBottom="xs" styleProp={marchPromoStyles.discountMessage}>
            {customLaunchDiscountCopy}:
          </Typography>
          <Typography as="p" size="xs" marginBottom="xs" styleProp={marchPromoStyles.discountMessage}>
            -${Number(finalPrice) - regularPriceOverride }
          </Typography>
        </Container>
      )}

      <div {...stylex.props(styles.horizontal)} />

      <Container as="div" flex flexRow spaceBetween>
        <Typography as="p" size="xs" fontBold styleProp={styles.finalPayment}>
          You Pay:
        </Typography>
        <Typography as="p" size="xs" fontBold styleProp={styles.finalPayment}>
          ${regularPriceOverride ?? finalPrice}
        </Typography>
      </Container>
    </Container>
  )
}

export default ProductDetailsTooltip
