import CallToAction, { AnchorProps, ButtonProps } from '@components/Generic/CallToAction'
import Typography from '@components/Typography'
import Container, { Size } from '@components/layout/Container'
import Wrapper from '@components/layout/Wrapper'
import DialogTrigger from '@components/Generic/Dialog/DialogTrigger'
import { condition, equals, notEmpty } from '@utils/checking'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing } from '@/app/themeTokens.stylex'
import { ContentfulTokenSettings } from '@/lib/contentful/types/generic'
import { TypographyThemes } from '@/app/typographyThemes.stylex'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'
import * as stylex from '@stylexjs/stylex'

type ExtendedAnchorProps = AnchorProps & { text: string }
type ExtendedButtonProps = ButtonProps & { text: string }

const isAnchorProps = (button: ExtendedAnchorProps): button is ExtendedAnchorProps => (button as ExtendedAnchorProps).href !== ''

const isButtonProps = (button: ExtendedButtonProps): button is ExtendedButtonProps => (button as ExtendedButtonProps).onClick !== undefined

type PageCTAProps = {
  image?: string
  theme?: ThemeColors
  header?: string
  subheader?: string
  content?: { json: any }
  buttons?: Array<ExtendedAnchorProps | ExtendedButtonProps>
  contentAlignment?: 'top' | 'center',
  settings?: ContentfulTokenSettings
}

const styles = stylex.create({
  background: (image: string, contentAlignment: string) => ({
    display: 'flex',
    alignItems: 'center',
    backgroundImage: `url(${image})`,
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    paddingLeft: spacing.md,
    paddingRight: spacing.md,
    height: {
      default: '458px',
      '@media (min-width: 1024px)': '500px',
    },
    paddingTop: {
      default: '0',
      '@media (max-width: 1024px)': condition<String>(equals(contentAlignment, 'top'), '46px', 'none'),
      '@media (min-width: 1024px)': condition<String>(equals(contentAlignment, 'top'), '100px', 'none'),
    },
  }),
  buttonContainer: {
    flexDirection: {
      default: 'column',
      '@media (min-width: 1024px)': 'row',
    }
  },
})

const PageCTA = ({
  image = '',
  theme = 'cream',
  header,
  subheader,
  content,
  buttons = [],
  contentAlignment = 'center',
  settings
}: PageCTAProps) => {
  const DEFAULT_WIDTH = 2
  const containerWidth = condition<Size>(
    notEmpty(settings?.width),
    settings?.width,
    DEFAULT_WIDTH
  )

  const subheaderTheme = condition<TypographyThemes>(
    equals(settings?.fontFamily, 'Primary (Moderat, sans-serif)'),
    'h3Primary',
    'h2Secondary'
  )

  return (
    <Container
      as="section"
      theme={theme}
      flex={equals(contentAlignment, 'top')}
      flexCentered={equals(contentAlignment, 'center')}
      styleProp={styles.background(image, contentAlignment)}
    >
      <Container
        as="div"
        flex={equals(contentAlignment, 'top')}
        flexCentered={equals(contentAlignment, 'center')}
        gap="3"
        size={containerWidth}
      >
        {header && (
        <Typography as="h2">
          {header}
        </Typography>
        )}
        {subheader && (
        <Typography
          textCentered
          typographyTheme={subheaderTheme}
          as="p"
        >
            {subheader}
        </Typography>
        )}
        {content?.json && (
          <Wrapper size="sm">
            <Typography
              textCentered
              typographyTheme="bodyLarge"
              as="p"
            >
              {documentToPlainTextString(content.json)}
            </Typography>
          </Wrapper>
        )}
        {buttons.length > 0 && (
        <Container as="div" gap="2" flex flexRow styleProp={styles.buttonContainer}>
          {buttons.map((button: any) => {
            const {
              text,
              href,
              id,
              variant,
              onClick,
              icon,
            } = button
            if (isAnchorProps(button)) {
              return (
                <CallToAction
                  key={id}
                  variant={variant}
                  href={href}
                  icon={icon}
                >
                  {text}
                </CallToAction>
              )
            }
            if (isButtonProps(button)) {
              return <DialogTrigger key={id} text={text} id={onClick} />
            }
            return null
          })}
        </Container>
        )}
      </Container>
    </Container>
  )
}

export default PageCTA
