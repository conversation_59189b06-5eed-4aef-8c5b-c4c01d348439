'use client'

import { DotButton, useDotButton } from './EmblaCarouselDotButton'
import LargeSlide from './LargeSlide'

import Container from '@/components/layout/Container'
import RenderIf from '@/utils/renderIf'
import { notEmpty } from '@/utils/checking'
import TextBlock from '@/components/Generic/TextBlock'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { colors, spacing } from '@/app/themeTokens.stylex'
import { toCamelCase } from '@/utils/regex'
import { CallToActionType } from '@/components/Generic/ContactUsForm'

import React, { ReactNode } from 'react'
import { EmblaOptionsType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import stylex from '@stylexjs/stylex'

const styles = stylex.create({
  embla: {
    maxWidth: '48rem',
    margin: 'auto',
  },
  embla_viewport: {
    overflow: 'hidden',
  },
  embla_container: {
    display: 'flex',
    touchAction: 'pan-y pinch-zoom',
    marginLeft: 'calc(1rem * -1)',
  },
  embla_slide: {
    transform: 'translate3d(0, 0, 0)',
    flex: '0 0 100%',
    minWidth: '0',
    paddingLeft: '1rem',
    height: 'auto',
    display: 'flex',
  },
  embla_controls: {
    marginTop: spacing.sm,
    justifyContent: 'end',
  },
  embla_dot: {
    WebkitTapHighlightColor: 'rgba(var(--text-high-contrast-rgb-value), 0.5)',
    appearance: 'none',
    backgroundColor: colors.gray300,
    touchAction: 'manipulation',
    textDecoration: 'none',
    cursor: 'pointer',
    padding: 0,
    margin: 0,
    width: spacing.xs,
    height: spacing.xs,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
  },
  embla_dots_selected: {
    backgroundColor: colors.gray,
  },
  sliderWithoutPadding: {
    paddingLeft: 0,
  }
})

export type SlideProps = {
  id: string
  title: string
  content: ReactNode
  settings: { theme: ThemeColors }
  referencesCollection: {
    callToAction: CallToActionType
  }[]
  assets?: {
    url: string
    alt?: string,
    fileName: string
  }[]
}

type PropType = {
  slides: SlideProps[],
  options?: EmblaOptionsType
}

export const Slide = ({ slide, noPadding = false }: { slide: SlideProps, noPadding?: boolean }) => {
  const themeSettings = toCamelCase(slide.settings?.theme) as ThemeColors || 'navy'

  return (
    <Container
      as="div"
      styleProp={[
        styles.embla_slide,
        noPadding && styles.sliderWithoutPadding
      ]}
    >
      <RenderIf condition={notEmpty(slide.content)}>
        <TextBlock theme={themeSettings}>
          {slide.content}
        </TextBlock>
      </RenderIf>
    </Container>
  )
}

const EmblaCarousel: React.FC<PropType> = (props) => {
  const { slides, options } = props
  const [emblaRef, emblaApi] = useEmblaCarousel(options)
  const {
    selectedIndex,
    scrollSnaps,
    onDotButtonClick
  } = useDotButton(emblaApi)

  return (
    <Container
      as="section"
      styleProp={[styles.embla]}
      paddingBlock="2"
    >
      <div
        {...stylex.props(styles.embla_viewport)}
        ref={emblaRef}
      >
        <Container
          as="div"
          styleProp={[styles.embla_container]}
        >
          {slides?.length > 0 && slides.map((slide) => {
            if (slide.title) {
              return (
                <LargeSlide key={slide.id} slide={slide} />
              )
            } return (
              <Slide key={slide.id} slide={slide} />
            )
          })}
        </Container>
      </div>

      <Container
        as="div"
        flex
        flexRow
        styleProp={[styles.embla_controls]}
      >
        <Container
          as="div"
          flex
          flexRow
          gap="1"
          justifyItemsCenter
        >
          {scrollSnaps.map((_, index) => (
            <DotButton
              // eslint-disable-next-line react/no-array-index-key
              key={index}
              onClick={() => onDotButtonClick(index)}
              {...stylex.props([
                styles.embla_dot,
                index === selectedIndex && styles.embla_dots_selected,
              ])}
            />
          ))}
        </Container>
      </Container>
    </Container>
  )
}

export default EmblaCarousel
