import LargeSlide from './LargeSlide'

import EmblaCarousel, { Slide, SlideProps } from '@/components/Content/PromotionalBlockSlider/EmblaCarousel'

import React from 'react'
import { EmblaOptionsType } from 'embla-carousel'

const OPTIONS: EmblaOptionsType = { loop: true }

export type PromotionalBlockSliderProps = {
  slides: SlideProps[]
};

const PromotionalBlockSlider = ({ slides }: PromotionalBlockSliderProps) => {
  if (slides.length > 1) {
    return (
      <div style={{ maxWidth: '90vw' }}>
        <EmblaCarousel slides={slides} options={OPTIONS} />
      </div>
    )
  }

  return slides[0]?.title
    ? <LargeSlide slide={slides[0]} singleSlide />
    : <Slide noPadding slide={slides[0]} />
}

export default PromotionalBlockSlider
