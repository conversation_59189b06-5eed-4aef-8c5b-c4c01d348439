import { ThemeColors } from '@/app/themeThemes.stylex'
import { AnchorProps } from '@/components/Generic/CallToAction'
import RenderCTAS from '@/components/Generic/RenderCTAS'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import generateCallToActionProps from '@/utils/generateCallToActionProps'
import { toCamelCase } from '@/utils/regex'
import { spacing } from '@/app/themeTokens.stylex'
import { CallToActionType } from '@/components/Generic/ContactUsForm'

import Image from 'next/image'
import stylex from '@stylexjs/stylex'
import { ReactNode } from 'react'

const DESKTOP = '@media (min-width: 414px)'

const styles = stylex.create({
  embla_slide: {
    transform: 'translate3d(0, 0, 0)',
    flex: '0 0 100%',
    minWidth: '0',
    paddingLeft: '1rem',
    height: 'auto',
    display: 'flex',
  },
  singleSlide: {
    width: '100%',
    paddingLeft: 0,
    margin: `${spacing.lg} 0`
  },
  title: {
    marginBottom: spacing.xxs
  },
  callToActionContainer: {
    marginTop: spacing.sm
  },
  largeSlide: {
    borderRadius: spacing.xs,
    padding: '24px 20px',
    gap: spacing.md
  },
  content: {
    flex: '1',
  },
  cta: {
    marginTop: spacing.sm,
    alignSelf: 'flex-start'
  },
  mobileImage: {
    display: {
      default: 'flex',
      [DESKTOP]: 'none'
    }
  },
  desktopImage: {
    display: {
      default: 'none',
      [DESKTOP]: 'block'
    }
  },
  titleMobile: {
    gap: '15px'
  }
})

export type LargeSlideProps = {
  slide: {
    title: string
    content: ReactNode
    settings: {
      theme: ThemeColors
    }
    referencesCollection: {
      callToAction: CallToActionType
    }[]
    assets?: {
      url: string
      fileName: string,
      alt?: string
    }[]
  },
  singleSlide?: boolean
}

const LargeSlide = ({ slide, singleSlide = false }: LargeSlideProps) => {
  const themeSettings = toCamelCase(slide.settings?.theme) as ThemeColors || 'navy'
  const callToActionProps = slide?.referencesCollection?.[0]?.callToAction &&
    generateCallToActionProps(slide?.referencesCollection?.[0]?.callToAction)
  const asset = slide?.assets?.[0]

  return (
    <Container
      as="div"
      styleProp={[
        !singleSlide && styles.embla_slide,
        singleSlide && styles.singleSlide
      ]}
    >
      <Container
        flex
        flexRow
        theme={themeSettings}
        styleProp={styles.largeSlide}
      >
        {asset && (
          <Container styleProp={styles.desktopImage}>
            <Image
              src={asset?.url}
              alt={asset?.fileName}
              width={80}
              height={80}
            />
          </Container>
        )}
        <Container flex styleProp={styles.content}>
          <Container flex flexRow noWrap styleProp={styles.titleMobile}>
            {asset && (
              <Container styleProp={styles.mobileImage}>
                <Image
                  src={asset?.url}
                  alt={asset?.fileName}
                  width={55}
                  height={55}
                />
              </Container>
            )}
            <Typography
              as="p"
              typographyTheme="bodyLarge"
              fontBold
              styleProp={styles.title}
            >
              {slide.title}
            </Typography>
          </Container>
          <Container>{slide.content}</Container>
          {callToActionProps && (
            <Container styleProp={styles.cta}>
              <RenderCTAS buttons={[callToActionProps as AnchorProps]} />
            </Container>
          )}
        </Container>
      </Container>
    </Container>
  )
}

export default LargeSlide
