import CallToAction, { AnchorProps } from '@components/Generic/CallToAction'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing, fontSizes, breakpoints } from '@/app/themeTokens.stylex'
import LogoSlides from '@components/Generic/LogosSlides'

import * as stylex from '@stylexjs/stylex'

type ExtendedAnchorProps = AnchorProps & { text: string };

type IconLogosProps = {
  theme?: ThemeColors
  header?: string;
  subheader?: string;
  slides: { image: any; text: string, name: string, id: string }[];
  buttons?: ExtendedAnchorProps[];
  layout?: string;
}

const MOBILE = '@media (max-width: 1024px)'

const styles = stylex.create({
  parentContainer: {
    paddingInline: {
      default: spacing.lg,
      [MOBILE]: spacing.md,
    },
  },
})

const layout1 = stylex.create({
  fullContainerLayout: {
    flexDirection: {
      default: 'row',
      [MOBILE]: 'column',
    },
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerLayout: {
    fontSize: fontSizes.h4,
    maxWidth: '350px',
    textAlign: {
      default: 'left',
      [MOBILE]: 'center',
    }
  },
  textImageContainerLayout: {
    justifyContent: 'space-around',
    alignItems: {
      default: 'flex-start',
      [MOBILE]: 'center',
    },
    width: '60%',
    flexDirection: {
      default: 'row',
      [MOBILE]: 'column',
    }
  },
})

const layout2 = stylex.create({
  fullContainerLayout: {
    flexDirection: 'column',
    alignItems: 'center',
    textAlign: 'center',
  },
  headerLayout: {
    fontSize: fontSizes.h6,
    textAlign: 'center',
  },
  textImageContainerLayout: {
    maxWidth: breakpoints.md,
    margin: '0 auto',
    justifyContent: 'center',
    gap: {
      default: spacing.xl,
      [MOBILE]: spacing.lg,
    },
  },
})

const layouts = new Map<string, any>([
  ['layout1', layout1],
  ['layout2', layout2],
])

const IconLogos = ({
  theme = 'navy',
  header,
  subheader,
  buttons = [],
  slides,
  layout = 'Layout 1'
}: IconLogosProps) => {
  const mainLayoutStyles = (layouts.get(layout) || layout1) as typeof layout1 & typeof layout2
  return (
    <Container
      as="section"
      theme={theme}
      paddingBlock="5"
      gap="3"
      styleProp={styles.parentContainer}
    >
      <Container
        as="div"
        gap="4"
        flex
        styleProp={mainLayoutStyles.fullContainerLayout}
      >
        {header && (
          <Typography
            as="h2"
            typographyTheme="h4Primary"
          >
            {header}
          </Typography>
        )}
        <Container
          as="div"
          flex
          flexRow
          alignCentered
          paddingBlock="3"
          gap="5"
          styleProp={mainLayoutStyles.textImageContainerLayout}
        >
          {slides.length > 0 && slides.map((slide) => {
            const { url, fileName } = slide.image.items[0] || {}
            return (
              <LogoSlides
                id={slide.id}
                mediaURL={url}
                mediaFileName={fileName}
                textIcon={slide.text}
                layout={layout}
              />
            )
          })}
        </Container>
      </Container>
      <Container
        as="div"
        flex
        flexRow
        flexCentered
        alignCentered
        gap="3"
      >
        {subheader && (
          <Typography
            as="p"
            textCentered
          >
            {subheader}
          </Typography>
        )}
        {buttons.length > 0 && (
          <Container as="div" gap="2" flex flexRow>
            {buttons.map((button) => {
              const {
                text,
                href,
                id,
                variant,
              } = button
              return (
                <CallToAction
                  key={id}
                  variant={variant}
                  href={href}
                >
                  {text}
                </CallToAction>
              )
            })}
          </Container>
        )}
      </Container>
    </Container>
  )
}

export default IconLogos
