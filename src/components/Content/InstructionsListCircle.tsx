import { ThemeColors } from '@/app/themeThemes.stylex'
import {
  defaultTheme as $T,
  colors,
  spacing
} from '@/app/themeTokens.stylex'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'

import * as stylex from '@stylexjs/stylex'

const MOBILE = '@media (max-width: 1024px)'
const styles = stylex.create({
  mainContainer: {
    paddingBlock: {
      default: spacing.lg,
      [MOBILE]: spacing.md
    },
    paddingInline: spacing.md,
  },
  mainHeader: {
    width: '100%',
    maxWidth: '600px',
    textAlign: {
      default: 'center',
      [MOBILE]: 'left'
    }
  },
  slideContainer: {
    maxWidth: '600px',
    gap: spacing.lg,
  },
  slideCard: {
    alignItems: 'flex-start',
    gap: spacing.sm,
    width: '100%',
  },
  slideTitleContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: spacing.sm,
  },
  slideCounter: {
    width: '30px',
    height: '30px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: $T.primaryCTASurface,
    color: $T.primaryCTAText,
    borderRadius: '50%',
  },
  offWhite: {
    backgroundColor: colors.cream,
    color: colors.navy,
  }
})

type InstructionsListCircleProps = {
  header?: string;
  slides: { title: string, text: string, id: string }[];
  theme?: ThemeColors,
  uppercaseTitle?: boolean,
};
const InstructionsListCircle = ({
  header,
  slides,
  theme = 'navy',
  uppercaseTitle = false,
}: InstructionsListCircleProps) => {
  let slideCounter = 0
  return (
    <Container
      as="section"
      flex
      flexCentered
      theme={theme}
      styleProp={styles.mainContainer}
    >
      {header && (
        <Typography
          as="h4"
          size="h3"
          fontBold
          fontSecondary
          marginBottom="lg"
          styleProp={styles.mainHeader}
        >
          {header}
        </Typography>
      )}
      <Container
        as="div"
        flex
        flexCentered
        styleProp={styles.slideContainer}
      >
        {slides.map((slide) => {
          slideCounter += 1
          return (
            <Container
              as="div"
              key={slide.id}
              flex
              flexCentered
              styleProp={styles.slideCard}
            >
              <Container as="div" styleProp={styles.slideTitleContainer}>
                <Typography
                  as="p"
                  size="bodyLarge"
                  fontBold
                  textCentered
                  styleProp={[styles.slideCounter, theme === 'offWhite' && styles.offWhite]}
                >
                  {slideCounter}
                </Typography>
                <Typography
                  as="h5"
                  size="md"
                  fontBold
                  textLeft
                  uppercase={uppercaseTitle}
                >
                  {slide.title}
                </Typography>
              </Container>
              <Container
                as="div"
              >
                {slide.text}
              </Container>
            </Container>
          )
        })}
      </Container>
    </Container>
  )
}

export default InstructionsListCircle
