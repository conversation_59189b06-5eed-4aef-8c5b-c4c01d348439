// TODO: Refactor typography
import Rating from '@components/Generic/Rating'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { fontSizes, spacing } from '@/app/themeTokens.stylex'
import RenderIf from '@utils/renderIf'
import { notEmpty } from '@utils/checking'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

type ReviewCardProps = {
  theme?: ThemeColors
  logo?: {
    url: string;
  };
  content?: string;
  author?: string;
  rating?: number;
  ratingCustomStyle?: {};
}

const styles = stylex.create({
  content: {
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'column',
    padding: spacing.md,
    height: '100%',
    minHeight: '300px',
  },
  image: {
    position: 'relative',
    width: {
      default: '10%',
      '@media (min-width: 1024px)': '30%',
    },
    overflow: 'hidden',
  },
  author: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.xs,

  },
  quote: {
    textAlign: 'center',
    fontSize: fontSizes.xs,
    lineHeight: '1.5',
    marginBottom: spacing.sm,
  },
})

const ReviewCard = ({
  theme = 'cream',
  logo = {
    url: '',
  },
  content,
  author,
  rating,
  ratingCustomStyle = {},
}: ReviewCardProps) => (
  <Container
    theme={theme}
    as="section"
    styleProp={styles.content}
  >
    <RenderIf condition={notEmpty(logo)}>
      <Container
        as="div"
        flex
        flexRow
        align="center"
        paddingBlock="2"
      >
        <Image
          src={logo.url}
          alt="Icon"
          loading="lazy"
          style={{ objectFit: 'contain' }}
          width="30"
          height="30"
        />
      </Container>
    </RenderIf>
    <RenderIf condition={notEmpty(content)}>
      <Container as="div" styleProp={styles.quote}>
        {content}
      </Container>
    </RenderIf>
    <RenderIf condition={notEmpty(author)}>
      <Container
        as="section"
        styleProp={styles.author}
      >
        <Typography
          styleProp={styles.author}
          fontBold
          textCentered
          fontSecondary
          as="span"
        >
          {author}
        </Typography>
        <Typography as="span">
          {' | '}
        </Typography>
        <Rating
          iconColor="navy"
          withoutReviews
          withoutRating
          styleProp={ratingCustomStyle}
          rating={rating}
        />
      </Container>
    </RenderIf>
  </Container>
)

export default ReviewCard
