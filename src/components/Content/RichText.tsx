import Typography from '@components/Typography'
import Spacer from '@components/layout/Spacer'
import RichTextRenderer from '@utils/RichTextRenderer'
import Wrapper from '@components/layout/Wrapper'
import Container from '@components/layout/Container'

type RichTextProps = {
  header: string,
  content: {
    json: any
  }
}

const RichText = ({ header, content }: RichTextProps) => (
  <Wrapper as="div" size="5" pageGap>
    {header && (
      <>
        <Spacer size="lg" />
        <Typography as="h2" typographyTheme="h3Secondary">{header}</Typography>
        <Spacer size="md" />
      </>
    )}
    {content && (
      <Container as="article" flex gap="2">
        <RichTextRenderer content={content.json} />
      </Container>
    )}
  </Wrapper>
)

export default RichText
