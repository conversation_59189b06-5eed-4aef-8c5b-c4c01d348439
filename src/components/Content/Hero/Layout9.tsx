'use client'

import {
  spacing,
} from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import MediaContainer, { AssetType } from '@components/Content/MediaContainer'
import Container from '@components/layout/Container'
import {
  condition, equals, notEmpty
} from '@utils/checking'
import RenderIf from '@utils/renderIf'
import Typography from '@components/Typography'
import { AnchorProps } from '@components/Generic/CallToAction'
import RenderCTAS from '@/components/Generic/RenderCTAS'
import getMediaType from '@/utils/getMediaType'
import { TypographyThemes } from '@/app/typographyThemes.stylex'
import useIsMobile from '@/hooks/isMobile'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

const DEFAULT_ICON_SIZE = 14

type HeroLayout9Props = {
  id?: string
  inverse?: boolean
  media?: { items: AssetType[] }
  mobileMedia?: { items: AssetType[] }
  logo?: AssetType[]
  mediaPlaying?: boolean
  theme?: ThemeColors
  subHeadline?: string
  header?: string
  text?: string
  typeFace?: string
  buttons?: AnchorProps[]
  onPlayerChangeState?: (state: boolean) => void
  videoIconSize?: number
  layout?: string
}

const DESKTOP_QUERY = '@media (min-width: 1024px)'

const styles = stylex.create({
  container: {
    display: 'grid',
    gridTemplateColumns: {
      default: '1fr',
      [DESKTOP_QUERY]: '1fr 1fr',
    },
  },
  container12: {
    display: {
      default: 'flex',
      [DESKTOP_QUERY]: 'grid',
    },
    flexDirection: {
      default: 'column-reverse',
      [DESKTOP_QUERY]: null,
    },
  },
  ctas: {
    width: {
      default: '100%',
      [DESKTOP_QUERY]: 'auto',
    }
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    padding: {
      default: `${spacing.lg} ${spacing.md}`,
      [DESKTOP_QUERY]: `${spacing.lg} ${spacing.xl}`,
    }
  },
  media: {
    position: 'relative',
    lineHeight: '0',
    order: {
      default: '-1',
      [DESKTOP_QUERY]: 'initial',
    }
  },
  image: {
    width: '100%',
    maxWidth: '100%',
    height: 'auto',
  },
  justMobile: {
    display: {
      default: 'initial',
      [DESKTOP_QUERY]: 'none',
    },
  },
  justDesktop: {
    display: {
      default: 'none',
      [DESKTOP_QUERY]: 'initial',
    },
  },
  title: {
    margin: `${spacing.xs} 0 ${spacing.sm}`
  },
  logoContainer: {
    width: '100%',
    margin: `calc(${spacing.md} + ${spacing.md}) 0`,
    display: 'grid',
    gridTemplateColumns: {
      default: '1fr',
      [DESKTOP_QUERY]: '1fr 1fr',
    },
    gap: `calc(${spacing.xxs} * 4)`,
  },
  buttonsContainer: {
    marginTop: spacing.md,
  },
  inversedImage: {
    order: '-1'
  },
  mediaContainerHeight: { maxHeight: '100%' },
})

const HeroLayout9 = ({
  inverse = false,
  id = '',
  media = { items: [] },
  mobileMedia = { items: [] },
  logo = undefined,
  mediaPlaying = true,
  theme = 'cream',
  subHeadline = '',
  header = '',
  text = '',
  typeFace = 'primaryFontFamily',
  buttons = [],
  onPlayerChangeState = () => {},
  videoIconSize = DEFAULT_ICON_SIZE,
  layout,
}: HeroLayout9Props) => {
  const [desktopAsset] = media.items || []
  const [mobileAsset] = mobileMedia.items || []
  const layout12 = layout === 'layout12'
  const { isMobile } = useIsMobile()
  const textCenterMobile = layout12 && isMobile

  const renderMedia = (asset: AssetType, mediaStyles: {}) => (
    // Media Container Image do not have the configuration that I need for this layout
    // And I don't want to create any side effect by changing it
    <>
      <RenderIf condition={equals(getMediaType(asset?.url), 'image')}>
        <Image
          src={asset?.url}
          alt={header}
          width={asset?.width}
          height={asset?.height}
          quality={95}
          {...stylex.props(mediaStyles)}
        />
      </RenderIf>
      <RenderIf condition={equals(getMediaType(asset?.url), 'video')}>
        <MediaContainer
          key={asset?.url}
          fixedIcon
          iconSize={videoIconSize}
          onPlayerChangeState={onPlayerChangeState}
          autoPlay={mediaPlaying}
          header={header}
          asset={asset}
          imageHeight="280px"
          playIcon={false}
          styleProps={styles.mediaContainerHeight}
        />
      </RenderIf>
    </>
  )

  const titleTheme = condition<TypographyThemes>(
    equals(typeFace, 'primaryFontFamily'),
    'h4Primary',
    'h1Secondary'
  )

  const desktopMediaStyles = notEmpty(mobileAsset) ? [styles.justDesktop, styles.image] : [styles.image]

  return (
    <Container
      id={id}
      styleProp={[styles.container, layout12 && styles.container12]}
      theme={theme}
    >
      <Container styleProp={styles.content}>
        <Typography
          as="span"
          typographyTheme="subheading"
          textCentered={textCenterMobile}
        >
          { subHeadline}
        </Typography>
        <Typography
          as="h1"
          typographyTheme={titleTheme}
          styleProp={styles.title}
          textCentered={textCenterMobile}
        >
          { header }
        </Typography>
        <Container
          as="div"
          alignCentered={textCenterMobile}
        >
          { text }
        </Container>

        <RenderIf condition={notEmpty(logo)}>
          <Container styleProp={styles.logoContainer}>
            {
            logo?.map((logoItem) => (
              <Container key={logoItem.url} flex flexRow gap="1" alignCentered>
                <Image
                  src={logoItem?.url || ''}
                  width={logoItem?.width || 0}
                  height={logoItem?.height || 0}
                  alt={logoItem?.title || ''}
                />
                <Typography as="span" typographyTheme="bodySmall">{logoItem.title}</Typography>
              </Container>
            ))
          }
          </Container>
        </RenderIf>

        <RenderIf condition={notEmpty(buttons)}>
          <Container flex flexRow gap="2" styleProp={styles.buttonsContainer}>
            <RenderCTAS buttons={buttons} styleProp={layout12 && styles.ctas} />
          </Container>
        </RenderIf>

      </Container>
      <Container
        styleProp={
          [
            styles.media,
            inverse && styles.inversedImage
          ]
        }
      >
        { renderMedia(desktopAsset, desktopMediaStyles) }
        { renderMedia(mobileAsset, [styles.justMobile, styles.image]) }
      </Container>
    </Container>
  )
}

export default HeroLayout9
