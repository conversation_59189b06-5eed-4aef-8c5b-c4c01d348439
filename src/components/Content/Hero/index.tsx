'use client'

import { HeroSliderProps, LayoutProxy } from './SingleHeroSlider'

import {
  spacing
} from '@/app/themeTokens.stylex'
import RenderIf from '@/components/Generic/RenderIf'
import { notEmpty } from '@/utils/checking'
import Slider from '@/components/Generic/CustomSlider'
import { ThemeColors } from '@/app/themeThemes.stylex'

import Autoplay from 'embla-carousel-autoplay'
import * as stylex from '@stylexjs/stylex'
import { useState } from 'react'
import { EmblaOptionsType } from 'embla-carousel'

export type HeroProps = {
  anchor?: string;
  props: HeroSliderProps[];
  slideOptions?: EmblaOptionsType;
  theme?: ThemeColors;
}

const styles = stylex.create({
  sliderButtonsContainer: {
    position: 'absolute',
    bottom: {
      default: '1.7rem',
      '@media (min-width: 1024px)': '2.15rem',
    },
    right: {
      default: spacing.xl,
      '@media (min-width: 1024px)': `${spacing.xxl}`,
    },
  },
})

const checkIfIsSlider = (props: HeroSliderProps[]) => props.length > 1

const Hero = ({
  anchor = '',
  props = [],
  slideOptions = { slidesToScroll: 'auto', loop: true },
  theme
}: HeroProps) => {
  const [videoPlaying, setVideoPlaying] = useState(true)
  const [emblaApi, setEmblaApi] = useState<any>(null)
  const isSlider = checkIfIsSlider(props)
  const [firstSlide] = props

  const onPlayerChangeState = (playing: boolean, isSelected: boolean) => {
    const autoPlayApi = emblaApi?.plugins().autoplay

    if (isSelected && !playing) {
      autoPlayApi?.stop()
      setVideoPlaying(false)
    } else {
      autoPlayApi?.play()
      setVideoPlaying(true)
    }
  }

  return (
    <div id={anchor || 'hero'}>
      <RenderIf condition={!isSlider && notEmpty(firstSlide)}>
        <LayoutProxy {...firstSlide} />
      </RenderIf>
      <RenderIf condition={isSlider}>
        <Slider
          withSimpleDots
          getEmblaApi={setEmblaApi}
          plugins={[Autoplay({ playOnInit: true, delay: 5000 })]}
          buttonsContainerStyles={styles.sliderButtonsContainer}
          renderPrevNextButtons={() => null}
          extractSlideKey={({ id }) => id}
          playIcon
          videoPlaying={videoPlaying}
          renderSlide={(
            heroProps: HeroSliderProps,
            isSelected: boolean,
          ) => (
            <LayoutProxy
              {...heroProps}
              mediaPlaying={isSelected}
              onPlayerChangeState={(playing) => onPlayerChangeState(playing, isSelected)}
            />
          )}
          slidesPerPage={1}
          slides={props}
          options={slideOptions}
          theme={theme}
        />
      </RenderIf>
    </div>
  )
}

export default Hero
