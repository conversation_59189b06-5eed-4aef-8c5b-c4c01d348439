'use client'

import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@components/layout/Container'
import Slider, { CustomSliderProps } from '@/components/Generic/CustomSlider'
import MediaVideo, { MediaVideoProps } from '@/components/Generic/MediaVideo'
import RenderIf from '@/utils/renderIf'
import { notEmpty } from '@/utils/checking'
import Typography from '@/components/Typography'
import { spacing } from '@/app/themeTokens.stylex'
import CallToAction from '@/components/Generic/CallToAction'
import useIsMobile from '@/hooks/isMobile'

import * as stylex from '@stylexjs/stylex'

export type AssetSliderProps = Partial<CustomSliderProps> & {
  theme?: ThemeColors;
  slides: MediaVideoProps[];
  header?: string;
}

const styles = stylex.create({
  container: {
    padding: spacing.lg,
  },
  header: {
    marginBottom: spacing.lg,
  },
})

const DEFAULT_SLIDES_PER_PAGE = 3
const DEFAULT_SLIDES_PER_PAGE_MOBILE = 1.35
const AssetSlider = ({
  theme = undefined,
  header = '',
  slides = [],
  slidesPerPage = DEFAULT_SLIDES_PER_PAGE,
  ...rest
}: AssetSliderProps) => {
  const { isMobile } = useIsMobile()
  const safeSlidesPerPage = isMobile ? DEFAULT_SLIDES_PER_PAGE_MOBILE : slidesPerPage

  return (
    <Container as="div" theme={theme} styleProp={styles.container}>
      <RenderIf condition={notEmpty(header)}>
        <Typography as="h3" textCentered typographyTheme="h4Secondary" styleProp={styles.header}>
          {header}
        </Typography>
      </RenderIf>
      <Slider
        slidesPerPage={safeSlidesPerPage}
        slides={slides}
        renderDotsButtons={() => null}
        renderPrevNextButtons={() => null}
        extractSlideKey={({ id }) => id}
        renderSlide={({
          content,
          callToAction,
          ...restSlider
        }) => (
          <Container as="div" flex contentGap gap="2">
            <MediaVideo {...restSlider} objectFit="contain" imageHeight="350px" />
            { content }
            <RenderIf condition={notEmpty(callToAction)}>
              <CallToAction {...callToAction} />
            </RenderIf>
          </Container>
        )}
        {...rest}
      />
    </Container>
  )
}

export default AssetSlider
