import MediaContainer from './MediaContainer'

import * as stylex from '@stylexjs/stylex'

type MediaContainerWrapperProps = {
  asset: {
    url: string;
  };
}

const styles = stylex.create({
  root: {
    height: 'clamp(350px, 80vh, 750px)',
  },
})

const MediaContainerWrapper: React.FC<MediaContainerWrapperProps> = ({
  asset,
}: MediaContainerWrapperProps) => (
  <MediaContainer
    asset={asset}
    imageHeight="100%"
    autoPlay
    styleProps={styles.root}
    fixedIcon
  />
)

export default MediaContainerWrapper
