'use client'

import TetrisBlock from './TetrisBlock'

import { TetrisBlockProps } from '../types'

import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing } from '@/app/themeTokens.stylex'
import Slider from '@/components/Generic/Slider'

import React, { useState, useCallback } from 'react'
import * as stylex from '@stylexjs/stylex'

type TetrisModuleProps = {
  header: string
  subheader: string
  blocks: Array<TetrisBlockProps>
  theme: ThemeColors
}

const DESKTOP = '@media (min-width: 1024px)'
const LARGER = '@media (min-width: 1230px)'

const styles = stylex.create({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: {
      default: spacing.lg,
      [DESKTOP]: spacing.xl,
    },
    paddingTop: {
      default: spacing.xl,
      [DESKTOP]: '90px',
    },
    paddingBottom: spacing.xl,
    paddingInline: {
      default: '0px',
      [DESKTOP]: '20px',
      [LARGER]: '0px',
    },
  },
  titleWrapper: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    maxWidth: '1200px',
    paddingLeft: {
      default: spacing.md,
      [DESKTOP]: '0px',
    }
  },
  text: {
    maxWidth: '60ch',
  },
  grid: {
    width: '100%',
    display: {
      default: 'flex',
      [DESKTOP]: 'grid',
    },
    gridTemplateAreas: `
      "Primary Primary Secondary Secondary"
      "Primary Primary Tertiary Tertiary"
    `,
    gridTemplateColumns: '2fr 2fr 3fr',
    gap: {
      default: 0,
      [DESKTOP]: spacing.md,
    },
    marginInline: 'auto',
    maxWidth: '1200px',
    flexDirection: 'row',
  },
  blockWrapper: {
    scrollSnapAlign: 'start',
    flex: '0 0 auto',
    width: '300px',
    display: {
      default: 'block',
      [DESKTOP]: 'none',
    },
  },
  blockPadding: {
    paddingLeft: '20px',
  },
  lastBlock: {
    paddingRight: '20px',
  }
})

const getType = (index: number) => {
  if (index === 0) return 'Primary'
  if (index === 1) return 'Secondary'
  return 'Tertiary'
}

const TetrisModule = ({
  header,
  subheader,
  blocks,
  theme
}: TetrisModuleProps) => {
  const [emblaApi, setEmblaApi] = useState<any[]>([])

  useCallback(
    () => (emblaApi.length > 0 ? emblaApi[0].scrollPrev() : null),
    [emblaApi]
  )
  useCallback(
    () => (emblaApi.length > 0 ? emblaApi[0].scrollNext() : null),
    [emblaApi]
  )

  return (
    <Container as="div" theme={theme} paddingBlock="5" styleProp={styles.wrapper}>
      <Container as="div" flex start theme={theme} styleProp={styles.titleWrapper} gap="2">
        {header && (
          <Typography as="h2" typographyTheme="h2Secondary" styleProp={styles.text}>
            {header}
          </Typography>
        )}
        {subheader && (
          <Typography as="p" typographyTheme="bodyLarge" styleProp={styles.text}>
            {subheader}
          </Typography>
        )}
      </Container>

      <Container as="div" styleProp={styles.grid}>
        <Slider
          options={{ align: 'start' }}
          setEmblaList={setEmblaApi}
        >
          {blocks && blocks.map((block, index) => (
            <Container as="div" styleProp={[styles.blockWrapper, styles.blockPadding, index === blocks.length - 1 && styles.lastBlock]} key={block.text}>
              <TetrisBlock source={block.source} mobileSource={block.mobileSource} text={block.text} type={getType(index)} />
            </Container>
          ))}
        </Slider>
        {
          blocks?.length > 0 && blocks.map((block, index) => (
            <TetrisBlock source={block.source} mobileSource={block.mobileSource} text={block.text} type={getType(index)} />
          ))
        }
      </Container>
    </Container>
  )
}

export default TetrisModule
