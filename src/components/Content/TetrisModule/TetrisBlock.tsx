import { TetrisBlockProps } from '../types'

import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { colors, spacing } from '@/app/themeTokens.stylex'
import useIsMobile from '@/hooks/isMobile'
import getMediaType from '@/utils/getMediaType'

import React from 'react'
import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

const styles = stylex.create({
  gridMainItem: {
    gridArea: 'Primary',
    position: 'relative',
    overflow: 'hidden',
    borderRadius: spacing.xs,
  },
  gridSecondaryItem: {
    gridArea: 'Secondary',
    position: 'relative',
    overflow: 'hidden',
    borderRadius: spacing.xs,
  },
  gridTertiaryItem: {
    gridArea: 'Tertiary',
    position: 'relative',
    overflow: 'hidden',
    borderRadius: spacing.xs,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    color: colors.white,
    padding: 0,
    borderRadius: spacing.xxs,
  },
  text: {
    textWrap: 'balance',
    display: 'inline-block',
    marginBlockEnd: {
      default: spacing.sm,
      '@media (min-width: 1024px)': '50px',
    },
    marginInlineStart: {
      default: spacing.md,
      '@media (min-width: 1024px)': '50px',
    },
    maxWidth: '23ch',
  },
  imageWrapper: {
    position: 'relative',
    width: '100%',
    height: '100%',
    minHeight: '300px',
  },
  video: {
    width: '100%',
    height: '100%',
    minHeight: '300px',
    objectFit: 'cover',
    objectPosition: 'center',
    borderRadius: spacing.xs,
  }
})

const gridStyle = (type: string) => {
  switch (type) {
    case 'Primary':
      return styles.gridMainItem
    case 'Secondary':
      return styles.gridSecondaryItem
    case 'Tertiary':
      return styles.gridTertiaryItem
    default:
      return styles.gridMainItem
  }
}
const TetrisBlock = ({
  source,
  mobileSource,
  text,
  type
}: TetrisBlockProps) => {
  const { isMobile } = useIsMobile()
  const imageSource = mobileSource !== null && isMobile ? mobileSource : source

  return (
    <Container as="div" styleProp={gridStyle(type)}>
      <Container as="div" styleProp={styles.imageWrapper}>
        {getMediaType(source) !== 'video' ? (
          <Image
            src={imageSource}
            alt={text}
            layout="fill"
            objectFit="cover"
          />
        ) : (
          <video
            loop
            muted
            playsInline
            autoPlay
            {...stylex.props(styles.video)}
          >
            <source src={source} type="video/mp4" />
          </video>
        )}
        <Container as="div" styleProp={styles.imageOverlay}>
          <Typography as="span" typographyTheme="bodyLarge" fontBold styleProp={styles.text}>
            {text}
          </Typography>
        </Container>
      </Container>
    </Container>
  )
}

export default TetrisBlock
