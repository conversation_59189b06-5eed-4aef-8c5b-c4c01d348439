import { spacing } from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import Wrapper from '@components/layout/Wrapper'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'
import Link from 'next/link'
import { FC } from 'react'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  root: {
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: spacing.sm,
    },
  },
  grid: {
    display: 'grid',
    gridTemplateColumns: {
      default: 'repeat(1, 1fr)',
      [DESKTOP]: 'repeat(3, 1fr)'
    }
  },
  card: {
    display: 'grid',
    gridAutoFlow: 'row',
    position: 'relative',
    overflow: 'hidden',
  },
  headerContainer: {
    marginBottom: spacing.lg,
  },
  imageContainer: {
    flex: '1',
    position: 'relative',
    width: '100%',
    height: {
      default: 145,
      [DESKTOP]: 'auto',
    },
    aspectRatio: '16/9',
  },
  detailsContainer: {
    flex: '1',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
  },
})

const FeaturedStories: FC<{
  articles: {
    image: string,
    category: string,
    title: string,
    id: string,
    url: string
  }[]
  header?: string
}> = ({ articles, header = 'Featured Stories' }) => (
  <Wrapper as="section" size="xl" paddingBlock="4" styleProp={styles.root}>
    {header && (
      <Container styleProp={styles.headerContainer}>
        <Typography as="h5" typographyTheme="h5Secondary" fontBold textCentered>
          {header}
        </Typography>
      </Container>
    )}

    <Container gap="4" styleProp={styles.grid}>
      {articles.map((article) => (
        <Link href={article.url} key={`article-${article.id}`}>
          <Container gap="2" styleProp={styles.card}>
            {article.image && (
              <Container styleProp={styles.imageContainer}>
                <Image
                  fill
                  src={article.image}
                  alt={article.title || ''}
                  objectFit="cover"
                />
              </Container>
            )}
            <Container gap="1" flex>
              {article.category && (
                <Typography
                  as="span"
                  typographyTheme="subheading"
                  uppercase
                >
                  {article.category}
                </Typography>
              )}
              {article.title && (
                <Typography
                  as="h6"
                  typographyTheme="h6Secondary"
                  fontBold
                >
                  {article.title}
                </Typography>
              )}
            </Container>
          </Container>
        </Link>
      ))}
    </Container>
  </Wrapper>
)

export default FeaturedStories
