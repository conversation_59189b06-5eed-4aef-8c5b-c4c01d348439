import { ThemeColors } from '@/app/themeThemes.stylex'
import { breakpoints, colors, spacing } from '@/app/themeTokens.stylex'
import CallToAction, { CTAProps } from '@/components/Generic/CallToAction'
import Container from '@/components/layout/Container'
import Wrapper from '@/components/layout/Wrapper'
import Typography from '@/components/Typography'
import RichTextRender from '@/utils/RichTextRenderer'

import { Document } from '@contentful/rich-text-types'
import { FC } from 'react'
import * as stylex from '@stylexjs/stylex'

const TABLET = '@media (max-width: 640px)'

type TradeProps = {
  header: string;
  content: { json: Document };
  theme?: ThemeColors;
  slides?: Array<{ label: string; tags: string[]; }>;
  buttons?: CTAProps[];
};

const styles = stylex.create({
  wrapper: {
    backgroundColor: colors.offWhite,
    justifyContent: 'center',
    display: 'flex',
  },
  main: {
    maxWidth: breakpoints.md,
    paddingInline: 20,
    paddingBlock: {
      default: 80,
      [TABLET]: 40,
    },
  },
  cta: {
    width: {
      [TABLET]: '100%',
    },
  },
  contentContainer: {
    flexDirection: {
      default: 'row',
      [TABLET]: 'column',
    },
    paddingTop: spacing.md,
    gap: {
      default: 60,
      [TABLET]: 20,
    },
    width: {
      default: 'auto',
      [TABLET]: '100%',
    },
  },
  contentLabel: {
    flex: '1',
  },
  contentDescription: {
    flex: {
      default: '3',
      [TABLET]: '1',
    },
    minWidth: 300,
  },
  tags: {
    overflow: 'hidden',
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr',
    gap: 16,
  },
  tagRow: {
    flex: '1',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 35.25,
  },
  tag: {
    position: 'relative',
    flex: '1',
    textAlign: 'center',
    '::after': {
      display: 'inline-block',
      position: 'absolute',
      content: '•',
      left: '100%',
      marginLeft: spacing.xs
    }
  },
  bullet: {
    height: 4,
    width: 4,
    borderRadius: '100%',
    backgroundColor: 'black',
    content: ' ',
    marginInline: 6,
  },
  textCenter: {
    textAlign: 'center',
    maxWidth: '645px',
  }
})

const Trade: FC<TradeProps> = ({
  header,
  content,
  theme = 'white',
  slides,
  buttons = [],
}) => {
  const { tags, label } = slides?.length ? slides[0] : { tags: [], label: '' }

  return (
    <Wrapper theme={theme} styleProp={styles.wrapper}>
      <Container flexCentered as="section" gap="4" styleProp={styles.main}>
        <Typography as="h4" fontSecondary fontBold textCentered>
          {header}
        </Typography>
        {content && (
          <Container styleProp={styles.textCenter}>
            <RichTextRender content={content?.json} />
          </Container>
        )}

        <Container flex flexRow alignCentered gap="2">
          {buttons && buttons.map((button, i) => (
            <CallToAction
              larger={i === 0}
              key={button.id}
              {...button}
            />
          ))}
        </Container>

        {label ? (
          <Container as="div" alignCentered flex styleProp={styles.contentContainer}>
            <div {...stylex.props(styles.contentLabel)}>
              <Typography size="h3" as="h3" lineHeight="md">
                {label}
              </Typography>
            </div>
            <div {...stylex.props(styles.contentDescription)}>
              <div {...stylex.props(styles.tags)}>
                {
                  tags.map((tag, index) => (
                    <Typography
                      key={`tag-${tag}`}
                      as="span"
                      styleProp={styles.tag}
                      size="bodySmall"
                      textLeft={index > 1}
                      textCentered={index === 1}
                      textRight={index === 0}
                    >
                      {tag}
                    </Typography>
                  ))

                }
              </div>
            </div>
          </Container>
        ) : null}
      </Container>
    </Wrapper>
  )
}

export default Trade
