import { ThemeColors } from '@/app/themeThemes.stylex'
import { globalTokens } from '@/app/themeTokens.stylex'
import CallToAction from '@/components/Generic/CallToAction'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'

import * as stylex from '@stylexjs/stylex'

type SmallSliderTabProps = {
  title: string;
  theme: ThemeColors;
  index: number;
  activeIndex: number;
  setActiveIndex: React.Dispatch<React.SetStateAction<number>>;
};

const styles = stylex.create({
  wrapper: {
    transition: globalTokens.transition,
    height: 'fit-content',
    width: '100%',
  },
  tabbed: {
    display: 'flex',
    alignContent: 'center',
    justifyContent: 'center',
    minHeight: '72px',
    textAlign: 'center',
    paddingTop: {
      default: 0,
      '@media (min-width: 1024px)': 24
    },
  },
  label: {
    display: 'flex',
    alignSelf: 'center',
    maxWidth: '120px'
  },
  underLineLabel: {
    display: 'flex',
    alignSelf: 'center',
    textDecoration: 'underline',
    maxWidth: '120px'
  }
})

const SmallSliderTab = ({
  title,
  theme,
  index,
  setActiveIndex,
  activeIndex,
}: SmallSliderTabProps) => (
  <Container as="div" styleProp={styles.wrapper} flexCentered theme={theme}>
    <CallToAction
      variant="transparent"
      onClick={() => setActiveIndex(index)}
      styleProp={styles.wrapper}
    >
      <div
        className={`tabbed tab-${index} ${stylex.props(styles.tabbed).className}`}
      >
        <Typography as="h6" typographyTheme="h6Primary" styleProp={activeIndex === index ? styles.underLineLabel : styles.label}>
          {title}
        </Typography>
      </div>
    </CallToAction>
  </Container>

)

export default SmallSliderTab
