'use client'

import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

type SliderItemProps = {
  image: string;
  description: string;
  title: string;
  text: string;
  theme: ThemeColors;
  isOpen: boolean;
  setActiveIndex: (index: number) => void;
  activeIndex: number;
  index: number;
  length: number;
};

const styles = stylex.create({
  container: {
    height: '100%',
    display: 'flex',
    justifyContent: 'end'
  },
  wrapper: {
    width: 'calc(100% - 260px)',
    display: 'block',
    position: 'absolute',
    top: 0,
    height: '100%',
    transition: 'transform 0.4s'
  },
  hoverWrapper: {
    width: 'calc(100% - 260px)',
  },
  visible: {
    display: 'flex',
  },
  hidden: {
    display: 'none',
  },
  transition: {
    transform: 'translateX(calc(-100% + 130px))'
  },
  sliderOpen: {
    padding: '45px',
    width: {
      default: '510px',
      '@media (min-width: 990px)': '75vw',
    },
    maxWidth: {
      default: '510px',
      '@media (min-width: 990px)': '86vw',
      '@media (min-width: 2560px)': '64vw !important',
    },
    maxHeight: {
      default: '600px',
      '@media (min-width: 990px)': '800px',
    },
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: {
      default: 'column',
      '@media (min-width: 1440px)': 'row',
    }
  },
  sliderClose: {
    position: 'relative',
    display: 'flex',
    alignItems: 'end',
    justifyContent: 'center',
    width: {
      default: '7vw',
      '@media (min-width: 2560px)': '4vw !important',
    },
  },
  closeText: {
    position: 'absolute',
    bottom: '12vh',
    rotate: '-90deg',
    width: '150px',
  },
  contentWrapper: {
    flexDirection: {
      default: 'column',
      '@media (min-width: 1440px)': 'row',
    },
    flexWrap: 'nowrap',
    alignItems: 'center',
    gap: {
      default: '1rem',
      '@media (min-width: 1440px)': '3rem',
    },
    height: '100%',
  },
  imageContainer: {
    width: '100%',
    maxWidth: {
      default: '540px',
      '@media (min-width: 1440px)': '770px',
    },
  },
  image: {
    maxHeight: '450px',
    display: 'block',
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    opacity: 1,
  },
  text: {
    maxWidth: {
      default: '530px',
      '@media (min-width: 1440px)': '340px',
    },
  },
  title: {
    marginBottom: '12px',
  }
})

const SliderItem = ({
  image,
  description,
  title,
  text,
  theme,
  isOpen,
  setActiveIndex,
  activeIndex,
  index,
  length
}: SliderItemProps) => {
  const handleMouseEnter = () => {

  }

  const needTransition = activeIndex > index

  return (
    <div
      className={`
        ${stylex.props(styles.wrapper).className}
        ${isOpen && stylex.props(styles.hoverWrapper).className}
        ${needTransition && stylex.props(styles.transition).className}
    `}
      style={{ left: `calc(130px * ${index})`, zIndex: length - index }}
      onMouseMove={() => {
        handleMouseEnter()
        setActiveIndex(index)
      }}
    >
      <Container as="div" styleProp={styles.container} theme={theme}>
        {isOpen ? (
          <div className={`slider-open 
          ${stylex.props(styles.sliderOpen).className}
        `}
          >
            <Container as="div" flex styleProp={styles.contentWrapper}>
              <Container as="div" styleProp={styles.imageContainer}>
                <Image
                  src={image}
                  alt={title}
                  width={333}
                  height={509}
                  sizes="100vw"
                  {...stylex.props(styles.image)}
                />
              </Container>
              <Container as="div" styleProp={styles.text}>
                <Typography as="p" typographyTheme="subheading" fontBold uppercase>{text}</Typography>
                <Typography as="h2" typographyTheme="h4Secondary" styleProp={styles.title}>{title}</Typography>
                <Container as="div">{description}</Container>
              </Container>
            </Container>
          </div>
        ) : (
          <div className={`slider-close ${stylex.props(styles.sliderClose).className}`}>
            <Typography as="h6" typographyTheme="h6Secondary" fontSecondary fontBold styleProp={styles.closeText}>{title}</Typography>
          </div>
        )}
      </Container>
    </div>
  )
}

export default SliderItem
