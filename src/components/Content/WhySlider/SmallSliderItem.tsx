import { ThemeColors } from '@/app/themeThemes.stylex'
import { globalTokens } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { condition } from '@/utils/checking'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

type SmallSliderItemProps = {
  image: string;
  description: string;
  title: string;
  text: string;
  theme: ThemeColors;
  index: number;
  activeIndex: number;
};

const styles = stylex.create({
  wrapper: {
    transition: globalTokens.transition,
    height: 'fit-content',
    width: '100%',
    padding: 24,
  },
  image: {
    width: '100%',
    height: '333px',
    objectFit: 'cover'
  },
  visible: {
    display: 'block',
    width: '100%',
  },
  hidden: {
    display: 'none',
  },
})

const SmallSliderItem = ({
  image,
  description,
  title,
  theme,
  index,
  activeIndex,
}: SmallSliderItemProps) => (
  <div
    {...stylex.props(condition<{}>(
      activeIndex === index,
      styles.visible,
      styles.hidden
    ))}
  >
    <Container
      as="div"
      styleProp={styles.wrapper}
      flexCentered
      theme={theme}
      gap="2"
    >
      <Image
        src={image}
        alt={title}
        width={0}
        height={0}
        sizes="100vw"
        {...stylex.props(styles.image)}
      />
      <Container as="div">
        <Typography as="p" typographyTheme="bodyLarge">
          {description}
        </Typography>
      </Container>
    </Container>
  </div>
)

export default SmallSliderItem
