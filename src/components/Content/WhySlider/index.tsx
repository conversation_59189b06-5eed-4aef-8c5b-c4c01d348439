'use client'

import SliderItem from './SliderItem'
import SmallSliderItem from './SmallSliderItem'
import SmallSliderTab from './SmallSliderTab'

import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@/components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import { useState } from 'react'

type WhySliderProps = {
  theme: ThemeColors;
  items: SliderItemProps[];
};

type SliderItemProps = {
  image: string;
  description: string;
  title: string;
  text: string;
  theme: ThemeColors;
};

const styles = stylex.create({
  wrapperBig: {
    position: 'relative',
    height: '568px',
    flexWrap: 'nowrap',
    display: {
      default: 'none',
      '@media (min-width: 992px)': 'flex',
    },
  },
  wrapperSmall: {
    flexWrap: 'nowrap',
    width: '100%',
    display: {
      default: 'flex',
      '@media (min-width: 992px)': 'none',
    },
  },
})

const WhySlider = ({ theme, items }: WhySliderProps) => {
  const [activeIndex, setActiveIndex] = useState(0)
  return (
    <Container as="section" theme={theme} flexRow>
      <Container as="div" flexRow styleProp={styles.wrapperBig}>
        {items.map((item, index) => (
          <SliderItem
            key={item.title}
            image={item.image}
            description={item.description}
            title={item.title}
            text={item.text}
            theme={item.theme}
            isOpen={index === activeIndex}
            setActiveIndex={setActiveIndex}
            index={index}
            activeIndex={activeIndex}
            length={items.length}
          />
        ))}
      </Container>
      <Container as="div" flexCentered styleProp={styles.wrapperSmall}>
        <Container as="div" flexRow styleProp={styles.wrapperSmall}>
          {items.map((item, index) => (
            <SmallSliderTab
              key={item.title}
              index={index}
              title={item.title}
              theme={item.theme}
              activeIndex={activeIndex}
              setActiveIndex={setActiveIndex}
            />
          ))}
        </Container>
        {items.map((item, index) => (
          <SmallSliderItem
            key={item.title}
            image={item.image}
            index={index}
            description={item.description}
            title={item.title}
            text={item.text}
            theme={item.theme}
            activeIndex={activeIndex}
          />
        ))}
      </Container>
    </Container>
  )
}

export default WhySlider
