import {
  defaultTheme as $T,
  globalTokens as $,
  spacing
} from '@/app/themeTokens.stylex'
import themes, { ThemeColors } from '@/app/themeThemes.stylex'

import * as stylex from '@stylexjs/stylex'

const marquee = stylex.keyframes({
  from: {
    transform: 'translateX(0)',
  },
  to: {
    transform: 'translateX(calc(-100% - 1rem))',
  },
})

const styles = stylex.create({
  marquee: {
    position: 'relative',
    display: 'flex',
    overflow: 'hidden',
    userSelect: 'none',
    gap: spacing.xxl,
    paddingBlock: '24px',
    backgroundColor: $T.primarySurface,
    color: $T.primaryText,
  },
  animation: {
    animationName: marquee,
    animationDuration: '16s',
    animationTimingFunction: 'linear',
    animationIterationCount: 'infinite',
    animationPlayState: {
      default: 'running',
      '@media (prefers-reduced-motion: reduce)': 'paused',
    }
  },
  content: {
    flexShrink: 0,
    display: 'flex',
    justifyContent: 'start',
    gap: spacing.xxl,
    minWidth: 'initial',
  },
  item: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontWeight: $.fontWeightBold,
  }
})

type MarqueeProps = {
  slides: Array<{ text: string, id: string }>;
  theme?: ThemeColors;
};

const Marquee = ({ slides, theme = 'navy' }: MarqueeProps) => {
  const themeStyles = themes[theme]
  const MAX_SLIDES = 5
  const DUPLICATE_FACTOR_FEW_SLIDES = 10
  const DUPLICATE_FACTOR_MANY_SLIDES = 2
  let duplicateItems
  if (slides.length === 0) {
    duplicateItems = 1
  } else if (slides.length <= MAX_SLIDES) {
    duplicateItems = Math.floor(DUPLICATE_FACTOR_FEW_SLIDES / slides.length)
  } else {
    duplicateItems = DUPLICATE_FACTOR_MANY_SLIDES
  }

  return (
    <section>
      <div
        {...stylex.props(themeStyles, styles.marquee)}
      >
        {[...Array(duplicateItems)].map((_, i) => (
          <div
            key={`${slides[i]?.id}-${String(i)}`}
            {...(i === 1 && { 'aria-hidden': true })}
            {...stylex.props(
              styles.content,
              styles.animation
            )}
          >
            {slides.map((slide) => (
              <div key={slide.id} {...stylex.props(styles.item)}>
                {slide.text}
              </div>
            ))}
          </div>
        ))}
      </div>
    </section>
  )
}

export default Marquee
