import Typography from '@/components/Typography'
import Container from '@/components/layout/Container'
import { colors, spacing } from '@/app/themeTokens.stylex'
import CallToAction from '@/components/Generic/CallToAction'
import { WeeklyDealsDeal } from '@/components/Content/WeeklyDeals/types'
import { ThemeColors } from '@/app/themeThemes.stylex'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  dealsGrid: {
    display: {
      default: 'none',
      [DESKTOP]: 'grid',
    },
    gridAutoFlow: 'dense',
    gridAutoRows: 'repeat(4, auto)',
    gridTemplateColumns: 'repeat(auto-fit, minmax(min(230px, 100%), 1fr))',
    paddingInline: spacing.sm,
  },
  dealCard: {
    gridRow: 'span 4',
    gridTemplateRows: 'subgrid',
    justifyItems: 'center',
    borderRadius: spacing.lg,
    paddingBlock: spacing.lg,
  },
  button: {
    display: 'inline-block',
    padding: 'unset',
    backgroundColor: 'transparent',
    textAlign: 'unset',
    color: {
      default: colors.navy,
      ':hover': colors.gray500,
    },
    cursor: 'pointer',
    textDecoration: 'underline',
    marginBlockStart: spacing.md,
  },
  buttonDisabled: {
    color: colors.gray,
    cursor: 'not-allowed',
    opacity: 0.6,
    pointerEvents: 'none',
    textDecoration: 'none',
  },
  description: {
    maxWidth: '17ch',
    textAlign: 'center',
    color: colors.navy,
  },
})

const WeeklyDealsDesktopBlock = ({ deals }: { deals: WeeklyDealsDeal[] }) => {
  const activeIndex = deals.findIndex((deal) => deal.settings.active)
  return (
    <Container grid gap="2" styleProp={styles.dealsGrid}>
      {deals?.map((deal, index) => {
        const disabled = activeIndex !== -1 && index !== activeIndex
        return (
          <Container
            grid
            key={deal.id}
            theme={deal.settings.active ? deal.settings.theme as ThemeColors : undefined}
            styleProp={styles.dealCard}
          >
            {deal.media?.items[0] && (
              <Image
                src={deal.media.items[0].url}
                alt={deal.media.items[0].title}
                width={deal.media.items[0].width}
                height={deal.media.items[0].height}
              />
            )}
            <Typography
              as="h3"
              typographyTheme="bodyLarge"
              fontBold={index === activeIndex}
            >
              {deal.title}
            </Typography>
            <Container styleProp={styles.description}>
              {deal.text}
            </Container>
            {deal.button && (
              <CallToAction
                styleProp={[
                  styles.button,
                  disabled && styles.buttonDisabled
                ]}
                theme="transparent"
                href={deal.button.href}
                disabled={disabled}
              >
                {deal.button.children}
              </CallToAction>
            )}
          </Container>
        )
      })}
    </Container>
  )
}

export default WeeklyDealsDesktopBlock
