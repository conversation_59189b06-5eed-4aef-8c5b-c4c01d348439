import WeeklyDealsDesktopBlock from './WeeklyDealsDesktop'
import WeeklyDealsMobileSliderBlock from './WeeklyDealsMobile'

import Typography from '@/components/Typography'
import Container from '@/components/layout/Container'

import * as stylex from '@stylexjs/stylex'

import type { WeeklyDealsProps } from './types'

const styles = stylex.create({
  container: {
    paddingBlock: '2rem',
    margin: '0 auto',
  }
})

const WeeklyDeals = ({ header, deals }: WeeklyDealsProps) => (
  <Container grid gap="4" size="5" styleProp={styles.container}>
    <Typography as="h2" textCentered typographyTheme="h2Secondary">{header}</Typography>
    <WeeklyDealsDesktopBlock deals={deals} />
    <WeeklyDealsMobileSliderBlock deals={deals} />
  </Container>
)

export default WeeklyDeals
