import { CallToActionExtractProps } from '@/utils/generateCallToActionProps'

import type { ContentfulRichText } from '@/lib/contentful/types/generic'

export type WeeklyDealsSettings = {
  theme?: string;
  layout?: string;
  active?: boolean;
}

type WeeklyDealsBlockContent = {
  name: string;
  content: ContentfulRichText;
  settings: WeeklyDealsSettings;
  assetsCollection: {
    url: string;
  };
  referencesCollection: any;
  sys: {
    id: string;
  };
}

type WeeklyDealsBlocksCollectionType = {
  items: WeeklyDealsBlockContent[];
}

type WeeklyDealsMediaItem = {
  url: string;
  title: string;
  fileName: string;
  width: number;
  height: number;
}

export type WeeklyDealsDeal = {
  media: {
    items: WeeklyDealsMediaItem[];
  };
  title: string;
  text: React.ReactNode;
  id: string;
  button?: CallToActionExtractProps;
  settings: WeeklyDealsSettings;
}

export type WeeklyDealsSection = {
  blocksCollection: WeeklyDealsBlocksCollectionType;
  deals: WeeklyDealsDeal[];
}

export type WeeklyDealsProps = {
  header: string;
  deals: WeeklyDealsDeal[];
}
