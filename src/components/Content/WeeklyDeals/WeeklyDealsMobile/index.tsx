'use client'

import Typography from '@/components/Typography'
import Container from '@/components/layout/Container'
import { colors, spacing } from '@/app/themeTokens.stylex'
import CallToAction from '@/components/Generic/CallToAction'
import Slider, { Slide } from '@/components/Generic/Slider'
import { WeeklyDealsDeal } from '@/components/Content/WeeklyDeals/types'
import { ThemeColors } from '@/app/themeThemes.stylex'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const MOBILE = '@media (max-width: 1023px)'

const styles = stylex.create({
  dealCard: {
    justifyItems: 'center',
    display: {
      default: 'none',
      [MOBILE]: 'grid',
    },
    gridAutoFlow: 'dense',
    gridAutoRows: 'repeat(4, auto)',
    gap: spacing.md,
    flex: {
      default: '0 0 100%',
      '@media (min-width: 320px)': '0 0.07 100%',
      '@media (min-width: 400px)': '0 0.12 100%',
      '@media (min-width: 500px)': '0 0.15 100%',
      '@media (min-width: 700px)': '0 0.2 100%',
    },
  },
  dealCardContent: {
    display: 'grid',
    gridRow: 'span 4',
    gridTemplateRows: 'subgrid',
    justifyItems: 'center',
    borderRadius: spacing.sm,
    paddingBlock: spacing.lg,
    paddingInline: spacing.md,
  },
  description: {
    maxWidth: '17ch',
    textAlign: 'center',
  },
  button: {
    display: 'inline-block',
    padding: 'unset',
    textDecoration: 'underline',
    backgroundColor: 'transparent',
    color: colors.navy,
    textAlign: 'unset',
  },
  buttonDisabled: {
    color: colors.gray,
    cursor: 'not-allowed',
    opacity: 0.6,
    pointerEvents: 'none',
    textDecoration: 'none',
  }
})

const WeeklyDealsMobileSliderBlock = ({ deals }: { deals: WeeklyDealsDeal[] }) => {
  const activeIndex = deals.findIndex((deal) => deal.settings.active)

  return (
    <Slider
      setEmblaList={() => { }}
      options={{
        containScroll: false,
        startIndex: Math.max(activeIndex, 0)
      }}
    >
      {deals.map((deal, index) => {
        const disabled = activeIndex !== -1 && index !== activeIndex
        return (
          <Slide key={deal.id} styleProp={styles.dealCard}>
            <Container
              styleProp={styles.dealCardContent}
              theme={deal.settings.active ? deal.settings.theme as ThemeColors : undefined}
            >
              {deal.media.items[0] && (
              <Image
                src={deal.media.items[0].url}
                alt={deal.media.items[0].title}
                width={deal.media.items[0].width}
                height={deal.media.items[0].height}
              />
              )}
              <Typography
                as="h3"
                typographyTheme="bodyLarge"
                fontBold={index === activeIndex}
              >
                {deal.title}
              </Typography>
              <Container styleProp={styles.description}>{deal.text}</Container>
              {deal.button && (
              <CallToAction
                styleProp={[
                  styles.button,
                  disabled && styles.buttonDisabled
                ]}
                theme="transparent"
                href={deal.button.href}
                disabled={disabled}
              >
                <Typography as="span" typographyTheme="bodyLarge" fontBold>{deal.button.children}</Typography>
              </CallToAction>
              )}
            </Container>
          </Slide>
        )
      })}
    </Slider>
  )
}

export default WeeklyDealsMobileSliderBlock
