import { toCamelCase } from '@/utils/regex'

// Define reusable types for better type safety
type DeviceType = 'desktop' | 'tablet' | 'mobile';
type Alignment = 'left' | 'right' | 'center' | 'top' | 'bottom' | 'mediaRight' | 'mediaLeft' | 'mediaBottom' | 'mediaTop' | 'full';

// Utility function to determine if the current device matches the given type
const isCurrentDevice = (deviceType: DeviceType, isTablet: boolean, isMobile: boolean): boolean => (deviceType === 'desktop' && !isTablet && !isMobile) ||
        (deviceType === 'tablet' && isTablet && !isMobile) ||
        (deviceType === 'mobile' && isMobile)

// Get the maximum height for media based on the device type
export const getMediaMaxHeight = (
  desktopMediaMaxH?: string,
  tabletMediaMaxH?: string,
  mobileMediaMaxH?: string,
  isTablet: boolean = false,
  isMobile: boolean = false
): string | null => {
  if (isTablet) return tabletMediaMaxH || null
  if (isMobile) return mobileMediaMaxH || null
  return desktopMediaMaxH || null
}

// Generate styles for media max height
export const getMediaMaxHeightStyles = (maxHeight?: string): React.CSSProperties => ({
  maxHeight: maxHeight || 'initial',
  ...maxHeight ? { height: maxHeight } : {},
})

// Generate alignment styles for a specific device
const getAlignmentStyles = (
  verticalAlignment: Alignment,
  horizontalAlignment: Alignment,
  isDevice: boolean
): React.CSSProperties => {
  if (!isDevice) return {}

  const alignmentStyles: React.CSSProperties = {}

  if (verticalAlignment === 'left') alignmentStyles.left = '0px'
  if (verticalAlignment === 'right') alignmentStyles.right = '0px'
  if (verticalAlignment === 'center') {
    alignmentStyles.left = '0px'
    alignmentStyles.right = '0px'
    alignmentStyles.margin = 'auto'
  }

  if (horizontalAlignment === 'top') alignmentStyles.top = '0px'
  if (horizontalAlignment === 'bottom') alignmentStyles.bottom = '0px'
  if (horizontalAlignment === 'center') {
    alignmentStyles.top = '0px'
    alignmentStyles.bottom = '0px'
    alignmentStyles.margin = 'auto'
  }

  return alignmentStyles
}

// Get alignment styles for a specific device type
export const getDeviceAlignmentStyles = (
  deviceType: DeviceType,
  verticalAlignment: Alignment = 'center',
  horizontalAlignment: Alignment = 'center',
  isTablet: boolean = false,
  isMobile: boolean = false
): React.CSSProperties => {
  const isDevice = isCurrentDevice(deviceType, isTablet, isMobile)
  return getAlignmentStyles(verticalAlignment, horizontalAlignment, isDevice)
}

// Get media alignment breakpoints
export const getMediaAlignmentBreakpoints = (
  desktopAlignment?: string,
  tabletAlignment?: string,
  mobileAlignment?: string,
  isTablet: boolean = false,
  isMobile: boolean = false
): string | null => {
  if (isTablet) return tabletAlignment ? toCamelCase(tabletAlignment) : null
  if (isMobile) return mobileAlignment ? toCamelCase(mobileAlignment) : null
  return desktopAlignment ? toCamelCase(desktopAlignment) : null
}

// Generate styles for media alignment
export const getMediaAlignmentStyles = (alignment?: Alignment): React.CSSProperties => {
  const alignmentStyles: React.CSSProperties = {}

  if (alignment !== 'full') {
    alignmentStyles.display = 'flex'
  }
  if (alignment === 'mediaRight') {
    alignmentStyles.flexDirection = 'row-reverse'
  }
  if (alignment === 'mediaLeft') {
    alignmentStyles.flexDirection = 'row'
  }
  if (alignment === 'mediaBottom') {
    alignmentStyles.flexDirection = 'column-reverse'
  }
  if (alignment === 'mediaTop') {
    alignmentStyles.flexDirection = 'column'
  }

  return alignmentStyles
}

// Generate styles for the content wrapper based on alignment
export const getMediaAlignmentContentWrapperStyles = (
  alignment?: Alignment,
  isTablet: boolean = false,
  isMobile: boolean = false
): React.CSSProperties => {
  const alignmentStyles: React.CSSProperties = {}

  if (alignment !== 'full') {
    alignmentStyles.position = 'relative'
    alignmentStyles.width = '100%'
    alignmentStyles.minHeight = isMobile || isTablet ? 'initial' : undefined
    alignmentStyles.height = isTablet ? '100%' : undefined
  }

  return alignmentStyles
}

// Generate styles for the media alignment wrapper
export const getMediaAlignmentWrapperStyles = (alignment?: Alignment): React.CSSProperties => {
  const alignmentStyles: React.CSSProperties = {}

  if (alignment === 'mediaLeft' || alignment === 'mediaRight') {
    alignmentStyles.aspectRatio = 'auto'
  }

  return alignmentStyles
}
