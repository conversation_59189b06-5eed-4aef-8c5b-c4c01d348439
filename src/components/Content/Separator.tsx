import themes, { ThemeColors } from '@/app/themeThemes.stylex'
import {
  spacing,
  defaultTheme as $T
} from '@/app/themeTokens.stylex'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'

type Size = '1px' | '2px' | '3px' | '4px' | '5px' | '6px' | '7px' | '8px' | '9px' | '10px';
type Padding = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10';
type Width = '25%' | '50%' | '75%' | '100%';
type Align = 'start' | 'center' | 'end';

type SeparatorProps = {
  parentSize?: Size;
  childrenSize?: Size;
  padding?: Padding;
  width?: Width;
  align?: Align;
  theme?: ThemeColors
}

const styles = stylex.create({
  root: {
    height: spacing.md,
    borderStyle: 'none',
    overflow: 'visible',
    marginTop: spacing.sm,
    '::before': {
      display: 'block',
      position: 'relative',
      top: `calc(${spacing.sm} * -1)`,
      content: '""',
      width: '100%',
      backgroundColor: $T.primarySurface,
      marginBottom: spacing.xs,
    },
  },
  height: (parentSize: Size) => ({
    '::before': {
      height: parentSize,
    }
  }),
  width: (childrenSize: Size) => ({
    background: `repeating-linear-gradient(90deg, ${$T.primarySurface}, ${$T.primarySurface} ${childrenSize}, transparent 0, transparent ${spacing.xs})`,
  })
})

const hrWidth = stylex.create({
  '25%': {
    width: '25%',
  },
  '50%': {
    width: '50%',
  },
  '75%': {
    width: '75%',
  },
  '100%': {
    width: '100%',
  },
})

const alignContent = stylex.create({
  start: {
    justifyContent: 'start',
  },
  center: {
    justifyContent: 'center',
  },
  end: {
    justifyContent: 'end',
  },
})

const Separator = ({
  parentSize = '5px',
  childrenSize = '1px',
  padding = '5',
  width = '100%',
  align = 'center',
  theme = 'babyBlue300',
}: SeparatorProps) => (
  <Container
    as="div"
    flex
    flexRow
    paddingBlock={padding}
    styleProp={alignContent[align]}
  >
    <hr {...stylex.props(
      styles.root,
      styles.height(parentSize),
      styles.width(childrenSize),
      hrWidth[width],
      themes[theme]
    )}
    />
  </Container>
)

export default Separator
