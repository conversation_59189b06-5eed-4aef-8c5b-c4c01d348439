import Typography from '../Typography'

import { ThemeColors } from '@/app/themeThemes.stylex'
import {
  globalTokens as $,
  defaultTheme as $T,
  colors,
  fontSizes,
  spacing,
} from '@/app/themeTokens.stylex'
import CallToAction, { AnchorProps } from '@components/Generic/CallToAction'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

type ExtendedAnchorProps = AnchorProps & { text: string };

const MOBILE = '@media (max-width: 1024px)'

export type ValuePropsProps = {
  header?: string;
  slides: { text: string, id: string, image: { url: string, fileName: string }[] }[];
  buttons?: ExtendedAnchorProps[];
  theme?: ThemeColors;
  layout?: string;
}

const styles = stylex.create({
  btn: {
    maxWidth: {
      default: '211px',
      [MOBILE]: '100%'
    }
  }
})

const layout1 = stylex.create({
  container: {
    paddingBlock: {
      default: spacing.xxl,
      [MOBILE]: spacing.md,
    },
    paddingInline: {
      default: spacing.xxl,
      [MOBILE]: spacing.md,
    }
  },
  headerTypography: {
    textAlign: 'left',
    color: $T.secondaryText
  },
  slidesContainer: {
    maxWidth: '1350px',
    marginBottom: spacing.lg,
    justifyContent: {
      default: 'space-between',
      [MOBILE]: 'center',
    }
  },
  slideContent: {
    maxWidth: '320px',
    display: 'flex',
    textAlign: 'left',
    flexDirection: {
      default: 'column',
      [MOBILE]: 'row',
    },
    gap: {
      default: '0',
      [MOBILE]: spacing.md
    },
  },
  titleContainer: {
    display: 'block',
    color: $T.secondaryText
  },
  textContainer: {
    display: 'block',
  },
  slideImage: {
    marginBottom: spacing.md,
    alignItems: 'flex-start',
    width: {
      default: spacing.xxl,
      [MOBILE]: spacing.xl,
    },
    height: {
      default: spacing.xxl,
      [MOBILE]: spacing.xl,
    }
  },
  textStyle: {
    color: $T.secondaryText
  }
})

const layout2 = stylex.create({
  container: {
    alignItems: 'center',
    paddingBlock: {
      default: spacing.xxl,
      [MOBILE]: spacing.md,
    },
    paddingInline: {
      default: spacing.xxl,
      [MOBILE]: spacing.md,
    }
  },
  headerTypography: {
    textAlign: 'center',
  },
  slidesContainer: {
    maxWidth: '1350px',
    width: '100%',
    marginBottom: spacing.lg,
    justifyContent: 'space-evenly',
    gap: spacing.xxl,
  },
  slideContent: {
    maxWidth: '320px',
    display: 'flex',
    alignItems: 'center',
    textAlign: 'center',
    flexDirection: {
      default: 'column',
      [MOBILE]: 'row',
    },
    gap: {
      default: '0',
      [MOBILE]: spacing.md
    },
  },
  titleContainer: {
    maxWidth: '170px',
    margin: '0 auto',
    display: 'block',
    fontWeight: 'normal',
  },
  textContainer: {
    display: 'block',
  },
  slideImage: {
    marginBottom: spacing.md,
    alignItems: 'flex-start',
    width: '100%',
    maxWidth: '100px',
    height: {
      default: spacing.xxl,
      [MOBILE]: spacing.xl,
    }
  },
  textStyle: {}
})

const layout3 = stylex.create({
  container: {
    alignItems: 'center',
    paddingBlock: {
      default: spacing.xxl,
      [MOBILE]: spacing.md,
    },
    paddingInline: {
      default: spacing.xxl,
      [MOBILE]: spacing.md,
    }
  },
  headerTypography: {
    marginBottom: spacing.lg,
  },
  slidesContainer: {
    maxWidth: '1350px',
    marginBottom: spacing.lg,
    justifyContent: 'center',
    gap: {
      default: spacing.lg,
      [MOBILE]: spacing.md,
    }
  },
  slideContent: {
    maxWidth: '150px',
    display: 'flex',
    alignItems: 'center',
    textAlign: 'center',
    flexDirection: 'column',
    gap: spacing.sm,
  },
  titleContainer: {
    display: 'none',
  },
  slideImage: {
    alignItems: 'flex-start',
    width: {
      default: spacing.xxl,
      [MOBILE]: spacing.xl,
    },
    height: {
      default: spacing.xxl,
      [MOBILE]: spacing.xl,
    },
    objectFit: 'none',
  },
  textStyle: {}
})

const layout4 = stylex.create({
  container: {
    alignItems: 'center',
    paddingBlock: {
      default: spacing.lg,
      [MOBILE]: spacing.md,
    },
    paddingInline: spacing.md,
    color: colors.cream,
  },
  headerTypography: {
    textAlign: 'center',
    fontSize: fontSizes.h4,
    marginBottom: spacing.md,
  },
  slidesContainer: {
    maxWidth: 640,
    marginBottom: 0,
    justifyContent: 'center',
    flexWrap: {
      default: 'nowrap',
      [MOBILE]: 'wrap'
    },
    gap: {
      default: spacing.lg,
      [MOBILE]: spacing.md,
    }
  },
  slideContent: {
    maxWidth: '150px',
    display: 'flex',
    alignItems: 'center',
    textAlign: 'center',
    flexDirection: 'column',
    gap: spacing.sm,
  },
  titleContainer: {
    display: 'none',
  },
  slideImage: {
    alignItems: 'flex-start',
    width: {
      default: spacing.xxl,
      [MOBILE]: spacing.xl,
    },
    height: {
      default: spacing.xxl,
      [MOBILE]: spacing.xl,
    },
    objectFit: 'none',
  },
  textStyle: {
    textTransform: 'uppercase',
    fontWeight: $.fontWeightBold,
    fontSize: fontSizes.xs,
  }
})

const layout5 = stylex.create({
  container: {
    alignItems: 'center',
    paddingBlock: {
      default: spacing.lg,
      [MOBILE]: spacing.md,
    },
    paddingInline: {
      default: spacing.xxl,
      [MOBILE]: spacing.md,
    }
  },
  headerTypography: {
    textAlign: 'center',
  },
  slidesContainer: {
    maxWidth: '1350px',
    width: '100%',
    marginBottom: 0,
    justifyContent: 'center',
    gap: {
      default: spacing.xxl,
      [MOBILE]: spacing.sm,
    },
  },
  slideContent: {
    maxWidth: {
      default: '320px',
      [MOBILE]: '100px',
    },
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    flex: '1',
    flexDirection: {
      default: 'row',
      [MOBILE]: 'column',
    },
    gap: {
      default: spacing.md,
      [MOBILE]: spacing.md
    },
  },
  titleContainer: {
    maxWidth: '115px',
    margin: '0 auto',
    display: 'block',
    fontWeight: 'normal',
    fontSize: {
      default: fontSizes.bodySmall,
      [MOBILE]: fontSizes.captionLarge,
    }
  },
  textContainer: {
    display: 'block',
  },
  slideImage: {
    marginBottom: 0,
    alignItems: 'flex-start',
    width: '100%',
    height: 'auto',
  },
  textStyle: {}
})

type Layout =
  typeof layout1 |
  typeof layout2 |
  typeof layout3 |
  typeof layout4 |
  typeof layout5;

const layouts = new Map<string, Layout>([
  ['layout1', layout1],
  ['layout2', layout2],
  ['layout3', layout3],
  ['layout4', layout4],
  ['layout5', layout5],
])

const ValueProps = ({
  header,
  slides,
  buttons = [],
  theme = 'cream',
  layout = 'layout1',
}: ValuePropsProps) => {
  const mainLayoutStyles = layouts.get(layout) || layout1
  return (
    <Container
      as="section"
      theme={theme}
      styleProp={mainLayoutStyles.container}
      flex
    >
      {header && (
        <Typography
          as="h4"
          marginBottom="xl"
          typographyTheme="h4Secondary"
          styleProp={mainLayoutStyles.headerTypography}
        >
          {header}
        </Typography>
      )}

      <Container
        as="div"
        flex
        flexRow
        gap="4"
        styleProp={mainLayoutStyles.slidesContainer}
      >
        {slides?.length > 0 && slides.map((slide: any) => (
          <Container
            as="div"
            styleProp={mainLayoutStyles.slideContent}
            key={slide.id}
          >
            {slide.image[0] && (
              <Image
                alt={slide.image[0].fileName}
                src={slide.image[0].url}
                width={slide.image[0].width}
                height={slide.image[0].height}
                style={{
                  maxWidth: `${slide.image[0].width}px`,
                  aspectRatio: `${slide.image[0].width}/${slide.image[0].height}`,
                }}
                unoptimized
                {...stylex.props(mainLayoutStyles.slideImage)}
              />
            )}
            <Container as="div">
              {slide.title && (
                <Typography
                  as="h4"
                  marginBottom="xs"
                  size="md"
                  fontBold
                  styleProp={mainLayoutStyles.titleContainer}
                >
                  {slide.title}
                </Typography>
              )}
              {slide.text && (
                <Typography
                  as="p"
                  typographyTheme="bodyLarge"
                  styleProp={mainLayoutStyles.textStyle}
                >
                  {slide.text}
                </Typography>
              )}
            </Container>
          </Container>
        ))}
      </Container>
      {buttons && buttons.map((button, i) => (
        <CallToAction
          key={button.id}
          larger={i === 0}
          {...button}
          styleProp={styles.btn}
        />
      ))}
    </Container>
  )
}

export default ValueProps
