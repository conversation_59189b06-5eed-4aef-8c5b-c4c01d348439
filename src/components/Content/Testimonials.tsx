'use client'

import ReviewCard from './ReviewCard'

import CustomSlider from '@components/Generic/CustomSlider'
import Rating, { RatingProps } from '@components/Generic/Rating'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { fontSizes, spacing, colors } from '@/app/themeTokens.stylex'

import stylex from '@stylexjs/stylex'
import Image from 'next/image'
import { EmblaOptionsType } from 'embla-carousel'

export type TestimonialSlide = {
  type: 'quote' | 'image';
  url?: string;
  rating?: number;
  content: any;
  author: string,
  logo: {
    url: string
  },
}

type TestimonialsProps = {
  theme?: ThemeColors
  header?: string;
  ratingProps?: RatingProps;
  slides?: TestimonialSlide[];
}

const DESKTOP_SLIDES_PER_PAGE = 3
const MOBILE_SLIDES_PER_PAGE = 3
const SLIDER_MOBILE_WIDTH_CALC = 1.45

const EMBLA_CAROUSEL_OPTIONS: EmblaOptionsType = {
  align: 'center',
  slidesToScroll: MOBILE_SLIDES_PER_PAGE,
  startIndex: 1,
  loop: true,
  breakpoints: {
    '(min-width: 1024px)': {
      slidesToScroll: DESKTOP_SLIDES_PER_PAGE,
      startIndex: 0,
    },
  }
}

const styles = stylex.create({
  header: {
    lineHeight: fontSizes.h2
  },
  subHeader: {
    lineHeight: fontSizes.md
  },
  wrapper: {
    maxWidth: '890px',
    margin: 'auto',
  },
  dotsContainerStyles: {
    marginTop: '32px',
    justifyContent: 'center',
    gap: '32px',
  },
  dotsEachContainerStyles: {
    gap: '16px',
  },
  dotsStyles: {
    display: 'inline-block',
    width: spacing.sm,
    height: spacing.sm,
    borderRadius: '50%',
    borderStyle: 'solid',
    borderWidth: '2px',
    borderColor: colors.gray300,
    backgroundColor: colors.gray300,
    textIndent: '-9999px',
    cursor: 'pointer',
  },
  slideCustom: {
    position: 'relative',
    minWidth: '272px',
    marginRight: spacing.md,
  },
  ratingCustomStyle: { marginBottom: null },
})

const Testimonials = ({
  theme = 'white',
  header,
  slides = [],
  ratingProps
}: TestimonialsProps) => {
  const MOBILE = 768

  const isMobile = () => {
    // TODO: Remove this unnecessary check
    if (typeof window !== 'undefined') {
      return window.innerWidth < MOBILE
    }
    return false
  }
  const slidesPerpage = isMobile() ? SLIDER_MOBILE_WIDTH_CALC : DESKTOP_SLIDES_PER_PAGE
  // Store Rating and Review Count
  const { reviewCount, rating: storeRating } = ratingProps || {}
  return (
    <Container
      as="section"
      theme={theme}
      gap="3"
      paddingBlock="5"
    >
      <Container
        as="div"
        flex
        flexCentered
        paddingBlock="5"
      >
        {header && (
          <Typography
            marginBottom="xs"
            typographyTheme="h2Primary"
            as="h2"
            styleProp={styles.header}
          >
              {header}
          </Typography>
        )}
        <Container
          as="div"
          flex
          flexRow
          gap="1"
        >
          <Rating
            withoutReviews={!reviewCount}
            withoutRating={!storeRating}
            reviewCount={reviewCount}
            iconColor="marigold"
            textContentOn="right"
            rating={storeRating}
            isAggregated
          />
        </Container>
      </Container>
      <Container as="div" styleProp={styles.wrapper}>
        <CustomSlider
          slides={slides}
          slidesPerPage={slidesPerpage}
          extractSlideKey={({ id }) => id}
          dotsContainerStyles={styles.dotsContainerStyles}
          dotsEachContainerStyles={styles.dotsEachContainerStyles}
          dotsStyles={styles.dotsStyles}
          renderDotsWithArrows
          renderPrevNextButtons={() => null}
          slideStyleProp={styles.slideCustom}
          arrowsSize="large"
          renderSlide={({
            type,
            content,
            author,
            logo,
            rating,
            url,
            title,
          }) => (
            type === 'quote'
              ? (
                <ReviewCard
                  author={author}
                  content={content}
                  logo={logo}
                  rating={rating}
                  ratingCustomStyle={styles.ratingCustomStyle}
                />
              )
              : (
                <Image
                  loading="lazy"
                  src={url}
                  alt={title || ''}
                  fill
                  style={{ objectFit: 'cover' }}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              )
          )}
          options={EMBLA_CAROUSEL_OPTIONS}
          theme={theme}
        />
      </Container>
    </Container>
  )
}

export default Testimonials
