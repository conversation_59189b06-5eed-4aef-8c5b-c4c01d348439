'use client'

import AnchorNavigationCollection from '@/components/Content/AnchorNavigationCollection'
import InstructionsListCircle from '@/components/Content/InstructionsListCircle'
import ValueProps from '@/components/Content/ValueProps'
import Hero from '@/components/Content/Hero'
import {
  globalTokens as $,
  colors, fontSizes, spacing
} from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Faq from '@/components/layout/Faq'
import Typography from '@components/Typography'
import debounce from '@/utils/debounce'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'
import { useCallback, useEffect, useState } from 'react'

const LG = '@media screen and (min-width: 992px)'
const XL = '@media screen and (min-width: 1280px)'

const styles = stylex.create({
  root: {
    minHeight: '100vh',
  },
  desktopImage: {
    objectFit: 'cover',
  },
  intro: (bgImage: string, bgImageMobile: string) => ({
    display: 'flex',
    alignItems: 'center',
    backgroundImage: {
      [LG]: `url(${bgImage})`,
      default: `url(${bgImageMobile})`,
    },
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    minHeight: {
      [LG]: 375,
      default: 275
    },
    justifyContent: {
      default: 'flex-start',
      [LG]: 'center',
    },
    flexDirection: {
      default: 'column',
      [LG]: 'row',
    },
    paddingLeft: spacing.lg,
    paddingRight: spacing.lg,
    paddingTop: spacing.md,
  }),
  pageTitle: {
    flex: '1',
  },
  desktopNav: {
    flex: {
      default: '0',
      [XL]: '1'
    },
    display: {
      default: 'none',
      [LG]: 'block'
    },
    minWidth: 350,
    position: 'sticky',
    paddingRight: spacing.md,
    paddingBottom: spacing.md,
    paddingTop: 104,
    paddingLeft: 100,
    top: spacing.lg
  },
  desktopLinks: {
    marginTop: spacing.lg,
    display: 'block',
    color: colors.gray300,
  },
  activeLink: {
    color: colors.navy,
  },
  linkWrapper: { cursor: 'pointer', },
  activeLinkWrapper: { pointerEvents: 'none', },
  mobileNav: {
    display: {
      default: 'flex',
      [LG]: 'none'
    },
    paddingBlock: 0,
    position: 'sticky',
    top: 95,
    zIndex: 50,
    boxShadow: '0 4px 10px 0 rgba(0 0 0 / 15%)',
    transition: $.transitionSmooth,
    transform: 'translateY(0)',
  },
  mobileNavSticky: {
    transform: `translateY(calc(${$.promoBarHeight} * -1))`,
  },
  slideContainer: {
    paddingInline: spacing.md
  },
  pill: {
    background: colors.cream,
    color: colors.navy,
    marginInline: spacing.xs
  },
  contentContainer: {
    display: 'flex',
    alignItems: 'flex-start',
    maxWidth: {
      default: '100%',
      [XL]: 1440
    },
    marginInline: 'auto',
    marginBlock: 0,
  },
  divider: {
    height: spacing.xxs,
    borderBlockWidth: 1,
    borderBlockStyle: 'solid',
    borderBlockColor: colors.navy,
    marginBlockStart: spacing.md,
    marginInline: 'auto',
    maxWidth: {
      default: 600,
      [LG]: 750
    },
  },
  textStyle: {
    textTransform: 'uppercase',
    fontWeight: $.fontWeightBold,
    fontSize: fontSizes.xs,
  },
  imageMobileContainer: {
    position: 'relative',
    height: '60vh',
    display: {
      default: 'block',
      [LG]: 'none'
    }
  },
  imageDesktopContainer: {
    position: 'relative',
    height: 378,
    display: {
      default: 'none',
      [LG]: 'block'
    }
  },
  sectionsContainer: {
    flex: '1',
    maxWidth: 930,
    minWidth: {
      default: 0,
      [XL]: 930
    }
  },
  section: {
    marginBlock: spacing.md,
    scrollMarginTop: {
      default: 171,
      [LG]: 168
    }
  },
  faq: {
    paddingInline: {
      default: 0,
      [LG]: spacing.xxl
    }
  },
  faqBase: {
    paddingBlock: spacing.lg,
    paddingInline: 0,
  },
  pillWrapper: {
    color: {
      default: colors.navy,
      ':hover': null
    },
    backgroundColor: {
      default: colors.cream,
      ':hover': null
    },
    borderColor: colors.white
  },
  pillWrapperActive: {
    color: {
      default: colors.white,
      ':hover': null
    },
    backgroundColor: {
      default: colors.navy,
      ':hover': null
    },
    borderColor: colors.white
  },
})

type CareAndCleaningProps = {
  sections: any
}

const scrollThreshold = 500
const scrollTop = 400
const debounceDelay = 50

const CareAndCleaning = ({
  sections = []
}: CareAndCleaningProps) => {
  const [isSticky, setIsSticky] = useState<boolean>(false)
  const [lastScrollY, setLastScrollY] = useState<number>(0)
  const [filteredSections, setFilteredSections] = useState<any>([])
  const [activeBtn, setActiveBtn] = useState<number>(0)
  const [lastActiveBtn, setLastActiveBtn] = useState<number>(0)

  const handleScroll = useCallback(debounce(() => {
    const { scrollY: currentScrollY } = window
    if (currentScrollY > scrollThreshold && currentScrollY > lastScrollY) {
      setIsSticky(true)
    } else {
      setIsSticky(false)
    }

    sections.filter((section: any) => section.subtype === 'Value Props')
      .forEach((section: any, index: any) => {
        const element = document.getElementById(section.sys.id)
        if (element) {
          const rect = element.getBoundingClientRect()
          const foundElement = rect.top >= 0 && rect.bottom <= window.innerHeight
          if (foundElement) {
            setLastActiveBtn(parseInt(index, 10) - 1)
            setActiveBtn(parseInt(index, 10))
            setLastScrollY(currentScrollY)
          }
          if (currentScrollY < (lastScrollY - scrollTop)) {
            setActiveBtn(lastActiveBtn)
          }
        }
      })
  }, debounceDelay), [lastScrollY, sections, lastActiveBtn])

  useEffect(() => {
    const filtered = sections
      .filter((section: any) => section.subtype === 'Value Props')
      .map((section: any) => ({
        ...section,
        text: section.header,
        heading: section.header,
        id: section.sys.id,
        href: `#${section.sys.id}`,
        variant: 'primary'
      }))

    setFilteredSections(filtered)

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [handleScroll, sections])

  const handleActive = useCallback((id: string) => {
    const element = document.getElementById(id)
    if (element) {
      const yOffset = -200
      const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset
      window.scrollTo({ top: y, behavior: 'instant' })
    }
  }, [])

  const renderLink = (link: any, index: any) => (
    <Container
      as="div"
      key={`link-${link.id}`}
      onClick={() => handleActive(link.id)}
      styleProp={[
        styles.linkWrapper,
        activeBtn === parseInt(index, 10) && styles.activeLinkWrapper,
      ]}
    >
      <Typography
        as="h6"
        fontBold
        typographyTheme="h6Primary"
        styleProp={[styles.desktopLinks, activeBtn === parseInt(index, 10) && styles.activeLink]}
      >
        {link.heading}
      </Typography>
    </Container>
  )

  const renderIntro = (section: any) => (
    <Container key={`section-${section.sys.id}`} id={section.sys.id} styleProp={styles.section}>
      <ValueProps
        layout={section.layout}
        header={section.header}
        slides={section.slides}
        theme="navy"
      />
    </Container>
  )

  const renderIntroImage = (section: any) => {
    const mobileImage = section?.mobileAssetsCollection?.items[0]?.url || null
    const desktopImage = section?.assetsCollection?.items[0]?.url || null
    return (
      <Container key={`section-${section.sys.id}`} id={section.sys.id} styleProp={styles.section}>
        {mobileImage && (
          <Container styleProp={styles.imageMobileContainer}>
            <Image alt="banner" fill src={mobileImage} />
          </Container>
        )}
        {desktopImage && (
          <Container styleProp={styles.imageDesktopContainer}>
            <Image
              {...stylex.props(styles.desktopImage)}
              alt="banner"
              fill
              src={desktopImage}
            />
          </Container>
        )}
      </Container>
    )
  }
  const renderInstruction = (section: any) => (
    <Container key={`section-${section.sys.id}`} id={section.sys.id} styleProp={styles.section}>
      {!section.header.includes('Before Cooking') && (
        <div {...stylex.props(styles.divider)} />
      )}
      <InstructionsListCircle
        header={section.header}
        slides={section.slides}
        theme={section.theme}
        uppercaseTitle
      />
    </Container>
  )

  const renderFAQ = (section: any) => (
    <Container
      key={`section-${section.sys.id}`}
      id={section.sys.id}
      styleProp={{ ...styles.section, ...styles.faq }}
      theme={section.theme}
    >
      <Faq
        header={section.header ?? ''}
        slides={section.slides}
        centered
        theme={section.theme}
        baseStyle={styles.faqBase}
        settings={section?.settings}
      />
    </Container>
  )

  const renderSections = (section: any) => {
    switch (section.subtype) {
      case 'Value Props':
        return renderIntro(section)
      case 'Media Container':
        return renderIntroImage(section)
      case 'Instructions List Circle':
        return renderInstruction(section)
      case 'Faq':
        return renderFAQ(section)
      default:
        return null
    }
  }

  return (
    <Container as="section" styleProp={styles.root}>
      {sections[0]?.subtype === 'Hero' && (
        <Hero props={sections[0].props} />
      )}

      {filteredSections.length > 0 && (
        <AnchorNavigationCollection
          containerStyle={{
            ...styles.mobileNav,
            ...isSticky && styles.mobileNavSticky
          }}
          emblaOptions={{
            dragFree: true,
            skipSnaps: true
          }}
          slides={filteredSections}
          theme="white"
          startEndMargins
          centered={false}
          pillStyles={styles.pillWrapper}
          pillActiveStyles={styles.pillWrapperActive}
        />
      )}
      <Container as="section" styleProp={styles.contentContainer}>
        <Container as="aside" styleProp={styles.desktopNav}>
          {filteredSections.map(renderLink)}
        </Container>
        <Container id="caraway-non-stick" styleProp={styles.sectionsContainer}>
          {sections.map(renderSections)}
        </Container>
      </Container>
    </Container>
  )
}

export default CareAndCleaning
