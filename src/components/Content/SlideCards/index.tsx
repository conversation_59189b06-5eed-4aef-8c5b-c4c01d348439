'use client'

import MediaContainer from '../MediaContainer'

import {
  globalTokens,
  spacing,
} from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Slider from '@/components/Generic/CustomSlider'
import Container from '@components/layout/Container'
import Wrapper from '@/components/layout/Wrapper'
import Typography from '@/components/Typography'
import RenderIf from '@/utils/renderIf'
import useIsMobile, { useMatchMedia } from '@/hooks/isMobile'
import { empty, notEmpty } from '@/utils/checking'
import CallToAction, { CTAProps } from '@/components/Generic/CallToAction'

import * as stylex from '@stylexjs/stylex'
import { ReactNode, useState } from 'react'

type AssetType = {
  url: string
  alt: string
}

type HoverCardProps = {
  setPlayVideo?: boolean
  theme: ThemeColors
  isMobile: boolean
  title: string
  content: string
  asset: AssetType,
  mobileAsset: AssetType,
  mobileContent?: string
  callToAction: CTAProps
}

export type SlideCardsProps = {
  theme?: ThemeColors
  header: string
  subheader: string
  content: string
  slides: HoverCardProps[]
}

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  container: {
    paddingBlock: spacing.xl
  },
  headingContainer: {
    marginBottom: spacing.lg,
  },
  heading: {
    marginTop: spacing.xs,
    marginBottom: spacing.md,
    whiteSpace: {
      default: 'normal',
      [DESKTOP]: 'nowrap'
    }
  },
  slide: {
    position: 'relative',
    aspectRatio: '0.73',
    borderRadius: globalTokens.borderRadius,
    overflow: 'hidden',
  },
  slideContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: `calc(${spacing.md} + ${spacing.xs}) ${spacing.md}`,
    zIndex: 2,
    transform: 'translate3d(0, 0, 0)',
  },
  media: {
    maxHeight: 'none',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 1,
  },
  sliderHovered: {
    display: 'block',
  },
  slideWrapper: {
    padding: {
      default: `0 ${spacing.md}`,
      [DESKTOP]: `0 ${spacing.md}`,
    }
  },
  cardText: {
    margin: `${spacing.xs} 0 ${spacing.md}`,
  },
  hideOverlay: {
    display: 'none',
  },
  skeleton: {
    width: '100%',
    boxSizing: 'border-box',
    aspectRatio: {
      default: '1',
      [DESKTOP]: '1.6',
    },
  }
})

const HoverCard = ({
  setPlayVideo = false,
  theme,
  asset,
  mobileAsset,
  isMobile,
  title,
  callToAction,
  content,
  mobileContent
}: HoverCardProps) => {
  const [hovered, setHovered] = useState(false)

  const onMouseEnter = !isMobile
    ? () => {
      setHovered(true)
    }
    : null

  const onMouseLeave = !isMobile
    ? () => {
      setHovered(false)
    }
    : () => null

  const noTitle = empty(title)

  const contentStyles = () => [
    styles.slideContent,
    (noTitle && !hovered && !isMobile) && styles.hideOverlay,
    hovered && styles.sliderHovered,
  ]
  const mobileMedia = () => mobileAsset || asset
  const media = () => (isMobile ? mobileMedia() : asset)
  const mobileText = () => mobileContent || content
  const text = () => (isMobile ? mobileText() : content) as ReactNode

  const isPlaying = isMobile
    ? setPlayVideo
    : hovered

  const autoPlay = isMobile && setPlayVideo

  return (
    <Container styleProp={styles.slide} onMouseLeave={onMouseLeave} onMouseEnter={onMouseEnter}>
      <MediaContainer
        iconOnTopCorner
        objectFit="contain"
        asset={media() as AssetType}
        styleProps={styles.media}
        autoPlay={autoPlay}
        setIsPlaying={isPlaying}
      />
      <Container styleProp={contentStyles()} theme={theme}>
        <RenderIf condition={notEmpty(title)}>
          <Typography as="h5" typographyTheme="bodyLarge" fontBold>
            {title}
          </Typography>
        </RenderIf>
        <RenderIf condition={hovered || isMobile}>
          <RenderIf condition={notEmpty(text)}>
            <Typography as="p" typographyTheme="bodyLarge" styleProp={styles.cardText}>
              {text()}
            </Typography>
          </RenderIf>
          <RenderIf condition={notEmpty(callToAction)}>
            <Container>
              <CallToAction {...callToAction} />
            </Container>
          </RenderIf>
        </RenderIf>
      </Container>
    </Container>
  )
}

const ONE_SLIDES_PER_PAGE = 1.15
const THREE_SLIDES_PER_PAGE = 3
const FOUR_SLIDES_PER_PAGE = 4

const SlideCards = ({
  theme = 'offWhite',
  header,
  subheader,
  content,
  slides = []
}: SlideCardsProps) => {
  const { isMobile, isReady } = useIsMobile()

  // 1024 - 1150
  const desktopIntermediate = useMatchMedia('(min-width: 1024px) and (max-width: 1150px)')

  const DESKTOP_SLIDES_PER_PAGE = desktopIntermediate
    ? THREE_SLIDES_PER_PAGE
    : FOUR_SLIDES_PER_PAGE

  const slidesPerPage = isMobile
    ? ONE_SLIDES_PER_PAGE
    : DESKTOP_SLIDES_PER_PAGE

  return (
    <Container as="section" theme={theme} styleProp={styles.container}>
      <Wrapper size="sm" styleProp={styles.headingContainer} pageGap>
        <RenderIf condition={notEmpty(subheader)}>
          <Typography as="p" typographyTheme="subheading" uppercase textCentered>
            { subheader }
          </Typography>
        </RenderIf>
        <RenderIf condition={notEmpty(header)}>
          <Typography as="h4" typographyTheme="h4Secondary" textCentered styleProp={styles.heading}>
            { header }
          </Typography>
        </RenderIf>
        <RenderIf condition={notEmpty(content)}>
          <Container as="div" flex alignCentered>
            { content }
          </Container>
        </RenderIf>
      </Wrapper>
      <Wrapper size="xl" styleProp={styles.slideWrapper}>
        <RenderIf condition={isReady}>
          <Slider
            options={{ align: 'start' }}
            slidesPerPage={slidesPerPage}
            slides={slides}
            renderDotsButtons={() => null}
            renderPrevNextButtons={() => null}
            extractSlideKey={({ id }) => id}
            gap={20}
            renderSlide={(slider, isSelected) => (
              <HoverCard isMobile={isMobile} setPlayVideo={isSelected} {...slider} />
            )}
          />
        </RenderIf>
        <RenderIf condition={!isReady}>
          <div {...stylex.props(styles.skeleton)} />
        </RenderIf>
      </Wrapper>

    </Container>
  )
}

export default SlideCards
