'use client'

import { CallToActionVariants } from '@components/Generic/CallToAction/types'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing } from '@/app/themeTokens.stylex'
import PillCollection from '@components/Generic/PillFilter/PillCollection'
import Slider, { Slide } from '@components/Generic/Slider'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import debounce from '@/utils/debounce'

import * as stylex from '@stylexjs/stylex'
import { useCallback, useEffect, useState } from 'react'

// TODO: combine with AnchorNavigation.tsx to reduce overhead

type PillProps = {
  text:string
  href: string
  variant?: CallToActionVariants
};
const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  base: {
    boxSizing: 'border-box',
    margin: 0,
    minWidth: 0,
    width: '100%',
    maxWidth: 'unset',
    overflow: 'hidden',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  swiper: {
    paddingInlineStart: {
      default: spacing.md,
      [DESKTOP]: spacing.xl
    },
    margin: '1rem 0',
    paddingBlock: '.25rem',
    maxWidth: '100%',
    listStyle: 'none',
    position: 'relative',
    display: 'flex',
    zIndex: 1,
    boxSizing: 'border-box',
    height: '100%',
    transitionProperty: 'transform',
    width: '100%',
    transform: 'translate3d(0px, 0px, 0px)',
    transitionDuration: '0ms',
    transitionTimingFunction: 'ease-out',
  },
  sliderItems: {
    margin: '16px 0px',
    width: '100%',
    justifyContent: 'flex-start',
    maxWidth: '1240px',
    overflow: 'unset'
  },
  slideItem: {
    display: 'flex',
    alignItems: 'center',
    textAlign: 'center',
    maxWidth: 'fit-content',
    paddingLeft: '10px',
  },
  firstSlide: {
    marginLeft: spacing.md,
    paddingLeft: 0
  },
  lastSlide: {
    marginRight: spacing.md,
  },
})

const ANCHOR_LIMIT = 3
const SLIDES_THRESHOLD = 6
const debounceDelay = 200

const AnchorNavigationCollection = ({
  theme = 'offWhite',
  header,
  subheader,
  slides,
  emblaOptions = {},
  containerStyle = {},
  startEndMargins = false,
  centered = true,
  pillStyles,
  pillActiveStyles,
}: {
  theme: ThemeColors
  header?: string
  subheader?: string,
  emblaOptions?: Record<string, any>,
  containerStyle?: Record<string, any>,
  slides: PillProps[],
  startEndMargins?: boolean,
  centered?: boolean,
  pillStyles?: {}
  pillActiveStyles?: {}
}) => {
  const [activeId, setActiveId] = useState('')
  const [emblaApi, setEmblaApi] = useState<any[]>([])

  useCallback(
    () => (emblaApi.length > 0 ? emblaApi[0].scrollPrev() : null),
    [emblaApi]
  )
  useCallback(
    () => (emblaApi.length > 0 ? emblaApi[0].scrollNext() : null),
    [emblaApi]
  )

  const handleScroll = useCallback(debounce(() => {
    slides.forEach((item: PillProps) => {
      const element = document.getElementById(item.href.replace('#', ''))
      if (element) {
        const rect = element.getBoundingClientRect()
        const foundElement = rect.top >= 0 && rect.bottom <= window.innerHeight
        if (foundElement) setActiveId(item.href)
      }
    })
  }, debounceDelay), [slides])

  useEffect(() => {
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  return (
    <Container
      as="div"
      theme={theme}
      flex
      paddingBlock="4"
      styleProp={{ ...styles.base, ...containerStyle }}
    >
      {(subheader || header) && (
      <Container gap="2" flexCentered paddingBlock="2" paddingInline="3">
        {subheader && <Typography as="p" typographyTheme="subheading" uppercase textCentered>{subheader}</Typography>}
        {header && <Typography as="h1" typographyTheme="h1Secondary" textCentered>{header}</Typography>}
      </Container>
      )}
      {slides && (
        <Slider
          startThenCenter
          options={{ align: 'start', container: 'div', ...emblaOptions }}
          center={slides?.length < ANCHOR_LIMIT && centered}
          styleProp={styles.sliderItems}
          justifyStart={slides?.length >= SLIDES_THRESHOLD}
          setEmblaList={setEmblaApi}
        >
          {slides.map((item, index) => (
            <Slide
              key={item.href}
              styleProp={[
                styles.slideItem,
                startEndMargins && index === 0 && styles.firstSlide,
                startEndMargins && index === slides.length - 1 && styles.lastSlide
              ]}
            >
              <PillCollection
                styleProp={[
                  pillStyles,
                  activeId === item.href && pillActiveStyles,
                ]}
                onClick={() => setActiveId(item.href)}
                pill={item}
              />
            </Slide>
          ))}
        </Slider>
      )}
    </Container>
  )
}

export default AnchorNavigationCollection
