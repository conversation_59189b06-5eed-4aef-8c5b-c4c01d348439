'use client'

import {
  defaultTheme as $T,
} from '@/app/themeTokens.stylex'
import MediaVideo from '@components/Generic/MediaVideo'
import Wrapper from '@components/layout/Wrapper'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import {
  motion,
  useMotionValue,
  useMotionValueEvent,
  useScroll,
  useTransform
} from 'framer-motion'
import {
  FC,
  useEffect,
  useRef,
  useState
} from 'react'

type ScrollFeatureSliderProps = {
  header: string;
  subheader: string;
  body: {
    id?: number;
    title: string;
    content: string;
  }[];
  media: string;
};

const SMALL = '@media (max-width: 768px)'

const styles = stylex.create({
  root: {
    backgroundColor: $T.primarySurface,
  },
  main: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'row',
    backgroundColor: $T.primarySurface,
    flex: '1',
    paddingBlock: {
      default: 60,
      [SMALL]: 40
    },
    paddingInline: {
      default: 100,
      [SMALL]: 20
    },
    gap: 100,
  },
  left: {
    display: {
      default: 'flex',
      [SMALL]: 'none'
    },
    flexDirection: 'column',
    gap: 20,
    flex: '1',
    position: 'relative',
  },
  right: {
    display: 'flex',
    flexDirection: 'column',
    flex: '1',
    overflow: 'hidden',
  },
  rightListItem: {
    maxWidth: {
      default: 325,
      [SMALL]: 'calc(100vw - 60px)'
    },
    marginBottom: 30,
    flexDirection: 'row',
    display: 'flex',
    gap: 17,
  },
  bullet: {
    display: 'block',
    minWidth: 17,
    width: 17,
    height: 17,
    backgroundColor: $T.secondaryCTASurface,
    borderRadius: '100%',
    marginTop: 6
  },
  progress: {
    position: 'absolute',
    left: 8,
    width: 1,
    marginTop: 6,
    backgroundColor: $T.secondaryCTASurface,
    maxHeight: '100%'
  },
  body: {
    position: 'relative',
    marginTop: 40,
  },
  media: {
    display: 'block',
    transition: 'transform 0.3s ease-in-out',
    maxWidth: '100%',
    height: 'auto',
    width: '100%',
    aspectRatio: '16 / 9',
  },
  subheading: {
    textTransform: 'uppercase',
  }
})

const ScrollFeatureSlider: FC<ScrollFeatureSliderProps> = ({
  header,
  subheader,
  body = [],
  media,
}) => {
  const halfOpacity = 0.5
  const wrapperRef = useRef<HTMLDivElement | null>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const bodyRef = useRef<HTMLDivElement | null>(null)
  const { scrollYProgress } = useScroll({
    target: wrapperRef,
    offset: ['end end', 'start start']
  })
  const [itemOpacities, setItemOpacities] = useState<number[]>(Array(body.length).fill(1, 0, 0).fill(halfOpacity, 1, body.length))
  const paddingTotal = 120
  // state to store the allowable distance the image can move
  const [imgAllowableDistance, setImgAllowableDistance] = useState(0)
  const [bodyAllowableDistance, setBodyAllowableDistance] = useState(0)

  useEffect(() => {
    const interval = 2000
    const intervalId = setInterval(() => {
      if (imageRef.current && wrapperRef.current && bodyRef.current) {
        // Get the height of the wrapper and image
        const wrapperHeight = wrapperRef.current.getBoundingClientRect().height
        const imageHeight = imageRef.current.getBoundingClientRect().height
        const bodyHeight = bodyRef.current.getBoundingClientRect().height

        // Maximum distance the image can move down
        const imgAllowable = Math.max(0, wrapperHeight - imageHeight - paddingTotal)
        const bodyAllowable = Math.max(0, wrapperHeight - bodyHeight - paddingTotal)

        setBodyAllowableDistance(bodyAllowable)
        setImgAllowableDistance(imgAllowable)
      }
    }, interval)

    return () => clearInterval(intervalId)
  }, [])

  const y = useTransform(scrollYProgress, [0, 1], [0, imgAllowableDistance])
  const heightPercentage = useMotionValue('0%')

  useMotionValueEvent(scrollYProgress, 'change', (latest: number) => {
    const absoluteValue = 100

    // compute the percentage of the distance scrolled for the progress bar
    heightPercentage.set(`${((latest * bodyAllowableDistance) / bodyAllowableDistance) * absoluteValue}%`)
  })

  useEffect(() => {
    // Compute individual opacities dynamically based on the scroll progress
    const unsubscribe = scrollYProgress.on('change', (latest: number) => {
      const newOpacities = body.map((_, index) => {
        // Calculate relative progress for each item
        const start = (index - 1) / body.length
        const end = (index + 1) / body.length

        const opacity = Math.min(Math.max((latest - start) / (end - start), halfOpacity), 1)
        // eslint-disable-next-line no-magic-numbers
        return opacity > 0.5 ? 1 : 0.5
      })

      setItemOpacities(newOpacities)
    })

    return () => unsubscribe()
  }, [scrollYProgress, body])

  return (
    <div ref={wrapperRef} {...stylex.props(styles.root)}>
      <Wrapper styleProp={styles.main} theme="offWhite">
        <div
          {...stylex.props(styles.left)}
        >
          <motion.div
            ref={imageRef}
            {...stylex.props(styles.media)}
            initial={{ y: 0 }}
            style={{ y }}
            transition={{ type: 'spring', damping: 100, stiffness: 100 }}
          >
            <MediaVideo media={media} imageHeight="366px" imageHeightMobile="200px" />
          </motion.div>
        </div>

        <div {...stylex.props(styles.right)}>
          <div>
            <Typography as="span" typographyTheme="subheading" marginBottom="sm" styleProp={styles.subheading}>
              {subheader}
            </Typography>
            <Typography as="h4" typographyTheme="h4Secondary">
              {header}
            </Typography>
          </div>
          <div ref={bodyRef} {...stylex.props(styles.body)}>
            <motion.div
              id="progress-bar"
              {...stylex.props(styles.progress)}
              style={{
                height: heightPercentage
              }}
              transition={{ type: 'spring', damping: 100, stiffness: 100 }}
            />
            {body.map((item, index) => (
              <motion.div
                key={`${item.title}-${item.id}`}
                {...stylex.props(styles.rightListItem)}
                style={{ opacity: index === 0 ? 1 : itemOpacities[index] }}
              >
                <div {...stylex.props(styles.bullet)} />
                <div>
                  <Typography as="h6" typographyTheme="h6Secondary" fontBold marginBottom="xs">
                    {item.title}
                  </Typography>
                  <Container as="div">
                    {item.content}
                  </Container>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </Wrapper>
    </div>
  )
}

export default ScrollFeatureSlider
