import Checkmark from './Checkmark'

import Typography from '@components/Typography'
import {
  colors,
} from '@/app/themeTokens.stylex'
import { sanitize } from '@/utils/regex'

import stylex from '@stylexjs/stylex'

type RowProps = {
  title: string
  description: string
  checkMarks: string[]
  lastRow?: boolean
  columnCount: number
}

const styles = stylex.create({
  row: {
    backgroundColor: {
      default: 'transparent',
      ':hover': colors.gray100,
    },
    borderBottomWidth: '1px',
    borderBottomStyle: 'solid',
    borderBottomColor: colors.gray200,
  },
  noBorder: {
    borderBottomWidth: '0'
  },
  textWrapper: {
    padding: {
      default: '15px 0 15px 15px',
      '@media (min-width: 450px)': '15px 30px 15px 15px',
    },
    width: {
      default: 55,
      '@media (min-width: 450px)': 85,
      '@media (min-width: 1024px)': '125px',
    },
    textAlign: 'left',
    textTransform: {
      default: 'uppercase',
      '@media (min-width: 1024px)': 'none',
    },
    borderWidth: {
      default: '0 0 1px',
      '@media (min-width: 1024px)': '0 0 0 1px',
    },
  },
  description: {
    fontWeight: 'normal',
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'block',
    }
  },
  shadow: {
    // eslint-disable-next-line @stylexjs/valid-styles
    '@media (min-width: 1024px)': {
      '::after': {
        display: 'block',
        position: 'absolute',
        content: "''",
        borderRadius: '35px',
        borderWidth: '1px',
        borderStyle: 'solid',
        borderColor: colors.gray200,
        top: '-15px',
        width: 'calc(100% / 5)',
        height: 'calc(100% + 30px)',
        pointerEvents: 'none',
        marginLeft: '90px',
        boxShadow: '3px 4px 5px -2px rgb(0 0 0 / 10%)',
        borderTopWidth: '1px',
        borderTopStyle: 'solid',
        borderTopColor: `rgb(from ${colors.gray200} r g b / 0.35)`,
      }
    }
  },
  checkMark: {
    width: 90,
    textAlign: 'center',
    padding: '0.5em 0.75em',
  },
  borderRadius: {
    borderRadius: '0 0 12px 12px'
  }
})

const shadow = stylex.create({
  4: {
    // eslint-disable-next-line @stylexjs/valid-styles
    '@media (min-width: 1024px)': {
      '::after': {
        width: 'calc(100% / 5.5)',
        marginLeft: '-15px',
      }
    }
  }
})

const Row = ({
  checkMarks,
  description,
  title,
  columnCount,
  lastRow = false
}: RowProps) => (
  <tr {...stylex.props(styles.row, lastRow && styles.noBorder)}>
    <th scope="row" {...stylex.props(styles.textWrapper)}>
      <Typography as="p" typographyTheme="bodyLarge" fontSecondary fontBold>{title}</Typography>
      <Typography as="span" typographyTheme="bodyLarge" fontSecondary styleProp={styles.description} lineHeight="lg">{description}</Typography>
    </th>
    {
      checkMarks.map((checkMark, index) => (
        <td
          {...stylex.props(
            styles.checkMark,
            index === 0 && styles.shadow,
            index === 0 && shadow[columnCount as keyof typeof shadow],
          )}
          key={sanitize(`row-${checkMark}-${String(index)}`)}
        >
          <Checkmark key={checkMark} type={checkMark} />
        </td>
      ))
    }
  </tr>
)

export default Row
