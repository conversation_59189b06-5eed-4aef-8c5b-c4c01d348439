import Row from './Row'

import { ThemeColors } from '@/app/themeThemes.stylex'
import {
  colors,
} from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { sanitize } from '@/utils/regex'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

type RowProps = {
  title: string;
  description: string;
  checkMarks: string[];
}

type ContentProps = {
  titles: string[];
  rows: RowProps[];
}

type HowWeCompareProps = {
  theme: ThemeColors;
  content: ContentProps;
  header?: string;
};

const styles = stylex.create({
  wrapper: {
    position: 'relative',
    width: '100%',
    alignSelf: 'center',
    padding: {
      default: '40px 0px',
      '@media (min-width: 1024px)': '60px 0px',
    },
  },
  tableWrapper: {
    position: 'relative',
    width: '100%',
    overflow: {
      default: 'scroll',
      '@media (min-width: 1024px)': 'visible',
    },
    scrollSnapType: {
      default: 'x mandatory',
      '@media (min-width: 1024px)': 'none',
    },
    scrollbarWidth: 'none',
  },
  table: {
    width: {
      default: '105%',
      '@media (min-width: 1024px)': '100%',
    },
    borderCollapse: 'collapse',
    maxWidth: '1440px',
    alignSelf: 'center',
    position: 'relative',
    margin: '0 auto',
    marginBottom: '40px',
    borderColor: 'gray',
    borderSpacing: 2,
    display: 'table',
    backgroundColor: 'transparent',
    borderRadius: '10px',
    height: '100%',
  },
  shadowBox: {
    position: 'absolute',
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'block',
    },
    top: '-10px',
    bottom: '-100px',
    left: {
      default: '44%',
      '@media (min-width: 1024px)': '29%',
    },
    width: {
      default: '80px',
      '@media (min-width: 1024px)': '18%',
    },
    height: '105%',
    borderRadius: '50px',
  },
  titleWrapper: {
    padding: '0.75em 0.3em',
    verticalAlign: 'middle',
    textAlign: 'center',
    maxWidth: '182px',
  },
  thead: {
    borderRadius: '8px',

  },
  header: {
    backgroundColor: 'transparent',
    borderBottomWidth: '1px',
    borderBottomStyle: 'solid',
    borderBottomColor: colors.gray200
  },
})

const HowWeCompare = ({
  theme,
  content,
  header
}: HowWeCompareProps) => (
  <Container as="section" theme={theme} styleProp={styles.wrapper} flex flexCentered gap="4">
    {header && <Typography as="h2" typographyTheme="h2Secondary" fontBold marginBottom="md">{header}</Typography>}
    <Container as="div" styleProp={styles.tableWrapper}>
      <table {...stylex.props(styles.table)}>
        <colgroup>
          <col style={{ width: '30%' }} />
          {content.titles.map((_) => (
            // eslint-disable-next-line no-magic-numbers
            <col key={_} style={{ width: `${70 / content.titles.length}%` }} />
          ))}
        </colgroup>
        <thead {...stylex.props(styles.thead)}>
          <tr {...stylex.props(styles.header)}>
            <th> {} </th>
            {
            content.titles.map((title) => (
              <th key={title} {...stylex.props(styles.titleWrapper)}>
                <Typography as="p" typographyTheme="bodyLarge" fontSecondary fontBold>
                  {title}
                </Typography>
              </th>
            ))
          }
          </tr>
        </thead>
        <tbody>
          {content.rows.map((row, index) => (
            <React.Fragment key={`${sanitize(row.title)}-${String(index)}`}>
              <Row
                key={row.description}
                checkMarks={row.checkMarks}
                description={row.description}
                title={row.title}
                lastRow={index === content.rows.length - 1}
                columnCount={content.titles.length}
              />
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </Container>
  </Container>
)

export default HowWeCompare
