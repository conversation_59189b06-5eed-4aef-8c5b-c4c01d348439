type CheckmarkProps = {
  type : string
};

const checkmarkList = [
  {
    type: 'orangeCheck',
    html:
  <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M14 28C21.732 28 28 21.732 28 14C28 6.26801 21.732 0 14 0C6.26801 0 0 6.26801 0 14C0 21.732 6.26801 28 14 28Z" fill="#D47555" />
    <path d="M7.72461 14.0005L11.8413 18.6426L21.0415 9.38672" stroke="#FCFCFA" />
  </svg>

  },
  {
    type: 'blueCheck',
    html:
  <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M14 28C21.732 28 28 21.732 28 14C28 6.26801 21.732 0 14 0C6.26801 0 0 6.26801 0 14C0 21.732 6.26801 28 14 28Z" fill="#1F3438" />
    <path d="M7.72461 14.0005L11.8413 18.6426L21.0415 9.38672" stroke="#FCFCFA" />
  </svg>

  },
  {
    type: 'outlineX',
    html:
  <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M27.5 14C27.5 21.4558 21.4558 27.5 14 27.5C6.54416 27.5 0.5 21.4558 0.5 14C0.5 6.54416 6.54416 0.5 14 0.5C21.4558 0.5 27.5 6.54416 27.5 14Z" stroke="#1F3438" />
    <path d="M14 27.5C21.4558 27.5 27.5 21.4558 27.5 14C27.5 6.54416 21.4558 0.5 14 0.5C6.54416 0.5 0.5 6.54416 0.5 14C0.5 21.4558 6.54416 27.5 14 27.5Z" stroke="#1F3438" />
    <path d="M8 8.45312L20.182 20.0811" stroke="#1F3438" />
    <path d="M20.1788 8.45312L8.42383 20.2852" stroke="#1F3438" />
  </svg>
  },

  {
    type: 'checkHalfFill',
    html:
  <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M27.5 14C27.5 21.4558 21.4558 27.5 14 27.5C6.54416 27.5 0.5 21.4558 0.5 14C0.5 6.54416 6.54416 0.5 14 0.5C21.4558 0.5 27.5 6.54416 27.5 14Z" stroke="#1F3438" />
    <path d="M14 27.5C21.4558 27.5 27.5 21.4558 27.5 14C27.5 6.54416 21.4558 0.5 14 0.5C6.54416 0.5 0.5 6.54416 0.5 14C0.5 21.4558 6.54416 27.5 14 27.5Z" stroke="#1F3438" />
    <path d="M21.755 8L10 19.832" stroke="#1F3438" />
    <g clipPath="url(#clip0_770_83313)">
      <path d="M14 28C21.732 28 28 21.732 28 14C28 6.26801 21.732 0 14 0C6.26801 0 0 6.26801 0 14C0 21.732 6.26801 28 14 28Z" fill="#1F3438" />
      <path d="M7.72461 14.0005L11.8413 18.6426L21.0415 9.38672" stroke="#FCFCFA" />
    </g>
    <defs>
      <clipPath id="clip0_770_83313">
        <rect width="15" height="28" fill="white" />
      </clipPath>
    </defs>
  </svg>

  }
]

const Checkmark = ({ type }: CheckmarkProps) => checkmarkList.filter((checkmark) => checkmark.type.toLowerCase() === type.toLowerCase()).map(
  (checkmark) => checkmark.html
)

export default Checkmark
