'use client'

import Typography from '@/components/Typography'
import CallToAction from '@/components/Generic/CallToAction'
import { colors } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@/components/layout/Container'

import React, { useState } from 'react'
import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

export type BlockProps = {
  id: string
  title: string
  imageSource: string
  href: string
  theme?: ThemeColors
}

const styles = stylex.create({
  container: {
    flex: '1',
  },
  block: {
    position: 'relative',
    minWidth: '280px',
    minHeight: '100px',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'end',
    padding: 0,
    zIndex: 1,
    cursor: 'pointer',
  },
  textWrapper: {
    position: 'absolute',
    display: 'flex',
    flexDirection: 'row',
    gap: '8px',
    alignItems: 'center',
    zIndex: 9,
    right: '16px',
    textShadow: {
      default: 'none',
      ':hover': 'rgb(35, 52, 55) 0px 0px 0.4px'
    },
    transition: {
      default: 'none',
      ':hover': 'text-shadow 0.3s ease-in-out'
    },
  },
  link: {
    position: 'relative',
    '::after': {
      content: '""',
      position: 'absolute',
      display: 'block',
      height: '2px',
      background: 'red',
      left: 0,
      width: '0%',
      bottom: '-5px',
      backgroundColor: colors.marigold,
      transition: 'width 0.3s ease-in-out',
    },
  },
  linkHovered: {
    '::after': {
      width: '100%',
    },
  },
  image: {
    maxWidth: '50%',
    maxHeight: '100%',
    objectFit: 'cover',
    position: 'absolute',
    left: '0',
    zIndex: 0
  }
})

const Block = ({
  theme,
  title,
  imageSource,
  href,
}: BlockProps) => {
  const [hovered, setHovered] = useState(false)
  return (
    <Container theme={theme} styleProp={styles.container}>
      <div
        onMouseOver={() => setHovered(true)}
        onFocus={() => setHovered(true)}
        onMouseOut={() => setHovered(false)}
        onBlur={() => setHovered(false)}
      >
        <CallToAction
          variant="transparent"
          size="inline"
          href={href}
          styleProp={styles.block}
        >
          <CallToAction
            variant="transparent"
            size="inline"
            styleProp={[styles.textWrapper, styles.link, hovered && styles.linkHovered]}
            useArrow
          >
            <Typography as="p" typographyTheme="bodyLarge">
              {title}
            </Typography>
          </CallToAction>
          <Image src={imageSource} alt={title} width={150} height={100} {...stylex.props(styles.image)} />
        </CallToAction>
      </div>
    </Container>
  )
}

export default Block
