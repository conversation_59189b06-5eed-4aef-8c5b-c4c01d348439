import Block, { BlockProps } from './Block'

import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import { spacing } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import RenderIf from '@/utils/renderIf'
import { notEmpty } from '@/utils/checking'
import { limitArray } from '@/utils/array'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

type ThreeBlockModuleProps = {
  theme: ThemeColors
  header: string
  blocks: Array<BlockProps>
}

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  withPaddingTop: {
    paddingTop: spacing.lg,
  },
  wrapper: {
    width: '100%',
    padding: '1rem',
    justifyContent: {
      default: 'start',
      [DESKTOP]: 'center',
    },
    flexDirection: {
      default: 'column',
      [DESKTOP]: 'row',
    },
    alignItems: 'stretch',
  },
})

const BLOCKS_LIMIT = 3

const ThreeBlockModule = ({
  theme = 'cream',
  header,
  blocks = []
}: ThreeBlockModuleProps) => {
  const hasHeader = notEmpty(header)
  const limitedToThree = limitArray<BlockProps>(blocks, BLOCKS_LIMIT)
  return (
    <Container as="div" flex flexCentered gap="1" styleProp={hasHeader && styles.withPaddingTop}>
      <RenderIf condition={hasHeader}>
        <Typography as="h4" typographyTheme="h4Secondary">{header}</Typography>
      </RenderIf>
      <Container as="div" flex flexCentered styleProp={styles.wrapper} gap="3" noWrap>
        {
          notEmpty(blocks) && limitedToThree.map((block) => (
            <Block
              id={block.id}
              theme={theme}
              key={block.id}
              title={block.title}
              imageSource={block.imageSource}
              href={block.href}
            />
          ))
        }
      </Container>
    </Container>
  )
}

export default ThreeBlockModule
