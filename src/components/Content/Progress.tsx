import Typography from '@/components/Typography'
import {
  spacing,
  globalTokens as $,
  colors,
  fontSizes
} from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import CallToAction, { CTAProps } from '@/components/Generic/CallToAction'
import Container from '@/components/layout/Container'

import * as stylex from '@stylexjs/stylex'

 type ProgressProps = {
   header: String;
   steps: any[];
   theme?: ThemeColors;
   callToAction: CTAProps
 };

const styles = stylex.create({
  container: {
    padding: {
      default: `${spacing.lg}`,
      '@media (min-width: 1024px)': `${spacing.xl} 0`
    }
  },
  wrapper: {
    margin: '0 auto',
    maxWidth: {
      default: 'auto',
      '@media (min-width: 1024px)': '957px'
    }
  },
  grid: {
    position: 'relative',
    margin: {
      default: `${spacing.md} 0`,
      '@media (min-width: 1024px)': `${spacing.xl} 0`
    },
    display: 'flex',
    flexDirection: {
      default: 'column',
      '@media (min-width: 1024px)': 'row'
    },
    gap: {
      default: spacing.md,
      '@media (min-width: 1024px)': '0',
    },
    justifyContent: {
      default: 'center',
      '@media (min-width: 1024px)': 'space-between'
    }
  },
  line: {
    position: 'absolute',
    backgroundColor: colors.perracotta,
    height: {
      default: 'auto',
      '@media (min-width: 1024px)': '1px'
    },
    width: {
      default: '1px',
      '@media (min-width: 1024px)': 'auto',
    },
    left: {
      default: '15px',
      '@media (min-width: 1024px)': '7.5%'
    },
    right: {
      default: 'inherit',
      '@media (min-width: 1024px)': '7.5%'
    },
    top: {
      default: 0,
      '@media (min-width: 1024px)': '20px'
    },
    bottom: {
      default: 0,
      '@media (min-width: 1024px)': 'inherit'
    },
    zIndex: 0,
  },
  stepContainer: {
    textAlign: 'center',
    display: {
      default: 'flex',
      '@media (min-width: 1024px)': 'block'
    },
    maxWidth: {
      default: '100%',
      '@media (min-width: 1024px)': '180px'
    },
    minWidth: {
      default: 'inherit',
      '@media (min-width: 1024px)': '180px'
    },
    gap: {
      default: spacing.md,
      '@media (min-width: 1024px)': 0
    },
    alignItems: 'center',
  },
  counter: {
    position: 'relative',
    color: colors.white,
    backgroundColor: colors.perracotta,
    display: 'flex',
    margin: {
      default: 0,
      '@media (min-width: 1024px)': `0 auto ${spacing.md}`
    },
    width: {
      default: spacing.md,
      '@media (min-width: 1024px)': spacing.lg
    },
    height: {
      default: spacing.md,
      '@media (min-width: 1024px)': spacing.lg
    },
    borderRadius: '50%',
    textAlign: 'center',
    fontSize: {
      default: fontSizes.md,
      '@media (min-width: 1024px)': fontSizes.lg
    },
    fontFamily: $.primaryFontFamily,
    justifyContent: 'center',
    alignItems: 'center',
  },
  counterSpan: {
    height: {
      default: 'auto',
      '@media (min-width: 1024px)': '36px'
    },
  },
  title: {
    fontFamily: $.secondaryFontFamily,
    fontSize: {
      default: fontSizes.md,
      '@media (min-width: 1024px)': fontSizes.lg
    },
    lineHeight: {
      default: '2.125rem',
      '@media (min-width: 1024px)': fontSizes.xl
    },
  }
})

const Progress = (
  {
    theme = 'cream',
    header = '',
    steps = [],
    callToAction = {}
  }: ProgressProps
) => (
  <Container as="section" theme={theme} styleProp={styles.container}>
    <Container as="div" styleProp={styles.wrapper}>
      <Typography as="h3" textCentered styleProp={styles.title}>
        { header }
      </Typography>
      <Container as="div" styleProp={styles.grid}>
        <div {...stylex.props(styles.line)} />
        {
                        steps.map(({ content, id }, i) => (
                          <Container key={id} as="div" styleProp={styles.stepContainer}>
                            <Container as="div" styleProp={styles.counter}>
                              <span {...stylex.props(styles.counterSpan)}>{ i + 1 }</span>
                            </Container>

                            <Container as="div">
                              { content }
                            </Container>
                          </Container>
                        ))
                    }
      </Container>
      <Container as="nav" flex alignCentered>
        <CallToAction {...callToAction} fullWidthMobile />
      </Container>
    </Container>
  </Container>
)

export default Progress
