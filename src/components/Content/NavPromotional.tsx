'use client'

import {
  globalTokens as $,
  spacing,
} from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@components/layout/Container'
import { toCamelCase } from '@/utils/regex'
import useIsMobile from '@/hooks/isMobile'
import CustomSlider from '@components/Generic/CustomSlider'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import { EmblaOptionsType } from 'embla-carousel'

const DESKTOP = '@media (min-width: 1024px)'
const DESKTOP_XL = '@media (min-width: 1280px)'

const styles = stylex.create({
  root: {
    paddingInline: spacing.md,
    paddingBlock: {
      default: spacing.md,
      [DESKTOP]: '26px',
    },
    overflow: 'hidden',
  },
  container: {
    borderRadius: $.borderRadiusSmall,
    width: {
      default: '100%',
      [DESKTOP_XL]: 'fit-content',
    },
    minWidth: {
      default: 'calc(100vw - 80px)',
      [DESKTOP_XL]: '1200px',
    },
    display: {
      default: '-webkit-box',
      [DESKTOP]: 'flex',
    },
    paddingInline: {
      default: 0,
      [DESKTOP]: spacing.lg,
    },
  },
  slide: {
    maxWidth: {
      default: '320px',
      [DESKTOP]: 'fit-content',
    },
    width: '100%',
  },
  icon: {
    maxWidth: '25px',
    maxHeight: '25px',
    width: 'auto',
    height: 'auto',
  },
  textLineThrough: {
    textDecoration: 'line-through'
  },
})

const slideStyle = stylex.create({
  root: {
    paddingInline: spacing.md,
    paddingBlock: {
      default: spacing.md,
      [DESKTOP]: '26px',
    },
    overflow: 'hidden',
    width: '100%',
  },
  dotsContainerStyles: {
    position: 'absolute',
    margin: 'auto',
    bottom: '0',
    top: '0',
    left: '0',
    right: '0',
    width: '97%',
  },
  dotsEachContainerStyles: {
    width: '100vh',
    visibility: 'hidden',
    pointerEvents: 'none',
  },
  slideCustom: {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
  },
})
type NavPromotionalProps = {
  slides: Array<{
    image: string | null,
    text: string,
    settings: { layout: string },
    id: string
  }>;
  theme?: ThemeColors;
};

const EMBLA_CAROUSEL_OPTIONS: EmblaOptionsType = {
  align: 'center',
  slidesToScroll: 1,
  startIndex: 1,
  loop: true,
}

const NavPromotional = ({ slides, theme = 'navy' }: NavPromotionalProps) => {
  const { isMobile } = useIsMobile()

  if (slides.length === 0) return null
  if (isMobile) {
    return (

      <Container
        as="section"
        flex
        noWrap
        alignCentered
        justifyContentCenter
        styleProp={slideStyle.root}
      >
        <Container
          as="div"
          theme={theme}
          flex
          flexRow
          justifyContentCenter
          gap="5"
          noWrap
          styleProp={styles.container}
        >
          <CustomSlider
            slides={slides}
            slidesPerPage={1}
            dotsContainerStyles={slideStyle.dotsContainerStyles}
            dotsEachContainerStyles={slideStyle.dotsEachContainerStyles}
            renderDotsWithArrows
            renderPrevNextButtons={() => null}
            slideStyleProp={slideStyle.slideCustom}
            arrowsSize="small"
            renderSlide={({
              id,
              image,
              text,
              settings,
            }) => (
              <Container
                key={id}
                flex
                flexRow
                paddingBlock="2"
                gap="1"
                alignCentered
                noWrap
                justifyContentCenter
                styleProp={styles.slide}
              >
                {image && (
                <Image
                  src={image}
                  alt="Icon"
                  width={16}
                  height={16}
                  {...stylex.props(styles.icon)}
                />
                )}
                <Container
                  as="div"
                  styleProp={toCamelCase(settings?.layout || '') === 'layout2' && styles.textLineThrough}
                >
                  {text}
                </Container>
              </Container>
            )}
            options={EMBLA_CAROUSEL_OPTIONS}
          />
        </Container>
      </Container>
    )
  }
  return (
    <Container
      as="section"
      flex
      noWrap
      alignCentered
      justifyContentCenter
      styleProp={styles.root}
    >
      <Container
        as="div"
        theme={theme}
        flex
        flexRow
        justifyContentCenter
        gap="5"
        noWrap
        styleProp={styles.container}
      >
        {slides.map((slide) => {
          const layout = slide.settings?.layout
          return (
            <Container
              key={slide.id}
              flex
              flexRow
              paddingBlock="2"
              gap="1"
              alignCentered
              noWrap
              justifyContentCenter
              styleProp={styles.slide}
            >
              {slide.image && (
                <Image
                  src={slide.image}
                  alt="Icon"
                  width={16}
                  height={16}
                  {...stylex.props(styles.icon)}
                />
              )}
              <Container
                as="div"
                styleProp={toCamelCase(layout || '') === 'layout2' && styles.textLineThrough}
              >
                {slide.text}
              </Container>
            </Container>
          )
        })}
      </Container>
    </Container>
  )
}

export default NavPromotional
