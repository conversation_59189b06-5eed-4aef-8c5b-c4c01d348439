import Typography from '@components/Typography'
import Container, { Size } from '@components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'
import RichTextRenderer from '@/utils/RichTextRenderer'
import CallToAction, { AnchorProps, ButtonProps } from '@components/Generic/CallToAction'
import DialogTrigger from '@components/Generic/Dialog/DialogTrigger'

import * as stylex from '@stylexjs/stylex'

type ExtendedAnchorProps = AnchorProps & { text: string }
type ExtendedButtonProps = ButtonProps & { text: string }

const isAnchorProps = (button: ExtendedAnchorProps): button is ExtendedAnchorProps => (button as ExtendedAnchorProps).href !== ''

const isButtonProps = (button: ExtendedButtonProps): button is ExtendedButtonProps => (button as ExtendedButtonProps).onClick !== undefined

type TextBannerProps = {
  subheader?: string,
  header: string,
  content?: { json: any },
  theme?: ThemeColors
  gap?: Size,
  paddingBlock?: Size
  buttons?: Array<ExtendedAnchorProps | ExtendedButtonProps>
}

const styles = stylex.create({
  buttonContainer: {
    flexDirection: {
      default: 'column',
      '@media (min-width: 1024px)': 'row',
    }
  },
})

const TextBanner = ({
  subheader,
  header,
  content,
  theme = 'cream',
  gap = '2',
  paddingBlock = '6',
  buttons = []
}: TextBannerProps) => (
  <Container
    as="section"
    theme={theme}
    flexCentered
    contentGap
  >
    <Container
      as="div"
      flexCentered
      gap={gap}
      paddingBlock={paddingBlock}
      size="2"
    >
      {subheader && <Typography as="p" typographyTheme="captionSmall" uppercase textCentered>{subheader}</Typography>}
      {header && <Typography as="h1" typographyTheme="h2Secondary" fontBold textCentered>{header}</Typography>}
      {content && <RichTextRenderer content={content?.json} settings={{ textCenter: true }} /> }
      {buttons.length > 0 && (
        <Container as="div" gap="2" flex flexRow styleProp={styles.buttonContainer}>
          {buttons.map((button: any) => {
            const {
              text,
              href,
              id,
              variant,
              onClick,
              icon,
            } = button
            if (isAnchorProps(button)) {
              return (
                <CallToAction
                  key={id}
                  variant={variant}
                  href={href}
                  icon={icon}
                >
                  {text}
                </CallToAction>
              )
            }
            if (isButtonProps(button)) {
              return <DialogTrigger key={id} text={text} id={onClick} />
            }
            return null
          })}
        </Container>
      )}
    </Container>
  </Container>
)

export default TextBanner
