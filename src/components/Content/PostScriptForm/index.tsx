import StandardFormHero from './StandardFormHero'
import SplitFormHero from './SplitFormHero'

import Container from '@/components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'

import stylex from '@stylexjs/stylex'

const TABLET = '@media (min-width: 768px)'
const DESKTOP = '@media (min-width: 992px)'

const styles = stylex.create({
  containerStyle: {
    width: '100%',
    height: {
      default: '120vh',
      [TABLET]: 'clamp(650px, 100vh, 850px)',
      [DESKTOP]: 'clamp(650px, 750px, 850px)'
    },
  },
})

type LayoutType = 'Layout 1' | 'Layout 2' | 'Layout 3' | 'Layout 4' | 'Layout 5'

type PropType = {
  content: any,
  header: string,
  subheader: string,
  settings: {
    theme: ThemeColors,
    layout: LayoutType,
    fontFamily: string | null,
    width: string | null,
    anchorTargeting: string | null,
    customProps: {
      keyword: string,
      klaviyoListId: string
    }
  },
  assetsCollection: {
    items: any[]
  },
  mobileAssetsCollection: {
    items: any[]
  },
  theme: ThemeColors,
  layout: LayoutType,
}

// eslint-disable-next-line complexity
const PostScriptForm = (props: PropType) => {
  const formDetailsObject = {
    header: props?.header || '',
    body: props?.subheader || '',
    disclaimer: props?.content.json,
    desktopImage: props?.assetsCollection?.items?.[0]?.url || '',
    mobileImage: props?.mobileAssetsCollection?.items?.[0]?.url || '',
    keyword: props?.settings?.customProps?.keyword || '',
    klaviyoListId: props?.settings?.customProps?.klaviyoListId || '',
    layout: props?.settings?.layout || 'Layout 1',
    theme: props?.settings?.theme || ''
  }

  const componentDesktopDictionary = {
    'Layout 1': StandardFormHero,
    'Layout 2': StandardFormHero,
    'Layout 3': StandardFormHero,
    'Layout 4': SplitFormHero,
    'Layout 5': SplitFormHero,
  }

  const Component = componentDesktopDictionary[props?.settings?.layout]

  return (
    <Container
      as="div"
      styleProp={styles.containerStyle}
    >
      <Component formDetailsObject={formDetailsObject} />
    </Container>
  )
}

export default PostScriptForm
