'use client'

/* eslint-disable jsx-a11y/label-has-associated-control */

import { colors, spacing } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import RichTextRenderer from '@/utils/RichTextRenderer'
import CallToAction from '@/components/Generic/CallToAction'
import formatPhoneNumber from '@/utils/phoneNumberConversion'
import { ThemeColors } from '@/app/themeThemes.stylex'
import RenderIf from '@/components/Generic/RenderIf'
import { subscribeToPostScript, subscribeToKlaviyo } from '@/app/forms/utils/serverFunctions'

import { ChangeEvent, useState } from 'react'
import stylex from '@stylexjs/stylex'
import { useForm } from 'react-hook-form'

const DESKTOP = '@media (min-width: 992px)'

const styles = stylex.create({
  formInput: {
    width: '100%',
    height: '50px',
    marginTop: '0.5rem',
    borderRadius: '4px',
    paddingLeft: spacing.sm,
    borderStyle: {
      default: 'none',
      ':focus': 'solid',
    },
    borderColor: colors.navy,
    borderWidth: '1px',
    outline: 'none',
    backgroundColor: colors.white,
    fontSize: '1rem',
  },
  header: {
    marginBottom: '-18px',
  },
  formInputError: {
    borderStyle: 'solid',
    borderWidth: '1px',
    borderColor: colors.red700,
    backgroundColor: '#FFEFEE',
  },
  errorLabel: {
    color: colors.red700
  },
  successFormCta: {
    color: colors.white,
    backgroundColor: colors.navy,
  }
})

const layout1 = stylex.create({
  formContainer: {
    zIndex: '1',
    width: {
      default: '100%',
      [DESKTOP]: '40%',
    },
    marginTop: {
      default: '10%',
      [DESKTOP]: 'initial',
    },
    padding: spacing.md,
  },
  formContainerPositioning: {
    marginInlineStart: {
      default: 'initial',
      [DESKTOP]: '10%',
    },
  },
})

const layout2 = stylex.create({
  formContainer: {
    zIndex: '1',
    width: {
      default: '100%',
      [DESKTOP]: '40%',
    },
    marginTop: {
      default: '10%',
      [DESKTOP]: 'initial',
    },
    padding: spacing.md,
  },
  formContainerPositioning: {
    marginInlineEnd: {
      default: 'initial',
      [DESKTOP]: '10%',
    },
  },
})

const layout3 = stylex.create({
  formContainer: {
    zIndex: '1',
    width: {
      default: '100%',
      [DESKTOP]: '40%',
    },
    marginTop: {
      default: '10%',
      [DESKTOP]: 'initial',
    },
    padding: spacing.md,
  },
  formContainerPositioning: {
    marginTop: {
      default: '10%',
      [DESKTOP]: '5%',
    },
  },
})

const layout4 = stylex.create({
  formContainer: {
    width: {
      default: '100%',
      [DESKTOP]: '50%',
    },
    padding: '1rem',
    justifyContent: 'center',
  },
  formContainerPositioning: {
    order: 1,
  },
})

const layout5 = stylex.create({
  formContainer: {
    width: {
      default: '100%',
      [DESKTOP]: '50%',
    },
    padding: '1rem',
    justifyContent: 'center',
  },
  formContainerPositioning: {
    order: 3,
  },
})

type LayoutType =
  typeof layout1 |
  typeof layout2 |
  typeof layout3 |
  typeof layout4 |
  typeof layout5

type FormDetailObjectType = {
  header: string,
  body: string,
  disclaimer: any,
  desktopImage: string,
  mobileImage: string,
  keyword: string,
  klaviyoListId: string,
  layout: string,
  theme: ThemeColors,
}

type PropType = {
  formDetailsObject: FormDetailObjectType,
}

type FormDataType = {
  email: string,
  phoneNumber: string,
}

const PostScriptForm = ({
  formDetailsObject,
}: PropType) => {
  const [successState, setSuccessState] = useState<boolean>(false)
  const [klaviyoApiErrorState, setKlaviyoApiErrorState] = useState<string>('')
  const [postscriptApiErrorState, setPostscriptApiErrorState] = useState<string>('')
  const {
    header,
    body,
    layout,
    keyword,
    klaviyoListId
  } = formDetailsObject

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    resetField,
  } = useForm<FormDataType>()

  const layouts = new Map<string, LayoutType>([
    ['Layout 1', layout1],
    ['Layout 2', layout2],
    ['Layout 3', layout3],
    ['Layout 4', layout4],
    ['Layout 5', layout5],
  ])

  const formLayout = layouts.get(layout) || layouts.get('Layout 1')

  // eslint-disable-next-line complexity
  const onFormSubmit = async (formData: FormDataType) => {
    const { phoneNumber, email } = formData

    const cleanedPhoneNumber = phoneNumber.replace(/\D/g, '')

    const payload = {
      number: cleanedPhoneNumber || '',
      email: email || '',
      keyword,
      klaviyoListId,
    }

    if (email) {
      const resKlaviyo = await subscribeToKlaviyo(payload)
      // Positive API Response from Klaviyo
      if ('status' in resKlaviyo) {
        setKlaviyoApiErrorState('')
        resetField('email')
      }
      // Error State
      if ('errors' in resKlaviyo) {
        setKlaviyoApiErrorState(resKlaviyo.errors?.[0]?.message || '')
        setSuccessState(false)
        return
      }
    }

    if (phoneNumber) {
      const resPS = await subscribeToPostScript(payload)
      // Positive API Response from PostScript
      if ('created_at' in resPS) {
        setPostscriptApiErrorState('')
        resetField('phoneNumber')
      }
      // Error State
      if ('errors' in resPS) {
        setPostscriptApiErrorState(resPS.errors?.[0]?.type || '')
        setSuccessState(false)
        return
      }
    }

    setSuccessState(true)
  }

  const handlePhoneChange = (e: ChangeEvent<HTMLInputElement>) => {
    const inputEvent = e.nativeEvent as InputEvent

    const formattedPhoneNumber = formatPhoneNumber(
      e.target.value,
      inputEvent.inputType === 'deleteContentBackward'
    )

    setValue('phoneNumber', formattedPhoneNumber)
  }

  return (
    <Container
      as="form"
      flex
      gap="4"
      styleProp={[formLayout?.formContainer, formLayout?.formContainerPositioning]}
      onSubmit={handleSubmit(onFormSubmit)}
    >
      <Typography fontSecondary fontBold textCentered as="h3" styleProp={styles.header}>
        {header}
      </Typography>
      <Typography textCentered as="p" size="sm">
        {body}
      </Typography>

      <RenderIf condition={!successState}>
        <div>
          <RenderIf condition={klaviyoApiErrorState === 'Unexpected end of JSON input'}>
            <span {...stylex.props(styles.errorLabel)}>Error with your email</span>
          </RenderIf>
          <RenderIf condition={!!errors?.email}>
            <span {...stylex.props(styles.errorLabel)}>Please enter a valid email</span>
          </RenderIf>

          <RenderIf condition={!errors?.email && !klaviyoApiErrorState}>
            <label htmlFor="email">Email Address*</label>
          </RenderIf>
          <input
            {...register('email', {
              required: 'Email is required'
            })}
            id="email"
            type="email"
            placeholder="e.g. <EMAIL>"
            {...stylex.props(styles.formInput, errors?.email && styles.formInputError)}
          />
        </div>

        <div>
          <RenderIf condition={postscriptApiErrorState === 'v2.entity_conflict'}>
            <span {...stylex.props(styles.errorLabel)}>You&apos;re already subscribed but don&apos;t worry, you&apos;ll still be entered to win!</span>
          </RenderIf>
          <RenderIf condition={postscriptApiErrorState === 'value_error'}>
            <span {...stylex.props(styles.errorLabel)}>Could not validate phone number.</span>
          </RenderIf>

          <RenderIf condition={(!errors?.phoneNumber && !postscriptApiErrorState)}>
            <label htmlFor="phone">Phone Number <em>(Optional)</em></label>
          </RenderIf>
          <input
            {...register('phoneNumber', {
              onChange: handlePhoneChange,
            })}
            id="phone"
            type="tel"
            placeholder="Phone Number"
            {...stylex.props([styles.formInput, errors?.phoneNumber || postscriptApiErrorState ? styles.formInputError : null])}
          />
        </div>

        <CallToAction variant="tertiary" submit useArrow fullWidth fullWidthMobile>
          Sign Up
        </CallToAction>

        {formDetailsObject.disclaimer && (
          <Typography as="p" size="xxs" textCentered>
            <RichTextRenderer content={formDetailsObject.disclaimer.content[0]} settings={{ typographyTheme: 'captionLarge' }} />
          </Typography>
        )}
      </RenderIf>

      <RenderIf condition={successState}>
        <Typography textCentered as="p" size="md" fontBold>
          You’re in! Stay tuned for exclusive updates and early access.
        </Typography>

        <CallToAction href="/collections/best-sellers" useArrow fullWidth fullWidthMobile styleProp={styles.successFormCta}>
          Shop Best Sellers
        </CallToAction>
      </RenderIf>
    </Container>
  )
}

export default PostScriptForm
