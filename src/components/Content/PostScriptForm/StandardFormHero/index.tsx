import PostScriptForm from '../Form'

import Container from '@/components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'

import stylex from '@stylexjs/stylex'
import Image from 'next/image'

const TABLET = '@media (min-width: 768px)'
const DESKTOP = '@media (min-width: 992px)'

const styles = stylex.create({
  containerStyle: {
    height: '100%',
    width: '100%',
    position: 'relative',
    justifyContent: {
      default: 'start',
      [DESKTOP]: 'center',
    },
    aspectRatio: '16 / 9',
  },
  containerStyleLayout1: {
    alignItems: 'start',
  },
  containerStyleLayout2: {
    alignItems: 'end',
  },
  containerStyleLayout3: {
    justifyContent: 'start',
  },
  desktopImage: {
    display: {
      default: 'none',
      [DESKTOP]: 'block',
    },
    objectFit: 'cover',
  },
  mobileImage: {
    display: {
      default: 'block',
      [DESKTOP]: 'none',
    },
    objectFit: {
      default: 'fill',
      [TABLET]: 'cover'
    },
  },
})

type FormDetailObjectType = {
  header: string,
  body: string,
  disclaimer: string,
  desktopImage: string,
  mobileImage: string,
  keyword: string,
  klaviyoListId: string,
  layout: string,
  theme: ThemeColors,
}

type PropType = {
  formDetailsObject: FormDetailObjectType,
}

const StandardFormHero = ({
  formDetailsObject,
}: PropType) => {
  const { layout } = formDetailsObject

  return (
    <Container
      as="div"
      flexCentered
      styleProp={[styles.containerStyle,
        layout === 'Layout 1' && styles.containerStyleLayout1,
        layout === 'Layout 2' && styles.containerStyleLayout2,
        layout === 'Layout 3' && styles.containerStyleLayout3,
      ]}
    >
      <Image src={formDetailsObject.desktopImage} alt="desktop" fill {...stylex.props(styles.desktopImage)} />
      <Image src={formDetailsObject.mobileImage} alt="mobile" fill {...stylex.props(styles.mobileImage)} />

      <PostScriptForm formDetailsObject={formDetailsObject} />
    </Container>
  )
}

export default StandardFormHero
