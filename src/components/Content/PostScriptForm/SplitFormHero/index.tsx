import PostScriptForm from '../Form'

import Container from '@/components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'

import stylex from '@stylexjs/stylex'
import Image from 'next/image'

const DESKTOP = '@media (min-width: 992px)'

const styles = stylex.create({
  containerStyle: {
    height: '100%',
    width: '100%',
    display: 'flex',
    flexDirection: {
      default: 'column',
      [DESKTOP]: 'row'
    }
  },
  imageContainer: {
    order: {
      default: 0,
      [DESKTOP]: 2
    },
    width: {
      default: '100%',
      [DESKTOP]: '50%'
    },
    height: {
      default: '25%',
      [DESKTOP]: '100%'
    },
    position: 'relative'
  },
  desktopImage: {
    display: {
      default: 'none',
      [DESKTOP]: 'block',
    },
    objectFit: 'cover',
  },
  mobileImage: {
    display: {
      default: 'block',
      [DESKTOP]: 'none',
    },
    objectFit: 'cover',
  },
})

type FormDetailObjectType = {
  header: string,
  body: string,
  disclaimer: any,
  desktopImage: string,
  mobileImage: string,
  keyword: string,
  klaviyoListId: string,
  layout: string,
  theme: ThemeColors,
}

type PropType = {
  formDetailsObject: FormDetailObjectType,
}

const SplitFormHero = ({
  formDetailsObject,
}: PropType) => (
  <Container
    as="div"
    styleProp={[styles.containerStyle]}
    theme={formDetailsObject?.theme}
  >
    <PostScriptForm formDetailsObject={formDetailsObject} />

    <Container styleProp={[styles.imageContainer]}>
      <Image src={formDetailsObject.desktopImage} alt="desktop" fill {...stylex.props(styles.desktopImage)} />
      <Image src={formDetailsObject.mobileImage} alt="mobile" fill {...stylex.props(styles.mobileImage)} />
    </Container>
  </Container>
)

export default SplitFormHero
