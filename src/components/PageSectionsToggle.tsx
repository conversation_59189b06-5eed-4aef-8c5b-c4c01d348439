import Product from './Product/Product'
import Content from './Content/ContentToggle'
import AppsWidgets from './AppsWidgets/AppsWidgets'
import ReadyToUse from './ReadyToUse/ReadyToUse'

import { removeSpaces } from '@utils/regex'
import { empty } from '@utils/checking'

import React from 'react'

const RegisteredSections = {
  PageSectionsContent: Content,
  PageSectionsProduct: Product,
  PageSectionsReadyToUse: ReadyToUse,
  PageSectionsAppsWidgets: AppsWidgets,
  CompareChart: Content,
}

const PageSections = ({ sections }: any) => sections.map((section: any) => {
  const { subtype } = section
  if (empty(subtype)) return null
  const sectionType = section.sectionType as keyof typeof RegisteredSections
  const RegisteredSection = RegisteredSections[sectionType]

  // Note that this field name is "Type" in Contentful with an id of "subtype"
  const sectionSubtype = removeSpaces(subtype.replace(':', ''))

  if (empty(RegisteredSection)) return null

  const Component = RegisteredSection[sectionSubtype as keyof typeof RegisteredSection]
  if (!Component) {
    // eslint-disable-next-line no-console
    console.log(`No component found for ${sectionType} with subtype ${sectionSubtype}`)
    return null
  }

  return React.createElement(Component, { ...section })
})

export default PageSections
