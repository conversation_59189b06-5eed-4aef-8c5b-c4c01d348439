import {
  fontSizes,
  globalTokens,
  spacing,
  defaultTheme as $T,
  typographyTheme as $Type,
  typographyThemeMobile as $MType,
} from '@/app/themeTokens.stylex'
import typographyThemes, { TypographyThemes } from '@/app/typographyThemes.stylex'
import typographyThemesMobile from '@/app/typographyThemesMobile.stylex'

import * as stylex from '@stylexjs/stylex'

type Type = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'caption' | 'strong' | 'em' | 'code' | 'a'
export type Size = 'xxs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'subheading' | 'bodyLarge' | 'bodySmall' | 'captionLarge' | 'captionSmall'
export type ValidSize<T> = T extends Size ? T : never

type LineHeight = 'xxs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';

const styles = stylex.create({
  root: {
    fontFamily: $Type.fontFamily,
    fontSize: $Type.fontSize,
    fontWeight: $Type.fontWeight,
    lineHeight: $Type.lineHeight,
    letterSpacing: $Type.letterSpacing
  },
  responsiveRoot: {
    fontFamily: {
      default: $MType.fontFamily,
      '@media (min-width: 1024px)': $Type.fontFamily
    },
    fontSize: {
      default: $MType.fontSize,
      '@media (min-width: 1024px)': $Type.fontSize
    },
    fontWeight: {
      default: $MType.fontWeight,
      '@media (min-width: 1024px)': $Type.fontWeight
    },
    lineHeight: {
      default: $MType.lineHeight,
      '@media (min-width: 1024px)': $Type.lineHeight
    },
    letterSpacing: {
      default: $MType.letterSpacing,
      '@media (min-width: 1024px)': $Type.letterSpacing
    }
  },
  secondaryText: {
    color: $T.secondaryText,
  },
})

type Props = {
  children: React.ReactNode
  as: Type;
  typographyTheme?: TypographyThemes;
  typographyThemeMobile?: TypographyThemes;
  fontBold?: boolean;
  fontSecondary?: boolean;
  colorSecondary?: boolean;
  size?: Size;
  underline?: boolean;
  lineThrough? :boolean;
  lineHeight?: LineHeight;
  textLeft?: boolean;
  textRight?: boolean;
  textCentered?: boolean;
  textJustified?: boolean;
  uppercase?: boolean;
  lowercase?: boolean;
  capitalize?: boolean;
  marginBottom?: Size;
  styleProp?: {}
  suppressHydrationWarning?: boolean
}

const baseTypography = stylex.create({
  h1: {
    fontSize: fontSizes.h1,
  },
  h2: {
    fontSize: fontSizes.h2,
  },
  h3: {
    fontSize: fontSizes.h3,
  },
  h4: {
    fontSize: fontSizes.h4,
  },
  h5: {
    fontSize: fontSizes.h5,
  },
  h6: {
    fontSize: fontSizes.h6,
  },
  p: {
    fontSize: fontSizes.xs,
  },
  caption: {
    fontSize: fontSizes.xxs,
  },
})

const baseTypographyBold = stylex.create({
  base: {
    fontWeight: 400,
  },
  thicker: {
    fontWeight: 700,
  },
})

const primaryFontFamily = stylex.create({
  primary: {
    fontFamily: globalTokens.primaryFontFamily,
  },
  secondary: {
    fontFamily: globalTokens.secondaryFontFamily,
  },
})

const fontSize = stylex.create({
  'text-xxs': {
    fontSize: fontSizes.xxs,
  },
  'text-xs': {
    fontSize: fontSizes.xs,
  },
  'text-sm': {
    fontSize: fontSizes.sm,
  },
  'text-md': {
    fontSize: fontSizes.md,
  },
  'text-lg': {
    fontSize: fontSizes.lg,
  },
  'text-xl': {
    fontSize: fontSizes.xl,
  },
  'text-xxl': {
    fontSize: fontSizes.xxl,
  },
  'text-h1': {
    fontSize: fontSizes.h1
  },
  'text-h2': {
    fontSize: fontSizes.h2
  },
  'text-h3': {
    fontSize: fontSizes.h3
  },
  'text-h4': {
    fontSize: fontSizes.h4
  },
  'text-h5': {
    fontSize: fontSizes.h5
  },
  'text-h6': {
    fontSize: fontSizes.h6
  },
  'text-subheading': {
    fontSize: fontSizes.subheading
  },
  'text-bodyLarge': {
    fontSize: fontSizes.bodyLarge
  },
  'text-bodySmall': {
    fontSize: fontSizes.bodySmall
  },
  'text-captionLarge': {
    fontSize: fontSizes.captionLarge
  },
  'text-captionSmall': {
    fontSize: fontSizes.captionSmall
  }
})

const fontLineHeight = stylex.create({
  xxs: {
    lineHeight: '1',
  },
  xs: {
    lineHeight: '1.15',
  },
  sm: {
    lineHeight: '1.25',
  },
  md: {
    lineHeight: '1.35',
  },
  lg: {
    lineHeight: '1.50',
  },
  xl: {
    lineHeight: '1.75',
  },
  xxl: {
    lineHeight: '2',
  }
})

const alignText = stylex.create({
  left: {
    textAlign: 'left',
  },
  right: {
    textAlign: 'right',
  },
  center: {
    textAlign: 'center',
  },
  justify: {
    textAlign: 'justify',
  },
})

const textDecoration = stylex.create({
  underline: {
    textDecoration: 'underline',
  },
  lineThrough: {
    textDecoration: 'line-through'
  }
})

const transformText = stylex.create({
  uppercase: {
    textTransform: 'uppercase',
  },
  lowercase: {
    textTransform: 'lowercase',
  },
  capitalize: {
    textTransform: 'capitalize',
  },
})

const bottomMargin = stylex.create({
  xxs: {
    marginBlockEnd: spacing.xxs,
  },
  xs: {
    marginBlockEnd: spacing.xs,
  },
  sm: {
    marginBlockEnd: spacing.sm,
  },
  md: {
    marginBlockEnd: spacing.md,
  },
  lg: {
    marginBlockEnd: spacing.lg,
  },
  xl: {
    marginBlockEnd: spacing.xl,
  },
  xxl: {
    marginBlockEnd: spacing.xxl,
  },
})

// eslint-disable-next-line complexity
const Typography = ({
  children,
  as: Component = 'h1',
  typographyTheme,
  typographyThemeMobile,
  fontBold,
  fontSecondary,
  colorSecondary,
  size,
  underline,
  lineThrough,
  lineHeight,
  textLeft,
  textRight,
  textCentered,
  textJustified,
  uppercase,
  lowercase,
  capitalize,
  marginBottom,
  styleProp,
  suppressHydrationWarning
} : Props) => (
  <Component
    {...stylex.props(
      !fontSecondary ? primaryFontFamily.primary : primaryFontFamily.secondary,
      baseTypography[Component as keyof typeof baseTypography],
      (!typographyThemeMobile && typographyTheme) && [styles.root, typographyThemes[typographyTheme]],
      (typographyThemeMobile && typographyTheme) && [styles.responsiveRoot, typographyThemes[typographyTheme], typographyThemesMobile[typographyThemeMobile]],
      fontBold && baseTypographyBold.thicker,
      colorSecondary && styles.secondaryText,
      size && fontSize[`text-${size}`],
      underline && textDecoration.underline,
      lineThrough && textDecoration.lineThrough,
      lineHeight && fontLineHeight[lineHeight as keyof typeof fontLineHeight],
      textLeft && alignText.left,
      textRight && alignText.right,
      textCentered && alignText.center,
      textJustified && alignText.justify,
      uppercase && transformText.uppercase,
      lowercase && transformText.lowercase,
      capitalize && transformText.capitalize,
      marginBottom && bottomMargin[marginBottom as keyof typeof bottomMargin],
      styleProp && styleProp
    )}
    suppressHydrationWarning={suppressHydrationWarning}
  >
    {children}
  </Component>
)

export default Typography
