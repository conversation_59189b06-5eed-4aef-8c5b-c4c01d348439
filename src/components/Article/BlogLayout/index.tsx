import {
  colors,
  spacing,
} from '@/app/themeTokens.stylex'
import Wrapper from '@components/layout/Wrapper'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import makeCommaSeperatedList from '@utils/formatCommaSeperatedList'
import { formatDate } from '@utils/date'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const DESKTOP_MEDIA_QUERY = '@media (min-width: 1024px)'

const styles = stylex.create({
  grid: {
    padding: {
      default: `${spacing.xl} 0`,
      [DESKTOP_MEDIA_QUERY]: `${spacing.xxl} 0`,
    },
    display: {
      default: 'block',
      [DESKTOP_MEDIA_QUERY]: 'grid',
    },
    gridTemplateColumns: {
      default: 'initial',
      [DESKTOP_MEDIA_QUERY]: '260px 1fr',
    },
    gap: spacing.xl,
  },
  aside: {
    marginBottom: {
      default: spacing.lg,
      [DESKTOP_MEDIA_QUERY]: '0',
    },
  },
  infoContainer: {
    paddingBlockEnd: {
      default: spacing.md,
      '@media (min-width: 1024px)': spacing.lg
    }
  },
  hrStyle: {
    borderWidth: 'none',
    height: '2px',
    backgroundColor: colors.navy,
    marginTop: spacing.xs,
    marginBottom: spacing.xs,
  },
  linkContainer: {
    gap: spacing.xs
  },
  iconContainer: {
    display: 'inline-block',
    textAlign: 'center',
    borderRadius: '50%',
    borderWidth: 1,
    borderColor: colors.navy,
    borderStyle: 'solid',
    height: spacing.lg,
    width: spacing.lg,
    padding: spacing.xs,
    marginRight: spacing.xs,
    opacity: {
      default: '.5',
      ':hover': '1'
    }
  }
})

type BlogLayoutProps = {
  children: React.ReactNode
  articleInfo: {
    authors?: {
      items: {
        name: string
      }[]
    }
    datePublished?: string
    path: string
  }
}

type ShareLinksProps = {
  path: string
}

const ShareLinks = ({ path }: ShareLinksProps) => (
  <Container
    flex
    styleProp={styles.linkContainer}
  >
    <Typography
      as="p"
      size="bodyLarge"
    >
      Share the love
    </Typography>
    <Container>
      <a
        target="_blank"
        rel="noreferrer"
        href={`https://www.facebook.com/sharer/sharer.php?u=${path}`}
        aria-label="Share on Facebook"
        {...stylex.props(styles.iconContainer)}
      >
        <Image
          src="/assets/facebookIcon.svg"
          alt="Cart Icon"
          width={22}
          height={22}
        />
      </a>
      <a
        target="_blank"
        rel="noreferrer"
        href={`https://twitter.com/intent/tweet?text=${path}`}
        aria-label="Share on Twitter"
        {...stylex.props(styles.iconContainer)}
      >
        <Image
          src="/assets/twitterIcon.svg"
          alt="Cart Icon"
          width={22}
          height={22}
        />
      </a>
    </Container>
  </Container>
)

const BlogLayout = ({ children, articleInfo }: BlogLayoutProps) => {
  const {
    authors,
    datePublished,
    path
  } = articleInfo || {}
  const authorsList = authors?.items?.length ? makeCommaSeperatedList(authors.items) : 'Caraway Home'
  const formattedDate = datePublished && formatDate(datePublished)
  const showDate = formattedDate !== 'error'

  return (
    <Wrapper
      size="lg"
      styleProp={styles.grid}
    >
      <aside {...stylex.props(styles.aside)}>
        <Container
          flex
          styleProp={styles.infoContainer}
        >
          <Typography
            as="p"
            size="bodySmall"
          >
            <strong>Cooked By: </strong>
            {authorsList}
          </Typography>
          <hr {...stylex.props(styles.hrStyle)} />
          <Typography
            as="p"
            size="bodySmall"
          >
            <strong>Subject: </strong>
            Home & Kitchen
          </Typography>
          <hr {...stylex.props(styles.hrStyle)} />
          {showDate && (
          <Typography
            as="p"
            size="bodySmall"
          >
            <strong>Date: </strong>
            {formattedDate}
          </Typography>
          )}
        </Container>
        <ShareLinks path={path} />
      </aside>
      <Container
        as="div"
        flex
        gap="2"
      >
        {children}
      </Container>
    </Wrapper>
  )
}

export default BlogLayout
