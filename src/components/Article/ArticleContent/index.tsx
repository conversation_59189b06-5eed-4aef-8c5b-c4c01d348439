import RichTextRenderer from '@utils/RichTextRenderer'
import RenderIf from '@utils/renderIf'
import { notEmpty } from '@utils/checking'
import BlogLayout from '@components/Article/BlogLayout'

type ArticleTypeProps = {
  authors?: {
    items: {
      name: string
    }[]
  }
  datePublished?: string
  path: string
}

type ArticleSectionType = {
  content: {
    links: unknown
    json: any
  },
  links: {
    entries: any
  }
  articleInfo: ArticleTypeProps
}

export const extractor = (section: ArticleSectionType): ArticleSectionType => section

const ArticleContent = ({ content, articleInfo }: ArticleSectionType) => (
  <BlogLayout articleInfo={articleInfo}>
    <RenderIf condition={notEmpty(content)}>
      <RichTextRenderer content={content.json} links={content?.links} />
    </RenderIf>
  </BlogLayout>
)

export default ArticleContent
