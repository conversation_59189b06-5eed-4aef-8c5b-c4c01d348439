import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import CallToAction from '@/components/Generic/CallToAction'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

type BlogHeroHeaderProps = {
  category?: { name: string };
  title: string;
  image: { src: string; title: string };
  callToAction?: any;
  theme?: ThemeColors;
};

const styles = stylex.create({
  headerBack: {
    position: 'absolute',
    width: '100%',
    height: {
      default: '75%',
      '@media (min-width: 1024px)': '60%'
    },
  },
  grid: {
    padding: {
      default: '48px 20px 0 20px',
      '@media (min-width: 1024px)': '64px 45px 0 45px'
    },
    position: 'relative',
    textAlign: 'center',
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  ctaWrapper: {
    marginTop: spacing.md,
  },
  title: {
    maxWidth: '600px',
    margin: '0 auto',
  },
  imageWrapper: {
    marginTop: {
      default: '32px',
      '@media (min-width: 1024px)': '50px'
    },
    width: '100%',
    height: 'auto',
  },
})

const BlogHeroHeader = ({
  category,
  image,
  title,
  callToAction,
  theme
}: BlogHeroHeaderProps) => (
  <Container as="section">
    <Container as="div" theme={theme} styleProp={styles.headerBack}><div /></Container>
    <Container as="div" gap="sm" size="xl" grid styleProp={styles.grid}>
      {category && <Typography as="p" size="sm">{category.name}</Typography>}
      <Typography
        as="h1"
        typographyTheme="h2Secondary"
        styleProp={styles.title}
      >
        {title}
      </Typography>
      {callToAction && (
        <Container as="div" styleProp={styles.ctaWrapper}>
          <CallToAction theme={theme} />
        </Container>
      )}
      <Container as="div" styleProp={styles.imageWrapper}>
        <Image
          src={image.src}
          alt={image.title || ''}
          width={100}
          height={100}
          layout="responsive"
        />
      </Container>
    </Container>
  </Container>
)

export default BlogHeroHeader
