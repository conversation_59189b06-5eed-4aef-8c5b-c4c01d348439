import {
  spacing,
  globalTokens as $,
  defaultTheme as $T,
  breakpoints
} from '@/app/themeTokens.stylex'
import themes, { ThemeColors } from '@/app/themeThemes.stylex'

import * as stylex from '@stylexjs/stylex'

type Type = 'section' | 'div' | 'article' | 'aside' | 'footer' | 'header' | 'main' | 'nav'
type Size = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | 'xxs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'

type Props = {
  children: React.ReactNode;
  as?: Type;
  theme?: ThemeColors
  size?: Size;
  paddingBlock?: Size
  pageGap?: boolean
  styleProp?: {}
  anchorTargeting?: string
}

const elementPadding = stylex.create({
  1: {
    paddingBlock: `calc(1 * ${spacing.xs})`
  },
  2: {
    paddingBlock: `calc(2 * ${spacing.xs})`
  },
  3: {
    paddingBlock: `calc(3 * ${spacing.xs})`
  },
  4: {
    paddingBlock: `calc(4 * ${spacing.xs})`
  },
  5: {
    paddingBlock: `calc(5 * ${spacing.xs})`
  },
  6: {
    paddingBlock: `calc(6 * ${spacing.xs})`
  },
  7: {
    paddingBlock: `calc(7 * ${spacing.xs})`
  },
  8: {
    paddingBlock: `calc(8 * ${spacing.xs})`
  },
  9: {
    paddingBlock: `calc(9 * ${spacing.xs})`
  },
  10: {
    paddingBlock: `calc(10 * ${spacing.xs})`
  },
  xxs: {
    gap: `calc(0.5 * ${spacing.xs})`
  },
  xs: {
    gap: `calc(1 * ${spacing.xs})`
  },
  sm: {
    gap: `calc(2 * ${spacing.xs})`
  },
  md: {
    gap: `calc(3 * ${spacing.xs})`
  },
  lg: {
    gap: `calc(4 * ${spacing.xs})`
  },
  xl: {
    gap: `calc(5 * ${spacing.xs})`
  },
  xxl: {
    gap: `calc(6 * ${spacing.xs})`
  },
})

const styles = stylex.create({
  root: {
    backgroundColor: $T.primarySurface,
    color: $T.primaryText,
  },
  pageGap: {
    paddingInline: $.pageGap
  },
})

const maxWidth = stylex.create({
  root: {
    marginInline: 'auto'
  },
  1: {
    width: '100%',
    maxWidth: `calc(30rem + ${$.pageGap} * 2)`,
  },
  2: {
    width: '100%',
    maxWidth: `calc(40rem + ${$.pageGap} * 2)`,
  },
  3: {
    width: '100%',
    maxWidth: `calc(50rem + ${$.pageGap} * 2)`,
  },
  4: {
    width: '100%',
    maxWidth: `calc(60rem + ${$.pageGap} * 2)`,
  },
  5: {
    width: '100%',
    maxWidth: `calc(75rem + ${$.pageGap} * 2)`,
  },
  6: {
    width: '100%',
    maxWidth: `calc(85rem + ${$.pageGap} * 2)`,
  },
  7: {
    width: '100%',
    maxWidth: `calc(95rem + ${$.pageGap} * 2)`,
  },
  8: {
    width: '100%',
    maxWidth: `calc(105rem + ${$.pageGap} * 2)`,
  },
  9: {
    width: '100%',
    maxWidth: `calc(110rem + ${$.pageGap} * 2)`,
  },
  10: {
    width: '100%',
    maxWidth: `calc(120rem + ${$.pageGap} * 2)`,
  },
  xxs: {
    maxWidth: breakpoints.xxs,
  },
  xs: {
    maxWidth: breakpoints.xs,
  },
  sm: {
    maxWidth: breakpoints.sm,
  },
  md: {
    maxWidth: breakpoints.md,
  },
  lg: {
    maxWidth: breakpoints.lg,
  },
  xl: {
    maxWidth: breakpoints.xl,
  },
  xxl: {
    maxWidth: breakpoints.xxl,
    width: breakpoints.xxl,
  },
})

const Wrapper = ({
  children,
  as,
  theme,
  pageGap,
  paddingBlock,
  size,
  styleProp = false,
  anchorTargeting
}: Props) => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const Component = as || 'section'
  return (
    <Component
      {...stylex.props(
        theme && [styles.root, themes[theme]],
        pageGap && styles.pageGap,
        styleProp && styleProp,
        paddingBlock && elementPadding[paddingBlock],
        size && [maxWidth[size], maxWidth.root]
      )}
      id={anchorTargeting}
    >
      {children}
    </Component>
  )
}

export default Wrapper
