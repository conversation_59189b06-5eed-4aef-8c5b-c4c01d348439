import Typography from '@components/Typography'
import NavigationLink from '@nav/NavigationLinks/NavigationLink'
import NavigationImageLink from '@nav/NavigationLinks/NavigationImageLink'
import { colors } from '@/app/themeTokens.stylex'
import { LinkCollectionProps } from '@footer/types'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

const MOBILE = '@media (max-width: 1024px)'

const styles = stylex.create({
  imageLinkContainer: {
    padding: 'unset',
    flex: '1 0 auto',
    maxWidth: '100%'
  },
  textColor: {
    color: colors.navy,
    padding: 'unset'
  },
  image: {
    width: '100%',
    maxWidth: {
      default: '100%',
      [MOBILE]: '150px'
    },
    height: 'auto',
  },
  socialIcon: {
    aspectRatio: '1/1'
  }
})

const isEmail = (content: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(content)

const getLinkContent = (link: any) => link.name || link.linkTitle || ''

const renderEmailLink = (link: any, content: string) => (
  <a key={link.id} href={`mailto:${content}`} {...stylex.props(styles.textColor)}>
    {content}
  </a>
)

const renderHeader = (link: any, content: string) => (
  <Typography key={link.id} as="span" styleProp={styles.textColor}>
    {content}
  </Typography>
)

const renderNavigationLink = (link: any) => (
  <NavigationLink key={link.id} slug={link.slug} styleProp={styles.textColor} footerLink>
    {link.linkTitle}
  </NavigationLink>
)
const isSocialImage = 25
const defaultImageSize = 150
const renderImageLink = (link: any, isSocial: boolean) => (
  <NavigationImageLink
    key={link.id}
    slug={link.slug}
    imageSrc={link.image}
    imageAlt={getLinkContent(link)}
    imageWidth={isSocial ? isSocialImage : link.imageWidth || defaultImageSize}
    imageHeight={isSocial ? isSocialImage : link.imageHeight || defaultImageSize}
    styleProp={styles.imageLinkContainer}
    linkTarget={link.linkTarget}
    containImage
    imageStyleProp={isSocial ? styles.socialIcon : styles.image}
  />
)

const LinkCollection = ({ collection, isSocial }: LinkCollectionProps) => (
  <>
    {collection.links.map((link) => {
      const content = getLinkContent(link)
      if (!link.slug || link.slug === '/') {
        return isEmail(content) ? renderEmailLink(link, content) : renderHeader(link, content)
      }

      if (collection.type === 'Text Links') {
        return renderNavigationLink(link)
      }

      if (collection.type === 'Image Link' || collection.type === 'imageLink') {
        return renderImageLink(link, isSocial)
      }

      return null
    })}
  </>
)

export default LinkCollection
