export type Widget = {
  subtype: string;
  name: string;
  id: string;
  settings: {
    theme: string | null;
  };
}

export type FooterProps = {
  items: {
    items: any[];
    widgets?: {
      subtype: string;
      id: string;
      settings: {
        theme: string;
      };
    };
  };
};

export type FooterItem = {
  id: string;
  name: string;
  linkTitle: string;
  slug: string;
  linkCollection: LinkCollection[];
}

export type LinkCollection = {
  type: string;
  links: Link[];
}

export type Link = {
  id: string;
  name: string;
  linkTitle: string;
  slug: string;
  image: string;
  linkTarget?: string;
}

export type FooterItemProps = {
  item: FooterItem;
  index: number;
  totalItems: number;
  widgets?: {
    subtype: string;
    settings: any;
  };
}

export type LinkCollectionProps = {
  collection: LinkCollection;
  isSocial: boolean;
}
