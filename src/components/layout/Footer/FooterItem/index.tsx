import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import {
  colors, spacing, fontSizes, globalTokens as $
} from '@/app/themeTokens.stylex'
import { FooterItemProps } from '@footer/types'
import LinkCollection from '@footer/FooterLinkCollection'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

const DESKTOP_MEDIA_QUERY = '@media (min-width: 1024px)'

const styles = stylex.create({
  linkContainer: {
    paddingInline: spacing.md,
    maxWidth: {
      default: 'unset',
      [DESKTOP_MEDIA_QUERY]: '1440px',
    },
    gridColumn: {
      default: 'span 1',
      [DESKTOP_MEDIA_QUERY]: 'span 3',
    }
  },
  isHeader: {
    color: colors.navy,
    fontSize: fontSizes.sm,
    marginBlockEnd: spacing.xxs,
    fontFamily: $.secondaryFontFamily,
  },
  isFirstChild: {
    maxWidth: '100%',
    gridColumn: {
      default: 'span 1',
      [DESKTOP_MEDIA_QUERY]: 'span 2',
    },
    gridRow: {
      default: '1',
      [DESKTOP_MEDIA_QUERY]: 'span 2',
    }
  },
  isLastChild: {
    maxWidth: {
      default: '60ch',
      [DESKTOP_MEDIA_QUERY]: 'unset',
    },
    textAlign: {
      default: 'left',
      [DESKTOP_MEDIA_QUERY]: 'center',
    },
  },
  isLegal: {
    gridArea: {
      default: 'unset',
      [DESKTOP_MEDIA_QUERY]: '3 / 1 / 4 / 10'
    },
    flexDirection: {
      default: 'column',
      [DESKTOP_MEDIA_QUERY]: 'row',
    },
    justifyContent: {
      default: 'unset',
      [DESKTOP_MEDIA_QUERY]: 'center',
    },
    alignItems: {
      default: 'unset',
      [DESKTOP_MEDIA_QUERY]: 'center',
    },
    gap: spacing.md,
  },
  isSocial: {
    flexDirection: 'row',
    flexWrap: 'nowrap',
    margin: '0',
    marginBlock: spacing.sm,
  },
  isBlogArticle: {
    gridArea: {
      default: 'unset',
      [DESKTOP_MEDIA_QUERY]: '2 / 9 / 3 / 13'
    }
  },
  isShipping: {
    gridArea: {
      default: 'unset',
      [DESKTOP_MEDIA_QUERY]: '4 / 1 / 4 / 13'
    },
    justifyContent: {
      default: 'unset',
      [DESKTOP_MEDIA_QUERY]: 'center',
    },
    alignItems: {
      default: 'unset',
      [DESKTOP_MEDIA_QUERY]: 'center',
    },
    textAlign: {
      default: 'left',
      [DESKTOP_MEDIA_QUERY]: 'center',
    },
  }
})

const LEGAL_INDEX = 5
const SOCIAL_INDEX = 7
const SHIPPING_INDEX = 8

const isFirstItem = (index: number) => index === 0
const isLegalItem = (index: number) => index === LEGAL_INDEX
const isSocialItem = (index: number) => index === SOCIAL_INDEX
const isShippingItem = (index: number) => index === SHIPPING_INDEX
const isBlogItem = (item: FooterItemProps['item']) => item?.name?.includes('Blog') ?? false
const FooterItem = ({ item, index }: FooterItemProps) => (
  <Container
    as="div"
    flex
    gap="1"
    styleProp={[
      styles.linkContainer,
      isFirstItem(index) && styles.isFirstChild,
      isBlogItem(item) && styles.isBlogArticle,
      isSocialItem(index) && styles.isSocial,
      isShippingItem(index) && styles.isShipping,
      isLegalItem(index) && styles.isLegal,
    ]}
  >
    {item.linkTitle && (
    <Typography as="h3" styleProp={styles.isHeader}>
      {item.linkTitle}
    </Typography>
    )}
    {item.linkCollection?.map((collection, idx) => (
      <React.Fragment key={`${collection.links[idx].id}-${String(idx)}`}>
        <LinkCollection key={collection.links[idx].id} collection={collection} isSocial={isSocialItem(index)} />
      </React.Fragment>
    ))}
  </Container>
)

export default FooterItem
