import {
  spacing,
  globalTokens as $,
  defaultTheme as $T,
  breakpoints
} from '@/app/themeTokens.stylex'
import themes, { ThemeColors } from '@/app/themeThemes.stylex'

import * as stylex from '@stylexjs/stylex'

type Type = 'section' | 'div' | 'article' | 'aside' | 'footer' | 'header' | 'main' | 'nav' | 'ul' | 'form' | 'li'
export type Size = '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | 'xxs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'
type Align = 'left' | 'center' | 'right'

type Props = {
  children: React.ReactNode;
  as?: Type;
  theme?: ThemeColors
  flex?: boolean
  flexCentered?: boolean
  alignCentered?: boolean
  flexRow?: boolean
  start?: boolean
  end?: boolean
  spaceBetween?: boolean;
  noWrap?: boolean
  grid?: boolean
  gridCentered?: boolean
  gridColumns?: boolean
  gridAutoFit?: boolean
  gridAutoFill?: boolean
  gridPile?: boolean
  gap?: Size
  justifyItemsCenter?: boolean
  justifyContentCenter?: boolean
  size?: Size | 'full'
  align?: Align
  paddingBlock?: Size
  paddingInline?: Size
  pageGap?: boolean
  contentGap?: boolean
  boxShadow?: boolean
  styleProp?: {}
  onMouseLeave?: (e: React.MouseEvent) => void,
  onClick?: (e: React.MouseEvent) => void,
  id?: string
  [key: string | number | symbol]: unknown
}

const styles = stylex.create({
  root: {
    backgroundColor: $T.primarySurface,
    color: $T.primaryText,
    position: 'relative',
  },
  boxShadow: {
    boxShadow: $.boxShadow,
  }
})

const layoutFlex = stylex.create({
  flex: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'column',
  },
  flexCentered: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alignCentered: {
    alignItems: 'center',
  },
  flexRow: {
    flexDirection: 'row'
  },
  start: {
    justifyContent: 'start'
  },
  end: {
    justifyContent: 'end'
  },
  spaceBetween: {
    justifyContent: 'space-between'
  },
  noWrap: {
    flexWrap: 'nowrap'
  },
  grid: {
    display: 'grid',
  },
  gridColumns: {
    display: 'grid',
    gridAutoFlow: 'column',
  },
  gridAutoFit: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(min(30ch, 100%), 1fr))',
  },
  gridAutoFill: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(min(30ch, 100%), 1fr))',
  },
  gridCentered: {
    display: 'grid',
    placeContent: 'center',
  },
  gridPile: {
    display: 'grid',
    placeContent: 'center',
    gridTemplateAreas: 'pile',
  },
  justifyItemsCenter: {
    justifyItems: 'center',
  },
  justifyContentCenter: {
    justifyContent: 'center',
  }
})

// NOTE: These styles are used for element spacing including gap
const elementGap = stylex.create({
  1: {
    gap: `calc(1 * ${spacing.xs})`
  },
  2: {
    gap: `calc(2 * ${spacing.xs})`
  },
  3: {
    gap: `calc(3 * ${spacing.xs})`
  },
  4: {
    gap: `calc(4 * ${spacing.xs})`
  },
  5: {
    gap: `calc(5 * ${spacing.xs})`
  },
  6: {
    gap: `calc(6 * ${spacing.xs})`
  },
  7: {
    gap: `calc(7 * ${spacing.xs})`
  },
  8: {
    gap: `calc(8 * ${spacing.xs})`
  },
  9: {
    gap: `calc(9 * ${spacing.xs})`
  },
  10: {
    gap: `calc(10 * ${spacing.xs})`
  },
  xxs: {
    gap: `calc(0.5 * ${spacing.xs})`
  },
  xs: {
    gap: `calc(1 * ${spacing.xs})`
  },
  sm: {
    gap: `calc(2 * ${spacing.xs})`
  },
  md: {
    gap: `calc(3 * ${spacing.xs})`
  },
  lg: {
    gap: `calc(4 * ${spacing.xs})`
  },
  xl: {
    gap: `calc(5 * ${spacing.xs})`
  },
  xxl: {
    gap: `calc(6 * ${spacing.xs})`
  },
})

const containerSize = stylex.create({
  1: {
    width: '100%',
    maxWidth: `calc(30rem + ${$.pageGap} * 2)`,
  },
  2: {
    width: '100%',
    maxWidth: `calc(40rem + ${$.pageGap} * 2)`,
  },
  3: {
    width: '100%',
    maxWidth: `calc(50rem + ${$.pageGap} * 2)`,
  },
  4: {
    width: '100%',
    maxWidth: `calc(60rem + ${$.pageGap} * 2)`,
  },
  5: {
    width: '100%',
    maxWidth: `calc(75rem + ${$.pageGap} * 2)`,
  },
  6: {
    width: '100%',
    maxWidth: `calc(85rem + ${$.pageGap} * 2)`,
  },
  // TODO: investigate decision below
  7: {
    width: '100%',
    maxWidth: '93.875rem',
  },
  8: {
    width: '100%',
    maxWidth: `calc(105rem + ${$.pageGap} * 2)`,
  },
  9: {
    width: '100%',
    maxWidth: `calc(110rem + ${$.pageGap} * 2)`,
  },
  10: {
    width: '100%',
    maxWidth: `calc(120rem + ${$.pageGap} * 2)`,
  },
  xxs: {
    maxWidth: breakpoints.xxs,
  },
  xs: {
    maxWidth: breakpoints.xs,
  },
  sm: {
    maxWidth: breakpoints.sm,
  },
  md: {
    maxWidth: breakpoints.md,
  },
  lg: {
    maxWidth: breakpoints.lg,
  },
  xl: {
    maxWidth: breakpoints.xl,
  },
  xxl: {
    maxWidth: breakpoints.xxl,
  },
  full: {
    width: '100%',
    maxWidth: '100%',
  }
})

const alignContainer = stylex.create({
  left: {
    marginInlineEnd: 'auto',
  },
  center: {
    marginInline: 'auto',
  },
  right: {
    marginInlineStart: 'auto',
  }
})

const elementPaddingBlock = stylex.create({
  1: {
    paddingBlock: `calc(1 * ${spacing.xs})`
  },
  2: {
    paddingBlock: `calc(2 * ${spacing.xs})`
  },
  3: {
    paddingBlock: `calc(3 * ${spacing.xs})`
  },
  4: {
    paddingBlock: `calc(4 * ${spacing.xs})`
  },
  5: {
    paddingBlock: `calc(5 * ${spacing.xs})`
  },
  6: {
    paddingBlock: `calc(6 * ${spacing.xs})`
  },
  7: {
    paddingBlock: `calc(7 * ${spacing.xs})`
  },
  8: {
    paddingBlock: `calc(8 * ${spacing.xs})`
  },
  9: {
    paddingBlock: `calc(9 * ${spacing.xs})`
  },
  10: {
    paddingBlock: `calc(10 * ${spacing.xs})`
  },
  xxs: {
    paddingBlock: `calc(0.5 * ${spacing.xs})`
  },
  xs: {
    paddingBlock: `calc(0.75 * ${spacing.xs})`
  },
  sm: {
    paddingBlock: `calc(1.5 * ${spacing.xs})`
  },
  md: {
    paddingBlock: `calc(3 * ${spacing.xs})`
  },
  lg: {
    paddingBlock: `calc(4 * ${spacing.xs})`
  },
  xl: {
    paddingBlock: `calc(5 * ${spacing.xs})`
  },
  xxl: {
    paddingBlock: `calc(6 * ${spacing.xs})`
  }
})

const elementPaddingInline = stylex.create({
  1: {
    paddingInline: `calc(1 * ${spacing.xs})`
  },
  2: {
    paddingInline: `calc(2 * ${spacing.xs})`
  },
  3: {
    paddingInline: `calc(3 * ${spacing.xs})`
  },
  4: {
    paddingInline: `calc(4 * ${spacing.xs})`
  },
  5: {
    paddingInline: `calc(5 * ${spacing.xs})`
  },
  6: {
    paddingInline: `calc(6 * ${spacing.xs})`
  },
  7: {
    paddingInline: `calc(7 * ${spacing.xs})`
  },
  8: {
    paddingInline: `calc(8 * ${spacing.xs})`
  },
  9: {
    paddingInline: `calc(9 * ${spacing.xs})`
  },
  10: {
    paddingInline: `calc(10 * ${spacing.xs})`
  },
  xxs: {
    paddingInline: `calc(0.5 * ${spacing.xs})`
  },
  xs: {
    paddingInline: `calc(0.75 * ${spacing.xs})`
  },
  sm: {
    paddingInline: `calc(1.5 * ${spacing.xs})`
  },
  md: {
    paddingInline: `calc(3 * ${spacing.xs})`
  },
  lg: {
    paddingInline: `calc(4 * ${spacing.xs})`
  },
  xl: {
    paddingInline: `calc(5 * ${spacing.xs})`
  },
  xxl: {
    paddingInline: `calc(6 * ${spacing.xs})`
  },
  pageGap: {
    paddingInline: $.pageGap
  },
  contentGap: {
    paddingInline: $.contentGap
  },
})

// eslint-disable-next-line complexity
const Container = ({
  children,
  as = 'div',
  theme,
  flex,
  flexCentered,
  alignCentered,
  flexRow,
  start,
  end,
  spaceBetween,
  noWrap,
  grid,
  gridCentered,
  gridColumns,
  gridAutoFit,
  gridAutoFill,
  gridPile,
  gap,
  justifyItemsCenter,
  justifyContentCenter,
  size,
  align,
  paddingBlock,
  paddingInline,
  pageGap,
  contentGap,
  boxShadow,
  styleProp = {},
  onMouseLeave,
  onClick,
  id,
  ...props
}: Props) => {
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const Component = as || 'div'
  return (
    <Component
      id={id}
      {...stylex.props(
        theme && [styles.root, themes[theme]],
        flex && layoutFlex.flex,
        flexCentered && layoutFlex.flexCentered,
        alignCentered && layoutFlex.alignCentered,
        flexRow && layoutFlex.flexRow,
        start && layoutFlex.start,
        end && layoutFlex.end,
        spaceBetween && layoutFlex.spaceBetween,
        noWrap && layoutFlex.noWrap,
        grid && layoutFlex.grid,
        gridCentered && layoutFlex.gridCentered,
        gridColumns && layoutFlex.gridColumns,
        gridAutoFit && layoutFlex.gridAutoFit,
        gridAutoFill && layoutFlex.gridAutoFill,
        gridPile && layoutFlex.gridPile,
        gap && elementGap[gap],
        justifyItemsCenter && layoutFlex.justifyItemsCenter,
        justifyContentCenter && layoutFlex.justifyContentCenter,
        size && containerSize[size],
        align && alignContainer[align],
        paddingBlock && elementPaddingBlock[paddingBlock],
        paddingInline && elementPaddingInline[paddingInline],
        pageGap && elementPaddingInline.pageGap,
        contentGap && elementPaddingInline.contentGap,
        boxShadow && styles.boxShadow,
        styleProp && styleProp
      )}
      onMouseLeave={onMouseLeave}
      onClick={onClick}
      {...props}
    >
      {children}
    </Component>
  )
}

export default Container
