import Base from './layouts/Base'
import LayoutWithSlider from './layouts/LayoutWithSlider'
import { featureProps } from './types'

const Feature = ({
  slides,
  content = slides[0],
  type = 'base',
  reverse = false,
  theme = 'black',
  layout = 'layout1',
  size = 'lg',
}: featureProps) => {
  if (slides.length > 1) type = 'slider'
  if (layout === 'layout2') reverse = true

  if (type === 'slider') {
    return (
      <LayoutWithSlider slides={slides} reverse={reverse} theme={theme} size={size} />
    )
  }

  return (
    <Base
      subheader={content.subheader}
      header={content.header}
      body={content.body}
      id={content.id}
      button={content.button}
      image={content.image}
      reverse={reverse}
      theme={theme}
      smallHeadline={false}
      size={size}
    />
  )
}

export default Feature
