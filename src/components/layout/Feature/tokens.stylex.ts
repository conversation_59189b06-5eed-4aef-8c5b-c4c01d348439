import {
  spacing,
  defaultTheme as $T
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const DEKSTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  container: {
    paddingInline: {
      default: spacing.lg,
      [DEKSTOP]: 0,
    },
    gap: {
      default: spacing.lg,
      [DEKSTOP]: spacing.xxl,
    },
    marginInline: 'auto',
    textAlign: {
      default: 'center',
      [DEKSTOP]: 'left',
    },
  },
  containerBase: {
    flexDirection: {
      default: 'column-reverse',
      [DEKSTOP]: 'row',
    },
  },
  containerSlider: {
    flexDirection: {
      default: 'column',
      [DEKSTOP]: 'row',
    },
  },
  baseReversed: {
    flexDirection: {
      default: 'column-reverse',
      [DEKSTOP]: 'row-reverse',
    },
  },
  sliderWrapper: {
    maxWidth: '500px',
    margin: '0 auto',
    minWidth: {
      default: 'auto',
      [DEKSTOP]: '450px',
    }
  },
  sliderReversed: {
    flexDirection: {
      default: 'column',
      [DEKSTOP]: 'row-reverse',
    },
  },
  baseContent: {
    placeSelf: 'center',
    gap: spacing.md,
    flex: '1',
    maxWidth: '100%',
    width: {
      default: 'clamp(350px, 75%, 500px)',
      [DEKSTOP]: '50%',
    }
  },
  imageContainer: {
    width: {
      default: '100%',
      [DEKSTOP]: '50%',
    },
  },
  sliderContent: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    paddingBlock: spacing.sm,
    gap: spacing.md,
    minWidth: {
      default: '100%',
      [DEKSTOP]: '200px',
    }
  },
  image: {
    width: '100%',
    height: 'auto',
    objectFit: 'cover',
  },
  content: {
    marginBottom: spacing.lg,
  },
  slideContentWrapper: {
    marginBottom: spacing.md,
  },
  space: {
    borderWidth: null,
    borderBottomWidth: 1,
    borderColor: $T.primaryText,
  },
  arrows: {
    display: {
      default: 'none',
      [DEKSTOP]: 'flex',
    },
    gap: spacing.xl,
    marginTop: 'auto',
    marginBottom: spacing.xs,
  },
  dots: {
    gap: spacing.md,
    justifyContent: 'center',
    paddingTop: spacing.md,
  },
  desktopDots: {
    display: {
      default: 'none',
      [DEKSTOP]: 'flex',
    },
  },
  mobileDots: {
    display: {
      default: 'flex',
      [DEKSTOP]: 'none',
    },
  },
})

export default styles
