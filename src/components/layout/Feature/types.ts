import { ThemeColors } from '@/app/themeThemes.stylex'
import { Size } from '@components/layout/Container'

export type baseProps = {
  id?: string,
  subheader?: string,
  header?: string,
  body?: string,
  button?: {
    anchor?: string,
    children: string,
    href: string,
    variant: 'primary' | 'secondary',
    id: string,
    useArrow: boolean,
  },
  image: string,
  smallHeadline?: boolean,
  size?: Size,
  theme?: ThemeColors,
  reverse?: boolean,
}

export type layoutWithSliderProps = {
  slides: Array<baseProps>,
  size?: Size,
  theme?: ThemeColors,
  reverse?: boolean,
}

export type featureProps = {
  slides: Array<baseProps>,
  content?: baseProps,
  type: 'slider' | 'base',
  reverse?: boolean,
  theme?: ThemeColors,
  layout?: string,
  size?: Size,
}
