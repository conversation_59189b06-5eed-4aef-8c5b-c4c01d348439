import Container from '@components/layout/Container'
import Typography from '@/components/Typography'
import CallToAction from '@/components/Generic/CallToAction'
import { baseProps } from '@/components/layout/Feature/types'
import styles from '@/components/layout/Feature/tokens.stylex'

import React from 'react'
import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const custom = stylex.create({
  space: { borderBottomWidth: '0px' }
})

const Base = ({
  subheader,
  header,
  body,
  image,
  button,
  theme,
  size = 'lg',
  reverse,
  smallHeadline,
}: baseProps) => (
  <Container as="section" theme={theme} paddingBlock="10">
    <Container
      as="div"
      flex
      flexRow
      spaceBetween
      noWrap
      styleProp={{
        ...styles.container,
        ...styles.containerBase,
        ...reverse && styles.baseReversed
      }}
      size={size}
    >
      {image && (
        <Container
          as="div"
          styleProp={styles.imageContainer}
        >
          <Image
            src={image}
            alt="slider-1"
            width={1200}
            height={675}
            {...stylex.props(styles.image)}
          />
        </Container>
      )}
      <Container as="div" flex styleProp={styles.baseContent}>
        {subheader && (
          <Typography as="span" size="xs" uppercase>
            {subheader}
          </Typography>
        )}
        <Typography
          as="h4"
          size={smallHeadline ? 'h5' : 'h4'}
          fontSecondary
        >
          {header}
        </Typography>
        <hr {...stylex.props(styles.space, custom.space)} />
        <Container as="div">
          <Typography as="p" typographyTheme="bodyLarge" styleProp={styles.content}>
            {body}
          </Typography>
          {button && (
            <CallToAction
              href={button.anchor ? `${button.href}#${button.anchor}` : button.href}
              variant={button.variant}
              id={button.id}
              useArrow
            >
              {button.children}
            </CallToAction>
          )}
        </Container>
      </Container>
    </Container>
  </Container>
)

export default Base
