'use client'

import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import CallToAction from '@components/Generic/CallToAction'
import Slider, { Slide } from '@components/Generic/Slider'
import { DotButtons, NextButton, PrevButton } from '@components/Generic/Slider/SliderButtons'
import { layoutWithSliderProps } from '@components/layout/Feature/types'
import styles from '@components/layout/Feature/tokens.stylex'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const LayoutWithSlider = ({
  slides,
  reverse,
  theme,
  size,
}: layoutWithSliderProps) => {
  const [emblaList, setEmblaList] = useState<any>([])
  const [currentSlide, setCurrentSlide] = useState(0)
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])

  const handleNext = () => {
    emblaList.forEach((embla: any) => embla.scrollNext())
    setCurrentSlide(emblaList[0].selectedScrollSnap())
  }

  const handlePrev = () => {
    emblaList.forEach((embla: any) => embla.scrollPrev())
    setCurrentSlide(emblaList[0].selectedScrollSnap())
  }

  const handleScroll = (index: number) => () => {
    emblaList.forEach((embla: any) => embla.scrollTo(index))
    setCurrentSlide(index)
  }

  useEffect(() => {
    if (!emblaList.length) return
    setScrollSnaps(emblaList[0].scrollSnapList())
    emblaList.forEach((embla: any) => {
      embla.on('select', () => {
        setCurrentSlide(embla.selectedScrollSnap())
      })
    })
  }, [emblaList])

  useEffect(() => {
    if (!emblaList.length) return
    emblaList.forEach((embla: any) => {
      embla.scrollTo(currentSlide)
    })
  }, [emblaList, currentSlide])

  return (
    <Container as="section" theme={theme} paddingBlock="7">
      <Container
        as="div"
        flex
        flexRow
        spaceBetween
        noWrap
        size={size}
        styleProp={{
          ...styles.container,
          ...styles.containerSlider,
          ...reverse && styles.sliderReversed
        }}
      >
        <Container
          as="div"
          styleProp={styles.sliderWrapper}
        >
          <Slider setEmblaList={setEmblaList}>
            {slides.map((slide) => (
              <Slide key={slide.id}>
                <Image
                  src={slide.image}
                  alt={slide.header || ''}
                  width={900}
                  height={506}
                  layout="responsive"
                  objectFit="cover"
                />
              </Slide>
            ))}
          </Slider>
          <DotButtons
            scrollSnaps={scrollSnaps}
            handleScroll={handleScroll}
            styleProp={{ ...styles.dots, ...styles.desktopDots }}
            currentSlide={currentSlide}
          />
        </Container>
        <Container as="div" flex>
          <Slider setEmblaList={setEmblaList}>
            {slides.map((slide) => (
              <Slide key={slide.id} styleProp={styles.sliderContent}>
                <Typography as="span" size="xs" uppercase>
                  {slide.subheader}
                </Typography>
                <Typography
                  as="h3"
                  size={slide.smallHeadline ? 'h5' : 'h3'}
                  fontSecondary
                >
                  {slide.header}
                </Typography>
                <hr {...stylex.props(styles.space)} />
                <Container
                  as="div"
                  styleProp={styles.slideContentWrapper}
                >
                  <Typography as="p" size="bodyLarge" styleProp={styles.content}>
                    {slide.body}
                  </Typography>
                  {slide.button && (
                  <CallToAction
                    href={slide.button.href}
                    variant={slide.button.variant}
                    id={slide.button.id}
                    useArrow
                  >
                    {slide.button.children}
                  </CallToAction>
                  )}
                </Container>
              </Slide>
            ))}
          </Slider>
          <DotButtons
            scrollSnaps={scrollSnaps}
            handleScroll={handleScroll}
            styleProp={{ ...styles.dots, ...styles.mobileDots }}
            currentSlide={currentSlide}
          />
          <Container as="div" styleProp={styles.arrows}>
            <PrevButton enabled onClick={() => handlePrev()} />
            <NextButton enabled onClick={() => handleNext()} />
          </Container>
        </Container>
      </Container>
    </Container>
  )
}

export default LayoutWithSlider
