import Container from '@components/layout/Container'
import Typography from '@/components/Typography'
import CallToAction from '@/components/Generic/CallToAction'

import React from 'react'
import stylex from '@stylexjs/stylex'

type PropType = {
  selected: boolean
  index: number
  onClick: () => void
  data?: any
}

const DESKTOP = '@media (min-width: 1500px)'

const styles = stylex.create({
  embla_thumbs_slide: (image) => ({
    minWidth: '130px',
    width: '130px',
    height: '100%',
    transition: 'min-width 0.3s ease-in',
    position: 'relative',
    backgroundImage: `url(${image})`,
    backgroundPosition: 'center right',
    backgroundRepeat: 'no-repeat',
    backgroundAttachment: 'fixed',
    backgroundSize: {
      default: '1500px auto',
      [DESKTOP]: 'cover',
    },
  }),
  embla_thumbs_slide_selected: {
    backgroundPosition: 'center center',
    minWidth: 'calc(100% - 260px)',
    padding: '45px',
  },
  embla_thumbs_slide_number: {
    overflow: 'hidden',
    touchAction: 'manipulation',
    textDecoration: 'none',
    cursor: 'pointer',
    padding: 0,
    margin: 0,
    boxShadow: 'inset 0 0 0 0.2rem var(--detail-medium-contrast)',
    fontSize: '1.8rem',
    fontWeight: 600,
    color: 'var(--detail-high-contrast)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    width: '100%',
    '::after': {
      display: 'block',
      position: 'absolute',
      content: '""',
      width: '100%',
      height: '100%',
      background: 'rgba(0, 0, 0, 0.3)',
      top: '0px',
      left: '0px',
    },
  },
  embla_thumbs_slide_number_selected: {
    alignItems: 'flex-end',
    justifyContent: 'flex-start',
    cursor: 'initial',
  },
  embla_mini_content: {
    transform: 'rotate(-90deg)',
    position: 'absolute',
    bottom: '190px',
    width: '300px',
    zIndex: 1,
  },
  embla_mini_content_selected: {
    display: 'none',
  },
  embla_main_content: {
    position: 'relative',
    zIndex: 1,
    gridTemplateRows: 'repeat(3, auto)',
    width: '100%',
    minWidth: '430px',
    maxWidth: '430px',
    justifyItems: 'start',
    gap: '20px',
    transformOrigin: 'right',
    display: 'none',
  },
  embla_main_content_selected: {
    display: 'grid',
  },
})

const Thumb: React.FC<PropType> = (props) => {
  const {
    selected,
    onClick,
    data
  } = props

  return (
    <Container
      as="div"
      styleProp={[
        styles.embla_thumbs_slide(data.media.src),
        selected && styles.embla_thumbs_slide_selected
      ]}
    >
      <button
        onClick={onClick}
        type="button"
        {...stylex.props([
          styles.embla_thumbs_slide_number,
          selected && styles.embla_thumbs_slide_number_selected
        ])}
      >
        <Container
          as="div"
          styleProp={[
            styles.embla_mini_content,
            selected && styles.embla_mini_content_selected
          ]}
        >
          <Typography
            as="p"
            size="bodyLarge"
            fontBold
            uppercase
          >
            {data.subheading}
          </Typography>
        </Container>
        <Container
          as="div"
          styleProp={[
            styles.embla_main_content,
            selected && styles.embla_main_content_selected
          ]}
        >
          <Typography
            as="p"
            size="bodySmall"
            uppercase
          >
            {data.subheading}
          </Typography>
          <Typography
            as="h2"
            size="h3"
            fontBold
            fontSecondary
          >
            {data.heading}
          </Typography>
          <CallToAction
            useArrow
            variant="primary"
            href={data.button.url}
          >
            {data.button.label}
          </CallToAction>
        </Container>
      </button>
    </Container>
  )
}

export default Thumb
