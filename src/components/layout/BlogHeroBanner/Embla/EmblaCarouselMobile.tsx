'use client'

import { DotButton, useDotButton } from './EmblaCarouselMobielDotButton'

import CallToAction from '@/components/Generic/CallToAction'
import Typography from '@/components/Typography'
import { colors, fontSizes } from '@/app/themeTokens.stylex'
import Container from '@components/layout/Container'

import React from 'react'
import { EmblaOptionsType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import Image from 'next/image'
import stylex from '@stylexjs/stylex'

export type SlidesDataType = {
  subheading: string;
  heading: string;
  button?: {
    label: string;
    url: string;
  };
  media?: {
    src: string;
    alt: string;
  };
}

type PropType = {
  slidesData: SlidesDataType[]
  slides: number[]
  options?: EmblaOptionsType
}

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  embla: {
    margin: 'auto',
    backgroundColor: colors.navy,
    display: {
      default: 'block',
      [DESKTOP]: 'none',
    },
  },
  embla_viewport: {
    overflow: 'hidden',
  },
  embla_container: {
    display: 'flex',
    touchAction: 'pan-y pinch-zoom',
    marginLeft: 'calc(1rem * -1)',
  },
  embla_slide: {
    transform: 'translate3d(0, 0, 0)',
    flex: '0 0 100%',
    minWidth: '0',
    paddingLeft: '1rem',
  },
  embla_slide_number: {
    boxShadow: 'inset 0 0 0 0.2rem var(--detail-medium-contrast)',
    borderRadius: '1.8rem',
    fontSize: '4rem',
    fontWeight: 'bold',
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    userSelect: 'none',
    flexDirection: 'column',
    gap: '40px',
  },
  embla_slide_image_container: {
    height: '375px',
    width: '100%',
  },
  embla_slide_image: {
    height: '100%',
    width: '100%',
    objectFit: 'cover',
  },
  embla_slide_content_container: {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
    alignItems: 'center',
    width: '100%',
    padding: '0 24px',
  },
  embla_slide_link: {
    fontSize: fontSizes.h3,
    textAlign: 'center',
  },
  embla_controls: {
    display: 'block',
    padding: '32px 0',
  },
  embla_dots: {
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '16px',
  },
  embla_dot: {
    appearance: 'none',
    backgroundColor: '#C7C7C7',
    touchAction: 'manipulation',
    display: 'inline-flex',
    textDecoration: 'none',
    cursor: 'pointer',
    padding: 0,
    margin: 0,
    width: '8px',
    height: '8px',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
  },
  embla_dot_selected: {
    backgroundColor: '#F7E6C1',
  },
})

const EmblaCarousel: React.FC<PropType> = (props) => {
  const { options, slidesData } = props
  const [emblaRef, emblaApi] = useEmblaCarousel(options)

  const {
    selectedIndex,
    scrollSnaps,
    onDotButtonClick
  } = useDotButton(emblaApi)

  return (
    <Container
      as="div"
      styleProp={[styles.embla]}
      theme="sage"
    >
      <div
        ref={emblaRef}
        {...stylex.props([styles.embla_viewport])}
      >
        <Container
          as="div"
          styleProp={[styles.embla_container]}
        >
          {slidesData.map((slide) => (
            <Container
              as="div"
              styleProp={[styles.embla_slide]}
            >
              <Container
                as="div"
                styleProp={[styles.embla_slide_number]}
              >
                {slide.media && (
                <Container
                  as="div"
                  styleProp={[styles.embla_slide_image_container]}
                >
                  <Image
                    src={slide.media.src}
                    alt={slide.media.alt}
                    width={1000}
                    height={500}
                    {...stylex.props([styles.embla_slide_image])}
                  />
                </Container>
                )}
                <Container
                  as="div"
                  styleProp={[styles.embla_slide_content_container]}
                >
                  <Typography
                    as="p"
                    size="bodySmall"
                  >
                    {slide.subheading}
                  </Typography>
                  {slide.button && (
                  <CallToAction
                    useArrow
                    variant="primary"
                    href={slide.button.url}
                    {...stylex.props([styles.embla_slide_link])}
                  >
                    {slide.heading}
                  </CallToAction>
                  )}
                </Container>
              </Container>
            </Container>
          ))}
        </Container>
      </div>

      <Container
        as="div"
        styleProp={[styles.embla_controls]}
      >
        <Container
          as="div"
          styleProp={[styles.embla_dots]}
        >
          {scrollSnaps.map((_, index) => (
            <DotButton
              onClick={() => onDotButtonClick(index)}
              {...stylex.props([
                styles.embla_dot,
                index === selectedIndex && styles.embla_dot_selected,
              ])}
            />
          ))}
        </Container>
      </Container>
    </Container>
  )
}

export default EmblaCarousel
