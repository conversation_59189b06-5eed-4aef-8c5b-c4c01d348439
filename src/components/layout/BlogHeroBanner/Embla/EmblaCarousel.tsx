'use client'

import Thumb from './EmblaCarouselThumbsButton'

import Container from '@components/layout/Container'

import React, { useState, useEffect, useCallback } from 'react'
import { EmblaOptionsType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import stylex from '@stylexjs/stylex'

type PropType = {
  slidesData: any
  slides: number[]
  options?: EmblaOptionsType
}

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  embla: {
    margin: 'auto',
    display: {
      default: 'none',
      [DESKTOP]: 'initial',
    },
  },
  embla_viewport: {
    overflow: 'hidden',
    visibility: 'hidden',
    height: '0',
  },
  embla_container: {
    display: 'flex',
    touchAction: 'pan-y pinch-zoom',
    marginLeft: 'calc(1rem * -1)',
  },
  embla_slide: {
    transform: 'translate3d(0, 0, 0)',
    flex: '0 0 100%',
    minWidth: '0',
    paddingLeft: '1rem',
  },
  embla_thumbs: {
    height: '568px',
  },
  embla_thumbs_viewport: {
    overflow: 'hidden',
    height: '100%',
  },
  embla_thumbs_container: {
    display: 'flex',
    flexDirection: 'row',
    marginLeft: 'calc(0.8rem * -1)',
    height: '100%',
  }
})

const EmblaCarousel: React.FC<PropType> = (props) => {
  const {
    slides,
    options,
    slidesData
  } = props
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [emblaMainRef, emblaMainApi] = useEmblaCarousel(options)
  const [emblaThumbsRef, emblaThumbsApi] = useEmblaCarousel({
    containScroll: 'keepSnaps',
    dragFree: true
  })

  const onThumbClick = useCallback(
    (index: number) => {
      if (!emblaMainApi || !emblaThumbsApi) return
      emblaMainApi.scrollTo(index)
    },
    [emblaMainApi, emblaThumbsApi]
  )

  const onSelect = useCallback(() => {
    if (!emblaMainApi || !emblaThumbsApi) return
    setSelectedIndex(emblaMainApi.selectedScrollSnap())
    emblaThumbsApi.scrollTo(emblaMainApi.selectedScrollSnap())
  }, [emblaMainApi, emblaThumbsApi, setSelectedIndex])

  useEffect(() => {
    if (!emblaMainApi) return
    onSelect()

    emblaMainApi.on('select', onSelect).on('reInit', onSelect)
  }, [emblaMainApi, onSelect])

  return (
    <Container
      as="div"
      styleProp={[styles.embla]}
    >
      <div
        ref={emblaMainRef}
        {...stylex.props(styles.embla_viewport)}
      >
        <Container
          as="div"
          styleProp={[styles.embla_container]}
        >
          {slides.map((index) => (
            <Container
              as="div"
              key={index}
              styleProp={[styles.embla_slide]}
            >
                &nbsp;
            </Container>
          ))}
        </Container>
      </div>

      <Container
        as="div"
        styleProp={[styles.embla_thumbs]}
      >
        <div
          ref={emblaThumbsRef}
          {...stylex.props(styles.embla_thumbs_viewport)}
        >
          <Container
            as="div"
            styleProp={[styles.embla_thumbs_container]}
          >
            {slidesData.map((slide: any, index: number) => (
              <Thumb
                onClick={() => onThumbClick(index as number)}
                selected={index === selectedIndex}
                index={index}
                data={slide}
              />
            ))}
          </Container>
        </div>
      </Container>
    </Container>
  )
}

export default EmblaCarousel
