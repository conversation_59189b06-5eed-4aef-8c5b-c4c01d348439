import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

type SpacerSize = 'xxs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';

type SpacerProps = {
  size?: SpacerSize;
  styleProp?: {}
}

const baseSpacings = stylex.create({
  xxs: {
    height: spacing.xxs,
  },
  xs: {
    height: spacing.xs,
  },
  sm: {
    height: spacing.sm,
  },
  md: {
    height: spacing.md,
  },
  lg: {
    height: spacing.lg,
  },
  xl: {
    height: spacing.xl,
  },
  xxl: {
    height: spacing.xxl,
  },
})

const Spacer = ({ size = 'md', styleProp = false }: SpacerProps) => (
  <div {...stylex.props(size && baseSpacings[size], styleProp && styleProp)} />
)

export default Spacer
