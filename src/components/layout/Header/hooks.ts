import { useLayoutEffect, useState } from 'react'

export default function useStickyNav() {
  const [isSticky, setIsSticky] = useState(false)
  const [isProductTitleSticky, setIsProductTitleSticky] = useState(false)
  const [mainNavHeight, setMainNavHeight] = useState(0)

  const scrollThreshold = 500

  useLayoutEffect(() => {
    let lastScrollY = 0

    const handleScroll = () => {
      const { scrollY: currentScrollY } = window

      if (currentScrollY > scrollThreshold && currentScrollY > lastScrollY) {
        setIsSticky(true)
      } else {
        setIsSticky(false)
      }

      lastScrollY = currentScrollY
    }

    window.addEventListener('scroll', handleScroll)

    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useLayoutEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting || entry.boundingClientRect.top > 0) {
            setIsProductTitleSticky(false)
          } else {
            setIsProductTitleSticky(true)
          }

          const nav = document.getElementById('main-nav')

          if (nav) {
            setMainNavHeight(nav.offsetHeight)
          }
        })
      }
    )

    const atcButton = document.getElementById('add-to-cart-button')

    if (atcButton) {
      observer.observe(atcButton)
    }
  }, [])

  return {
    isSticky,
    isProductTitleSticky,
    mainNavHeight,
  }
}
