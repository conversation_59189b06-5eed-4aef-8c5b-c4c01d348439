'use client'

import Nav from './Nav'
import useStickyNav from './hooks'

import {
  colors,
  globalTokens as $
} from '@/app/themeTokens.stylex'

import React from 'react'
import * as stylex from '@stylexjs/stylex'
import { usePathname } from 'next/navigation'

// TODO: Multiple Promotions: Allow multiple promo banners to be stacked or rotated (either manually via a carousel or timed auto-rotation).
const styles = stylex.create({
  header: {
    position: 'sticky',
    top: 0,
    width: '100%',
    zIndex: 50,
    backgroundColor: colors.white,
    transform: 'translateY(0)',
    transition: $.transitionSmooth,
    isolation: 'isolate'
  },
  sticky: {
    transform: `translateY(calc(${$.promoBarHeight} * -1))`,
  },
  pdpSticky: {
    transform: 'translateY(-100%)',
  },
})

const Header = ({
  aboutNavigationData,
  navigationData,
  promoBarData,
  mobileNavigationData,
  mobileQuickNavigationData,
  promoBar,
  searchMenuData
}: any) => {
  const pathname = usePathname()
  const isProductPage = pathname.includes('/products/')
  const { isSticky, isProductTitleSticky } = useStickyNav()

  return (
    <header {...stylex.props(
      styles.header,
      isSticky && styles.sticky,
      isSticky && isProductTitleSticky && isProductPage && styles.pdpSticky,
    )}
    >
      <Nav
        aboutNavigationData={aboutNavigationData}
        navigationData={navigationData}
        promoBarData={promoBarData}
        mobileNavigationData={mobileNavigationData}
        mobileQuickNavigationData={mobileQuickNavigationData}
        isSticky={isSticky}
        promoBar={promoBar}
        searchMenuData={searchMenuData}
      />
    </header>
  )
}

export default Header
