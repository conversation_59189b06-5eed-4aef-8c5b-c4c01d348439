import MobileSearchMenu from './Navigation/MobileNavigation/MobileSearchMenu'

import {
  spacing,
  fontSizes,
  colors
} from '@/app/themeTokens.stylex'
import CartToggle from '@components/Cart/CartToggle'
import NavigationBlock from '@nav/NavigationBlock'
import MobileNavigationBlock from '@nav/MobileNavigation/MobileNavigationBlock'
import CarawayLogo from '@nav/MobileNavigation/CarawayLogo'
import HamburgerIcon from '@nav/MobileNavigation/HamburgerIcon'
import MeilisearchInputDesktop from '@/components/Meilisearch/MeilisearchInputDesktop'
import MeilisearchMobileIcon from '@/components/Meilisearch/MeilisearchMobileIcon'

import React, { useState } from 'react'
import Link from 'next/link'
import * as stylex from '@stylexjs/stylex'

const DESKTOP_MEDIA_QUERY = '@media (min-width: 1024px)'
const styles = stylex.create({
  nav: {
    position: 'sticky',
    top: 0,
    width: '100%',
    zIndex: 1,
    backgroundColor: colors.white
  },
  wrapper: {
    display: 'grid',
    gridTemplateColumns: '1fr 1fr 1fr',
    minHeight: {
      default: '52px',
      [DESKTOP_MEDIA_QUERY]: '67px'
    },
    alignItems: 'center',
    paddingInline: {
      default: spacing.sm,
      [DESKTOP_MEDIA_QUERY]: spacing.lg
    },
    borderBottomWidth: '1px',
    borderBottomStyle: 'solid',
    borderBottomColor: colors.black,
    backgroundColor: colors.white,
    position: 'relative',
    zIndex: 2
  },
  menu: {
    display: 'flex',
    justifyContent: 'center',
    gap: spacing.md,
    fontSize: fontSizes.xs,
    listStyle: 'none',
    color: colors.gray,
    marginBottom: '-8px',
  },
  isCart: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    height: '100%',
    marginBottom: '0px',
  },
  mobileMenu: {
    display: {
      default: 'block',
      [DESKTOP_MEDIA_QUERY]: 'none',
    },
  },
  isLogoAnchor: {
    display: 'inline-block',
  },
  isLogo: {
    width: {
      default: '108px',
      [DESKTOP_MEDIA_QUERY]: '135px'
    },
  },
})

const Nav = ({
  aboutNavigationData,
  navigationData,
  mobileNavigationData,
  mobileQuickNavigationData,
  isSticky,
  promoBar,
  searchMenuData
}: any) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isMobileSearchMenuOpen, setIsMobileSearchMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen((prevState) => !prevState)
    setIsMobileSearchMenuOpen(() => false)
  }

  const toggleMobileSearchMenu = () => {
    setIsMobileSearchMenuOpen((prevState) => !prevState)
    setIsMenuOpen(() => false)
  }

  return (
    <nav {...stylex.props(styles.nav)} {...(isMenuOpen ? { id: 'mobile-menu-open' } : {})} id="main-nav">
      {promoBar}
      <div id="page-promo-bar" />

      {/* Mobile Menu */}
      <MobileNavigationBlock isOpen={isMenuOpen} items={mobileNavigationData} onClose={toggleMenu} isSticky={isSticky} />

      {/* Mobile Search Menu */}
      <MobileSearchMenu searchMenuData={searchMenuData} isOpen={isMobileSearchMenuOpen} onClose={toggleMobileSearchMenu} isSticky={isSticky} />

      <div {...stylex.props(styles.wrapper)}>
        <menu {...stylex.props(styles.mobileMenu)}>
          {/* Hamburger Icon */}
          <HamburgerIcon isOpen={isMenuOpen} toggleMenu={toggleMenu} />
        </menu>
        <NavigationBlock about items={aboutNavigationData} />
        <menu {...stylex.props(styles.menu)}>
          <li>
            <Link href="/" {...stylex.props(styles.isLogoAnchor)}>
              <CarawayLogo styleProp={styles.isLogo} />
            </Link>
          </li>
        </menu>
        <menu {...stylex.props(
          styles.menu,
          styles.isCart
        )}
        >
          <MeilisearchInputDesktop />
          <MeilisearchMobileIcon toggleMenu={toggleMobileSearchMenu} />
          <li>
            <CartToggle />
          </li>
        </menu>
      </div>
      <NavigationBlock items={navigationData} />
      <NavigationBlock items={mobileQuickNavigationData} mobileQuickNavigation />
    </nav>
  )
}

export default Nav
