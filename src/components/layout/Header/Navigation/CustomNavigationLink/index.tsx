import NavigationLink from '@nav/MobileNavigation/NavigationLink'
import DesktopNavigationLink from '@nav/NavigationLinks/NavigationLink'
import { NavigationItem as NavigationItemType } from '@nav/types'
import { spacing } from '@/app/themeTokens.stylex'
import Hearts from '@nav/CustomNavigationLink/hearts'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'

type SpecialNavigationLinkProps = {
  item: NavigationItemType,
  onLinkTap?: (slug: string) => void,
  desktop?: boolean,
  mobileQuickNavigation?: boolean
}
const DESKTOP_MEDIA_QUERY = '@media (min-width: 1024px)'

const styles = stylex.create({
  linkContainer: {
    display: 'grid',
    alignItems: 'center',
    cursor: 'pointer'
  },
  linkStyle: {
    gridArea: '1/1'
  },
  birthdayLink: {
    gridArea: '1/1',
    width: '100%',
    paddingLeft: {
      default: 0,
      [DESKTOP_MEDIA_QUERY]: spacing.xxs
    },
    paddingRight: {
      default: 0,
      [DESKTOP_MEDIA_QUERY]: spacing.xxs
    },
    fontFamily: 'inherit'
  },
  holidayIcon: {
    gridArea: '1/1',
    width: 165,
    height: {
      default: 50,
      [DESKTOP_MEDIA_QUERY]: 59
    }
  }
})

const animationComponents = {
  hearts: Hearts,
}

const CustomNavigationLink = ({
  item,
  onLinkTap,
  desktop
}: SpecialNavigationLinkProps) => {
  const AnimationComponent = animationComponents[item.animationType as keyof typeof animationComponents]

  const LinkComponent = desktop ? DesktopNavigationLink : NavigationLink

  return (
    <Container {...stylex.props(styles.linkContainer)}>
      {AnimationComponent && <AnimationComponent />}
      <LinkComponent
        slug={item?.slug || '/'}
        callouts={item.callouts}
        linkTarget={item.linkTarget}
        onClick={() => item.slug && onLinkTap && onLinkTap(item.slug)}
        hasCollection={item.linkCollection && item.linkCollection.length > 0}
        styleProp={styles.linkStyle}
      >
        {item.name}
      </LinkComponent>
    </Container>
  )
}

export default CustomNavigationLink
