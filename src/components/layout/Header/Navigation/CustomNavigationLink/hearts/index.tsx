import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

type HolidayIconProps = {
  styleProp?: stylex.StyleXStyles
}

const pulseAnimation = stylex.keyframes({
  '0%': { transform: 'scale(0)' },
  '50%': { transform: 'scale(var(--pulse-scale))' },
  '100%': { transform: 'scale(0)' }
})

const styles = stylex.create({
  basePath: {
    strokeWidth: 2,
    strokeMiterlimit: 0,
    strokeLinejoin: 'round',
    animationTimingFunction: 'ease-in-out',
    animationFillMode: 'both',
    transformBox: 'fill-box'
  },
  svgContainer: {
    cursor: 'pointer',
    gridArea: '1/1',
    height: '100%'
  },
  heartPath: {
    strokeWidth: '2px',
    animationDuration: '2s',
    animationIterationCount: 'infinite',
    animationTimingFunction: 'ease-in-out',
  },
  heart1: {
    transformOrigin: '32px 63px',
    animationName: pulseAnimation,
    animationDelay: '0s',
    rotate: '-35deg',
    fill: colors.perracotta,
  },
  heart2: {
    transformOrigin: '109px 160px',
    animationName: pulseAnimation,
    animationDelay: '-0.5s',
    rotate: '10deg',
    fill: colors.perracotta,
  },
  heart3: {
    transformOrigin: '245px 55px',
    animationName: pulseAnimation,
    animationDelay: '-1s',
    rotate: '-20deg',
    fill: colors.perracotta,
  },
  heart4: {
    transformOrigin: '289px 163px',
    animationName: pulseAnimation,
    animationDelay: '-1.5s',
    rotate: '35deg',
    fill: colors.perracotta,
  },
})

const Hearts = ({ styleProp }: HolidayIconProps) => (
  <svg
    viewBox="0 0 384 216"
    xmlns="http://www.w3.org/2000/svg"
    {...stylex.props(styles.basePath, styles.svgContainer, styleProp)}
  >
    <path
      style={{ '--pulse-scale': '1.1' }}
      id="heart"
      d={`M 31.951 55.046 C 31.895 54.929 28.141 47.231 21.639 52.711 
          C 12.559 62.277 31.768 75.312 31.95 75.436 
          C 32.134 75.312 51.341 62.275 42.263 52.71 
          C 35.76 47.229 32.007 54.927 31.95 55.044 
          L 31.951 55.046 Z`}
      {...stylex.props(styles.heartPath, styles.heart1, styleProp)}
    />
    <path
      style={{ '--pulse-scale': '.95' }}
      id="path-4"
      d={`M 109.077 152.178 C 109.021 152.061 105.267 144.363 98.765 149.843 
          C 89.685 159.409 108.894 172.444 109.076 172.568 
          C 109.26 172.444 128.467 159.407 119.389 149.842 
          C 112.886 144.361 109.133 152.059 109.076 152.176 
          L 109.077 152.178 Z`}
      {...stylex.props(styles.heartPath, styles.heart2, styleProp)}
    />
    <path
      style={{ '--pulse-scale': '.8' }}
      id="path-1"
      d={`M 244.583 46.609 C 244.527 46.492 240.773 38.794 234.271 44.274 
          C 225.19 53.84 244.4 66.875 244.582 66.999 
          C 244.766 66.875 263.973 53.838 254.895 44.273 
          C 248.392 38.792 244.639 46.49 244.582 46.607 
          L 244.583 46.609 Z`}
      {...stylex.props(styles.heartPath, styles.heart3, styleProp)}
    />
    <path
      style={{ '--pulse-scale': '.85' }}
      id="path-2"
      d={`M 289.349 154.433 C 289.293 154.316 285.539 146.618 279.037 152.098 
          C 269.957 161.664 289.166 174.699 289.348 174.823 
          C 289.532 174.699 308.739 161.662 299.661 152.097 
          C 293.158 146.616 289.405 154.314 289.348 154.431 
          L 289.349 154.433 Z`}
      {...stylex.props(styles.heartPath, styles.heart4, styleProp)}
    />
  </svg>
)

export default Hearts
