import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

type HolidayIconProps = {
  styleProp?: stylex.StyleXStyles
}

const sparkle = stylex.keyframes({
  '0%': { transform: 'scale(0)' },
  '50%': { transform: 'scale(0)' },
  '70%': { transform: 'scale(-1, 0)' },
  '80%': { transform: 'scale(1)' },
  '100%': { transform: 'scale(0)' }
})

const styles = stylex.create({
  star1: {
    animationName: sparkle,
    animationDuration: '3.5s',
    animationDelay: '-.2s',
    animationIterationCount: 'infinite',
    transformOrigin: '20px 50px',
    fill: colors.peach
  },
  star2: {
    animationName: sparkle,
    animationDuration: '2.7s',
    animationDelay: '-0.4s',
    animationIterationCount: 'infinite',
    transformOrigin: '350px 50px',
    fill: colors.lavender
  },
  star3: {
    animationName: sparkle,
    animationDuration: '3.2s',
    animationDelay: '-0.9s',
    animationIterationCount: 'infinite',
    transformOrigin: '100px 160px',
    fill: colors.emerald
  },
  basePath: {
    strokeWidth: 2,
    strokeMiterlimit: 0,
    strokeLinejoin: 'round',
    animationTimingFunction: 'ease-in-out',
    animationFillMode: 'both',
    transformBox: 'fill-box'
  },
  svgContainer: {
    maxWidth: '150px',
    cursor: 'pointer'
  }
})

const HolidayIcon = ({ styleProp }: HolidayIconProps) => (
  <svg
    viewBox="0 0 384 216"
    xmlns="http://www.w3.org/2000/svg"
    {...stylex.props(styles.basePath, styles.svgContainer, styleProp)}
  >
    <defs>
      <circle
        {...stylex.props(styleProp)}
        id="circle"
        cx="20"
        cy="40"
        r="20"
      />
    </defs>
    <use href="#circle" x="0" y="0" {...stylex.props(styles.star1)} />
    <use href="#circle" x="305" y="5" {...stylex.props(styles.star2)} />
    <use href="#circle" x="100 " y="130" {...stylex.props(styles.star3)} />
  </svg>
)

export default HolidayIcon
