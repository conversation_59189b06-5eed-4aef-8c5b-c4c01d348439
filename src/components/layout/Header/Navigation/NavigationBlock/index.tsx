import { NavigationItem } from '../types'

import Container from '@components/layout/Container'
import { colors, globalTokens as $ } from '@/app/themeTokens.stylex'
import NavigationDropdown from '@nav/NavigationDropdown'
import NavigationLinks from '@nav/NavigationLinks'

import * as stylex from '@stylexjs/stylex'
import { useState } from 'react'

type NavigationBlockProps = {
  items: NavigationItem[]
  about?: boolean
  mobileQuickNavigation?: boolean
}

const DESKTOP_MEDIA_QUERY = '@media (min-width: 1024px)'

const styles = stylex.create({
  nav: {
    boxShadow: $.boxShadow,
    display: {
      default: 'none',
      [DESKTOP_MEDIA_QUERY]: 'block',
    },
  },
  about: {
    display: {
      default: 'none',
      [DESKTOP_MEDIA_QUERY]: 'flex',
    },
    alignItems: 'center',
    height: '100%',
    borderBottomWidth: '1px',
    borderBottomStyle: 'solid',
    borderBottomColor: colors.navy,
    marginBlockStart: '2px',
    boxShadow: 'unset',
    backgroundColor: colors.white
  },
  mobileQuickNav: {
    display: {
      default: 'flex',
      [DESKTOP_MEDIA_QUERY]: 'none',
    },
    boxShadow: `0 1px 0 0 ${colors.navy}`,
    backgroundColor: colors.white
  }
})

const NavigationBlock = ({
  items,
  about,
  mobileQuickNavigation
}: NavigationBlockProps) => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null)

  const handleMouseEnter = (linkDestination: string) => {
    const matchingItem = items.find((navItem) => navItem.slug === linkDestination)
    if (matchingItem && matchingItem.linkCollection && matchingItem.linkCollection.length > 0) {
      setOpenDropdown(linkDestination)
    } else {
      setOpenDropdown(null)
    }
  }

  const handleMouseLeave = () => setOpenDropdown(null)

  return (
    <Container
      as="nav"
      data-type="desktop-nav"
      styleProp={[
        styles.nav,
        about && styles.about,
        mobileQuickNavigation && styles.mobileQuickNav
      ]}
      onMouseLeave={handleMouseLeave}
    >
      <NavigationLinks
        items={items}
        onMouseEnter={handleMouseEnter}
        openDropdown={openDropdown}
        about={about}
        mobileQuickNavigation={mobileQuickNavigation}
      />
      <NavigationDropdown
        about={about}
        items={items}
        openDropdown={openDropdown}
        onMouseUp={handleMouseLeave}
      />
    </Container>
  )
}

export default NavigationBlock
