'use client'

import NavigationLink from './NavigationLink'

import { NavigationItem } from '../types'

import CustomNavigationLink from '@nav/CustomNavigationLink'
import DropdownPointer from '@nav/NavigationDropdown/DropdownPointer'
import { spacing, colors } from '@/app/themeTokens.stylex'

import { useEffect, useRef } from 'react'
import useEmblaCarousel from 'embla-carousel-react'
import * as stylex from '@stylexjs/stylex'
import { EmblaOptionsType } from 'embla-carousel'

const styles = stylex.create({
  menu: {
    position: 'relative',
    isolation: 'isolate',
    backgroundColor: colors.white,
    overflow: 'hidden',
    width: '100%'
  },
  emblaContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginInline: 'auto',
    width: '100%',
    columnGap: spacing.md
  },
  emblaContainerCentered: {
    justifyContent: 'center'
  },
  emblaSlide: {
    position: 'relative',
    flexShrink: 0,
    minWidth: 'auto',
  },
  linkWrapper: {
    position: 'relative',
    listStyle: 'none',
    backgroundColor: colors.white,
    alignItems: 'center',
    gap: spacing.xxs
  },
  isAboutNav: {
    justifyContent: 'flex-start'
  },
  mobileQuickNavigation: {
    paddingInline: spacing.xs,
  }
})

type NavigationLinksProps = {
  items: NavigationItem[]
  onMouseEnter: (slug: string) => void
  openDropdown?: string | null
  about?: boolean
  mobileQuickNavigation?: boolean
}

const renderPointer = (openDropdown: string | null | undefined) => <DropdownPointer size={16} color={colors.black} openDropdown={openDropdown} />

const NavigationLinks = ({
  items,
  about,
  openDropdown,
  onMouseEnter,
  mobileQuickNavigation
}: NavigationLinksProps) => {
  const sliderOptions = {
    align: 'start',
    containScroll: 'trimSnaps',
    dragFree: false,
    loop: true
  }
  const [emblaRef, emblaApi] = useEmblaCarousel(sliderOptions as EmblaOptionsType)
  const quickLinksRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    const onResize = () => {
      if (!quickLinksRef.current) return
      let totalWidth = 0
      const quickLinks = quickLinksRef.current.childNodes
      quickLinks.forEach((link) => {
        totalWidth += (link as HTMLElement).clientWidth
      })

      if (totalWidth < window.innerWidth) {
        emblaApi?.reInit({
          ...sliderOptions,
          active: false
        } as EmblaOptionsType)
        quickLinksRef.current.style.justifyContent = 'center'
      } else {
        emblaApi?.reInit({
          ...sliderOptions,
          active: true,
        } as EmblaOptionsType)
        quickLinksRef.current.style.justifyContent = 'unset'
      }
    }
    onResize()
    window.addEventListener('resize', onResize)
    return () => window.removeEventListener('resize', onResize)
  }, [emblaApi, quickLinksRef])

  return (
    <menu {...stylex.props(styles.menu)}>
      <div className="embla" style={{ display: 'flex' }} ref={mobileQuickNavigation ? emblaRef : null}>
        <div
          ref={mobileQuickNavigation ? quickLinksRef : null}
          {...stylex.props(
            styles.emblaContainer,
            (!about && !mobileQuickNavigation) && styles.emblaContainerCentered,
            about && styles.isAboutNav
          )}
        >
          {items.map((item, index) => (
            <div key={item.id} {...stylex.props(styles.emblaSlide)}>
              <ul
                {...stylex.props(
                  styles.linkWrapper,
                  mobileQuickNavigation && styles.mobileQuickNavigation
                )}
                onMouseEnter={() => onMouseEnter(item.slug)}
                onFocus={() => onMouseEnter(item.slug)}
              >
                {item.animationType ? (
                  <CustomNavigationLink item={item} desktop />
                ) : (
                  <NavigationLink
                    slug={item.slug}
                    callouts={item.callouts}
                    linkTarget={item.linkTarget}
                    mobileQuickNavigation={mobileQuickNavigation}
                    theme={item.theme}
                    openDropdown={openDropdown}
                  >
                    {item.name}
                  </NavigationLink>
                )}
                {about && index === 0 && renderPointer(openDropdown)}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </menu>
  )
}

export default NavigationLinks
