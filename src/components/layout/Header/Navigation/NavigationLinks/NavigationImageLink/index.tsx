import { spacing, colors, fontSizes } from '@/app/themeTokens.stylex'
import Callouts from '@components/Generic/Callout/Callouts'
import { CalloutsProps } from '@components/Generic/Callout/types'
import Container from '@components/layout/Container'
import { navigationClicked } from '@/redux/features/events/eventsSlice'
import { useAppDispatch } from '@redux/hooks'

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

type NavigationImageLinkProps = {
  slug: string
  imageSrc: string
  imageAlt: string
  callouts?: CalloutsProps
  styleProp?: stylex.StyleXStyles
  liStyleProp?: stylex.StyleXStyles
  imageWidth?: number
  imageHeight?: number
  linkTarget?: string,
  containImage?: boolean
  imageStyleProp?: stylex.StyleXStyles
  onClick?: () => void
  title?: string
  category?: string
}

const styles = stylex.create({
  list: {
    listStyle: 'none',
    display: 'inline-flex',
    alignItems: {
      default: 'flex-start',
      '@media (min-width: 992px)': 'center'
    },
    columnGap: '.5rem',
    flexDirection: 'column',
  },
  link: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    textDecoration: 'none',
    color: colors.navy,
    padding: spacing.sm,
    fontSize: fontSizes.sm,
    cursor: 'pointer',
  },
  image: {
    gridArea: '1/1',
    height: 'auto',
  },
  contain: {
    minWidth: '0',
  },
  calloutsWrapper: {
    gridArea: '1/1',
    paddingBlockStart: '11px',
    paddingInlineStart: '10px',
    alignItems: 'flex-start',
    maxHeight: 'fit-content'
  },
})

const renderImage = (imageSrc: string, imageAlt: string, imageWidth: number, imageHeight: number, imageStyleProp: stylex.StyleXStyles) => (
  <Image
    src={imageSrc}
    alt={imageAlt || ''}
    width={imageWidth}
    height={imageHeight}
    {...stylex.props(styles.image, imageStyleProp)}
    unoptimized
  />

)

const NavigationImageLink = ({
  slug,
  imageSrc,
  imageAlt,
  callouts,
  styleProp,
  liStyleProp,
  imageWidth = 0,
  imageHeight = 0,
  linkTarget,
  containImage,
  imageStyleProp,
  title,
  category
}: NavigationImageLinkProps) => {
  const isExternalURL = linkTarget === slug
  const filteredCallouts = callouts?.callouts.filter((callout) => callout.title !== 'Dot Indicator') || []
  const dispatch = useAppDispatch()
  const src = imageSrc || '/assets/kitchen-gadgets-hero.jpg'
  return (
    <li {...stylex.props(styles.list, liStyleProp, containImage && styles.contain)}>
      <Link
        href={isExternalURL ? linkTarget : slug}
        target={isExternalURL ? '_blank' : undefined}
        {...stylex.props(styles.link, styleProp)}
        onClick={() => dispatch(navigationClicked({
          category: category || '',
          label: title || '',
          navigationPlacement: 'Desktop',
          navigationTitle: title || '',
          navigationUrl: slug
        }))}
      >
        <Container as="div" grid>
          {renderImage(src, imageAlt, imageWidth, imageHeight, imageStyleProp)}
          <Container as="div" grid gap="1" styleProp={styles.calloutsWrapper}>
            {filteredCallouts.length > 0 && <Callouts callouts={filteredCallouts} />}
          </Container>
        </Container>
      </Link>
    </li>
  )
}

export default NavigationImageLink
