import NavigationLink from '@nav/MobileNavigation/NavigationLink'
import CustomNavigationLink from '@nav/CustomNavigationLink'
import { NavigationItem as NavigationItemType } from '@nav/types'
import DropdownDivider from '@nav/NavigationDropdown/DropdownDivider'
import MobileFeatureImageLink from '@nav/MobileNavigation/MobileFeatureImageLink'
import Typography from '@/components/Typography'
import { colors, spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  linkContainer: {
    display: 'flex',
    flexDirection: 'column',
  },
  captionLink: {
    paddingBlock: spacing.xxs,
  },
  navigationLink: {
    padding: 0,
    paddingBlock: spacing.xxs,
  },
  divider: {
    backgroundColor: colors.gray300,
    marginBlock: spacing.sm,
    marginBlockEnd: spacing.md,
  },
})

type MobileNavigationLinksProps = {
  items: NavigationItemType[];
  onLinkTap: (slug: string) => void;
};

const NavigationLinkItem = ({
  item,
  onLinkTap,
  category
}: { item: NavigationItemType; onLinkTap: (slug: string) => void; category: string }) => (
  <NavigationLink
    slug={item?.slug || '/'}
    callouts={item.callouts}
    linkTarget={item.linkTarget}
    onClick={() => item.slug && onLinkTap(item.slug)}
    styleProp={styles.navigationLink}
    hasCollection={item.linkCollection && item.linkCollection.length > 0}
    theme={item?.theme?.theme}
    category={category}
  >
    {item.name}
  </NavigationLink>
)

const NavigationItem = ({ item, onLinkTap }: { item: NavigationItemType; onLinkTap: (slug: string) => void }) => {
  const customLinks = ['Birthday Collection']
  const category = item.name

  if (item.slug === '' && (!item.linkCollection || item.linkCollection.length === 0)) {
    return <li {...stylex.props(styles.captionLink)}><Typography as="p" typographyTheme="captionLarge">{item.name}</Typography></li>
  }

  const firstLinkType = item.linkCollection?.[0]?.type?.toLowerCase()

  if (customLinks.includes(item.name)) {
    return <CustomNavigationLink item={item} onLinkTap={onLinkTap} />
  }

  switch (firstLinkType) {
    case 'divider':
      return <li><DropdownDivider direction="horizontal" styleProp={styles.divider} /></li>
    case 'featured image link':
      return <MobileFeatureImageLink item={item} onLinkTap={onLinkTap} category={category} />
    default:
      return <NavigationLinkItem item={item} onLinkTap={onLinkTap} category={category} />
  }
}

const MobileNavigationLinks = ({ items, onLinkTap }: MobileNavigationLinksProps) => (
  <menu>
    <ul {...stylex.props(styles.linkContainer)}>
      {items.map((item, index) => (
        // eslint-disable-next-line react/no-array-index-key
        <NavigationItem key={`${item.id}-${index}`} item={item} onLinkTap={onLinkTap} />
      ))}
    </ul>
  </menu>
)

export default MobileNavigationLinks
