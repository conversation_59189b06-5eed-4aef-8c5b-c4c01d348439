'use client'

import { colors, spacing } from '@/app/themeTokens.stylex'
import { NavigationItem as NavigationItemType } from '@nav/types'
import Typography from '@/components/Typography'
import Container from '@components/layout/Container'
import NavigationLink from '@nav/NavigationLinks/NavigationLink'
import NavigationImageLink from '@nav/NavigationLinks/NavigationImageLink'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

type ImageLinkProps = {
  link: any
  onLinkTap: (slug: string) => void
  category: string
}
const MOBILE_GRID_MEDIA_QUERY = '@media (min-width: 376px) and (max-width: 520px)' as const

const styles = stylex.create({
  wrapper: {
    overflow: 'hidden',
    overflowX: 'auto',
    marginBlockEnd: spacing.sm
  },
  container: {
    padding: spacing.md,
  },
  divider: {
    backgroundColor: colors.gray,
    height: '1px',
    marginBlock: spacing.md
  },
  link: {
    padding: '0px',
  },
  imageLink: {
    padding: '0px',
  },
  nameLink: {
    textDecoration: 'none',
    padding: '0px',
    maxWidth: '17ch',
    textWrap: 'pretty'
  },
  maxImageWidth: {
    maxWidth: '100%',
  },
  imageGrid: {
    gridTemplateColumns: 'repeat(auto-fill, minmax(min(160px, 100%), 1fr));',
    display: {
      [MOBILE_GRID_MEDIA_QUERY]: 'grid',
      default: 'flex'
    },
    rowGap: {
      [MOBILE_GRID_MEDIA_QUERY]: spacing.md,
      default: spacing.md
    },
    paddingBlock: spacing.xxs,
  },
  imageGridContainer: {
    flex: '1 0 200px'
  }
})

const DropdownImageLink = ({
  link,
  onLinkTap,
  category
}: ImageLinkProps) => (
  <React.Fragment key={link.id}>
    <Container
      as="ul"
      flex
      gap="1"
      styleProp={styles.imageGridContainer}
      onClick={() => onLinkTap(link.slug)}
    >
      <NavigationImageLink
        slug={link.slug}
        imageSrc={link.image}
        imageAlt={link.name}
        callouts={link.callouts}
        styleProp={styles.imageLink}
        imageWidth={500}
        imageHeight={300}
        imageStyleProp={styles.maxImageWidth}
        category={category}
        title={link.name}
      />
      <NavigationLink
        slug={link.slug}
        styleProp={[styles.link]}
        category={category}
      >
        <Typography as="p" underline typographyTheme="bodySmall">{link.name}</Typography>
      </NavigationLink>
    </Container>
  </React.Fragment>
)

const MobileFeatureImageLink = ({
  item,
  onLinkTap,
  category
}: { item: NavigationItemType; onLinkTap: (slug: string) => void; category: string }) => (
  <Container as="li" flex gap="2" styleProp={styles.wrapper}>
    <Typography as="p" typographyTheme="captionLarge">{item.name}</Typography>
    <Container as="div" grid gap="2" styleProp={styles.imageGrid}>
      {item?.linkCollection?.[0]?.links.map((link) => (
        <DropdownImageLink
          key={link.id}
          link={link}
          onLinkTap={onLinkTap}
          category={category}
        />
      ))}
    </Container>
  </Container>
)

export default MobileFeatureImageLink
