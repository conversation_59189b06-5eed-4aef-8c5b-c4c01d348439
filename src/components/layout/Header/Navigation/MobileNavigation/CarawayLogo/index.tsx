import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

type LogoProps = {
  styleProp?: stylex.StyleXStyles
}

const styles = stylex.create({
  base: {
    aspectRatio: '16 / 9',
    width: '135px',
    fill: colors.navy
  },
})

const CarawayLogo = ({ styleProp }: LogoProps) => (
  <svg
    {...stylex.props(styles.base, styleProp)}
    viewBox="0 0 115 30"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d={`M100.836 22.037c-.741 0-1.313 0-1.664-.548-.791-2.164-2.406-6.574-3.088-8.374l-.54-1.487c-.94-2.58-3.1-4.06-5.98-4.06-1.71 0-3.493.739-4.785
         1.68l1.142.912c.738-.594 1.813-1.193 2.813-1.193 2.335 0 3.587 1.089 4.405 3.34l.575 1.581c-.768-.479-2.129-1.094-3.942-1.094-2.81 0-5.657 
         1.843-5.657 5.365 0 3.488 2.36 5.33 6.823 5.33 2.928 0 4.659-1.126 5.552-1.976l.535 1.47h4.351l-.361-.943h-.179v-.003Zm-10.144.575c-2.522 
         0-4.052-1.789-4.052-4.348 0-1.653.268-4.43 3.608-4.43a6.4 6.4 0 0 1 3.931 1.33l1.805 4.96c-.652 1.694-3.062 2.488-5.292 2.488ZM65.362 
         22.037c-.74 0-1.313 0-1.663-.548-.792-2.164-2.407-6.574-3.089-8.374l-.54-1.487c-.939-2.58-3.1-4.06-5.98-4.06-1.71 0-3.263.739-4.555 
         1.68l1.142.912c.738-.594 1.813-1.193 2.814-1.193 2.334 0 3.356 1.089 4.175 3.34l.575 1.581c-.768-.479-2.13-1.094-3.943-1.094-2.81 
         0-5.423 1.843-5.423 5.365 0 3.488 2.129 5.33 6.592 5.33 2.929 0 4.659-1.126 5.552-1.976l.535 1.47h4.351l-.36-.943h-.18l-.002-.003Zm-10.144.575c-2.522
          0-3.821-1.789-3.821-4.348 0-1.653.037-4.43 3.378-4.43a6.4 6.4 0 0 1 3.93 1.33l1.806 4.96c-.653 1.694-3.062 2.488-5.293 
          2.488ZM38.049 22.037c-.741 0-1.313 0-1.664-.548-.791-2.164-2.407-6.574-3.089-8.374l-.54-1.487c-.939-2.58-3.1-4.06-5.98-4.06-1.709 
          0-3.493.739-4.784 1.68l1.142.912c.738-.594 1.813-1.193 2.813-1.193 2.335 0 3.587 1.089 
          4.405 3.34l.575 1.581c-.768-.479-2.129-1.094-3.942-1.094-2.811 
          0-5.654 1.843-5.654 5.365 0 3.488 2.359 5.33 6.823 5.33 2.928 0 4.658-1.126 5.552-1.976l.534 1.47h4.352l-.361-.943h-.18l-.002-.003Zm-10.144.575c-2.522 
          0-4.052-1.789-4.052-4.348 0-1.653.267-4.43 3.608-4.43a6.4 6.4 0 0 1 3.931 1.33l1.805 4.96c-.652 1.694-3.062 2.488-5.292 2.488ZM77.881 21.331 73.434 
          8.03h-2.947l2.8 7.905-2.482 5.95L66.173 8.03h-2.947l5.25 14.821.045.126h2.71l2.486-5.828 2.065 5.828h2.71l5.163-14.947h-1.067L77.881 21.33ZM114.994 
          17.397V8.023h-2.476l.23 8.925v1.28c0 1.924-1.651 3.756-3.552 4.052-.313.049-1.401.04-1.792 0-1.912-.198-3.552-2.128-3.552-4.051v-1.281l.23-8.925h-2.476v11.385c0 1.728 
          1.409 4.616 6.028 4.616h1.332c1.316 0 2.832-.733 3.728-1.47-.123 1.163-.241 2.345-.596 3.134-.495 1.102-1.621 2.415-4.215 2.415-2.525 
          0-5.167-2.172-5.419-2.552l-.061-.093-.794.072.117.243c.019.035 
          2.022 3.563 6.296 3.793 3.177.17 5.097-1.11 6.162-3.806.064-.166.125-.38.182-.623.403-1.586.556-3.731.607-5.36a3.69 3.69 0 0 0 
          .021-.372v-.502c.013-.904 0-1.503 0-1.503v-.003ZM42.237 
          10.593V7.964h-2.685v15.012h2.918V11.789c.526-.885 1.81-1.867 3.361-1.867.87 0 1.247.198 1.854.524V7.94c-.455-.163-.415-.262-1.324-.262-1.361 0-3.48 1.998-4.121 
          2.915h-.003ZM11.128 20.777c-5.276 
          0-8.726-5.557-8.726-10.547 0-2.87.655-5.232 2.332-6.836C6.21 1.984 8.256 1.206 10.5 1.206c2.182 0 3.862 1.37 4.883 2.517a11.838 
          11.838 0 0 1 1.733 2.548l.091.193 2.514-1.594c-.147-.318-1.067-1.348-1.35-1.647C16.744 1.524 14.835 0 10.56 0 6.51 
          0 4.022 1.998 2.651 3.675.964 5.734 0 8.473 0 11.187c0 1.712.297 4.996 2.281 7.772 1.848 2.586 4.702 3.953 8.25 3.953 
          5.184 0 7.968-3.552 9.27-5.991l-1.142-.484c-2.067 2.915-4.546 4.34-7.528 4.34h-.003Z`}
    />
  </svg>
)

export default CarawayLogo
