'use client'

import { NavigationItem } from '../../types'

import MobileNavigationPanel from '@nav/MobileNavigation/NavigationPanel'
import MobileNavigationLinks from '@nav/MobileNavigation/NavigationLinks'
import Container from '@components/layout/Container'
import { colors, spacing } from '@/app/themeTokens.stylex'
import SubscriptionForm from '@/components/Generic/SubscriptionForm'
import { Widget } from '@/components/layout/Footer/types'
import { ThemeColors } from '@/app/themeThemes.stylex'

import React, { useState } from 'react'
import * as stylex from '@stylexjs/stylex'

type MobileNavigationBlockProps = {
  isOpen: boolean
  items: {
    items: NavigationItem[]
    widgets: Widget
  }
  onClose: () => void
  isSticky: boolean
}
const DESKTOP_MEDIA_QUERY = '@media (min-width: 1024px)'

const styles = stylex.create({
  block: {
    display: {
      default: 'block',
      [DESKTOP_MEDIA_QUERY]: 'none',
    },
    position: 'fixed',
    width: '100%',
    height: '100dvh',
    backgroundColor: colors.white,
    transition: 'transform 480ms cubic-bezier(.35,.94,.68,.98)',
    zIndex: 1,
    overflow: 'hidden',
    transform: 'translateY(-100%)'
  },
  visible: {
    opacity: 1,
    transform: 'translateY(3rem)'
  },
  content: {
    position: 'relative',
    height: 'calc(100dvh - 81px)',
    overflow: 'hidden',
    overflowY: 'auto',
    paddingInline: '1rem',
    paddingBlock: spacing.xxl,
    paddingBlockStart: '28px',
  },
  stickyContent: {
    height: 'calc(100dvh - 46px)',
  },
  subscriptionForm: {
    paddingBlock: 'unset',
    paddingInline: 'unset',
    maxWidth: {
      default: '500px',
      [DESKTOP_MEDIA_QUERY]: '1440px',
    },
  }
})

const MobileNavigationBlock = ({
  isOpen,
  items: { items, widgets },
  onClose,
  isSticky
}: MobileNavigationBlockProps) => {
  const [activeSlug, setActiveSlug] = useState<string | null>(null)
  const [isPanelOpen, setIsPanelOpen] = useState(false)
  const handleClose = () => {
    setIsPanelOpen(false)
    onClose()
  }
  const handleLinkTap = (slug: string) => {
    const activePanel = items.find((navItem) => navItem.slug === slug)
    if (activePanel && activePanel.linkCollection && activePanel.linkCollection.length > 0) {
      setActiveSlug(slug)
      setIsPanelOpen(true)
    } else {
      handleClose()
    }
  }

  const handlePanelClose = () => {
    setIsPanelOpen(false)
  }

  return (
    <Container
      styleProp={[styles.block, isOpen && styles.visible]}
    >
      <Container styleProp={[
        styles.content,
        isSticky && styles.stickyContent
      ]}
      >
        <MobileNavigationLinks
          items={items}
          onLinkTap={handleLinkTap}
        />
        <MobileNavigationPanel
          isOpen={isPanelOpen}
          onClose={handlePanelClose}
          activeSlug={activeSlug}
          items={items}
          onItemClick={handleClose}
        />

        {widgets?.subtype === 'Subscription Form' && (
          <SubscriptionForm
            styleProp={styles.subscriptionForm}
            theme={(widgets?.settings?.theme || 'navy') as ThemeColors}
            formId={widgets.id}
            headerTypography="h6Secondary"
            bodyTypography="bodySmall"
          />
        )}
      </Container>
    </Container>
  )
}

export default MobileNavigationBlock
