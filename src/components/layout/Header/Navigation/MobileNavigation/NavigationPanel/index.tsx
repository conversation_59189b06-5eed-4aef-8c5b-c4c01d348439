import { NavigationItem, LinkCollection } from '@nav/types'
import { CalloutsProps } from '@components/Generic/Callout/types'
import { colors, spacing } from '@/app/themeTokens.stylex'
import NavigationLink from '@nav/MobileNavigation/NavigationLink'
import NavigationImageLink from '@nav/NavigationLinks/NavigationImageLink'
import Container from '@components/layout/Container'
import Typography from '@/components/Typography'
import DropdownDivider from '@nav/NavigationDropdown/DropdownDivider'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

type MobileNavigationPanelProps = {
  isOpen: boolean
  onClose: () => void
  activeSlug: string | null
  items: NavigationItem[]
  onItemClick: () => void
}

type LinkProps = {
  id: string
  slug: string
  linkTitle: string
  image?: string
  callouts?: CalloutsProps
  linkTarget?: string
}

type ImageLinkProps = {
  id: string;
  slug: string;
  name: string;
  linkTitle?: string;
  image: string;
  callouts?: CalloutsProps;
  linkTarget?: string;
};
const MOBILE_GRID_MEDIA_QUERY = '@media (min-width: 376px) and (max-width: 520px)' as const
const styles = stylex.create({
  panel: {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: 'calc(100dvh - 81px)',
    backgroundColor: colors.white,
    padding: spacing.md,
    overflowY: 'auto',
    transition: 'transform 0.3s ease-in-out',
    transform: 'translateX(100%)',
    pointerEvents: 'none',
    paddingBlockEnd: spacing.xxl,
  },
  panelOpen: {
    transform: 'translateX(0)',
    pointerEvents: 'auto',

  },
  closeButton: {
    color: colors.gray500,
    marginBlockStart: spacing.xxs,
  },
  textLink: {
    color: colors.navy,
    textDecoration: 'none',
    fontSize: '14px',
    lineHeight: '20px',
  },
  imageLinkWrapper: {
    gridTemplateColumns: 'repeat(auto-fill, minmax(min(160px, 100%), 1fr));',
    display: {
      default: 'flex',
      [MOBILE_GRID_MEDIA_QUERY]: 'grid',
    },
    gap: spacing.md,
    overflow: 'hidden',
    overflowX: 'auto',
  },
  imageLink: {
    textDecoration: 'none',
    color: colors.navy,
  },
  imageLinksContainer: {
    gridTemplateColumns: 'repeat(auto-fill, minmax(min(160px, 100%), 1fr));',
    display: {
      default: 'flex',
      [MOBILE_GRID_MEDIA_QUERY]: 'grid',
      ':has(> ul:only-child)': 'flex'
    },
    overflow: {
      default: 'hidden',
      [MOBILE_GRID_MEDIA_QUERY]: 'visible',
    },
    overflowX: {
      default: 'auto',
      [MOBILE_GRID_MEDIA_QUERY]: 'initial',
    },
    flexShrink: '0',
  },
  link: {
    padding: '0px'
  },
  images: {
    flex: '1 0 200px',
    flexShrink: '0',
  },
  maxImageWidth: {
    maxWidth: '100%',
  },
  divider: {
    backgroundColor: colors.gray300,
    marginBlock: spacing.sm,
  }
})

const ImageLink = ({
  link,
  onItemClick,
  index,
  category,
  title
}: { link: ImageLinkProps; onItemClick: () => void; index: number; category: string, title: string }) => (
  // eslint-disable-next-line react/no-array-index-key
  <React.Fragment key={`${link.id}-${index}`}>
    <Container
      as="ul"
      flex
      gap="2"
      styleProp={styles.images}
      onClick={onItemClick}
    >
      <NavigationImageLink
        slug={link.slug}
        imageSrc={link.image}
        imageAlt={link.name}
        callouts={link.callouts}
        styleProp={[styles.imageLink, styles.link]}
        imageWidth={500}
        imageHeight={300}
        imageStyleProp={styles.maxImageWidth}
        category={category}
        title={title}
      />
      <NavigationLink
        slug={link.slug}
        styleProp={[styles.link]}
        category={category}
        title={title}
      >
        <Typography as="p" underline typographyTheme="bodySmall">{link.linkTitle || link.name}</Typography>
      </NavigationLink>
    </Container>
  </React.Fragment>
)

const TextLinks = ({
  links,
  onItemClick,
  category,
  title
}: { links: LinkProps[]; onItemClick: () => void; category: string, title: string }) => (
  <Container flex gap="2">
    {links.map((link, index) => (
      <NavigationLink
        // eslint-disable-next-line react/no-array-index-key
        key={`${link.id}-${index}`}
        slug={link.slug}
        typographyTheme={index === 0 ? 'captionLarge' : 'h6Primary'}
        styleProp={styles.link}
        onClick={onItemClick}
        callouts={link.callouts}
        category={category}
        title={title}
      >
        {link.linkTitle}
      </NavigationLink>
    ))}
  </Container>
)

const ImageLinks = ({
  links,
  onItemClick,
  category,
  title
}: { links: ImageLinkProps[]; onItemClick: () => void; category: string, title: string }) => (
  <Container grid gap="2" styleProp={styles.imageLinksContainer}>
    {links.map((link, index) => (
      // eslint-disable-next-line react/no-array-index-key
      <ImageLink key={`${link.id}-${index}`} link={link} onItemClick={onItemClick} index={index} category={category} title={title} />
    ))}
  </Container>
)

const LinkCollectionRenderer = ({
  collections,
  onItemClick,
  category,
  title
}: { collections: LinkCollection[]; onItemClick: () => void; category: string, title: string }) => (
  <>
    {collections.map((collection, index) => {
      const key = `${collection.id}-${index}`
      switch (collection.type.toLowerCase()) {
        case 'image link':
          return <ImageLinks key={key} links={collection.links as ImageLinkProps[]} onItemClick={onItemClick} category={category} title={title} />
        case 'text links':
          return <TextLinks key={key} links={collection.links as LinkProps[]} onItemClick={onItemClick} category={category} title={title} />
        case 'divider':
          return <DropdownDivider key={key} direction="horizontal" styleProp={styles.divider} />
        default:
          return null
      }
    })}
  </>
)

const MobileNavigationPanel = ({
  isOpen,
  onClose,
  activeSlug,
  items,
  onItemClick
}: MobileNavigationPanelProps) => {
  const activeItem = items.find((item) => item.slug === activeSlug) || items[0]
  const category = activeItem.name
  return (
    <Container flex gap="2" noWrap styleProp={[styles.panel, isOpen && styles.panelOpen]}>
      <button type="button" onClick={onClose} {...stylex.props(styles.closeButton)}>
        <Typography as="p" typographyTheme="bodyLarge" fontBold styleProp={styles.closeButton}>← Back</Typography>
      </button>
      {activeItem && activeItem.linkCollection && (
        <LinkCollectionRenderer collections={activeItem.linkCollection} onItemClick={onItemClick} category={category} title={activeItem.name} />
      )}
    </Container>
  )
}

export default MobileNavigationPanel
