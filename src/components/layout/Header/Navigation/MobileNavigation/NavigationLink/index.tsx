import Typography from '@components/Typography'
import Callouts from '@components/Generic/Callout/Callouts'
import ChevronIcon from '@nav/MobileNavigation/ChevronIcon'
import { CalloutsProps } from '@components/Generic/Callout/types'
import {
  spacing,
  fontSizes,
  colors
} from '@/app/themeTokens.stylex'
import { TypographyThemes } from '@/app/typographyThemes.stylex'
import { navigationClicked } from '@/redux/features/events/eventsSlice'
import { useAppDispatch } from '@redux/hooks'

import * as stylex from '@stylexjs/stylex'
import Link from 'next/link'

type NavigationLinkProps = {
  slug: string;
  callouts?: CalloutsProps;
  children: React.ReactNode;
  styleProp?: stylex.StyleXStyles;
  columnLink?: boolean;
  linkTarget?: string;
  onClick?: () => void;
  hasCollection?: boolean | null;
  typographyTheme?: TypographyThemes;
  onItemClick?: () => void;
  theme?: string;
  category?: string;
  title?: string;
};

const styles = stylex.create({
  list: {
    listStyle: 'none',
    display: 'inline-flex',
    alignItems: 'center',
    columnGap: '.5rem',
  },
  link: {
    display: 'inline-flex',
    alignItems: 'flex-start',
    width: '100%',
    color: colors.navy,
    paddingBlock: spacing.sm,
    fontSize: fontSizes.sm,
    cursor: 'pointer',
    textDecoration: 'none',
    gap: spacing.xxs
  },
  columnLink: {
    paddingBlockEnd: spacing.sm,
  },
  calloutWrapper: {
    marginTop: '-10px',
  },
  columnLinkSvg: {
    marginLeft: '-18px',
    marginTop: '-10px',
  },
  typographyStyle: {
    display: 'inline-flex',
    justifyContent: 'space-between',
  },
  chevronIcon: {
    marginLeft: 'auto',
  },
  theme: {
    fontStyle: 'italic'
  }
})

const renderCallouts = (callouts: CalloutsProps | undefined, columnLink: boolean | undefined) => {
  if (!columnLink && callouts) {
    const filteredCallouts = callouts.callouts.filter((callout) => callout.title !== 'Dot Indicator')
    return <Callouts callouts={filteredCallouts} size="small" />
  }
  return null
}

const NavigationLink = ({
  slug,
  children,
  callouts,
  styleProp,
  columnLink,
  onClick,
  hasCollection,
  typographyTheme = 'h6Primary',
  theme,
  category,
  title
}: NavigationLinkProps) => {
  const dispatch = useAppDispatch()

  const handleClick = () => {
    if (onClick) onClick()
    dispatch(navigationClicked({
      category: category || children as string,
      label: title || children as string,
      navigationPlacement: 'Mobile',
      navigationTitle: title || children as string,
      navigationUrl: slug,
    }))
  }

  return (
    <li {...stylex.props(styles.list, styleProp)}>
      <Link
        href={slug}
        {...stylex.props(styles.link, styleProp, columnLink && styles.columnLink)}
        style={{ color: colors[theme as keyof typeof colors] as string }}
        onClick={handleClick}
      >
        <Typography
          as="p"
          typographyTheme={!theme ? typographyTheme : 'h6Secondary'}
          styleProp={theme && styles.theme}
        >
          {children}
        </Typography>
        {renderCallouts(callouts, columnLink)}
        {hasCollection && <ChevronIcon styleProp={styles.chevronIcon} />}
      </Link>
    </li>
  )
}

export default NavigationLink
