'use client'

import Container from '@components/layout/Container'
import MeilisearchInputMobile from '@/components/Meilisearch/MeilisearchInputMobile'
import { colors, spacing } from '@/app/themeTokens.stylex'
import Typography from '@/components/Typography'
import ProductCard from '@/components/Product/ProductCard'
import { BestSellerProductType, ExtractedSearchMenuDataType } from '@/lib/contentful/fetchSearchMenu'
import { ProductCardData } from '@/components/Product/ProductCard/utils'

import React from 'react'
import * as stylex from '@stylexjs/stylex'
import Link from 'next/link'

type MobileSearchMenuProps = {
  searchMenuData: ExtractedSearchMenuDataType
  isOpen: boolean
  onClose: () => void
  isSticky: boolean
}
const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  block: {
    display: {
      default: 'block',
      [DESKTOP]: 'none',
    },
    position: 'fixed',
    width: '100%',
    height: '100dvh',
    backgroundColor: colors.white,
    transition: 'transform 480ms cubic-bezier(.35,.94,.68,.98)',
    zIndex: 1,
    overflow: 'hidden',
    transform: 'translateY(-100%)'
  },
  visible: {
    opacity: 1,
    transform: 'translateY(3rem)'
  },
  content: {
    position: 'relative',
    height: 'calc(100dvh - 81px)',
    overflow: 'hidden',
    overflowY: 'auto',
    paddingInline: '1rem',
    paddingBlock: spacing.xxl,
    paddingBlockStart: '28px',
  },
  stickyContent: {
    height: 'calc(100dvh - 46px)',
  },
  suggestionsTitle: {
    color: {
      default: colors.navy,
      ':hover': colors.gray,
    },
    textDecoration: {
      default: 'none',
      ':hover': 'underline',
    }
  },
  hr: {
    marginTop: '2rem',
    marginBottom: '2rem',
  },
  productGrid: {
    display: 'grid',
    gridTemplateColumns: {
      default: 'auto auto',
    },
    rowGap: {
      default: spacing.lg,
    },
    marginTop: '1rem',
  },
})

const MobileSearchMenu = ({
  searchMenuData,
  isOpen,
  onClose,
  isSticky
}: MobileSearchMenuProps) => {
  const { bestSellerProducts, suggestions } = searchMenuData

  return (
    <Container
      styleProp={[styles.block, isOpen && styles.visible]}
    >
      <Container styleProp={[
        styles.content,
        isSticky && styles.stickyContent
      ]}
      >
        <MeilisearchInputMobile handleMobileNavClose={() => onClose()} />

        <Container flex gap="3">
          <Typography as="h6" fontBold>Suggestions</Typography>
          {suggestions.map((suggestion: ExtractedSearchMenuDataType['suggestions'][number]) => (
            <Link href={`/${suggestion.slug}`} onClick={() => onClose()} {...stylex.props(styles.suggestionsTitle)} key={suggestion.slug}>{suggestion.title}</Link>
          ))}
        </Container>

        <hr {...stylex.props(styles.hr)} />

        <Container>
          <Typography as="h6" fontBold>Best Sellers</Typography>
          <Container as="div" gap="3" styleProp={styles.productGrid}>
            {bestSellerProducts.map((bestSeller: BestSellerProductType) => {
              if (!bestSeller) {
                return null
              }

              return (
                <ProductCard product={bestSeller as unknown as ProductCardData} onClick={() => onClose()} key={bestSeller.title} />
              )
            })}
          </Container>
        </Container>

      </Container>
    </Container>
  )
}

export default MobileSearchMenu
