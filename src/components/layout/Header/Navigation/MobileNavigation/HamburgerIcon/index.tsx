import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

type HamburgerIconProps = {
  isOpen: boolean
  toggleMenu: () => void
  color?: string
}

const moveAndRotateTop = stylex.keyframes({
  '0%': { transform: 'translateY(0) rotate(0)' },
  '50%': { transform: 'translateY(5px) rotate(0)' },
  '100%': { transform: 'translateY(5px) rotate(45deg)' }
})

const moveAndRotateBottom = stylex.keyframes({
  '0%': { transform: 'translateY(0) rotate(0)' },
  '50%': { transform: 'translateY(-5px) rotate(0)' },
  '100%': { transform: 'translateY(-5px) rotate(-45deg)' }
})

// Technically dont need to fade in/out center bar we can just rotate it
const fadeOut = stylex.keyframes({
  '0%': { opacity: 1 },
  '100%': { opacity: 0 }
})

const fadeIn = stylex.keyframes({
  '0%': { opacity: 0 },
  '100%': { opacity: 1 }
})

const resetTop = stylex.keyframes({
  '0%': { transform: 'translateY(5px) rotate(45deg)' },
  '50%': { transform: 'translateY(5px) rotate(0)' },
  '100%': { transform: 'translateY(0) rotate(0)' }
})

const resetBottom = stylex.keyframes({
  '0%': { transform: 'translateY(-5px) rotate(-45deg)' },
  '50%': { transform: 'translateY(-5px) rotate(0)' },
  '100%': { transform: 'translateY(0) rotate(0)' }
})

const styles = stylex.create({
  button: {
    background: 'none',
    cursor: 'pointer',
    padding: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '24px',
    height: '24px',
    position: 'relative',
    zIndex: 3,
  },
  svg: {
    width: '100%',
    height: '100%',
  },
  line: {
    transition: 'opacity 0.2s',
    transformOrigin: 'center',
  },
  topLine: {
    transformOrigin: '12px 7px',
  },
  middleLine: {
    transformOrigin: '12px 12px',
  },
  bottomLine: {
    transformOrigin: '12px 17px',
  },
  openTop: {
    animationName: moveAndRotateTop,
    animationDuration: '0.4s',
    animationFillMode: 'forwards',
  },
  openMiddle: {
    animationName: fadeOut,
    animationDuration: '0.2s',
    animationFillMode: 'forwards',
  },
  openBottom: {
    animationName: moveAndRotateBottom,
    animationDuration: '0.4s',
    animationFillMode: 'forwards',
  },
  closeTop: {
    animationName: resetTop,
    animationDuration: '0.4s',
    animationFillMode: 'forwards',
  },
  closeMiddle: {
    animationName: fadeIn,
    animationDuration: '0.2s',
    animationFillMode: 'forwards',
  },
  closeBottom: {
    animationName: resetBottom,
    animationDuration: '0.4s',
    animationFillMode: 'forwards',
  },
})

const HamburgerIcon = ({
  isOpen,
  toggleMenu,
  color = colors.black
}: HamburgerIconProps) => (
  <button
    type="button"
    onClick={toggleMenu}
    aria-expanded={isOpen}
    aria-label={isOpen ? 'Close menu' : 'Open menu'}
    {...stylex.props(styles.button)}
  >
    <svg viewBox="0 0 24 24" aria-hidden="true" {...stylex.props(styles.svg)}>
      <g stroke={color} strokeWidth="2" strokeLinecap="round">
        <line
          x1="4"
          y1="7"
          x2="20"
          y2="7"
          {...stylex.props(
            styles.line,
            styles.topLine,
            isOpen ? styles.openTop : styles.closeTop
          )}
        />
        <line
          x1="4"
          y1="12"
          x2="20"
          y2="12"
          {...stylex.props(
            styles.line,
            styles.middleLine,
            isOpen ? styles.openMiddle : styles.closeMiddle
          )}
        />
        <line
          x1="4"
          y1="17"
          x2="20"
          y2="17"
          {...stylex.props(
            styles.line,
            styles.bottomLine,
            isOpen ? styles.openBottom : styles.closeBottom
          )}
        />
      </g>
    </svg>
  </button>
)

export default HamburgerIcon
