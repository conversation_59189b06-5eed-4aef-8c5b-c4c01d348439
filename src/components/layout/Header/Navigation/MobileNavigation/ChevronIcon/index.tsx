import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  base: {
    width: '100%',
    height: '100%',
  },
  up: {
    transform: 'rotate(180deg)',
  },
  down: {
    transform: 'rotate(0deg)',
  },
  left: {
    transform: 'rotate(90deg)',
  },
  right: {
    transform: 'rotate(270deg)',
  },
  width: {
    width: '24px',
  },
})

type Direction = 'up' | 'down' | 'left' | 'right'

type ChevronProps = {
  direction?: Direction
  width?: string | number | undefined | null
  styleProp?: stylex.StyleXStyles
}
// TODO: rotate on active
const ChevronIcon = ({
  direction = 'right',
  width = '24px',
  styleProp
}: ChevronProps) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...stylex.props(
      styles.base,
      styles[direction],
      width ? styles.width : null,
      styleProp
    )}
  >
    <path
      d="M6 9L12 15L18 9"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export default ChevronIcon
