import Container from '@components/layout/Container'
import NavigationLink from '@nav/NavigationLinks/NavigationLink'
import NavigationImageLink from '@nav/NavigationLinks/NavigationImageLink'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

const styles = stylex.create({
  link: {
    padding: '0px',
  },
  imageLink: {
    padding: '0px',
    marginBlockEnd: spacing.sm,
    borderRadius: spacing.xxs,
    overflow: 'hidden',
  },
  image: {
    aspectRatio: '240/226',
  },
  nameLink: {
    textDecoration: 'none',
    fontWeight: 'bold',
    padding: '0px',
  },
  descriptionLink: {
    textDecoration: 'none',
    padding: '0px',
  },
  staggerImageLinks: (delay: number) => ({
    animationName: stylex.keyframes({
      '0%': { transform: 'translate(0px, -20px)' },
      '100%': { transform: 'translate(0px, 0px)' },
    }),
    animationDuration: '800ms',
    animationFillMode: 'both',
    animationTimingFunction: 'cubic-bezier(0.25, 1, 0.3, 1)',
    animationIterationCount: 1,
    animationDelay: `${delay}ms`,
  }),
})

type ImageLinkProps = {
  link: any
  delay: number
  openDropdown: boolean
  category: string
}

const DropdownImageLink = ({
  link,
  delay,
  openDropdown,
  category
}: ImageLinkProps) => (
  <React.Fragment key={link.id}>
    <Container
      as="ul"
      flex
      gap="1"
      styleProp={[
        openDropdown ? styles.staggerImageLinks(delay) : null,
      ]}
    >
      <NavigationImageLink
        slug={link.slug}
        imageSrc={link.image}
        imageAlt={link.name}
        callouts={link.callouts}
        styleProp={styles.imageLink}
        imageWidth={240}
        imageHeight={226}
        title={link.name}
        category={category}
        imageStyleProp={styles.image}
      />
      <NavigationLink
        slug={link.slug}
        styleProp={[styles.link, styles.nameLink]}
        category={category}
      >
        {link.name}
      </NavigationLink>
      <NavigationLink
        slug={link.slug}
        styleProp={[styles.link, styles.descriptionLink]}
        category={category}
      >
        {link.description}
      </NavigationLink>
    </Container>
  </React.Fragment>
)

export default DropdownImageLink
