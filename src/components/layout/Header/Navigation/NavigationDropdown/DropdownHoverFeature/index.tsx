import Container from '@components/layout/Container'
import NavigationLink from '@nav/NavigationLinks/NavigationLink'
import NavigationImageLink from '@nav/NavigationLinks/NavigationImageLink'
import { spacing } from '@/app/themeTokens.stylex'

import React, { useEffect, useState } from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  link: {
    padding: '0px',
  },
  imageLink: {
    padding: '0px',
    marginBlockEnd: spacing.sm,
    borderRadius: spacing.xxs,
    overflow: 'hidden',
  },
  nameLink: {
    textDecoration: 'none',
    fontWeight: 'bold',
    padding: '0px',
  },
  descriptionLink: {
    textDecoration: 'none',
    padding: '0px',
  },
  staggerImageLinks: (delay: number) => ({
    animationName: stylex.keyframes({
      '0%': { transform: 'translate(0px, -20px)' },
      '100%': { transform: 'translate(0px, 0px)' },
    }),
    animationDuration: '800ms',
    animationFillMode: 'both',
    animationTimingFunction: 'cubic-bezier(0.25, 1, 0.3, 1)',
    animationIterationCount: 1,
    animationDelay: `${delay}ms`,
  }),
})

type ImageLinkProps = {
  link: any
  delay: number
  openDropdown: any
  hoveredImageSlug: string | null
  hoveredName?: string
  hoveredDescription?: string
  hoveredCallouts?: any
  category?: string
}

const DropdownHoverFeature = ({
  link,
  delay,
  openDropdown,
  hoveredImageSlug,
  hoveredName,
  hoveredDescription,
  hoveredCallouts,
  category
}: ImageLinkProps) => {
  const [currentImageSrc, setCurrentImageSrc] = useState(link.image)
  const [currentName, setCurrentName] = useState(link.name)
  const [currentDescription, setCurrentDescription] = useState(link.description)
  const [currentCallouts, setCurrentCallouts] = useState(link.callouts)

  useEffect(() => {
    if (hoveredImageSlug) {
      setCurrentImageSrc(hoveredImageSlug)
      setCurrentName(hoveredName || link.name)
      setCurrentDescription(hoveredDescription || link.description)
      setCurrentCallouts(hoveredCallouts || link.callouts)
    } else {
      setCurrentImageSrc(link.image)
      setCurrentName(link.name)
      setCurrentDescription(link.description)
      setCurrentCallouts(link.callouts)
    }
  }, [hoveredImageSlug, hoveredName, hoveredDescription, link.image, link.name, link.description])

  return (
    <React.Fragment key={link.id}>
      <Container
        as="ul"
        flex
        gap="1"
        styleProp={[
          openDropdown ? styles.staggerImageLinks(delay) : null,
        ]}
      >
        <NavigationImageLink
          slug={link.slug || '#'}
          imageSrc={currentImageSrc}
          imageAlt={currentName}
          callouts={currentCallouts}
          styleProp={styles.imageLink}
          imageWidth={363}
          imageHeight={225}
          category={category}
        />
        <NavigationLink
          slug={link.slug}
          styleProp={[styles.link, styles.nameLink]}
          category={category}
        >
          {currentName}
        </NavigationLink>
        <NavigationLink
          slug={link.slug}
          styleProp={[styles.link, styles.descriptionLink]}
          category={category}
        >
          {currentDescription}
        </NavigationLink>
      </Container>
    </React.Fragment>
  )
}

export default DropdownHoverFeature
