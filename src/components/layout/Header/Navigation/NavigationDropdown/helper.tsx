import { NavigationItem } from '../types'

import { RefObject } from 'react'

export const eventKeyEscape = (dropdownRef: RefObject<HTMLDivElement> | null) => {
  if (!dropdownRef) return
  const allElements = document.querySelectorAll<HTMLElement>('body *')
  allElements.forEach((element) => {
    element.blur()
  })
  if (dropdownRef.current) {
    dropdownRef.current.style.visibility = 'hidden'
    dropdownRef.current.style.pointerEvents = 'none'
  }
}

export const eventKeyTab = (
  dropdownRef: RefObject<HTMLDivElement> | null,
  prevFocusedElement: RefObject<HTMLDivElement> | null,
  lastItem: boolean,
  openDropdown: string | null,
  items: NavigationItem[],
  handleKeyDown: (event: KeyboardEvent) => void
) => {
  if (!dropdownRef || !prevFocusedElement) return

  const FOCUS_DELAY = 100
  const nextFocusableElement = document.querySelector('header + div')
  if (dropdownRef.current?.hasAttribute('tabIndex')) {
    dropdownRef.current?.removeAttribute('tabIndex')
    prevFocusedElement.current?.focus()
  } else if (lastItem && openDropdown === items[items.length - 1].slug) {
    if (nextFocusableElement instanceof HTMLElement) {
      setTimeout(() => {
        if (!dropdownRef.current?.hasAttribute('tabIndex')) {
          dropdownRef.current?.blur()
          nextFocusableElement.setAttribute('tabIndex', '0')
          nextFocusableElement.focus()
          document.removeEventListener('keydown', handleKeyDown)
          if (dropdownRef.current) {
            dropdownRef.current.style.visibility = 'hidden'
            dropdownRef.current.style.pointerEvents = 'none'
          }
        }
      }, FOCUS_DELAY)
    }
  }
}

export const eventKeyArrowNavigation = (
  direction: 'down' | 'up',
  active: boolean,
  dropdownRef: RefObject<HTMLDivElement> | null
) => {
  if (!active || !dropdownRef) return

  const focusableElements = dropdownRef.current?.querySelectorAll(
    'a, button, input, [tabindex]:not([tabindex="-1"])'
  ) as NodeListOf<HTMLElement> | null

  if (focusableElements && focusableElements.length > 0) {
    const currentIndex = Array.from(focusableElements).findIndex(
      (el) => el === document.activeElement
    )

    let nextIndex: number
    if (direction === 'down') {
      nextIndex = (currentIndex + 1) % focusableElements.length
    } else {
      nextIndex = (currentIndex - 1 + focusableElements.length) % focusableElements.length
    }

    focusableElements[nextIndex].setAttribute('tabIndex', '-1')
    focusableElements[nextIndex].focus()
  }
}
