import Container from '@components/layout/Container'
import DropdownTextLink from '@nav/NavigationDropdown/DropdownTextLink'
import DropdownImageLink from '@nav/NavigationDropdown/DropdownImageLink'
import DropdownDivider from '@nav/NavigationDropdown/DropdownDivider'
import DropdownFeaturedImageLink from '@nav/NavigationDropdown/DropdownFeaturedImageLink'
import DropdownHoverFeature from '@nav/NavigationDropdown/DropdownHoverFeature'
import { spacing } from '@/app/themeTokens.stylex'

import React, { useState } from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  linkContainer: {
    maxWidth: '1250px',
    marginInline: 'auto',
    width: 'fit-content',
    alignItems: 'flex-start',
  },
  textLinkContainer: {
    flexDirection: 'column',
    flex: '1 1 auto',
    gap: 0,
    marginInlineEnd: {
      ':not(:last-child)': spacing.lg,
    },
  },
  interactiveTextLinkContainer: {
    flexDirection: 'column',
    flex: '1 1 auto',
    gap: 'unset',
    marginInlineEnd: {
      ':not(:last-child)': spacing.lg,
    },
  },
  imageLinkContainer: {
    width: 'min-content',
  }
})

const staggerLinkDelay = 25
const staggerImageLinkDelay = 80

const renderImageLink = (link: any, imageLinksRendered: number, openDropdown: any, category: string) => {
  const delay = (imageLinksRendered + 1) * staggerImageLinkDelay
  return <DropdownImageLink key={link.id} link={link} delay={delay} openDropdown={openDropdown} category={category} />
}

interface HoverData {
  imageSlug: string | null;
  name?: string;
  description?: string;
  callouts?: any;
}

const renderTextLink = (link: any, linkIndex: number, openDropdown: any, category: string, about?: boolean, onHover?: (data: HoverData) => void) => {
  const delay = (linkIndex + 1) * staggerLinkDelay
  return (
    <DropdownTextLink
      key={link.id}
      link={link}
      index={linkIndex}
      delay={delay}
      openDropdown={openDropdown}
      onHover={onHover}
      about={about}
      category={category}
    />
  )
}

const renderFeaturedImageLink = (link: any, imageLinksRendered: number, openDropdown: any, category: string) => {
  const delay = (imageLinksRendered + 1) * staggerImageLinkDelay
  return <DropdownFeaturedImageLink key={link.id} link={link} delay={delay} openDropdown={openDropdown} category={category} />
}

const renderInteractiveImageLink = (link: any, imageLinksRendered: number, openDropdown: any, hoverData: HoverData, category: string) => {
  const delay = (imageLinksRendered + 1) * staggerImageLinkDelay
  return (
    <DropdownHoverFeature
      key={link.id}
      link={link}
      delay={delay}
      openDropdown={openDropdown}
      hoveredImageSlug={hoverData.imageSlug}
      hoveredName={hoverData.name}
      hoveredDescription={hoverData.description}
      hoveredCallouts={hoverData.callouts}
      category={category}
    />
  )
}

const NavigationDropdown = ({
  item,
  openDropdown,
  about
}: { item: any, openDropdown: any, about?: boolean }) => {
  const [hoverData, setHoverData] = useState<HoverData>({ imageSlug: null })

  const handleLinkHover = (data: HoverData) => {
    setHoverData(data)
  }
  const imageLinksRendered = 0

  const renderLink = (collection: any, link: any, linkIndex: number) => {
    const category = item.name
    switch (collection.type) {
      case 'Image Link':
        return renderImageLink(link, imageLinksRendered, openDropdown, category)
      case 'Text Links':
        return renderTextLink(link, linkIndex, openDropdown, category, about, handleLinkHover)
      case 'Interactive Text Link':
        return renderTextLink(link, linkIndex, openDropdown, category, false, handleLinkHover)
      case 'Featured Image Link':
        return renderFeaturedImageLink(link, imageLinksRendered, openDropdown, category)
      case 'Interactive Image Link':
        return renderInteractiveImageLink(link, imageLinksRendered, openDropdown, hoverData, category)
      default:
        return null
    }
  }

  const renderCollection = (collection: any) => {
    if (collection.type === 'Divider') {
      return <DropdownDivider key={collection.id} />
    }

    const containerStyle = [
      collection.type === 'Text Links' && styles.textLinkContainer,
      collection.type === 'Interactive Text Link' && styles.interactiveTextLinkContainer,
      (collection.type === 'Image Link' || collection.type === 'Featured Image Link' || collection.type === 'Interactive Image Link') && styles.imageLinkContainer
    ]

    return (
      <Container
        key={collection.id}
        as="div"
        flex
        noWrap
        gap="2"
        flexRow
        styleProp={containerStyle}
      >
        {collection.links.map((link: any, linkIndex: number) => renderLink(collection, link, linkIndex))}
      </Container>
    )
  }

  return (
    <Container as="div" flex flexRow noWrap gap="2" styleProp={styles.linkContainer}>
      {item.linkCollection?.map(renderCollection)}
    </Container>
  )
}

export default NavigationDropdown
