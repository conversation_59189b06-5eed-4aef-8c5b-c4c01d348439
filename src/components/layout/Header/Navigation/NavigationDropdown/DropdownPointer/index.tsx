import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

type DropdownPointerProps = {
  size?: number;
  openDropdown?: string | null;
  color?: typeof colors[keyof typeof colors];
}

const styles = stylex.create({
  base: {
    transition: 'transform 0.3s ease',
    transformOrigin: '50% 55%',
  },
  rotated: {
    transform: 'rotate(180deg)',
  }
})
const defaultSVGSize = 100
const DropdownPointer = ({ size = defaultSVGSize, openDropdown }: DropdownPointerProps) => {
  const rotateStyle = openDropdown ? styles.rotated : null
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        stroke={colors.navy}
        strokeWidth="8"
        fill="none"
        d="m 20 45 l 30 30 l 30 -30"
        {...stylex.props(styles.base, rotateStyle)}
      />
    </svg>
  )
}

export default DropdownPointer
