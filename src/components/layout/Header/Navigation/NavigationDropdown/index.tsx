import {
  eventKeyArrowNavigation, eventKeyEscape, eventKeyTab
} from './helper'

import { NavigationItem, LinkCollection } from '../types'

import { globalTokens as $, colors, spacing } from '@/app/themeTokens.stylex'
import LayoutOne from '@components/layout/Header/Navigation/NavigationDropdown/Dropdown'

import * as stylex from '@stylexjs/stylex'
import React, { useEffect, useRef } from 'react'

type LayoutKey = '1'

type DropdownsProps = {
  items: NavigationItem[];
  openDropdown: string | null;
  about?: boolean;
  onMouseUp: () => void
};

const layoutComponents: Record<LayoutKey, React.ComponentType<any>> = {
  1: LayoutOne,
}

// TODO: Update Dropdown easings, timing functions, and transitions
const styles = stylex.create({
  dropdown: {
    position: 'absolute',
    backgroundColor: colors.white,
    transition: `
      visibility 200ms ${$.timingFunction} 250ms,
      boxShadow 250ms ${$.timingFunction}
    `,
    visibility: 'hidden',
    zIndex: -1,
    width: '100%',
    paddingBlock: '2rem',
    paddingInline: '1rem',
    paddingBlockEnd: '4rem',
    pointerEvents: 'none',
  },
  dropdownOpen: {
    transition: `visibility 200ms ${$.timingFunction}`,
    visibility: 'visible',
    boxShadow: '-2px 0 5px rgba(0, 0, 0, 0.05), 2px 0 5px rgba(0, 0, 0, 0.05), 0 4px 5px rgba(0, 0, 0, 0.05)',
    pointerEvents: null,
  },
  dropdownOpenNotAbout: {
    minHeight: '410px',
  },
  about: {
    width: 'unset',
    transform: 'translate(5px, -100%)',
    paddingBlockEnd: '1rem',
    paddingInline: spacing.md,
  },
  aboutDropdownOpen: {
    transition: 'transform 750ms cubic-bezier(0.25, 1, 0.3, 1)',
    transform: 'translate(5px, 70%)',
    boxShadow: '-2px 0 5px rgba(0, 0, 0, 0.05), 2px 0 5px rgba(0, 0, 0, 0.05), 0 4px 5px rgba(0, 0, 0, 0.05)'
  }
})

// eslint-disable-next-line complexity
const DropdownItem = ({
  item,
  openDropdown,
  onMouseUp,
  about,
  lastItem = false,
  items,
}: {
  item: NavigationItem;
  openDropdown: string | null;
  onMouseUp: () => void;
  about?: boolean;
  lastItem?: boolean;
  items: NavigationItem[];
}) => {
  const dropdownRef = useRef<HTMLDivElement | null>(null)
  const prevFocusedElement = useRef<HTMLDivElement | null>(null)

  const handleMouseMove = () => {
    if (dropdownRef.current?.hasAttribute('tabIndex')) {
      dropdownRef.current.removeAttribute('tabIndex')
    }
  }

  const handleKeyDown = React.useCallback((event: KeyboardEvent) => {
    if (!dropdownRef.current) return

    dropdownRef.current.style.visibility = ''
    dropdownRef.current.style.pointerEvents = ''

    const isDropdownOpen = openDropdown === item.slug && !about

    switch (event.key) {
      case 'Escape':
        eventKeyEscape(dropdownRef)
        break
      case 'Tab':
        eventKeyTab(
          dropdownRef,
          prevFocusedElement,
          lastItem,
          openDropdown,
          items,
          handleKeyDown
        )
        break
      case 'ArrowDown':
        event.preventDefault()
        eventKeyArrowNavigation('down', isDropdownOpen, dropdownRef)
        break
      case 'ArrowUp':
        event.preventDefault()
        eventKeyArrowNavigation('up', isDropdownOpen, dropdownRef)
        break
      default:
        break
    }
  }, [about, dropdownRef, item.slug, items, lastItem, openDropdown])

  useEffect(() => {
    const addEventListeners = () => {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('keydown', handleKeyDown as EventListener)
    }

    const removeEventListeners = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('keydown', handleKeyDown)
    }

    const manageFocus = () => {
      if (openDropdown === item.slug && !about) {
        prevFocusedElement.current = document.activeElement as HTMLDivElement
        dropdownRef.current?.setAttribute('tabIndex', '-1')
        dropdownRef.current?.focus()
      } else {
        dropdownRef.current?.removeAttribute('tabIndex')
      }
    }

    manageFocus()
    addEventListeners()

    return () => {
      removeEventListeners()
    }
  }, [about, handleKeyDown, item.slug, openDropdown])

  const shouldRenderDropdown = Array.isArray(item.linkCollection) && item.linkCollection.length > 0
  if (!shouldRenderDropdown) return null

  const linkCollection = item.linkCollection as unknown as LinkCollection
  const LayoutComponent = layoutComponents[linkCollection.layout as LayoutKey] || LayoutOne

  return (
    <div
      ref={dropdownRef}
      {...stylex.props([
        styles.dropdown,
        openDropdown === item.slug && styles.dropdownOpen,
        openDropdown === item.slug && !about && styles.dropdownOpenNotAbout,
        about && styles.about,
        about && openDropdown === item.slug && styles.aboutDropdownOpen,
      ])}
      onMouseUp={onMouseUp}
    >
      <LayoutComponent
        about={about}
        item={item}
        openDropdown={openDropdown}
      />
    </div>
  )
}

const NavigationDropdown = ({
  items,
  openDropdown,
  onMouseUp,
  about
}: DropdownsProps) => (
  <>
    {items.map((item) => (
      <DropdownItem
        key={item.id}
        item={item}
        onMouseUp={onMouseUp}
        openDropdown={openDropdown}
        about={about}
        lastItem={items[items.length - 1].id === item.id}
        items={items}
      />
    ))}
  </>
)

export default NavigationDropdown
