import NavigationLink from '@nav/NavigationLinks/NavigationLink'
import { colors, spacing, fontSizes } from '@/app/themeTokens.stylex'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  link: {
    padding: '0px',
    flex: '1'
  },
  firstNavigationLink: {
    fontSize: fontSizes.xxs,
    textDecoration: 'none',
  },
  staggerLinks: (delay: number) => ({
    animationName: stylex.keyframes({
      '0%': { transform: 'translate(0px, -5px)', opacity: 0 },
      '100%': { transform: 'translate(0px, 0px)', opacity: 1 },
    }),
    animationDuration: '300ms',
    animationFillMode: 'forwards',
    animationTimingFunction: 'cubic-bezier(0.25, 1, 0.3, 1)',
    animationIterationCount: 1,
    opacity: 0,
    animationDelay: `${delay}ms`,
  }),
  nonClickableLink: {
    padding: '0px',
    flex: '1 1 100%',
    color: colors.gray500,
    listStyle: 'none',
    fontWeight: 'normal',
    marginBlockEnd: spacing.md,
  },
  aboutFirstLink: {
    fontSize: 'inherit',
    marginBlockEnd: 0,
  },
})

type TextLinkProps = {
  link: any
  index: number
  delay: number
  openDropdown: boolean
  onHover?: (data: { imageSlug: string | null, name?: string, description?: string, callouts?: any }) => void
  about?: boolean
  category?: string
}

const DropdownTextLink = ({
  link,
  index,
  delay,
  openDropdown,
  about,
  onHover,
  category
}: TextLinkProps) => {
  const handleMouseEnter = () => {
    if (onHover) {
      onHover({
        imageSlug: link.image,
        name: link.name,
        description: link.description,
        callouts: link.callouts
      })
    }
  }

  const handleMouseLeave = () => {
    if (onHover) {
      onHover({
        imageSlug: null,
        name: undefined,
        description: undefined,
        callouts: undefined
      })
    }
  }
  const commonStyles = [
    styles.link,
    index === 0 && !about && styles.firstNavigationLink,
    index === 0 && about && styles.aboutFirstLink,
    openDropdown ? styles.staggerLinks(delay) : null
  ]

  if (!link.slug) {
    return (
      <li {...stylex.props(commonStyles, styles.nonClickableLink)}>
        {link.linkTitle}
      </li>
    )
  }

  return (
    <NavigationLink
      key={link.id}
      slug={link.slug}
      callouts={link.callouts}
      styleProp={commonStyles}
      columnLink
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      linkTarget={link.linkTarget}
      category={category}
    >
      {link.linkTitle}
    </NavigationLink>
  )
}

export default DropdownTextLink
