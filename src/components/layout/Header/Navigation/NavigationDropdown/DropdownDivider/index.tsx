import { colors } from '@/app/themeTokens.stylex'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  base: {
    backgroundColor: colors.white,
    flexShrink: 0,
  },
  vertical: {
    width: '40px',
    alignSelf: 'stretch',
    flex: '1 10000 auto',
  },
  horizontal: {
    height: '1px',
    width: 'auto',
    marginBlock: '8px',
    marginBlockEnd: '12px',
  },
})

type DropdownDividerProps = {
  direction?: 'vertical' | 'horizontal'
  styleProp?: stylex.StyleXStyles
}

const DropdownDivider: React.FC<DropdownDividerProps> = ({ direction = 'vertical', styleProp }) => (
  <div
    {...stylex.props(
      styles.base,
      direction === 'vertical' ? styles.vertical : styles.horizontal,
      styleProp
    )}
  />
)

export default DropdownDivider
