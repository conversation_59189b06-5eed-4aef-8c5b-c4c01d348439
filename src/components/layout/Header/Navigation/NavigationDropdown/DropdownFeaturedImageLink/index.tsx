import Container from '@components/layout/Container'
import NavigationLink from '@nav/NavigationLinks/NavigationLink'
import NavigationImageLink from '@nav/NavigationLinks/NavigationImageLink'
import { spacing, colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

const styles = stylex.create({
  featuredImageLinkContainer: {
    alignItems: 'flex-start',
  },
  featuredImageLink: {
    gridArea: '1/1',
  },
  featuredImageContentContainer: {
    display: 'flex',
    alignItems: 'center',
    alignSelf: 'center',
    flexDirection: 'column',
    gap: '1',
    gridArea: '1/1',
  },
  featuredImageSubheader: {
    fontSize: '14px',
    fontWeight: 'bold',
    padding: '0px',
    color: colors.white
  },
  featuredImageHeader: {
    fontSize: '24px',
    fontWeight: 'bold',
    padding: '0px',
    color: colors.white,
    textAlign: 'center'
  },
  featuredImageDescription: {
    fontSize: '14px',
    paddingTop: spacing.xs,
    color: colors.white
  },
  staggerImageLinks: (delay: number) => ({
    animationName: stylex.keyframes({
      '0%': { transform: 'translate(0px, -20px)' },
      '100%': { transform: 'translate(0px, 0px)' },
    }),
    animationDuration: '800ms',
    animationFillMode: 'both',
    animationTimingFunction: 'cubic-bezier(0.25, 1, 0.3, 1)',
    animationIterationCount: 1,
    animationDelay: `${delay}ms`,
  }),
  imageLink: {
    padding: '0px',
    borderRadius: spacing.xxs,
    overflow: 'hidden',
  },
})

const DropdownFeaturedImageLink = ({
  link,
  delay,
  openDropdown,
  category
}: { link: any, delay: number, openDropdown: boolean, category: string }) => (
  <React.Fragment key={link.id}>
    <Container
      as="div"
      gridPile
      gap="1"
      styleProp={[
        styles.featuredImageLinkContainer,
        openDropdown ? styles.staggerImageLinks(delay) : null,
      ]}
    >
      <ul {...stylex.props(styles.featuredImageLink)}>
        <NavigationImageLink
          slug={link.slug}
          imageSrc={link.image}
          imageAlt={link.name}
          callouts={link.callouts}
          styleProp={[styles.imageLink]}
          liStyleProp={[styles.imageLink]}
          imageWidth={363}
          imageHeight={267}
          category={category}
          title={link.name}
        />
      </ul>
      <ul {...stylex.props(styles.featuredImageContentContainer)}>
        <NavigationLink
          slug={link.slug}
          styleProp={[styles.featuredImageSubheader]}
          category={category}
        >
          {link.name}
        </NavigationLink>
        <NavigationLink
          slug={link.slug}
          styleProp={[styles.featuredImageHeader]}
          category={category}
        >
          {link.linkTitle}
        </NavigationLink>
        <NavigationLink
          slug={link.slug}
          styleProp={[styles.featuredImageDescription]}
          category={category}
        >
          {link.description}
        </NavigationLink>
      </ul>
    </Container>
  </React.Fragment>
)

export default DropdownFeaturedImageLink
