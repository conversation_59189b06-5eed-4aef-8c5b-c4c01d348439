import { CalloutsProps } from '@components/Generic/Callout/types'

type BaseLink = {
  id: string
  name: string
  linkTitle: string
  slug?: string | null
  callout?: CalloutsProps
};

type ImageLink = BaseLink & {
  description: string | null
  image: string | null
};

type TextLink = BaseLink;

type HoverImageLink = BaseLink & {
  description: string | null
  image: string | null
};

export type LinkCollection = {
  type: 'imageLink' | 'textLink' | 'divider' | 'featuredImageLink' | 'interactiveImageLink' | 'interactiveTextLink';
  layout?: string;
  links: ImageLink[] | TextLink[] | HoverImageLink[];
  id: string;
};

export type NavigationItem = {
  name: string;
  id: string;
  linkTitle: string;
  slug: string;
  callouts?: CalloutsProps
  linkCollection?: LinkCollection[] | null;
  linkTarget: string;
  layout?: string;
  animationType?: string;
  theme?: {
    theme?: string;
  };
};
