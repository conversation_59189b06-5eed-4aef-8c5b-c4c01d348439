import Message from './Message'

import fetchPromoBar from '@lib/contentful/fetchPromoBar'
import {
  colors,
  globalTokens as $,
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  root: {
    height: $.promoBarHeight,
    minHeight: $.promoBarHeight,
    width: '100%',
    zIndex: '3',
    position: 'relative',
    backgroundColor: colors.navy,
    overflow: 'hidden'
  },
})

const Promobar = async ({ slug }: { slug: string }) => {
  const promoBarData = await fetchPromoBar(slug)
  return (
    <div {...stylex.props(styles.root)} id="global-promo-bar">
      {promoBarData.map(({
        id,
        message,
        mobileMessage,
        theme,
        countdown
      }, index) => (
        <Message
          key={id}
          index={index}
          length={promoBarData.length}
          message={message}
          mobileMessage={mobileMessage}
          theme={theme}
          countdown={countdown}
        />
      ))}
    </div>
  )
}

export default Promobar
