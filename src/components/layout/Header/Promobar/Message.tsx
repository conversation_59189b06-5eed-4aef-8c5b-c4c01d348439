import { MessageProps } from './types'

import Container from '@components/layout/Container'
import Countdown from '@components/Generic/Countdown'
import { DesktopOnly, MobileOnly } from '@utils/responsiveDisplay'
import { globalTokens as $ } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const slideUp = stylex.keyframes({
  '0%': { transform: 'translateY(100%)', opacity: 0 },
  '5%': { transform: 'translateY(0)', opacity: 1 },
  '50%': { transform: 'translateY(0)', opacity: 1 },
  '55%': { transform: 'translateY(-100%)', opacity: 0 },
  '100%': { transform: 'translateY(100%)', opacity: 0 },
})

const ANIMATION_DURATION_UNIT = 5

const styles = stylex.create({
  message: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    top: '0',
    transform: 'translateY(100%)',
    opacity: 0,
  },
  display: {
    transform: 'translateY(0)',
    opacity: 1,
  },
  // TODO: eliminate dynamic styles
  promoBar: (length: number, index: number) => ({
    animationName: slideUp,
    animationDuration: `${ANIMATION_DURATION_UNIT * length}s`,
    animationIterationCount: 'infinite',
    animationTimingFunction: $.timingFunction,
    animationDelay: `${ANIMATION_DURATION_UNIT * index}s`,
  })
})

// TODO: implement dialog triggers via props

const Message = ({
  message,
  mobileMessage,
  // dialogTriggers,
  countdown,
  theme = 'cream',
  length,
  index,
}: MessageProps) => (
  <Container
    as="div"
    flex
    flexCentered
    flexRow
    theme={theme}
    paddingBlock="1"
    gap="1"
    styleProp={[
      styles.message,
      length === 1 && styles.display,
      length !== 1 && styles.promoBar(length, index),
    ]}
  >
    {countdown && [<Countdown endDate={countdown} key="countdown" />, ' ']}
    {mobileMessage
      ? (
        <>
          <MobileOnly>{mobileMessage}</MobileOnly>
          <DesktopOnly>{message}</DesktopOnly>
        </>
      )
      : message}
  </Container>
)

export default Message
