'use client'

import PageSections from './PageSections'
import { ProductSectionSkeleton } from './InfiniteSectionsLoader/ProductSectionSkeleton'
import SectionSentinel from './InfiniteSectionsLoader/SectionSentinel'

import useInfiniteSectionsLoader from '@/hooks/useInfiniteSectionsLoader'
import { PageSection, PageSectionsProduct } from '@/lib/contentful/types'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

const styles = stylex.create({
  errorContainer: {
    color: 'red',
    padding: '1rem',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 8,
  },
  sectionWrapper: {
    position: 'relative',
  }
})

interface InfiniteSectionsLoaderProps {
  pageId: string
  initialSections: PageSection[]
  pageStructure: PageSection[]
}

export default function InfiniteSectionsLoader({
  pageId,
  initialSections,
  pageStructure
}: InfiniteSectionsLoaderProps) {
  const {
    loadedSections,
    loadingSections,
    fetchSection,
    shouldHaveSentinel,
    topObserverOptions,
    bottomObserverOptions
  } = useInfiniteSectionsLoader({ pageId, initialSections, pageStructure })

  // Render an unloaded product section with sentinels
  const renderUnloadedSection = (section: PageSectionsProduct, needsSentinel: boolean) => {
    const sectionId = section.sys.id

    return (
      <div {...stylex.props(styles.sectionWrapper)}>
        {needsSentinel && (
          <SectionSentinel
            key={`top-sentinel-${sectionId}`}
            sectionId={sectionId}
            fetchSection={fetchSection}
            observerOptions={topObserverOptions}
            position="top"
          />
        )}

        <ProductSectionSkeleton
          key={`skeleton-${sectionId}`}
          section={section}
        />

        {needsSentinel && (
          <SectionSentinel
            key={`bottom-sentinel-${sectionId}`}
            sectionId={sectionId}
            fetchSection={fetchSection}
            observerOptions={bottomObserverOptions}
            position="bottom"
          />
        )}
      </div>
    )
  }

  const renderSection = (section: PageSection) => {
    const sectionId = section.sys.id
    const isLoaded = loadedSections.has(sectionId)
    const isLoading = loadingSections.has(sectionId)
    const isProductSection = section.sectionType === 'PageSectionsProduct'
    const needsSentinel = shouldHaveSentinel(section)

    if (isLoaded) {
      return (
        <PageSections
          key={`loaded-${sectionId}`}
          sections={[loadedSections.get(sectionId)!]}
        />
      )
    }

    if (isProductSection && isLoading) {
      return (
        <ProductSectionSkeleton
          key={`skeleton-${sectionId}`}
          section={section as PageSectionsProduct}
        />
      )
    }

    if (isProductSection && !isLoaded && !isLoading) {
      return renderUnloadedSection(section as PageSectionsProduct, needsSentinel)
    }

    return null
  }

  return (
    <div>
      {pageStructure.map((section) => (
        <React.Fragment key={`section-${section.sys.id}`}>
          {renderSection(section)}
        </React.Fragment>
      ))}
    </div>
  )
}
