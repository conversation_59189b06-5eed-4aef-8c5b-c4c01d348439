import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartStainlessSteel = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: [
        'Caraway® Stainless Steel',
        'Other Stainless Brands'
      ],
      rows: [
        {
          title: 'Non-Toxic',
          description: 'Are the materials used safe for my family and the environment?',
          checkMarks: ['orangeCheck', 'outlineX']
        },
        {
          title: '5-Ply Construction',
          description: 'Is 5-ply construction used to maximize durability and longevity?',
          checkMarks: ['orangeCheck', 'checkHalfFill']
        },
        {
          title: 'Heat Conductivity',
          description: 'Does the surface heat evenly, avoid cooling spots and unevenly cooked food?',
          checkMarks: ['orangeCheck', 'checkHalfFill']
        },
        {
          title: 'Cleaning',
          description: 'Are the pots and pans easily washed and cared for?',
          checkMarks: ['orangeCheck', 'checkHalfFill']
        },
        {
          title: 'Corrosion Resistance',
          description: 'Has the surface been treated to eliminate contamination and reduce corrosive elements?',
          checkMarks: ['orangeCheck', 'outlineX']
        },
        {
          title: 'Lifetime Warranty',
          description: 'Does the brand offer a lifetime assurance for the product\'s performance and durability?',
          checkMarks: ['orangeCheck', 'outlineX']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartStainlessSteel
