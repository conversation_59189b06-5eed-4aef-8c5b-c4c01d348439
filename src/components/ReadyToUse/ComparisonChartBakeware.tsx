import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartBakeware = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: [
        'Caraway® Non-Stick',
        'PTFE Non-Stick (ex: Teflon®)',
        'Uncoated Stainless Steel',
        'Glass, Stoneware or Porcelain'
      ],
      rows: [
        {
          title: 'Non-Toxic',
          description: 'Are the materials used in the non-stick coating safe for my family and the environment?',
          checkMarks: ['orangeCheck', 'outlineX', 'blueCheck', 'blueCheck']
        },
        {
          title: 'Non-Stick',
          description: 'Do baked goods easily release from the baking surface with a minimal amount of oil or butter?',
          checkMarks: ['orangeCheck', 'blueCheck', 'outlineX', 'outlineX']
        },
        {
          title: 'Easy Cleaning',
          description: 'Is the baking surface easy to clean and care for?',
          checkMarks: ['orangeCheck', 'blueCheck', 'outlineX', 'outlineX']
        },
        {
          title: 'Even Heat Distribution',
          description: 'Does the surface heat consistently without any hot spots for evenly cooked food?',
          checkMarks: ['orangeCheck', 'blueCheck', 'blueCheck', 'outlineX']
        },
        {
          title: 'Storage Included',
          description: 'Does the product include organizers to keep your bakeware safely stored and easy to access?',
          checkMarks: ['orangeCheck', 'outlineX', 'outlineX', 'outlineX']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartBakeware
