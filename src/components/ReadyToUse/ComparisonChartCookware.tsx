import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartCookware = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: ['Caraway® Non-Stick', 'PTFE Non-Stick (ex: Teflon®)'],
      rows: [
        {
          title: 'Non-Toxic',
          description: 'Are the materials used in the non-stick coating safe for my family and the environment?',
          checkMarks: ['orangeCheck', 'outlineX']
        },
        {
          title: 'Non-Stick',
          description: 'Does food easily slide around the cooking surface with a minimal amount of oil or butter?',
          checkMarks: ['orangeCheck', 'blueCheck']
        },
        {
          title: 'Cleaning',
          description: 'Are the pots and pans easily washed and cared for?',
          checkMarks: ['orangeCheck', 'blueCheck']
        },
        {
          title: 'Heat Conductivity',
          description: 'Does the surface heat evenly, avoiding cool spots and unevenly cooked food?',
          checkMarks: ['orangeCheck', 'blueCheck']
        },
        {
          title: 'Versatility',
          description: 'Does the cookware perform well no matter what cooktop or environment?',
          checkMarks: ['orangeCheck', 'outlineX']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartCookware
