import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartSteamer = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: [
        'Caraway® Non-Stick',
        'Uncoated Stainless Steel',
        'Silicone or Plastic',
        'Cast Iron or Wood'
      ],
      rows: [
        {
          title: 'Non-Toxic',
          description: 'Are the materials used in the non-stick coating safe for my family and the environment?',
          checkMarks: ['orangeCheck', 'blueCheck', 'outlineX', 'blueCheck']
        },
        {
          title: 'Non-Stick',
          description: 'Does food easily slide around the cooking surface without sticking to the bottom of the steamer or leaving remnants behind?',
          checkMarks: ['orangeCheck', 'outlineX', 'outlineX', 'outlineX']
        },
        {
          title: 'Cleaning',
          description: 'Is the steamer easily washed and cared for?',
          checkMarks: ['orangeCheck', 'outlineX', 'checkHalfFill', 'checkHalfFill']
        },
        {
          title: 'Heat Conductivity',
          description: 'Does the surface heat quickly & evenly, preventing cool spots and unevenly cooked food?',
          checkMarks: ['orangeCheck', 'blueCheck', 'outlineX', 'outlineX']
        },
        {
          title: 'Durability',
          description: 'Is the steamer highly resilient and long-lasting?',
          checkMarks: ['orangeCheck', 'blueCheck', 'outlineX', 'checkHalfFill']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartSteamer
