import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartCuttingBoards = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: [
        'Caraway FSC-Certified Cutting Boards',
        'Varnished or Non-FSC-Certified Wood',
        'Plastic, Rubber, or Silicone',
        'Marble or Granite'
      ],
      rows: [
        {
          title: 'Anti-Bacterial',
          description: 'Are the boards designed with anti-bacterial materials that make it easy to stay clean?',
          checkMarks: ['orangeCheck', 'blueCheck', 'outlineX', 'blueCheck']
        },
        {
          title: 'Eco-Friendly',
          description: 'Are the materials sustainably made with FSC-certified wood & good for the environment?',
          checkMarks: ['orangeCheck', 'outlineX', 'outlineX', 'blueCheck']
        },
        {
          title: 'Durability',
          description: 'Are the surfaces durable for everyday use & easy to care for?',
          checkMarks: ['orangeCheck', 'checkHalfFill', 'blueCheck', 'checkHalfFill']
        },
        {
          title: 'Resistance',
          description: 'Will knives stay sharp with repeated use against the board surfaces?',
          checkMarks: ['orangeCheck', 'blueCheck', 'checkHalfFill', 'outlineX']
        },
        {
          title: 'Organization',
          description: 'Does the product include organizers to keep your Cutting Boards safely stored and easy to access?',
          checkMarks: ['orangeCheck', 'outlineX', 'outlineX', 'outlineX']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartCuttingBoards
