import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartTeaKettle = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: [
        'Caraway® Tea Kettle',
        'Aluminum, Copper, or Plastic',
        'Cast Iron or Stoneware',
        'Glass'
      ],
      rows: [
        {
          title: 'Non-Toxic',
          description: 'Are the materials used in the non-stick coating safe for my family and the environment?',
          checkMarks: ['orangeCheck', 'outlineX', 'checkHalfFill', 'blueCheck']
        },
        {
          title: 'Durable',
          description: 'Are the materials premium and made to last with everyday use?',
          checkMarks: ['orangeCheck', 'checkHalfFill', 'blueCheck', 'outlineX']
        },
        {
          title: 'Whistle',
          description: 'Does the kettle whistle when the water has reached a boil?',
          checkMarks: ['orangeCheck', 'blueCheck', 'outlineX', 'outlineX']
        },
        {
          title: 'Heat Conductivity',
          description: 'Does the kettle boil water fast and efficiently?',
          checkMarks: ['orangeCheck', 'blueCheck', 'outlineX', 'outlineX']
        },
        {
          title: 'Versatility',
          description: 'Is the kettle compatible with Induction, Gas, and Electric stoves?',
          checkMarks: ['orangeCheck', 'checkHalfFill', 'outlineX', 'outlineX']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartTeaKettle
