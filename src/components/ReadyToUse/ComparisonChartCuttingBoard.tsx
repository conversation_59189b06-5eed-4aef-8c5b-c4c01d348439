import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartCuttingBoard = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: [
        'Caraway FSC-Certified Cutting Boards',
        'Varnished or Non-FSC-Certified Wood',
        'Plastic, Rubber, or Silicone',
        'Marble or Granite'
      ],
      rows: [
        {
          title: 'Anti-Bacterial',
          description: 'Are the boards designed with anti-bacterial materials that make it easy to stay clean?',
          checkMarks: ['orangeCheck', 'blueCheck', 'outlinex', 'blueCheck']
        },
        {
          title: 'Eco-Friendly',
          description: 'Are the materials sustainably made with FSC-certified wood & good for the environment?',
          checkMarks: ['orangeCheck', 'outlinex', 'outlinex', 'blueCheck']
        },
        {
          title: 'Durability',
          description: 'Are the surfaces durable for everyday use & easy to care for?',
          checkMarks: ['orangeCheck', 'checkHalfFill', 'blueCheck', 'checkHalfFill']
        },
        {
          title: 'Resistance',
          description: 'Will knives stay sharp with repeated use against the board surfaces?',
          checkMarks: ['orangeCheck', 'blueCheck', 'checkHalfFill', 'outlinex']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartCuttingBoard
