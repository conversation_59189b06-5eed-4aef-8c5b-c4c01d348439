'use client'

import Container from '../layout/Container'

import useIsMobile from '@/hooks/isMobile'

import React, { useRef } from 'react'
import {
  motion, useScroll, useSpring, useTransform
} from 'framer-motion'
import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

type BlockType = {
  desktopUrl: string,
  mobileUrl: string,
  heading: string,
  body: string
}

const styles = stylex.create({
  wrapper: {
    gap: {
      default: '100px',
      '@media (max-width: 768px)': '60px'
    },
    padding: {
      default: '50px 0px',
      '@media (max-width: 768px)': '50px 10px'
    },
  },
  container: {
    height: '70vh',
    padding: '0px 50px',
  },
  block: {
    height: '100%',
    flex: '1',
  },
  imageWrapper: {
    position: 'relative',
  },
  copyWrapper: {
    flex: '1',
  }
})

const mobileStyles = stylex.create({
  container: {
    height: '75%',
    width: '100%',
  },
  block: {
    height: '100%',
  },
  contentWrapper: {
    textAlign: 'center',
    height: '100%',
    justifyContent: 'start'
  }
})

const blocks: BlockType[] = [
  {
    desktopUrl: '/assets/Seal - Still Life - Desktop.jpg',
    mobileUrl: '/assets/Seal - Still Life - Mobile.jpg',
    heading: 'Expertly Crafted True Airtight Seal',
    body: 'Precision-engineered airtight seal keeps food fresher longer, while keeping out air, moisture, and pests.'
  },
  {
    desktopUrl: '/assets/Stackable - Still Life - Desktop.jpg',
    mobileUrl: '/assets/Stackable - Still Life - Mobile.jpg',
    heading: 'Stackable, Space-Saving Design',
    body: 'Modular, flame-rounded containers stack seamlessly, save space, and fit every organizational need.'
  },
  {
    desktopUrl: '/assets/Glass - Still Life - Desktop.jpg',
    mobileUrl: '/assets/Glass - Still Life - Mobile.jpg',
    heading: 'High-Resistance Glass Built to Last',
    body: 'Dishwasher-safe, ultra-pure borosilicate glass is scratch, stain, and odor-resistant for reliable, everyday use.',
  },
]

const BlockDesktop = ({ block, index }: { block: BlockType, index: number }) => {
  const ref = useRef(null)

  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start'],
  })

  /* eslint-disable-next-line no-magic-numbers */
  const y1 = useSpring(useTransform(scrollYProgress, [0, 0.3, 1], [200, 0, 0]), { stiffness: 250, damping: 25 })

  /* eslint-disable-next-line no-magic-numbers */
  const y2 = useSpring(useTransform(scrollYProgress, [0, 0.2, 0.3, 1], [250, 250, 0, 0]), { stiffness: 250, damping: 30 })

  /* eslint-disable-next-line no-magic-numbers */
  const odd = index % 2

  return (
    <Container flex flexRow alignCentered styleProp={[styles.container]}>
      <div
        ref={ref}
        {...stylex.props([styles.block, styles.imageWrapper])}
        style={{ order: odd ? 1 : 0 }}
      >
        <Image src={block.desktopUrl} alt={block.heading} fill style={{ objectFit: 'contain' }} />
      </div>
      <Container flex flexCentered styleProp={styles.copyWrapper}>
        <Container flex gap="lg">
          <motion.h2
            initial={{ visibility: 'hidden' }}
            animate={{ visibility: 'visible' }}
            style={{ y: y1 }}
          >
            {block.heading}
          </motion.h2>
          <motion.p
            initial={{ visibility: 'hidden' }}
            animate={{ visibility: 'visible' }}
            style={{ y: y2, maxWidth: '450px' }}
          >
            {block.body}
          </motion.p>
        </Container>
      </Container>
    </Container>
  )
}

const BlockMobile = ({ block }: { block: BlockType }) => {
  const ref = useRef(null)

  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start'],
  })

  /* eslint-disable-next-line no-magic-numbers */
  const y1 = useSpring(useTransform(scrollYProgress, [0, 0.1, 15], [200, 0, 0]), { stiffness: 250, damping: 20 })
  /* eslint-disable-next-line no-magic-numbers */
  const y2 = useSpring(useTransform(scrollYProgress, [0, 0.05, 0.15, 1], [200, 200, 0, 0]), { stiffness: 250, damping: 25 })
  /* eslint-disable-next-line no-magic-numbers */
  const y3 = useSpring(useTransform(scrollYProgress, [0, 0.05, 0.15, 1], [200, 200, 30, 0]), { stiffness: 250, damping: 30 })

  return (
    <Container flex flexRow alignCentered flexCentered styleProp={mobileStyles.container}>
      <div
        ref={ref}
        {...stylex.props([mobileStyles.block])}
      />
      <Container flex flexCentered gap="3" styleProp={mobileStyles.contentWrapper}>
        <motion.h3
          initial={{ visibility: 'hidden' }}
          animate={{ visibility: 'visible' }}
          style={{ y: y1 }}
        >
          {block.heading}
        </motion.h3>
        <motion.p
          initial={{ visibility: 'hidden' }}
          animate={{ visibility: 'visible' }}
          style={{ y: y2 }}
        >
          {block.body}
        </motion.p>
        <motion.div
          initial={{ visibility: 'hidden' }}
          animate={{ visibility: 'visible' }}
          style={{ y: y3 }}
        >
          <Image src={block.mobileUrl} alt={block.mobileUrl} layout="responsive" width={500} height={500} style={{ width: '95%' }} />
        </motion.div>
      </Container>
    </Container>
  )
}

const renderBlock = ({
  block,
  index,
  isMobile
}: { block: BlockType, index: number, isMobile: boolean }) => {
  if (isMobile) {
    return <BlockMobile block={block} />
  }

  return <BlockDesktop block={block} index={index} />
}

const ParallaxAirtightStorage = () => {
  const { isMobile } = useIsMobile()

  return (
    <Container flex styleProp={styles.wrapper}>
      {blocks.map((block, index) => renderBlock({ block, index, isMobile }))}
    </Container>
  )
}

export default ParallaxAirtightStorage
