import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartUtensilSet = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: [
        'Caraway FSC-Certified Wooden Utensils',
        'Plastic or Nylon',
        'Silicone',
        'Stainless Steel'
      ],
      rows: [
        {
          title: 'Non-Toxic',
          description: 'Are the materials & surface finishes used safe for my family and the environment?',
          checkMarks: ['orangeCheck', 'outlineX', 'checkHalfFill', 'blueCheck']
        },
        {
          title: 'Eco-Friendly',
          description: 'Are the materials sustainably made with FSC-certified wood & good for the environment?',
          checkMarks: ['orangeCheck', 'outlineX', 'checkHalfFill', 'blueCheck']
        },
        {
          title: 'Easy Cleaning',
          description: 'Are the wooden utensils easily washed and cared for?',
          checkMarks: ['orangeCheck', 'blueCheck', 'checkHalfFill', 'outlineX']
        },
        {
          title: 'Versatility',
          description: 'Can the utensils be used with non-stick cookware?',
          checkMarks: ['orangeCheck', 'blueCheck', 'blueCheck', 'outlineX']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartUtensilSet
