import ComparisonChartBakeware from './ComparisonChartBakeware'
import ComparisonChartCookware from './ComparisonChartCookware'
import ComparisonChartCuttingBoard from './ComparisonChartCuttingBoard'
import ComparisonChartCuttingBoards from './ComparisonChartCuttingBoards'
import ComparisonChartFoodStorage from './ComparisonChartFoodStorage'
import ComparisonChartKitchenGadgets from './ComparisonChartKitchenGadgets'
import ComparisonChartKnifeSet from './ComparisonChartKnifeSet'
import ComparisonChartKnives from './ComparisonChartKnives'
import ComparisonChartStainlessSteel from './ComparisonChartStainlessSteel'
import ComparisonChartSteamer from './ComparisonChartSteamer'
import ComparisonChartTeaKettle from './ComparisonChartTeaKettle'
import ComparisonChartUtensils from './ComparisonChartUtensils'
import ComparisonChartUtensilSet from './ComparisonChartUtensilSet'
import ReferPage from './ReferPage'
import AnatomyAirtightStorage from './AnatomyAirtightStorage'
import ParallaxAirtightStorage from './ParallaxAirtightStorage'

import {
  AnatomyFoodStorage, AnatomyKnifeUtensilSet, AnatomyTeaKettle, AnatomyPetiteCooker
} from '../Product/Anatomies'

import { Suspense } from 'react'

export const SuspendedAnatomyAirtightStorage = () => <Suspense fallback={<div>Loading...</div>}><AnatomyAirtightStorage /></Suspense>

export default {
  ComparisonChartBakeware,
  ComparisonChartCookware,
  ComparisonChartCuttingBoard,
  ComparisonChartCuttingBoards,
  ComparisonChartFoodStorage,
  ComparisonChartKitchenGadgets,
  ComparisonChartKnifeSet,
  ComparisonChartKnives,
  ComparisonChartStainlessSteel,
  ComparisonChartSteamer,
  ComparisonChartTeaKettle,
  ComparisonChartUtensils,
  ComparisonChartUtensilSet,
  AnatomyFoodStorage,
  AnatomyKnifeUtensilSet,
  AnatomyTeaKettle,
  AnatomyPetiteCooker,
  AnatomyAirtightStorage: SuspendedAnatomyAirtightStorage,
  ReferPage,
  ParallaxAirtightStorage
}
