import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartKitchenGadgets = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: [
        'Caraway® Kitchen Gadgets',
        'Other Brands'
      ],
      rows: [
        {
          title: 'Non-Toxic',
          description: 'Are the tools free of plastics that could leach into my food?',
          checkMarks: ['orangeCheck', 'outlineX']
        },
        {
          title: 'Eco-Friendly',
          description: 'Are the tools made with sustainable materials that can be recycled to minimize future waste?',
          checkMarks: ['orangeCheck', 'checkHalfFill']
        },
        {
          title: 'Premium Materials',
          description: 'Are the tools designed with premium materials for long-term use?',
          checkMarks: ['orangeCheck', 'outlineX']
        },
        {
          title: 'Easy Cleaning',
          description: 'Are the tools easy to clean and care for?',
          checkMarks: ['orangeCheck', 'checkHalfFill']
        },
        {
          title: 'Storage',
          description: 'Does the set feature an organizer to keep my drawer tidy?',
          checkMarks: ['orangeCheck', 'outlineX']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartKitchenGadgets
