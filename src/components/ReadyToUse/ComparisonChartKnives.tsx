import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartKnives = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: [
        'Caraway German Stainless Steel Knives',
        'Japanese Steel',
        'Carbon Steel',
        'Ceramic'
      ],
      rows: [
        {
          title: 'Durability',
          description: 'Are the blades able to withstand chipping & breaking with everyday use?',
          checkMarks: ['orangeCheck', 'checkHalfFill', 'blueCheck', 'outlineX']
        },
        {
          title: 'Maintenance',
          description: 'Are the blades easy to sharpen and care for?',
          checkMarks: ['orangeCheck', 'checkHalfFill', 'blueCheck', 'outlineX']
        },
        {
          title: 'Versatility',
          description: 'Can the blades cut through various ingredients, from fine dicing to breaking down larger items?',
          checkMarks: ['orangeCheck', 'blueCheck', 'checkHalfFill', 'outlineX']
        },
        {
          title: 'Resistance',
          description: 'Are the blades able to withstand corrosion & staining with everyday use?',
          checkMarks: ['orangeCheck', 'blueCheck', 'outlineX', 'blueCheck']
        },
        {
          title: 'Optimal Weight',
          description: 'Are the knives designed with a balanced weight for easier cutting while remaining maneuverable and ergonomic?',
          checkMarks: ['orangeCheck', 'checkHalfFill', 'blueCheck', 'outlineX']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartKnives
