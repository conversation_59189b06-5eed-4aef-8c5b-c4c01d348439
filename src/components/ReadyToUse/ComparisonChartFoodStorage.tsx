import HowWeCompare from '@components/Content/HowWeCompare'

const ComparisonChartFoodStorage = () => (
  <HowWeCompare
    header="How We Compare"
    content={{
      titles: [
        'Caraway® Non-Stick',
        'Plastic, Acrylic, or Melamine',
        'Silicone',
        'Uncoated Glass'
      ],
      rows: [
        {
          title: 'Non-Toxic',
          description: 'Are the food storage materials safe for my family and the environment?',
          checkMarks: ['orangeCheck', 'outlineX', 'checkHalfFill', 'blueCheck']
        },
        {
          title: 'Non-Stick',
          description: 'Does food effortlessly slide out of the containers? Do the containers have a non-stick coating?',
          checkMarks: ['orangeCheck', 'outlineX', 'outlineX', 'outlineX']
        },
        {
          title: 'Cleaning',
          description: 'Are the containers easy to clean and prevent sauces or food from leaching into and staining the material?',
          checkMarks: ['orangeCheck', 'checkHalfFill', 'checkHalfFill', 'checkHalfFill']
        },
        {
          title: 'Storage',
          description: 'Do the containers store easily with included storage organizers?',
          checkMarks: ['orangeCheck', 'outlineX', 'outlineX', 'outlineX']
        },
        {
          title: 'Versatility',
          description: 'Are the container bodies microwave, oven, refrigerator, freezer, and dishwasher safe?',
          checkMarks: ['orangeCheck', 'checkHalfFill', 'checkHalfFill', 'blueCheck']
        }
      ]
    }}
    theme="white"
  />
)

export default ComparisonChartFoodStorage
