import { airtightStorageAnatomy } from './data'

import Container from '@/components/layout/Container'
import Spacer from '@/components/layout/Spacer'
import Typography from '@/components/Typography'
import { colors, spacing } from '@/app/themeTokens.stylex'
import Slider, { Slide } from '@/components/Generic/Slider'
import { DotButtons, SlideNavigationButtons } from '@/components/Generic/Slider/SliderButtons'
import useDotButton from '@/hooks/useDotButton'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import React, { useState, useEffect } from 'react'
import { EmblaCarouselType } from 'embla-carousel'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  container: {
    display: {
      default: 'flex',
      [DESKTOP]: 'none',
    },
  },
  spacer: {
    height: '1px',
    backgroundColor: colors.navy,
    marginTop: 28,
    marginBottom: 44,
  },
  slider: {
    width: '100%',
  },
  dots: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
  dot1: {
    position: 'absolute',
    top: 60,
    left: 60,
    width: 20,
    height: 20,
    outlineWidth: '4px',
  },
  dot2: {
    position: 'absolute',
    top: 110,
    left: 90,
    width: 20,
    height: 20,
    outlineWidth: '4px',
  },
  dot3: {
    position: 'absolute',
    left: 40,
    top: 160,
    width: 20,
    height: 20,
    outlineWidth: '4px',
  },
  dotsNavigation: {
    display: 'flex',
    justifyContent: 'center',
    gap: spacing.sm,
    marginTop: 44,
  },
  dotsNavigationButton: {
    background: 'transparent',
    outline: '1px solid',
    outlineColor: colors.navy,
  },
  dotsNavigationButtonSelected: {
    background: colors.navy,
  },
  video: {
    borderRadius: '12px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.navy,
    objectFit: 'cover',
  },
})

function SplitAnatomyMobile({ slides, selectedVariant }: { slides: any[], selectedVariant: any }) {
  const [emblaApi, setEmblaApi] = useState<EmblaCarouselType[]>([])
  const [currentSlide, setCurrentSlide] = useState(0)

  const { selectedIndex, scrollSnaps } =
    useDotButton(emblaApi[0])

  const onSelect = () => {
    if (!emblaApi.length) return
    setCurrentSlide(emblaApi[0].selectedScrollSnap())
  }

  useEffect(() => {
    if (!emblaApi.length) return
    onSelect()
    emblaApi[0].on('select', onSelect)
  }, [emblaApi])

  const handleScroll = (index: number) => () => {
    setCurrentSlide(index)
    if (emblaApi.length) {
      emblaApi[0].scrollTo(index)
    }
  }

  return (
    <Container
      flex
      flexColumn
      paddingBlock="4"
      paddingInline="2"
      styleProp={styles.container}
    >
      <Typography as="h2" typographyTheme="h4Secondary">
        {airtightStorageAnatomy.title}
      </Typography>
      <Spacer styleProp={styles.spacer} />
      <Slider setEmblaList={setEmblaApi} styleProp={styles.slider}>
        {slides.map((slide) => (
          <Slide key={slide.id}>
            <Container flex flexColumn gap="2">
              <Container flex flexRow alignCentered gap="4">
                <Container
                  styleProp={{ position: 'relative', maxWidth: '50%' }}
                >
                  <Image
                    alt="Hero"
                    src={selectedVariant.image}
                    width={164}
                    height={264}
                  />
                  <SlideNavigationButtons
                    totalSlides={slides.length}
                    handleScroll={handleScroll}
                    currentSlide={currentSlide}
                    containerStyleProp={styles.dots}
                    styleProps={[styles.dot1, styles.dot2, styles.dot3]}
                  />
                </Container>
                <video
                  muted
                  autoPlay
                  loop
                  playsInline
                  width={148}
                  height={190}
                  {...stylex.props(styles.video)}
                >
                  <source src={slide.mediaMobile} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </Container>
              <Typography as="h6" typographyTheme="h6Primary" fontBold>
                {slide.title}
              </Typography>
              <Typography as="p" typographyTheme="bodyLarge">
                {slide.description}
              </Typography>
            </Container>
          </Slide>
        ))}
      </Slider>
      <DotButtons
        scrollSnaps={scrollSnaps}
        currentSlide={selectedIndex}
        handleScroll={handleScroll}
        styleProp={styles.dotsNavigation}
        dotsStyleProp={styles.dotsNavigationButton}
        dotsSelectedStyleProp={styles.dotsNavigationButtonSelected}
      />
    </Container>
  )
}

export default SplitAnatomyMobile
