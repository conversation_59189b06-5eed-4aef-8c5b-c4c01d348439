import NextPrevButton from './SliderNextPrevButton'

import Container from '@/components/layout/Container'
import Spacer from '@/components/layout/Spacer'
import Typography from '@/components/Typography'
import { colors, fontSizes } from '@/app/themeTokens.stylex'
import Slider, { Slide } from '@/components/Generic/Slider'
import { SlideNavigationButtons } from '@/components/Generic/Slider/SliderButtons'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import React, { useState, useEffect } from 'react'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  wrapper: {
    display: {
      default: 'none',
      [DESKTOP]: 'flex',
    },
  },
  container: {
    position: 'relative',
    maxWidth: '1200px',
    marginInline: 'auto',
  },
  contentContainer: {
    width: 360,
  },
  header: {
    width: '17ch',
    maxWidth: '100%',
  },
  spacer: {
    height: '1px',
    backgroundColor: colors.navy,
  },
  slider: {
    width: '100%',
  },
  dots: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
  dot1: {
    position: 'absolute',
    width: 30,
    height: 30,
    top: 155,
    left: 140,
    outlineWidth: '6px',
  },
  dot2: {
    position: 'absolute',
    width: 30,
    height: 30,
    top: 263,
    left: 240,
    outlineWidth: '6px',
  },
  dot3: {
    position: 'absolute',
    width: 30,
    height: 30,
    top: 364,
    left: 80,
    outlineWidth: '6px',
  },
  video: {
    borderRadius: '12px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.navy,
    objectFit: 'cover',
  },
})

function SplitAnatomyDesktop({ slides, selectedVariant }: { slides: any[], selectedVariant: any }) {
  const [emblaApi, setEmblaApi] = useState<any[]>([])
  const [prevEnabled, setPrevEnabled] = useState(false)
  const [nextEnabled, setNextEnabled] = useState(false)
  const [currentSlide, setCurrentSlide] = useState(0)

  const onSelect = () => {
    if (!emblaApi.length) return
    setPrevEnabled(emblaApi[0].canScrollPrev())
    setNextEnabled(emblaApi[0].canScrollNext())
    setCurrentSlide(emblaApi[0].selectedScrollSnap())
  }

  useEffect(() => {
    if (!emblaApi.length) return
    onSelect()
    emblaApi[0].on('select', onSelect)
  }, [emblaApi])

  const handleScroll = (index: number) => () => {
    setCurrentSlide(index)
    if (emblaApi.length) {
      emblaApi[0].scrollTo(index)
    }
  }

  const ENABLED_OPACITY = 1
  const DISABLED_OPACITY = 0.5

  return (
    <Container
      flex
      flexRow
      flexCentered
      paddingBlock="8"
      paddingInline="2"
      styleProp={styles.wrapper}
    >
      <Container
        flex
        flexRow
        gap="8"
        alignCentered
        styleProp={styles.container}
      >
        <Container styleProp={{ position: 'relative' }}>
          <Image
            alt="Hero"
            src={selectedVariant.image}
            width={350}
            height={561}
          />
          <SlideNavigationButtons
            totalSlides={slides.length}
            handleScroll={handleScroll}
            currentSlide={currentSlide}
            containerStyleProp={styles.dots}
            styleProps={[styles.dot1, styles.dot2, styles.dot3]}
          />
        </Container>
        <Container flex flexColumn gap="3" styleProp={styles.contentContainer}>
          <Typography
            as="h4"
            typographyTheme="h4Secondary"
            styleProp={styles.header}
          >
            Non-Toxic Freshness That Stacks Up
          </Typography>
          <Spacer styleProp={styles.spacer} />
          <Slider setEmblaList={setEmblaApi} styleProp={styles.slider}>
            {slides.map((slide) => (
              <Slide key={slide.id}>
                <Container flex flexColumn gap="3">
                  <Container flex gap="2">
                    <Typography
                      as="h5"
                      typographyTheme="h5Primary"
                      fontBold
                      styleProp={{ fontSize: fontSizes.md }}
                    >
                      {slide.title}
                    </Typography>
                    <Typography as="p" typographyTheme="bodyLarge">
                      {slide.description}
                    </Typography>
                  </Container>
                  <video
                    muted
                    autoPlay
                    loop
                    playsInline
                    width={205}
                    height={198}
                    {...stylex.props(styles.video)}
                  >
                    <source src={slide.media} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </Container>
              </Slide>
            ))}
          </Slider>
          <Container flex flexRow gap="2">
            <NextPrevButton
              onClick={() => emblaApi.length && emblaApi[0].scrollPrev()}
              styleProp={{
                opacity: prevEnabled ? ENABLED_OPACITY : DISABLED_OPACITY,
              }}
            />
            <NextPrevButton
              onClick={() => emblaApi.length && emblaApi[0].scrollNext()}
              styleProp={{
                transform: 'rotate(180deg)',
                opacity: nextEnabled ? ENABLED_OPACITY : DISABLED_OPACITY,
              }}
            />
          </Container>
        </Container>
      </Container>
    </Container>
  )
}

export default SplitAnatomyDesktop
