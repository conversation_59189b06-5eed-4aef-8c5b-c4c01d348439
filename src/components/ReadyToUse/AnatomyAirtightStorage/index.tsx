'use client'

import SplitAnatomyDesktop from './Desktop'
import SplitAnatomyMobile from './Mobile'
import { airtightStorageAnatomy } from './data'

import Container from '@/components/layout/Container'

import React from 'react'
import * as stylex from '@stylexjs/stylex'
import { useSearchParams } from 'next/navigation'

const styles = stylex.create({
  rootBg: (color: string) => ({
    backgroundColor: color,
  }),
})

function AnatomyAirtightStorage() {
  const params = useSearchParams()
  const color = params.get('color')
  const { variants } = airtightStorageAnatomy.product
  const selectedIndex = variants.findIndex((variant) => variant.slug === color) === -1 ? 0 : variants.findIndex((variant) => variant.slug === color)
  const selectedVariant = variants[selectedIndex] || variants[0]

  return (
    <Container
      theme="cream"
      styleProp={styles.rootBg(selectedVariant.backgroundColor)}
    >
      <SplitAnatomyDesktop
        slides={airtightStorageAnatomy.slides}
        selectedVariant={selectedVariant}
      />
      <SplitAnatomyMobile
        slides={airtightStorageAnatomy.slides}
        selectedVariant={selectedVariant}
      />
    </Container>
  )
}

export default AnatomyAirtightStorage
