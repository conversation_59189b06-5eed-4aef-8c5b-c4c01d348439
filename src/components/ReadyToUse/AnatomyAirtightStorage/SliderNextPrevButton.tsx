import { colors } from '@/app/themeTokens.stylex'

import { MouseEvent } from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  button: {
    display: 'inline-block',
    padding: '0',
    margin: '0',
    width: 28,
    height: 28,
  },
})

const NextPrevButton = ({ onClick, styleProp }: {
  onClick: (event: MouseEvent<HTMLButtonElement>) => void;
  styleProp?: {};
}) => (
  <button type="button" onClick={onClick} {...stylex.props(styles.button, styleProp)}>
    <svg width={28} height={28} fill="none" viewBox="0 0 40 40">
      <g clipPath="url(#a)">
        <rect width={40} height={40} rx={20} strokeWidth={2} stroke={colors.navy} />
        <path
          stroke={colors.navy}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={1.667}
          d="m23.333 30-10-9.67 10-9.521"
        />
      </g>
      <defs>
        <clipPath id="a">
          <rect width={40} height={40} rx={20} />
        </clipPath>
      </defs>
    </svg>
  </button>
)

export default NextPrevButton
