'use client'

import Container from '@/components/layout/Container'

import * as stylex from '@stylexjs/stylex'

export interface SectionSkeletonProps {
  itemCount: number
  columns: number
  cardHeight: number
  gutter: number
}

const styles = stylex.create({
  gridContainer: (columns: number, rowGap: number, minHeight: number) => ({
    display: 'grid',
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    rowGap: `${rowGap}px`,
    minHeight: `${minHeight}px`
  }),
  shimmerCell: (height: number) => ({
    height: `${height}px`
  })
})

export default function SectionSkeleton({
  itemCount,
  columns,
  cardHeight,
  gutter
}: SectionSkeletonProps) {
  const rows = Math.ceil(itemCount / columns)
  const placeholderHeight = rows * cardHeight + (rows - 1) * gutter
  const totalCells = rows * columns

  return (
    <Container styleProp={styles.gridContainer(columns, gutter, placeholderHeight)}>
      {Array.from({ length: totalCells }).map((_, i) => (
        <div
          // eslint-disable-next-line react/no-array-index-key
          key={`skeleton-cell-${i}`}
          className="shimmer"
          {...stylex.props(styles.shimmerCell(cardHeight))}
        />
      ))}
    </Container>
  )
}
