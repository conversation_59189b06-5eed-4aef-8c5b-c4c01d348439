import { colors } from '@/app/themeTokens.stylex'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

interface DividerProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: 'horizontal' | 'vertical'
  thickness?: string
  length?: string
  color?: string
}

const styles = stylex.create({
  base: {},
  horizontal: {
    height: '1px',
    width: '100%',
  },
  vertical: {
    width: '1px',
    height: '100%',
  },
})

export default function Divider({
  orientation = 'horizontal',
  thickness,
  length,
  color = colors.gray300,
  ...props
}: DividerProps) {
  const isHorizontal = orientation === 'horizontal'

  // Dynamically apply thickness and length
  const horizontalStyles = isHorizontal ? { height: thickness } : { width: thickness }
  const thicknessStyles = thickness ? horizontalStyles : {}

  const verticalStyles = isHorizontal ? { width: length } : { height: length }
  const lengthStyles = length ? verticalStyles : {}

  const inlineStyles = {
    backgroundColor: color,
    ...thicknessStyles,
    ...lengthStyles,
  }
  return (
    <div
      role="separator"
      {...stylex.props(
        styles.base,
        isHorizontal ? styles.horizontal : styles.vertical,
      )}
      style={inlineStyles}
      {...props}
    />
  )
}
