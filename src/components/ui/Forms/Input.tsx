import {
  spacing,
  colors,
  globalTokens as $,
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  base: {
    paddingBlock: spacing.sm,
    paddingInline: spacing.sm,
    borderColor: {
      default: colors.gray300,
      ':active': colors.babyBlue300,
    },
    borderWidth: '1px',
    borderStyle: 'solid',
    borderRadius: $.borderRadius,
  }
})

const Input = ({
  id,
  placeholder,
  value,
  event,
  type = 'text',
  required = false,
}: {
  id: string,
  placeholder: string,
  value: string,
  event: (event: React.ChangeEvent<HTMLInputElement>) => void
  type?: 'text' | 'email' | 'password'
  required?: boolean
}) => (
  <input
    id={id}
    name={id}
    type={type}
    placeholder={placeholder}
    value={value}
    onChange={event}
    required={required}
    {...stylex.props(styles.base)}
  />
)

export default Input
