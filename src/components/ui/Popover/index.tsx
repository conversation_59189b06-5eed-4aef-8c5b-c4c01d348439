'use client'

import { ReactNode, useState } from 'react'
import * as PopoverPrimitive from '@radix-ui/react-popover'
import stylex from '@stylexjs/stylex'

interface PopoverContainerProps {
  children: ReactNode;
  hover?: boolean;
}

export const PopoverContainer = ({ children, hover = true }: PopoverContainerProps) => {
  const [isOpen, setIsOpen] = useState(false)

  const handleMouseEnter = () => {
    if (hover) {
      setIsOpen(true)
    }
  }

  const handleMouseLeave = () => {
    if (hover) {
      setIsOpen(false)
    }
  }

  return (
    <PopoverPrimitive.Root open={isOpen} onOpenChange={setIsOpen}>
      <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
        {children}
      </div>
    </PopoverPrimitive.Root>
  )
}

interface PopoverTriggerProps {
  children: ReactNode
}
export const PopoverTrigger = ({ children }: PopoverTriggerProps) => (
  <PopoverPrimitive.Trigger asChild>
    {children}
  </PopoverPrimitive.Trigger>
)

export interface PopoverContentProperties {
  hideArrow?: boolean;
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  sideOffset?: number;
  arrowProps?: {
    width?: number;
    height?: number;
    fill?: string;
  };
  contentStyleProps?: {};
}
interface PopoverContentProps extends PopoverContentProperties {
  children: ReactNode;
}
const defaultOffset = 10
export const PopoverContent = ({
  children,
  side = 'bottom',
  align = 'center',
  sideOffset = defaultOffset,
  contentStyleProps,
  arrowProps,
  hideArrow
}: PopoverContentProps) => (
  <PopoverPrimitive.Portal>
    <PopoverPrimitive.Content
      side={side}
      align={align}
      sideOffset={sideOffset}
      {...stylex.props(contentStyleProps)}
    >
      {children}
      {!hideArrow && <PopoverPrimitive.Arrow {...arrowProps} />}
    </PopoverPrimitive.Content>
  </PopoverPrimitive.Portal>
)
