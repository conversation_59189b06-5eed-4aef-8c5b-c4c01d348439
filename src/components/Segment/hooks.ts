'use client'

import combineCartAttributes from '@utils/combineCartAttributes'
import getCookie from '@utils/getCookie'

import { useEffect } from 'react'
import { useCart } from '@shopify/hydrogen-react'

import type { Attribute } from '@shopify/hydrogen-react/storefront-api-types'

const GA_COOKIE_PREFIX_LENGTH = 6

const useSegmentIdentifiers = () => {
  const {
    attributes,
    cartAttributesUpdate,
    status
  } = useCart()

  useEffect(() => {
    if (status !== 'idle') return

    const newAttributes = []
    const googleClientId = getCookie('_ga')?.substring(GA_COOKIE_PREFIX_LENGTH)
    const segmentAnonymousId = getCookie('ajs_anonymous_id')

    if (segmentAnonymousId) {
      newAttributes.push({
        // TODO: Update to '_cdpAnonymousId' when upgrading source function
        key: 'segmentId',
        value: segmentAnonymousId,
      })
    }

    if (googleClientId) {
      newAttributes.push({
        // TODO: Update to '_googleClientId' when upgrading source function
        key: 'googleClientId',
        value: googleClientId,
      })
    }

    // Check if any attributes need to be updated
    const requiresUpdate = newAttributes.some(({ key, value }) => {
      const cartAttribute = attributes?.find(
        (attr) => attr?.key === key,
      )
      return !cartAttribute || cartAttribute.value !== value
    })

    if (!requiresUpdate) return

    // Combine new cart attributes with existing cart attributes
    const combinedAttributes = combineCartAttributes(
      attributes as Attribute[] | undefined,
      newAttributes,
    )

    cartAttributesUpdate(combinedAttributes)
  }, [attributes, cartAttributesUpdate, status])
}

export default useSegmentIdentifiers
