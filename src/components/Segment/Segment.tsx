'use client'

import useSegmentIdentifiers from './hooks'

import { useAppDispatch } from '@redux/hooks'
import { pageViewed } from '@redux/features/events/eventsSlice'

import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect, Suspense } from 'react'

const pageTypes = {
  '/products': 'product',
  '/collections': 'collections',
  '/blog': 'blog',
  '/article': 'blog',
  '/cart': 'cart',
  '/search': 'search',
  '/404': '404',
}

const pages = Object.keys(pageTypes)
const names = Object.values(pageTypes)

const PageEvent = () => {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const dispatch = useAppDispatch()

  useSegmentIdentifiers()

  useEffect(() => {
    let match = false

    if (!pathname) {
      return
    }

    if (pathname === '/') {
      dispatch(pageViewed('home'))
    } else {
      pages.map((slug, index) => {
        if (pathname.includes(slug) && !match) {
          dispatch(pageViewed(names[index]))
          match = true
        }
        return null
      })
      if (!match) {
        dispatch(pageViewed({}))
      }
    }
  }, [pathname, searchParams, dispatch])

  return null
}

const Page = () => (
  <Suspense>
    <PageEvent />
  </Suspense>
)

export default Page
