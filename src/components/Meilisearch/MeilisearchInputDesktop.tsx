'use client'

import Container from '../layout/Container'

import { breakpoints, colors } from '@/app/themeTokens.stylex'

import { useRouter } from 'next/navigation'
import { useState, KeyboardEvent } from 'react'
import * as stylex from '@stylexjs/stylex'

const DESKTOP_MEDIA_QUERY = '@media (min-width: 1024px)'

const styles = stylex.create({
  wrapper: {
    display: {
      default: 'none',
      [DESKTOP_MEDIA_QUERY]: 'flex'
    },
    alignItems: 'center',
    justifyContent: 'space-between',
    maxWidth: breakpoints.sm,
    width: '100%',
    background: '#F1F1F0',
    borderRadius: '9999px',
    paddingInline: '27px',
  },
  desktopSearchInput: {
    width: '100%',
    height: '52px',
    borderStyle: 'none',
    backgroundColor: '#F1F1F0',
    color: colors.navy,
    outline: {
      default: 'none',
      ':focus': 'none'
    },
    paddingLeft: '20px',
    fontSize: '16px',
    opacity: '0.5'
  },
  activeInput: {
    opacity: '1'
  },
  searchBarIcon: {
    flexShrink: 0,
    cursor: {
      ':hover': 'pointer'
    },
    opacity: '0.5'
  },
  xIcon: {
    flexShrink: 0,
    cursor: {
      ':hover': 'pointer'
    }
  },
  hovered: {
    opacity: '1'
  }
})

const MeilisearchInputDesktop = () => {
  const router = useRouter()
  const [query, setQuery] = useState('')
  const [hovered, setHovered] = useState(false)

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      router.push(`/results/?query=${query}`)
      setQuery('')
    }
  }

  const handleSearchIconClick = () => {
    if (query) {
      router.push(`/results/?query=${query}`)
      setQuery('')
    }
  }

  const handleXIconClick = () => {
    setQuery('')
  }

  return (
    <Container as="div" styleProp={styles.wrapper} onMouseOver={() => setHovered(true)} onMouseOut={() => setHovered(false)}>
      {/* eslint-disable-next-line jsx-a11y/label-has-associated-control  */}
      <label htmlFor="search-input-desktop">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          {...stylex.props(styles.searchBarIcon, hovered && styles.hovered, !!query && styles.activeInput)}
          onClick={handleSearchIconClick}
        >
          <circle cx="10.0244" cy="9.52437" r="8.02437" stroke="#1F3438" strokeWidth="1" />
          <path d="M15.5706 16.3681L22.0607 22.8582" stroke="#1F3438" strokeWidth="1" strokeLinecap="round" />
        </svg>
      </label>

      <input
        type="text"
        id="search-input-desktop"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Search products"
        maxLength={50}
        {...stylex.props(styles.desktopSearchInput, hovered && styles.hovered, !!query && styles.activeInput)}
      />

      {query && (
        <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 23 23" fill="none" {...stylex.props(styles.xIcon)} onClick={handleXIconClick}>
          <path d="M7.66455 7.66655L15.3312 15.3332" stroke="#535353" strokeWidth="1.5" />
          <path d="M15.335 7.66655L7.66829 15.3332" stroke="#535353" strokeWidth="1.5" />
        </svg>
      )}
    </Container>
  )
}

export default MeilisearchInputDesktop
