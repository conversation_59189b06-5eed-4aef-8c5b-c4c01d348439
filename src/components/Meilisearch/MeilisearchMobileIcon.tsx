import React from 'react'
import * as stylex from '@stylexjs/stylex'

type MSMobileIconProps = {
  toggleMenu: () => void;
}

const DESKTOP_MEDIA_QUERY = '@media (min-width: 1024px)'
const styles = stylex.create({
  mobileSearchInput: {
    display: {
      default: 'block',
      [DESKTOP_MEDIA_QUERY]: 'none'
    },
  }
})

const MeilisearchMobileIcon = ({ toggleMenu }: MSMobileIconProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" {...stylex.props(styles.mobileSearchInput)} onClick={toggleMenu}>
    <circle cx="10.0244" cy="9.52437" r="8.02437" stroke="#1F3438" strokeWidth="1" />
    <path d="M15.5706 16.3681L22.0607 22.8582" stroke="#1F3438" strokeWidth="1" strokeLinecap="round" />
  </svg>
)

export default MeilisearchMobileIcon
