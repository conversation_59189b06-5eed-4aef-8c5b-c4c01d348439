import {
  globalTokens as $,
  spacing,
} from '@/app/themeTokens.stylex'
import CallToAction from '@components/Generic/CallToAction'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

const styles = stylex.create({
  hero: {
    display: 'flex',
    gap: 4,
    alignItems: 'center',
    margin: '0 auto',
    minHeight: '700px',
    height: '65vh',
  },
  item: {
    position: 'relative',
    width: '50%',
    height: '100%',
    padding: $.pageGap
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    maxWidth: '550px',
    gap: spacing.md,
    height: '100%',
    textAlign: 'left',
  },
})

const Hero = () => (
  <Container as="section" theme="navy">
    <div {...stylex.props(styles.hero)}>
      <div {...stylex.props(styles.item)}>
        <div {...stylex.props(styles.content)}>
          <Typography as="span" typographyTheme="subheading">
            New Arrivals
          </Typography>
          <Typography as="h1" typographyTheme="h1Secondary">
            Kitchen Gadgets Get an Upgrade
          </Typography>
          <Typography as="p" typographyTheme="bodyLarge">
            Introducing the first all-stainless kitchen tool set, designed to last.
          </Typography>
          <CallToAction variant="secondary" href="/collections">
            View Product
          </CallToAction>
          <CallToAction variant="primary" href="/demo-page">
            View Demo Landing Page
          </CallToAction>
        </div>
      </div>
      <div {...stylex.props(styles.item)}>
        <Image
          alt="Hero"
          src="/assets/kitchen-gadgets-hero.jpg"
          fill
          style={{
            objectFit: 'cover',
          }}
          sizes="(min-width: 1024px) 50vw"
        />
      </div>
    </div>
  </Container>
)

export default Hero
