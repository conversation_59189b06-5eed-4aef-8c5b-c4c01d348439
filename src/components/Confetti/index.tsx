'use client'

import { ConfettiContext } from '@/hooks/useConfetti'

import ReactConfetti from 'react-confetti'
import {
  useCallback,
  useState,
  useMemo,
  useEffect
} from 'react'
import stylex from '@stylexjs/stylex'

const styles = stylex.create({
  overlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    pointerEvents: 'none',
    zIndex: 9999,
  },
})

const HOLIDAY_COLORS = ['#5E6C51', '#F3C559']

type ConfettiProviderProps = {
  children: React.ReactNode
}

const TIMEOUT = 5000

const ConfettiProvider = ({ children }: ConfettiProviderProps) => {
  const [isActive, setIsActive] = useState(false)
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null)
  const [size, setSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  })

  const fire = useCallback(() => {
    setIsActive(true)
    if (timeoutId) clearTimeout(timeoutId)
    const newTimeout = setTimeout(() => setIsActive(false), TIMEOUT)
    setTimeoutId(newTimeout)
  }, [timeoutId])

  const value = useMemo(() => ({ fire }), [fire])

  useEffect(() => {
    if (typeof window === 'undefined') {
      return undefined
    }
    const handleResize = () => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight
      })
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return (
    <ConfettiContext.Provider value={value}>
      <div {...stylex.props(styles.overlay)}>
        {isActive && (
          <ReactConfetti
            colors={HOLIDAY_COLORS}
            numberOfPieces={600}
            width={size.width}
            height={size.height}
            gravity={0.2}
            recycle={false}
            onConfettiComplete={() => setIsActive(false)}
          />
        )}
      </div>
      {children}
    </ConfettiContext.Provider>
  )
}

export default ConfettiProvider
