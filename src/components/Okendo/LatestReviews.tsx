import ReadAllReviewsLink from './ReadAllReviewsLink'

import Rating from '@components/Generic/Rating'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import { colors, spacing } from '@/app/themeTokens.stylex'
import { Review } from '@/lib/okendo/types'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  container: {
    marginBlockStart: spacing.md,
  },
  review: {
    display: {
      default: 'flex',
    },
    flexDirection: {
      default: 'column',
      '@media (min-width: 768px)': 'row',
    },
    marginBlockEnd: 24,
  },
  reviewName: {
    marginBlockEnd: {
      default: 4,
      '@media (min-width: 768px)': 8,
    }
  },
  reviewTitle: {
    marginBlockEnd: 8,
    fontWeight: 'bolder',
  },
  reviewMeta: {
    flex: '1',
  },
  reviewContent: {
    flex: '3',
  },
  verified: {
    color: colors.gray,
    marginBlockEnd: {
      default: 16,
      '@media (min-width: 768px)': 0,
    },
  }
})

type LatestReviewsProps = {
  reviews: Review[];
};

const LatestReviews = ({ reviews }: LatestReviewsProps) => {
  if (!reviews) {
    return null
  }

  return (
    <Container as="div" styleProp={styles.container}>
      {reviews.map((review) => (
        <Container
          as="div"
          flex
          flexRow
          key={review.id}
          styleProp={styles.review}
        >
          <Container as="div" styleProp={styles.reviewMeta}>
            <Rating
              iconColor="marigold"
              rating={review.rating}
              textContentOn="none"
            />
            <Typography
              as="p"
              typographyTheme="captionLarge"
              marginBottom="xs"
              styleProp={styles.reviewName}
            >
              {review.name}
            </Typography>
            {review.verified && (
              <Typography
                as="p"
                typographyTheme="captionLarge"
                styleProp={styles.verified}
              >
                Verified
              </Typography>
            )}
          </Container>
          <Container
            as="div"
            styleProp={styles.reviewContent}
          >
            <Typography
              as="p"
              size="sm"
              styleProp={styles.reviewTitle}
              marginBottom="xs"
            >
              {review.title}
            </Typography>
            <Typography
              as="p"
              size="sm"
              lineHeight="lg"
            >
              {review.body}
            </Typography>
          </Container>
        </Container>
      ))}
      <ReadAllReviewsLink />
    </Container>
  )
}

export default LatestReviews
