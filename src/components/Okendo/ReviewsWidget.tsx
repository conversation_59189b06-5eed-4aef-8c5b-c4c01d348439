'use client'

import Wrapper from '@components/layout/Wrapper'

import { useEffect, useRef } from 'react'

type OkendoReviewsWidgetProps = {
  productId?: string;
};

const OkendoReviewsWidget = ({ productId }: OkendoReviewsWidgetProps) => {
  const widgetContainer = useRef(null)

  useEffect(() => {
    const initializeWidget = () => {
      if (!window.okeWidgetApi.initWidget) {
        return
      }

      window.okeWidgetApi.initWidget(widgetContainer.current)
    }

    if (window.okeWidgetApi?.initWidget) {
      initializeWidget()
    } else {
      document.addEventListener('oke-script-loaded', initializeWidget)
    }

    return () => {
      document.removeEventListener('oke-script-loaded', initializeWidget)
    }
  }, [productId])

  return (
    <Wrapper as="section" size="5" pageGap paddingBlock="2">
      {productId ? (
        <div
          ref={widgetContainer}
          data-oke-widget
          data-oke-reviews-product-id={`shopify-${productId}`}
        />
      ) : (
        <div
          ref={widgetContainer}
          data-oke-widget
        />
      )}
    </Wrapper>
  )
}

export default OkendoReviewsWidget
