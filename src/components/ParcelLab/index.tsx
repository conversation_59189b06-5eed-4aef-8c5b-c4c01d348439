'use client'

import Spinner from '@components/Generic/Icon/lib/Spinner'
import ParcelLab from '@scripts/parcelLab'

import { useEffect } from 'react'

const styles = `
  div#parcellab-track-and-trace-ui-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
  }
  .pl-space-bottom.pl-branding {
    display: none;
  }

`

const addStyles = () => {
  const styleSheet = document.createElement('style')
  styleSheet.innerText = styles
  document.head.appendChild(styleSheet)
}

const Parcel = () => {
  useEffect(() => {
    addStyles()
  }, [])
  return (
    <div
      id="parcellab-track-and-trace"
      style={{
        minHeight: '500px',
        display: 'grid',
        alignItems: 'center',
        padding: '0 20px',
        width: '100%',
      }}
    >
      <Spinner style={{ margin: '0 auto ' }} />
      <ParcelLab />
    </div>

  )
}

export default Parcel
