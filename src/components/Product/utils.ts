import { ContentfulProduct } from '@/lib/contentful/types/products'
import { fetchVariantAvailability } from '@/lib/shopify/fetchAvailability'
import { getProductUrl, ProductVariantParams, SUPPORTED_ATTRIBUTES } from '@/utils/urls'

import { ReadonlyURLSearchParams } from 'next/navigation'

export type ProductSearchParams = ProductVariantParams;

/**
 * @deprecated Use getProductUrl from @/utils/urlUtils instead
 */
export function buildProductUrl(
  product: ContentfulProduct,
  urlParams?: ProductSearchParams,
  existingUrlParams?: ReadonlyURLSearchParams,
) {
  const url = `/products/${product.slug}`
  const supportedAttributes = SUPPORTED_ATTRIBUTES

  type AttributeURLParam = `${(typeof supportedAttributes)[number]}=${string}`;

  const params: AttributeURLParam[] = []

  supportedAttributes.forEach((attribute) => {
    if (urlParams?.[attribute]) {
      params.push(`${attribute}=${urlParams[attribute]}`)
    }

    if (existingUrlParams?.get(attribute) && !urlParams?.[attribute]) {
      params.push(`${attribute}=${existingUrlParams.get(attribute)}`)
    }
  })

  if (params.length === 0) {
    return url
  }

  return `${url}?${params.join('&')}`
}

export async function getOOSSwatches(variants: any[]) {
  const results = await Promise.all(variants.map(async (variant) => {
    const availableForSale = await fetchVariantAvailability(variant.variantId)
    if (availableForSale === false) {
      return variant?.swatch?.slug
    }
    return null
  }))

  return results.filter(Boolean)
}

export { getProductUrl }
