'use client'

import { renderCtaStyles } from '../AddToCart/utils'

import CallToAction from '@/components/Generic/CallToAction'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'
import useVariantAvailability from '@/hooks/getVariantAvailability'

import type { ContentfulBlockCallToAction } from '@/lib/contentful/types/blocks'

type AddToCartOverrideProps = {
  cta: ContentfulBlockCallToAction,
  variant: ContentfulProductVariant,
  fullWidth?: boolean,
}

// eslint-disable-next-line complexity
const AddToCartOverride = ({
  cta,
  variant,
  fullWidth = false
}: AddToCartOverrideProps) => {
  const variantId = variant?.variantId ? Number(variant.variantId) : 0
  const { data: available, isLoading } = useVariantAvailability(variantId)

  if (!cta || cta.__typename !== 'BlockCallToAction') {
    return null
  }

  if (!variant) {
    return null
  }

  const variantAvailability = variant.variantAvailability || 'Unavailable'
  const isOOS = variantAvailability === 'Sold Out' || variantAvailability === 'Unavailable' || !available
  const ctaText = !isOOS ? cta.text : 'Sold Out'
  const availability = !isOOS ? variantAvailability : 'Sold Out'

  return (
    <CallToAction
      styleProp={renderCtaStyles(isLoading ? 'Loading' : availability)}
      href={available ? `/${cta?.page?.slug}` : undefined}
      fullWidth={fullWidth}
      variant="primary"
    >
      {isLoading ? 'Loading' : ctaText}
    </CallToAction>
  )
}

export default AddToCartOverride
