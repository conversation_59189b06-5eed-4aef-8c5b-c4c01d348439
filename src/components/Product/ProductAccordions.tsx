import Popover from '@/components/Generic/Popover'
import Close from '@components/Generic/Icon/lib/Close'
import { SingleOpenedAccordion as Accordion } from '@components/Generic/Accordion'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import {
  spacing,
  defaultTheme as $T,
  globalTokens as $
} from '@/app/themeTokens.stylex'
import { ContentfulProductDetailBlockData } from '@/lib/contentful/fetchProducts'
import RichTextRenderer from '@/utils/RichTextRenderer'
import { useSingleAccordionReturn } from '@/hooks/useSingleAccordion'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import React from 'react'

const styles = stylex.create({
  overviewContainer: {
    gap: '16px',
    display: 'grid',
    gridTemplateColumns: {
      default: 'repeat(2, 1fr)',
      '@media (max-width: 400px)': '1fr',
    },
  },
  overviewContainerPopover: {
    justifyContent: 'start',
  },
  overviewItems: {
    width: '100%',
  },
  close: {
    borderRadius: '50%',
    backgroundColor: $T.primarySurface
  },
  listContainer: {
    borderRadius: $.borderRadiusSmall,
    paddingInline: spacing.md,
    rowGap: spacing.sm,
    marginTop: '24px'
  },
  listItems: {
    borderRadius: '40px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: $T.indicatorInactive,
    backgroundColor: $T.secondaryCTASurface,
    cursor: 'default',
    userSelect: 'none'
  },
  titleContent: {
    textDecoration: 'underline',
    cursor: 'pointer',
    color: {
      default: 'inherit',
      ':hover': '#737373',
    }
  },
  imageContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  }
})

type AccordionProps = {
  block: ContentfulProductDetailBlockData,
  accordionState: useSingleAccordionReturn
};

export const OverviewAccordion = ({ block, accordionState }: AccordionProps) => {
  const {
    title,
    content,
    referencesCollection
  } = block

  return (
    <Accordion {...accordionState} key={title} title={title || 'Details'}>
      <Container as="div" flex flexRow gap="sm">
        {referencesCollection?.length && (
          <Container
            flex
            flexRow
            styleProp={styles.overviewContainer}
            gap="xxs"
          >
            {referencesCollection.map((reference: any, i: number) => {
              const hasPopover = reference.content?.json?.content?.length > 0

              const referenceContent = <RichTextRenderer content={reference?.content?.json} />

              const contentWithin = (
                <Container
                  as="div"
                  flex
                  flexRow
                  noWrap
                  alignCentered
                  key={reference.reference}
                  gap="2"
                  styleProp={[styles.overviewItems, reference.content && styles.titleContent]}
                >
                  <Image
                    key={reference.reference}
                    src={reference.asset[0].url}
                    alt={reference.reference || ''}
                    width="50"
                    height="50"
                  />
                  <Typography as="p" typographyTheme="bodyLarge">
                    {reference.reference}
                  </Typography>
                </Container>
              )

              if (hasPopover) {
                return (
                  <React.Fragment key={`OverviewAccordion-${i.toString()}`}>
                    <Popover content={referenceContent}>
                      { contentWithin }
                    </Popover>
                  </React.Fragment>
                )
              }

              return contentWithin
            })}
          </Container>
        )}
        {content && (
          <Container flex>
            {content}
          </Container>
        )}
      </Container>
    </Accordion>
  )
}

export const NonToxicMaterialsAccordion = ({ block, accordionState }: AccordionProps) => {
  const {
    title,
    content,
    referencesCollection
  } = block

  return (
    <Accordion {...accordionState} key={title} title={title || 'Missing accordion title'}>
      {content}
      {referencesCollection && (
        <Container
          as="div"
          flex
          gap="sm"
          paddingBlock="3"
          theme="gray100"
          styleProp={styles.listContainer}
        >
          <Typography
            as="p"
            typographyTheme="bodyLarge"
          >
            {block.referencesCollection?.[0].reference}
          </Typography>
          <Container
            as="div"
            flex
            flexRow
            gap="xs"
          >
            {referencesCollection[0].referencesCollection.map((reference: any, i: number) => {
              const referenceContent = <RichTextRenderer content={reference?.content?.json} />
              const hasPopoverContent = !!referenceContent?.props?.content

              const children = (
                <React.Fragment key={`NonToxicMaterialsAccordion-${i.toString()}`}>
                  <Container
                    as="div"
                    flex
                    flexRow
                    alignCentered
                    gap="1"
                    paddingBlock="sm"
                    paddingInline="sm"
                    styleProp={[styles.listItems, reference.content && styles.titleContent]}
                    theme="gray100"
                    key={reference.title}
                  >
                    <Container
                      flex
                      size="lg"
                      styleProp={styles.close}
                      paddingBlock="xs"
                      paddingInline="xs"
                      theme="navy"
                    >
                      <Close size="extraSmall" />
                    </Container>
                    <Typography
                      as="p"
                      typographyTheme="bodySmall"
                      capitalize
                    >
                      {reference.title}
                    </Typography>
                  </Container>
                </React.Fragment>
              )

              if (hasPopoverContent) {
                return (
                  <React.Fragment key={`NonToxicMaterialsAccordion-${i.toString()}`}>
                    <Popover content={referenceContent} hideArrow={false}>
                      {children}
                    </Popover>
                  </React.Fragment>
                )
              }

              return (
                <React.Fragment key={`NonToxicMaterialsAccordion-${i.toString()}`}>
                  {children}
                </React.Fragment>
              )
            })}
          </Container>
        </Container>
      )}
    </Accordion>
  )
}
