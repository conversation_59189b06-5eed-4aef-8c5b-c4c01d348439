'use client'

import QuantityToggleItem from './QuantityToggleItem'

import { useAppDispatch } from '@/redux/hooks'
import { setQuantity } from '@/redux/features/product/productSlice'
import Typography from '@/components/Typography'
import Container from '@/components/layout/Container'
import { spacing } from '@/app/themeTokens.stylex'
import { slugify } from '@/utils/regex'
import variantAttribute from '@/utils/variants'
import { parseVariantParams } from '@/utils/urls'

import React, { useEffect } from 'react'
import * as stylex from '@stylexjs/stylex'
import { useParams } from 'next/navigation'

import type { ProductQuantityToggleProps } from './types'

const styles = stylex.create({
  quantityToggle: {
    marginBlockStart: {
      default: 0,
      '@media (min-width: 1024px)': '28px',
    },
    marginBlockEnd: spacing.sm,
  },
})

const ProductQuantityToggle = ({
  quantityToggles,
  product,
  variant
}: ProductQuantityToggleProps) => {
  const dispatch = useAppDispatch()
  const params = useParams()

  // Get quantity from variantAttribute or fallback to URL params
  const activeQuantitySlug = variantAttribute.get('quantity') ||
    parseVariantParams(params.variants as string)?.quantity

  const activeQuantity = quantityToggles.find(
    (toggle) => slugify(toggle.slug) === activeQuantitySlug
  )

  useEffect(() => () => {
    dispatch(setQuantity(1))
  }, [dispatch])

  useEffect(() => {
    if (activeQuantity) {
      dispatch(setQuantity(activeQuantity.quantity))
    }
  }, [activeQuantity, dispatch])

  if (quantityToggles.length === 0) return null

  const quantityPresentation = activeQuantity?.presentation || quantityToggles[0]?.presentation

  return (
    <Container flex gap="2" styleProp={styles.quantityToggle}>
      <Typography as="span" typographyTheme="bodySmall">
        <strong>Quantity:</strong> {quantityPresentation}
      </Typography>
      <Container as="div" flex flexRow gap="1">
        {quantityToggles.map((item) => (
          <QuantityToggleItem
            key={item.slug}
            item={item}
            product={product}
            variant={variant}
            activeQuantitySlug={activeQuantitySlug}
          />
        ))}
      </Container>
    </Container>
  )
}

export default ProductQuantityToggle
