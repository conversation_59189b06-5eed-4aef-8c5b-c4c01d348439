import { spacing } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@/components/layout/Container'
import ProductLink from '@/components/Product/ProductLink'
import SwatchText from '@/components/Product/ProductSwatches/SwatchText'
import Callout from '@/components/Generic/Callout'
import { toCamelCase, slugify } from '@/utils/regex'

import * as stylex from '@stylexjs/stylex'

import type { QuantityToggleItemProps } from './types'

const styles = stylex.create({
  quantityToggle: {
    display: 'block',
    flex: `0 calc(25% - calc(${spacing.sm} / 2))`,
  },
  hasCallout: {
    flex: '1',
    display: 'grid',
  },
  isCalloutWithCallout: {
    gridArea: '1/1'
  },
  isCallout: {
    gridArea: '1/1',
    width: 'fit-content',
    placeSelf: 'start center',
    translate: '0 -50%'
  },
  toggleStyleProp: {
    paddingTop: spacing.sm,
    paddingBottom: spacing.sm,
  },
  calloutStyleProp: {
    display: {
      default: 'none',
      '@media (min-width: 375px)': 'block'
    }
  }
})

const QuantityToggleItem = ({
  item,
  product,
  variant,
  activeQuantitySlug
}: QuantityToggleItemProps) => {
  const hasCallout = item.calloutsCollection?.items?.[0]

  const productLink = (
    <ProductLink
      product={product}
      variant={variant}
      urlParams={{ quantity: slugify(item.slug) }}
      key={item.slug}
      styleProp={[styles.quantityToggle, hasCallout && styles.isCalloutWithCallout]}
      scroll={false}
    >
      <SwatchText
        swatch={item}
        attribute="quantity"
        selectedSwatch={activeQuantitySlug}
        styleProp={styles.toggleStyleProp}
      />
    </ProductLink>
  )

  if (!hasCallout) return productLink

  const calloutCollection = item.calloutsCollection?.items[0]
  const { title, settings } = calloutCollection || {}

  return (
    <Container styleProp={styles.hasCallout}>
      {productLink}
      <Container styleProp={[styles.isCallout, styles.calloutStyleProp]}>
        <Callout
          title={title}
          theme={settings?.theme && toCamelCase(settings.theme) as ThemeColors}
          size="small"
        />
      </Container>
    </Container>
  )
}

export default QuantityToggleItem
