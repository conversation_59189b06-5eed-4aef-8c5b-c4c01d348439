import { ContentfulProduct, ContentfulProductQuantityToggle } from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

export type ProductQuantityToggleProps = {
  quantityToggles: ContentfulProductQuantityToggle[]
  product: ContentfulProduct
  variant: ContentfulProductVariant
}

export type QuantityToggleItemProps = {
  item: ContentfulProductQuantityToggle
  product: ContentfulProduct
  variant: ContentfulProductVariant
  activeQuantitySlug?: string
}
