'use client'

import Container from '../layout/Container'
import Typography from '../Typography'
import SoundOnOffButton from '../Generic/SoundOnOffButton'

import React, { useRef, useState } from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  container: {
    padding: {
      default: '36px 20px 22px 20px',
      '@media (min-width: 415px)': '70px 20px 25px 20px',
    }
  },
  subheader: {
    marginBottom: '16px',
    textAlign: 'center'
  },
  header: {
    marginBottom: '20px',
    textAlign: 'center'
  },
  content: {
    marginBottom: '50px',
    textAlign: 'center'
  },
  videoContainer: {
    position: 'relative',
  },
  video: {
    cursor: 'pointer',
    width: '100%',
    maxWidth: '960px',
    height: '100%'
  },
  desktopVideo: {
    display: {
      default: 'none',
      '@media (min-width: 415px)': 'block',
    },
    width: '100%',
    height: 'auto'
  },
  mobileVideo: {
    display: {
      default: 'block',
      '@media (min-width: 415px)': 'none',
    },
    width: '100%',
    height: 'auto'
  },
  player: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    background: 'rgba(0, 0, 0, 0.5)',
    borderWidth: '0',
    borderStyle: 'none',
    borderColor: 'transparent',
    borderRadius: '50%',
    width: '60px',
    height: '60px',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  pauseButton: {
    position: 'absolute',
    pointerEvents: 'initial',
    cursor: 'pointer',
    right: '69px',
    bottom: '15px',
    zIndex: 1,
    background: 'rgba(0, 0, 0, 0.5)',
    borderRadius: '50%',
    padding: '11px',
    borderWidth: '0',
    borderStyle: 'none',
    borderColor: 'transparent',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  soundIcon: {
    position: 'absolute',
    pointerEvents: 'initial',
    cursor: 'pointer',
    right: '15px',
    bottom: '15px',
    zIndex: 1,
    background: 'rgba(0, 0, 0, 0.5)',
    borderRadius: '50%',
    padding: '8px',
  }
})

const VideoHeader = ({ subheader, header }: any) => (
  <Container flex flexCentered>
    {subheader && (
      <Typography
        as="p"
        typographyTheme="subheading"
        styleProp={styles.subheader}
      >
        {subheader}
      </Typography>
    )}
    {header && (
      <Typography
        as="h2"
        typographyTheme="h2Secondary"
        typographyThemeMobile="h4Secondary"
        styleProp={styles.header}
      >
        {header}
      </Typography>
    )}
  </Container>
)

const VideoElement = ({
  videoSource,
  styleProps,
  image
}: any) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  const handlePlayPause = () => {
    const vid = videoRef.current
    if (!vid) return

    if (vid.paused) {
      vid.play()
      setIsPlaying(true)
    } else {
      vid.pause()
      setIsPlaying(false)
    }
  }

  const handleMute = () => {
    const vid = videoRef.current
    if (vid) {
      vid.muted = !isMuted
      setIsMuted(!isMuted)
    }
  }

  return (
    <Container styleProp={styleProps}>
      <video
        src={videoSource.url}
        {...(image?.url && { poster: image.url })}
        {...stylex.props(styles.video)}
        controls={false}
        muted={isMuted}
        onClick={handlePlayPause}
        aria-label={videoSource.fileName}
        ref={videoRef}
      />
      {!isPlaying ? (
        <button
          onClick={handlePlayPause}
          type="button"
          aria-label="Play Video"
          {...stylex.props(styles.player)}
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="white"
          >
            <path d="M8 5v14l11-7z" />
          </svg>
        </button>
      ) : (
        <>
          <button
            onClick={handlePlayPause}
            type="button"
            aria-label="Pause Video"
            {...stylex.props(styles.pauseButton)}
          >
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="white"
            >
              <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
            </svg>
          </button>
          <SoundOnOffButton
            play_on={!isMuted}
            onClick={handleMute}
            styleProp={styles.soundIcon}
          />
        </>
      )}
    </Container>
  )
}

const VideoPlayer = ({
  video,
  mobileVideo,
  mobileImage,
  image
}: any) => (
  <div style={{ position: 'relative' }}>
    {mobileVideo?.url ? (
      <>
        <VideoElement
          videoSource={video}
          styleProps={styles.desktopVideo}
          image={image}
        />
        <VideoElement
          videoSource={mobileVideo}
          styleProps={styles.mobileVideo}
          image={mobileImage || image}
        />
      </>
    ) : (
      <VideoElement
        videoSource={video}
        image={image}
      />
    )}
  </div>
)

const VideoModule = (section: any) => {
  const {
    theme,
    header,
    subheader,
    content,
    assets,
    mobileAssets,
    settings
  } = section

  const { video, image } = assets
  const { mobileVideo, mobileImage } = mobileAssets || undefined

  return (
    <Container
      flex
      theme={theme || 'offWhite'}
      flexCentered
      styleProp={styles.container}
      id={settings?.anchorTargeting || 'video-module'}
    >
      <VideoHeader subheader={subheader} header={header} />
      <Container flex styleProp={styles.content}>{content}</Container>
      {video && (
        <VideoPlayer
          video={video}
          mobileVideo={mobileVideo}
          mobileImage={mobileImage}
          image={image}
        />
      )}
    </Container>
  )
}

export default VideoModule
