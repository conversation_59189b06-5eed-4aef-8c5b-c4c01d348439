/* eslint-disable react/no-array-index-key */

'use client'

import Container from '../layout/Container'
import LeftArrow from '../Generic/Icon/lib/LeftArrow'
import RightArrow from '../Generic/Icon/lib/RightArrow'

import usePrevNextButtons from '@/hooks/prevNextButtons'
import { colors } from '@/app/themeTokens.stylex'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'
import { ContentfulImage } from '@/lib/contentful/types/generic'

import stylex from '@stylexjs/stylex'
import Image from 'next/image'
import useEmblaCarousel from 'embla-carousel-react'
import { EmblaCarouselType } from 'embla-carousel'
import React, {
  useState, useCallback, useEffect, ReactElement
} from 'react'

const styles = stylex.create({
  galleryContainer: {
    position: 'sticky',
    top: 140
  },
  gallery: {
    position: 'relative',
    width: '100%',
    aspectRatio: '800/600',
  },
  galleryRef: {
    overflow: 'hidden',
  },
  galleryWrapper: {
    width: '100%',
    flexWrap: 'nowrap',
  },
  galleryItem: {
    flexShrink: 0,
    width: '100%',
    height: 'auto',
  },
  button: {
    position: 'absolute',
    top: '50%',
    transform: 'translateY(-50%)',
    zIndex: 1,
    background: 'transparent',
    borderWidth: 0,
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    display: {
      default: 'none',
      '@media (min-width: 768px)': 'flex',
    }
  },
  buttonNext: {
    right: '10px',
  },
  buttonPrev: {
    left: '10px',
  },
  thumbnails: {
    paddingBottom: '6px',
    display: {
      default: 'none',
      '@media (min-width: 768px)': 'flex',
    }
  },
  thumbnailButton: {
    position: 'relative',
    opacity: 0.4,
  },
  thumbnailActive: {
    opacity: 1,
    '::after': {
      content: '""',
      display: 'block',
      width: '100%',
      height: '2px',
      borderRadius: '1px',
      background: 'rgb(199, 199, 199)',
      position: 'absolute',
      bottom: '-6px',
    },
  },
  dotSlider: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: '10px',
    display: {
      default: 'flex',
      '@media (min-width: 768px)': 'none',
    }
  },
  dotButton: {
    width: '6px',
    height: '6px',
    backgroundColor: colors.gray300,
    opacity: 0.5,
    borderRadius: '50%',
  },
  dotButtonActive: {
    opacity: 0.7,
    backgroundColor: colors.gray500,
  },
})

export const useThumbnails = (emblaApi: EmblaCarouselType | undefined) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])

  const onThumbnailClick = useCallback(
    (index: number) => {
      if (!emblaApi) return
      emblaApi.scrollTo(index)
    },
    [emblaApi]
  )

  const onInit = useCallback((api: EmblaCarouselType) => {
    setScrollSnaps(api.scrollSnapList())
  }, [])

  const onSelect = useCallback((api: EmblaCarouselType) => {
    setSelectedIndex(api.selectedScrollSnap())
  }, [])

  useEffect(() => {
    if (!emblaApi) return

    onInit(emblaApi)
    onSelect(emblaApi)
    emblaApi.on('reInit', onInit).on('reInit', onSelect).on('select', onSelect)
  }, [emblaApi, onInit, onSelect])

  return {
    selectedIndex,
    scrollSnaps,
    onThumbnailClick,
  }
}

const IMAGE_WIDTH = 800
const IMAGE_HEIGHT = 600
const THUMBNAIL_WIDTH = 52
const THUMBNAIL_HEIGHT = 39

function getMediaItem(
  media: ContentfulImage,
  itemStyles: any,
  width = IMAGE_WIDTH,
  height = IMAGE_HEIGHT
): ReactElement | null {
  if (media.contentType.startsWith('image')) {
    return (
      <Image
        key={media.title}
        src={media.url}
        alt={media.title}
        width={width}
        height={height}
        quality={95}
        {...stylex.props(itemStyles)}
      />
    )
  }

  if (media.contentType.startsWith('video')) {
    return (
      <video
        width={width}
        height={height}
        playsInline
        loop
        muted
        autoPlay
        {...stylex.props(itemStyles)}
      >
        <source src={media.url} type={media.contentType} />
      </video>
    )
  }

  return null
}

type ProductGalleryProps = {
  variant: ContentfulProductVariant;
  hideThumbnails?: boolean;
};

const ProductGallery = ({ variant, hideThumbnails = false }: ProductGalleryProps) => {
  const { gallery } = variant

  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true
  })
  const { selectedIndex, onThumbnailClick } = useThumbnails(emblaApi)
  const { onPrevButtonClick, onNextButtonClick } = usePrevNextButtons(emblaApi)

  if (!gallery) {
    return null
  }

  return (
    <Container as="div" flex gap="2" styleProp={styles.galleryContainer}>
      <div {...stylex.props(styles.gallery)}>
        <div ref={emblaRef} {...stylex.props(styles.galleryRef)}>
          <Container as="div" flex flexRow styleProp={styles.galleryWrapper}>
            {gallery.items.map((item, index) => (
              <React.Fragment key={item.title + index}>
                {getMediaItem(item, styles.galleryItem)}
              </React.Fragment>
            ))}
          </Container>
        </div>
        <button
          type="button"
          onClick={onPrevButtonClick}
          {...stylex.props(styles.button, styles.buttonPrev)}
        >
          <LeftArrow dimensions="16" />
        </button>
        <button
          type="button"
          onClick={onNextButtonClick}
          {...stylex.props(styles.button, styles.buttonNext)}
        >
          <RightArrow dimensions="16" />
        </button>
        <Container as="div" flex flexCentered flexRow gap="2" styleProp={styles.dotSlider}>
          {gallery.items.map((_item, index) => (
            <button
              key={index}
              type="button"
              onClick={() => onThumbnailClick(index)}
              {...stylex.props(
                styles.dotButton,
                index === selectedIndex && styles.dotButtonActive
              )}
            />
          ))}
        </Container>
      </div>
      {!hideThumbnails && (
        <Container
          as="div"
          flex
          flexCentered
          flexRow
          gap="2"
          styleProp={styles.thumbnails}
        >
          {gallery.items.map((item, index) => (
            <button
              type="button"
              key={index}
              onClick={() => onThumbnailClick(index)}
              {...stylex.props(
                styles.thumbnailButton,
                index === selectedIndex && styles.thumbnailActive
              )}
            >
              {getMediaItem(item, null, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT)}
            </button>
          ))}
        </Container>
      )}
    </Container>
  )
}

export default ProductGallery
