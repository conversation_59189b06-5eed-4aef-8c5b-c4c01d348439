/* eslint-disable max-len */
export const teaKettleDummy = {

  product: {

    id: '6df75a02-4452-55bd-88ff-6c8248cbf5a7',
    name: 'Whistling Tea Kettle',
    slug: 'tea-kettle',
    variants: [
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1160',
        name: 'Whistling Tea Kettle - Cream',
        backgroundColor: '#FEF1DF',
        slug: 'cream',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/6UgPOsIeGrxCLVwdPlLHVD/0e1b284f703bc6b7f91848da7d606f30/tea-kettle-cream.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/PS8TssMWrlW1E4KyEtXtL/389808b58dbf91e3532798ff4c5fd461/cream.png'
      },
      {
        id: '1f4a37f9-af6d-5f2a-a6c6-9cfc8fd28b33',
        name: 'Whistling Tea Kettle - Navy',
        backgroundColor: '#8A9EA1',
        slug: 'navy',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/74ibiwci4D3FjopWrjGYCb/e8a2c89f18d7caec9f0ad405b61da5b3/tea-kettle-navy.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/2WMHbGProf6pCi0kvhym8p/bc91ab2c2d70a06222608abd5fd56431/navy.png?w=800&h=800&q=50&fm=png'
      },
      {
        id: 'b30bd35f-3bb4-5d52-b36a-bc4a9beec5e4',
        name: 'Whistling Tea Kettle - Gray',
        backgroundColor: '#E8E4DC',
        slug: 'gray',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/5zqkhrqHhuGu42W0YKpmYi/d177a595916419e821550fe692f373d7/tea-kettle-gray.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/3Y1prT5RfUxDsCI6F6cjtY/613781754e9af58f65de4ea563f6eac5/gray.png?w=800&h=800&q=50&fm=png'
      },
      {
        id: 'be0e7e4e-5dba-5398-b7e0-a9767f996129',
        name: 'Whistling Tea Kettle - Perracotta',
        backgroundColor: '#FFBDAD',
        slug: 'perracotta',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/3yqJi2Ti7rI8l1kq6A4Yn2/aecb093d806bf916c152099712372f20/tea-kettle-perracotta.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/56cisyxBqwaCWLRSkWmvj5/95aed0cf3014e799693bcba4daa13ca7/perracotta.png?w=800&h=800&q=50&fm=png'
      },
      {
        id: '41e4f3eb-bd44-547d-9ad6-a1a0e49ad74c',
        name: 'Whistling Tea Kettle - Marigold',
        backgroundColor: '#FFDB8D',
        slug: 'marigold',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/c86L50XTurjH0eX8tqUGz/550f5e7cd721289014f427cd29c019a0/tea-kettle-marigold.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/2g6bQqhhzFltVCvZKwSNfp/fbaa0be6b76d992156ce682956fcdd8c/marigold.png?w=800&h=800&q=50&fm=png'
      },
      {
        id: 'f249a0b6-a676-5336-b37c-b060fc9c08f5',
        name: 'Whistling Tea Kettle - Sage',
        backgroundColor: '#B4C2B1',
        slug: 'sage',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/2yKD2jcm8Rotnst1E1ryEI/7f9cfd967ff6d4b51cb504b384ac09dc/tea-kettle-sage.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/2hjrQMWVwP2iXlKKjqareg/d7981a137f14719d6be4dc0822bb469d/sage.png?w=800&h=800&q=50&fm=png'
      },
      {
        id: '89de757a-0036-5991-a304-93ec1122154f',
        name: 'Whistling Tea Kettle - Mist',
        backgroundColor: '#D2E6E0',
        slug: 'mist',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/5qN18BcvCtdENgJxZX8ypG/bd366f3f2a318fd314f001869b164513/tea-kettle-mist.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/1GfUUjdUMiT8EKcvo8dAxJ/45668abc8996744fb605db2976df00d3/mist.png?w=800&h=800&q=50&fm=png'
      },
      {
        id: 'c5366618-09d2-548c-8d26-b49fb8da3d61',
        name: 'Whistling Tea Kettle - White',
        backgroundColor: '#FCFCFA',
        slug: 'white',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/141QO3Jmanp4PMDMbd2b6P/9539510c6d51381225fdd9fc32f5e12c/caraway-tk-white.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/3LH9JpaBGvmHhLJZccmn7E/5de28b8045b948532c10d404f75e8ab6/white-tera-kettle-gold-handle.png?w=800&h=800&q=50&fm=png'
      },
      {
        id: '877352fe-c176-5a65-a592-1fefd71aef1c',
        name: 'Whistling Tea Kettle - Black',
        backgroundColor: '#9C9C9C',
        slug: 'black',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/3En6se6tQTPQ3FH8prPXpx/5ceba571a2597b51a70d66e9ef88c6e4/caraway-tk-black.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/6IiQDtwoGXW6NWvF3oAV67/b770dee18ffda8ce11e56a7fd1dc91ea/black-tea-kettle-gold-handle.png?w=800&h=800&q=50&fm=png'
      },
    ],
    parts: [
      {
        id: 'cd47a615-8004-59af-b3c4-977a5920f27f',
        title: 'Easy On The Ears',
        description: 'Features a soothing single-tone whistle call when boiling.',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/1gAks7GEsPNn1gW3wH7kBM/ae80fa2434ffc71a4598ba8bec7a95c7/pointer__1.svg'
      },
      {
        id: 'bd174780-474b-5760-ac68-4629e67660a0',
        title: 'Designed With You In Mind',
        description: 'Includes a complementary pot holder for safe handling.',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/66d3AWDfQVOM2fOcoiDXUK/a509962d27da3af0d04d2566b237b40b/pointer__2.svg'
      },
      {
        id: '343e4090-c62d-5f27-a95c-a5db853e9ed8',
        title: 'For Any And Every Stovetop',
        description: 'Compatible with electric, gas, and induction cooktops.',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/1Zeq1aeE9jYOARPNze86PP/e15d2e70da6cf6fea10011f25f83f84c/pointer__3.svg'
      },
      {
        id: '3ca5e4b2-b491-5010-8eee-271725d9aee3',
        title: 'High Function, High Design',
        description: 'Constructed with a ceramic-coated stainless steel body and our signature ceramic coating.',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/BVpZ1CVuqTA1ZN1mUDD6d/92d57401f7d68ff718ca7c787c5e5bd4/pointer__4.svg'
      }

    ]
  },

}

export const foodStorageDummy = {
  product: {
    id: '6df75a02-4452-55bd-88ff-6c8248cbf5a8',
    name: 'Food Storage Set',
    slug: 'food-storage-set',
    variants: [
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1160',
        name: 'Food Storage Set - Cream',
        backgroundColor: '#FEF1DF',
        slug: 'cream',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/1kAT4SIIaCuCM1PP9fVlXm/1f4eea6ff9eb3b51d182545732d28d2f/FoodStorage_Swatch_Cream.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/rkmZROvC25bERAc1DzvEL/0f0b39b38c75b661d46241c596c79c4c/fs_education_cream_.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1161',
        name: 'Food Storage Set - Sage',
        backgroundColor: '#B4C2B1',
        slug: 'sage',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/4OaQg71e1qMoPVCbaQsE19/b1d9955463d0992cd221f51124247617/FoodStorage_Swatch_Sage.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/7CLRSudgK9PWlH7PMjyyY3/fd66c8547f911bbc6e93054ff56e0720/fs_education_sage_.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1162',
        name: 'Food Storage Set - Gray',
        backgroundColor: '#E8E4DC',
        slug: 'gray',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/1D0DTnusSfVriRNKbjj54X/d2e03e126f90ba896df59dec54d632fd/FoodStorage_Swatch_Gray.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/sqc4WbOL5eNj32uQdvSjc/158a35d82e6d0b9fc9a405cc16aa7ec0/fs_education_gray_.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1163',
        name: 'Food Storage Set - Mist',
        backgroundColor: '#D2E6E0',
        slug: 'mist',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/5H5eaoh8i1mNBPxhWsd8EE/263c5b642b85f55f08d331367b33769d/FoodStorage_Swatch_Mist.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/265nQQBZgcjs45SkM0vxVO/312d77c914b7d0951df9cc6ff64b35e6/fs_education_mist_.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1164',
        name: 'Food Storage Set - Navy',
        backgroundColor: '#8A9EA1',
        slug: 'navy',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/78qGvzNedRXahqofacAkYC/fbf3ef14b642819bc7df3252532c9f32/FoodStorage_Swatch_Navy.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/5GKAzxTsZPwIRhg1nTwZJE/3a896921efa4e1fba683014f5082a962/fs_education_navy_.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1165',
        name: 'Food Storage Set - Perracotta',
        backgroundColor: '#FFBDAD',
        slug: 'perracotta',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/7iniWxzwMMR5mFWkeEJ4nb/d835785b9ea74bc49fec885468df156e/FoodStorage_Swatch_Perracotta.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/1LpqNbAVgBiGXBFOZaFHFY/98c641f333af0eca8ef4e910b805a4a8/fs_education_perracotta_.png'
      },
    ],
    parts: [
      {
        id: 'cd47a615-8004-59af-b3c4-977a5920f27f',
        title: 'Tight Sealed Glass Lid',
        description: 'Designed with an Air Release tech to keep food fresh.',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/5LspV5EwGViwCFspi8laaR/25623cec70f3734237f10945c0a3e124/Food_Storage_Pointer_1.svg'
      },
      {
        id: 'cd47a615-8004-59af-b3c4-977a5920f29f',
        title: 'Dot Inserts',
        description: 'The adorable home for dipping sauces and dressings.',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/1eYGHVMnW4dW2g5FoGRVfe/37b81271a4f3122a578131d6267de366/Food_Storage_Pointer_3.svg'
      },
      {
        id: 'cd47a615-8004-59af-b3c4-977a5920f28f',
        title: 'Dash Inserts',
        description: 'For more than a garnish, and less than a side.',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/3htEvv77w5S61bNYvSpVVr/413a49a775153d372e874eebae3291c6/Food_Storage_Pointer_2.svg'
      },
    ]
  },
}

export const knifeUtensilSetDummy = {
  product: {

    id: '6df75a02-4452-55bd-88ff-6c8248cbf5a8',
    name: 'Knife Utensil Set',
    slug: 'knife-utensil-set',
    variants: [
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1160',
        name: 'Knife Utensil Set - Cream',
        backgroundColor: '#FEF1DF',
        slug: 'cream',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/71ZKyNf94QS9tMjGqPhn2b/bc9116f36a1669ab4e9423a0e3960380/swatch-prepset-cream.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/308KTBY1ug8Qpo3wCmvFuw/c7c6d4e321a41479d8e7e65ef72b1c93/Prep_Set_-_Cream_-_Anatomy_Module.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1160',
        name: 'Knife Utensil Set - Navy',
        backgroundColor: '#8A9EA1',
        slug: 'navy',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/75nBJsZORfMPniWKhO15I4/50ba20950fb032bc7d496149760a514e/swatch-prepset-navy.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/2vSJidywOL5v4rx75vNwkl/8e31c2af5435e6f2d43797625413423a/Prep_Set_-_Navy_-_Anatomy_Module.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1160',
        name: 'Knife Utensil Set - Gray',
        backgroundColor: '#E8E4DC',
        slug: 'gray',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/gfPOty0afDKJ81gMQVF9E/1e7a89f08a4135f8c930db4d65135b3f/swatch-prepset-grey.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/4RKuBvMZCBOai0FUjvrjUF/bb91616ce8bee83affa99a07eeacec24/Prep_Set_-_Gray_-_Anatomy_Module.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1160',
        name: 'Knife Utensil Set - Mist',
        backgroundColor: '#D2E6E0',
        slug: 'mist',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/69AZHEfAf2MKCF05rOCp1W/aea4b4eb6d2bb791c16bdf7ad48085d1/swatch-prepset-mist.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/16AtQNa0MTRQUWvZZsLpgf/94d305e0e2d98a4cf343ad18a126cb4e/Prep_Set_-_Mist_-_Anatomy_Module.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1160',
        name: 'Knife Utensil Set - Perracotta',
        backgroundColor: '#FFBDAD',
        slug: 'perracotta',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/6XnZOLzCOREshldHu2Ipdv/40c2acfb1094172bb16738585d5ebbc2/swatch-prepset-pera.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/3xTwFUouhgywq4YRzPJwz0/8eaf678695bbe239d9f17e673c818e71/Prep_Set_-_Perracotta_-_Anatomy_Module.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1160',
        name: 'Knife Utensil Set - Charcoal',
        backgroundColor: '#97989B',
        slug: 'charcoal',
        icon: 'https://images.ctfassets.net/dfp1t53x5luq/5B5J49qrtpIhG5CKBnLBeH/8591d7cd1d31524cef508d9cafabd7c8/swatch-prepset-charcoal.png?w=100&h=100&q=50&fm=png',
        image: 'https://images.ctfassets.net/dfp1t53x5luq/6yy9gwHVu65IxFtxiybbG7/8694c3f1e5d4b87615f5f5d9fbffd1d3/Prep_Set_-_Charcoal_-_Anatomy_Module.png'
      },
    ],
    parts: [
      {
        id: 'cd47a615-8004-59af-b3c4-977a5920f27f',
        title: 'A Cut Above The Rest',
        description: 'Knives crafted from durable, premium German Stainless Steel.',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/2Op6QGA1yTjg9EZ7LuEbR2/902dd2166e0b737bad1696832f7f4808/pointer_1.svg'
      },
      {
        id: 'cd47a615-8004-59af-b3c4-977a5920f29f',
        title: 'FSC-Certified Wood',
        description: 'Sustainably sourced birch wood built to work seamlessly with our ceramic coated kitchenware.',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/F3i795P1kvqdibTE2w1UK/9e5e409cb27be757c5fbcb68b310c25d/pointer_2.svg'
      },
      {
        id: 'cd47a615-8004-59af-b3c4-977a5920f28f',
        title: 'Modular Organizers',
        description: 'Set include a magnetic storage system to keep your knives sharp & utensils tidy.',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/cbrbRXZnKfHO2idBmoA3g/3b87f5566acf5bb1026374b2a5f0c31a/pointer_3.svg'
      },
    ],
  }
}

export const petiteCookerDummy = {
  product: {
    id: '2aZx5stjrJUl5Wmc1jEsyx',
    name: 'Ceramic Petite Cooker',
    slug: 'ceramic-petite-cooker',
    variants: [
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1161',
        name: 'Ceramic Petite Cooker - Sky Blue',
        backgroundColor: '#EEF9FF',
        slug: 'sky-blue',
        icon: 'https://images.ctfassets.net/vitsw3itv0qj/7u78HDuIuUDG9iYGuaQpPX/3602552967fa76638eeea8f89a0ca022/Sky_Blue.png',
        image: 'https://images.ctfassets.net/vitsw3itv0qj/2wrr3PxVDrEKENuOVtwb6h/477227b888862cbedcb5ec034beba8c2/Petite_Cooker_-_Sky_Blue_-_Anatomy.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1162',
        name: 'Ceramic Petite Cooker - Lavender',
        backgroundColor: '#F0EAFA',
        slug: 'lavender',
        icon: 'https://images.ctfassets.net/vitsw3itv0qj/7cJTCyWJfAkL0WtzGzxqHe/b98516ab031ef0e34773ae2a019104a8/Lavender.png',
        image: 'https://images.ctfassets.net/vitsw3itv0qj/19aBS5XDwspgscM6YIzqts/a4bcbf97aa6056c31c129b98f52178c6/Petite_Cooker_-_Lavender_-_Anatomy.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1163',
        name: 'Ceramic Petite Cooker - Peach',
        backgroundColor: '#FFEDD4',
        slug: 'peach',
        icon: 'https://images.ctfassets.net/vitsw3itv0qj/6nRbVPtZyQKm59av10bpsr/650d726a2c8ac777b53095b4c5630ea3/Peach.png',
        image: 'https://images.ctfassets.net/vitsw3itv0qj/1mLs2miIeP7J3FeWS7T2wu/137bb7706d6257039d58bb7d1b42ebfc/Petite_Cooker_-_Orange_-_Anatomy.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1164',
        name: 'Ceramic Petite Cooker - Red',
        backgroundColor: '#FF959F',
        slug: 'brick-red',
        icon: 'https://images.ctfassets.net/vitsw3itv0qj/6uWfyf5lmxi1y7QqYeg8Eg/67859c6155ea2b9d13140bdee6151c8a/Red.png',
        image: 'https://images.ctfassets.net/vitsw3itv0qj/uKiiSzWQLGIHhDaRmMxAH/26eef9111794b2ca9b6d683e1a0e8c2c/Petite_Cooker_-_Red_-_Anatomy.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1160',
        name: 'Ceramic Petite Cooker - Stone',
        backgroundColor: '#FEF1DF',
        slug: 'stone',
        icon: 'https://images.ctfassets.net/vitsw3itv0qj/73youDP4Qma0PdQ9AFCpMl/67a2d9e234ec25446649ceb3cccc5cdc/Stone.png',
        image: 'https://images.ctfassets.net/vitsw3itv0qj/57bUwcM5B1NyWjfjko4P0F/4a3015f1978f1e844f95c2bdc4f783c1/Petite_Cooker_-_Biege_-_Anatomy.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1166',
        name: 'Ceramic Petite Cooker - Emerald',
        backgroundColor: '#A0BEBB',
        slug: 'emerald',
        icon: 'https://images.ctfassets.net/vitsw3itv0qj/3nFrivqi8EnSogrjpp8Upg/767333bed17090e698a3b5b807c23c81/Emerald.png',
        image: 'https://images.ctfassets.net/vitsw3itv0qj/7pfi3KkquURb6VzMimKR0w/b87298aed48bbad931f71a66dabd7e72/Petite_Cooker_-_Emerald_-_Anatomy.png'
      },
      {
        id: '231be276-3033-5691-a74b-6c0c73ba1167',
        name: 'Ceramic Petite Cooker - Midnight Blue',
        backgroundColor: '#A1B4CC',
        slug: 'midnight',
        icon: 'https://images.ctfassets.net/vitsw3itv0qj/2FJIuRf5nysX9Ss5GrLmU7/28fbd46ace02faf14cc8b33fc6bd8f82/Midnight.png',
        image: 'https://images.ctfassets.net/vitsw3itv0qj/eFJStGCKLhImWvPa0PRv2/7d880e4d59167b7399eb55fc19348572/Petite_Cooker_-_Midnight_-_Anatomy.png'
      }
    ],
    parts: [
      {
        id: 'cd47a615-8004-59af-b3c4-977a5920f27f',
        title: 'Limited Edition Colors',
        description: 'Fan-favorite shades return, exclusively for Petite Cookers',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/1gAks7GEsPNn1gW3wH7kBM/ae80fa2434ffc71a4598ba8bec7a95c7/pointer__1.svg'
      },
      {
        id: 'cd47a615-8004-59af-b3c4-977a5920f29f',
        title: 'No Toxins, Ever',
        description: 'Free of PTFE, PFAS, and PFOA for safe, healthy meals',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/1eYGHVMnW4dW2g5FoGRVfe/37b81271a4f3122a578131d6267de366/Food_Storage_Pointer_3.svg'
      },
      {
        id: 'cd47a615-8004-59af-b3c4-977a5920f28f',
        title: 'Your Stovetop Sidekick',
        description: 'Compact, versatile, and incredibly easy to clean',
        media: 'https://images.ctfassets.net/dfp1t53x5luq/1gAks7GEsPNn1gW3wH7kBM/ae80fa2434ffc71a4598ba8bec7a95c7/pointer__1.svg'
      },
    ]
  }
}
