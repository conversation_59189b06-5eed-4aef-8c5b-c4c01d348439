import blocks from './DetailBlocks'

import { ProductCardFieldsProps, ProductGroup } from '../types'

import { ContentfulProduct } from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'
import { ContentfulExtractedProductDetailBlock, ContentfulProductDetailBlocks } from '@/lib/contentful/fetchProducts'
import { sanitize } from '@/utils/regex'

import React from 'react'

type ProductDetailsBlocksProps = {
  details: ContentfulProductDetailBlocks;
  data: {
    product: ContentfulProduct;
    variant: ContentfulProductVariant;
    group: string;
    productGroup?: ProductGroup;
    groupProducts?: ProductGroup['products']['items'];
    reviews: ProductCardFieldsProps['reviews'];
    details?: ContentfulExtractedProductDetailBlock[];
    selectedSwatch?: any;
  };
};

const ProductDetailsBlocks = ({ details, data }: ProductDetailsBlocksProps) => (
  <div>
    {details.map((detail, i) => {
      const Block = blocks[detail.type]

      if (!Block) return null
      if (detail.type === 'ProductByline') return null

      return (
        <React.Fragment key={sanitize(`${detail.type}-${String(i)}`)}>
          <Block detail={detail} data={data} />
        </React.Fragment>
      )
    })}
  </div>
)

export default ProductDetailsBlocks
