import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

const getInstallments = (price: number): number => {
  const MAX_INSTALLMENTS = 4
  const MIN_INSTALLMENTS = 2
  const HIGH_PRICE_THRESHOLD = 50
  const LOW_PRICE_THRESHOLD = 30

  if (price >= HIGH_PRICE_THRESHOLD) {
    return MAX_INSTALLMENTS
  } if (price >= LOW_PRICE_THRESHOLD && price < HIGH_PRICE_THRESHOLD) {
    return MIN_INSTALLMENTS
  }
  // fallback for prices below LOW_PRICE_THRESHOLD
  return 1
}

const getInstallmentPayment = ({ price, installments }: { price:number, installments: number }): number => {
  if (installments <= 1) return price
  // Round up to the dollar
  return Math.ceil(price / installments)
}

type ProductInstallmentTitleProps = {
  price: number;
  regularPriceOverride?: number;
  slug?: string;
}

const styles = stylex.create({
  textColor: { color: colors.navy700 },
})

const ProductInstallmentTitle = ({
  price,
  regularPriceOverride,
  slug
}: ProductInstallmentTitleProps) => {
  const regularPrice = regularPriceOverride || price
  const numberOfInstallments = getInstallments(regularPrice)
  const installmentPayment = getInstallmentPayment({ price: regularPrice, installments: numberOfInstallments })
  const isInstallmentsSectionVisible = numberOfInstallments > 1
  const isGiftCard = slug === 'gift-card'

  if (!isInstallmentsSectionVisible || isGiftCard) return null

  const displayInstallmentsTitle = `${numberOfInstallments} payments of $${installmentPayment} with`

  return (
    <Container gap="xxs" flex flexRow flexCentered>
      <Typography
        as="p"
        typographyTheme="bodySmall"
        typographyThemeMobile="captionLarge"
        styleProp={styles.textColor}
      >
        {displayInstallmentsTitle}
      </Typography>
      <Image
        alt=""
        src="/assets/shop-pay.png"
        width={52}
        height={12.5}
      />
    </Container>
  )
}

export default ProductInstallmentTitle
