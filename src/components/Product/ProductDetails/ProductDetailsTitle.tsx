'use client'

import ProductSavings from './ProductSavings'
import ProductInstallmentTitle from './ProductInstallmentTitle'

import ProductPrice from '../ProductPrice'
import { ProductDetailProps, ProductGroup } from '../types'

import Rating, { MIN_RATING } from '@/components/Generic/Rating'
import Skeleton from '@/components/Generic/Skeleton'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ContentfulProduct } from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'
import { CalloutProps } from '@/components/Generic/Callout/types'
import Callouts from '@components/Generic/Callout/Callouts'
import { colors, spacing } from '@/app/themeTokens.stylex'
import useIsMobile from '@/hooks/isMobile'
import Spacer from '@/components/layout/Spacer'
import { ContentfulExtractedProductDetailBlock } from '@/lib/contentful/fetchProducts'

import * as stylex from '@stylexjs/stylex'
import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

const styles = stylex.create({
  titleDetailsDesktop: {
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'block'
    }
  },
  titleDetailsMobile: {
    display: {
      default: 'block',
      '@media (min-width: 1024px)': 'none'
    }
  },
  headerConstraint: {
    maxWidth: {
      default: '100%',
      '@media (min-width: 1024px)': '85%',
    },
  },
  priceConstraint: {
    gap: {
      default: '4px',
      '@media (min-width: 1024px)': '8px',
    },
    flexWrap: {
      default: 'nowrap',
      '@media (min-width: 1024px)': 'nowrap',
    },
    alignItems: {
      default: 'baseline',
      '@media (min-width: 1024px)': 'center',
    },
    maxHeight: {
      default: '100%',
      '@media (min-width: 1024px)': '50px',
    },
  },
  mobileByline: {
    width: {
      default: 'auto',
      '@media (max-width: 992px)': '100%',
    },
    marginBottom: {
      default: 'auto',
      '@media (max-width: 992px)': '1rem',
    }
  },
  desktopOnly: {
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'block'
    },
    marginBottom: {
      '@media (min-width: 1024px)': spacing.xs
    }
  },
  gray: {
    color: colors.gray,
    paddingBottom: {
      default: '10px',
      '@media (min-width: 1024px)': spacing.xs
    }
  },
  red: {
    color: colors.red700,
  },
  callouts: {
    marginBottom: spacing.xs
  },
  hr: {
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'block'
    },
    backgroundColor: colors.gray300,
    opacity: '.5',
    width: '100%',
    height: '1px',
    borderWidth: 0,
    marginTop: '28px'
  }
})

type ProductDetailsTitleProps = {
  title: string;
  slug: string;
  regularPriceOverride?: ContentfulProductVariant['regularPriceOverride'];
  price: ContentfulProductVariant['price'];
  compareAtPrice: ContentfulProductVariant['compareAtPrice'];
  onSale: ContentfulProductVariant['onSale'];
  calloutItems?: CalloutProps[];
  groupProducts?: ProductGroup['products']['items'];
  productGroup?: ProductGroup;
  product?: ProductDetailProps['product'];
  reviews: ContentfulProduct['reviews'];
  desktop?: boolean;
  showTooltip?: boolean;
  priceComponent?: React.ReactNode;
  details?: ContentfulExtractedProductDetailBlock[];
};

// TODO: we probably don't need to pass slug prop here since we can get it from product prop
// eslint-disable-next-line complexity
const ProductDetailsTitle = ({
  title,
  slug,
  regularPriceOverride,
  price,
  compareAtPrice,
  onSale,
  calloutItems,
  product,
  reviews,
  desktop = false,
  showTooltip = false,
  priceComponent,
  details,
}: ProductDetailsTitleProps) => {
  const findProductBylineItem = () => (product as any)?.productDetailBlocksCollection?.items?.find((item: any) => item.type === 'Product Byline')

  const overwriteProductByLine = details?.find((item) => item.type === 'ProductByline')
  const productBylineItem = overwriteProductByLine || findProductBylineItem()

  const { isMobile } = useIsMobile()
  const size = isMobile ? 'xsmall' : 'small'

  return (
    <div
      {...stylex.props(
        desktop ? styles.titleDetailsDesktop : styles.titleDetailsMobile
      )}
    >
      {calloutItems && <Callouts callouts={calloutItems} size="small" styleProp={styles.callouts} />}
      <Container as="header" flex flexRow spaceBetween noWrap>
        <Typography
          as="h2"
          typographyTheme="h5Secondary"
          typographyThemeMobile="h5Secondary"
          styleProp={styles.headerConstraint}
        >
          {title}
        </Typography>
      </Container>
      <Typography
        as="p"
        typographyTheme="bodyLarge"
        typographyThemeMobile="bodySmall"
        styleProp={styles.gray}
      >
        {documentToPlainTextString(productBylineItem?.description?.json)}
      </Typography>
      <Container flex flexRow noWrap spaceBetween alignCentered>
        {reviews && (reviews.rating >= MIN_RATING ? (
          <Rating
            rating={reviews.rating}
            reviewCount={reviews.count}
            textContentOn="right"
            iconColor="marigold"
            textSize="xxs"
            isAggregated
            size={size}
          />
        ) : (
          <Rating
            rating={0}
            withoutRating
            withoutReviews
          />
        ))}
        <Container
          flex
          flexRow
          alignCentered
          styleProp={styles.priceConstraint}
        >
          <Skeleton
            isLoaded={!!price}
            animation="shimmer"
            width={100}
            height={18}
          >
            {priceComponent || (
              <ProductPrice
                slug={slug}
                regularPriceOverride={regularPriceOverride}
                price={price!}
                compareAtPrice={compareAtPrice!}
                onSale={onSale}
                showTooltip={showTooltip}
              />
            )}
          </Skeleton>
        </Container>
      </Container>
      <Spacer size="xxs" />
      <Container flex flexRow noWrap spaceBetween alignCentered>
        <ProductInstallmentTitle
          slug={slug}
          price={price}
          regularPriceOverride={regularPriceOverride}
        />
        <ProductSavings
          slug={slug}
          price={price}
          regularPriceOverride={regularPriceOverride}
          compareAtPrice={compareAtPrice}
        />
      </Container>
      <hr {...stylex.props(styles.hr)} />
    </div>
  )
}

export default ProductDetailsTitle
