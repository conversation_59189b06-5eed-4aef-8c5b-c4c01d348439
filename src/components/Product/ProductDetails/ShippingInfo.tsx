import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { formatDate } from '@/utils/date'

import Image from 'next/image'

type ShippingInfoProps = {
  variant: {
    sku: string
    estimatedShippingDate?: string | null
  }
}
const ShippingInfo = ({ variant }: ShippingInfoProps) => {
  const { estimatedShippingDate } = variant || null
  const formattedDate = estimatedShippingDate ? formatDate(estimatedShippingDate, 'short', true) : null

  return formattedDate ? (
    <Container
      flex
      flexRow
      gap="1"
      alignCentered
    >
      <Image
        src="/shipping-Icon-green.svg"
        alt="Cart Icon"
        width={26}
        height={26}
      />
      <Typography
        as="p"
        typographyTheme="bodyLarge"
      >
        Estimated Ship Date: <strong>{formattedDate}</strong>
      </Typography>
    </Container>
  ) : null
}

export default ShippingInfo
