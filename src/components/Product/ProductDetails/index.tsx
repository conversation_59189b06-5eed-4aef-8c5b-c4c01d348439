/* eslint-disable complexity */
import ProductDetailsTitle from './ProductDetailsTitle'
import { getProductGroup, mergeProductDetailsOverrides } from './utils'
import ProductDetailsBlocks from './ProductDetailsBlocks'

import AddToCart from '@components/Product/AddToCart'
import ProductGallery from '@components/Product/ProductGallery'
import Spacer from '@components/layout/Spacer'
import SEOHeader from '@components/Generic/SEOHeader'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import { spacing } from '@/app/themeTokens.stylex'
import RichTextRenderer from '@/utils/RichTextRenderer'
import { notEmpty } from '@utils/checking'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

import type {
  ProductDetailProps,
} from '@components/Product/types'
import type { ContentfulProductDetailBlocks } from '@/lib/contentful/fetchProducts'

const styles = stylex.create({
  layout: {
    flexDirection: {
      default: 'column',
      '@media (min-width: 1024px)': 'row'
    }
  },
  gallery: {
    flex: '0.55',
  },
  details: {
    flex: {
      default: '1',
      '@media (min-width: 768px)': '0.45'
    }
  },
  detailsMobile: {
    display: {
      default: 'block',
      '@media (min-width: 1024px)': 'none'
    }
  },
  swatchesContainer: {
    gap: {
      default: spacing.sm,
      '@media (min-width: 768px)': '28px'
    },
    marginBlockEnd: '16px',
  }
})

const ProductDetails = ({
  variant,
  product,
  group,
  details,
  reviews,
  selectedSwatch
}: ProductDetailProps) => {
  if (!product || !variant || !details) {
    return null
  }

  const {
    title,
    slug,
    callouts,
    seoMetadata,
    groups,
  } = product

  const productGroup = groups ? getProductGroup(group, groups) : undefined
  const groupProducts = productGroup?.products?.items

  const calloutItems = callouts?.items?.filter(
    (callout) => callout && (!callout.placement || callout.placement.length === 0 || callout.placement.includes('PDP'))
  )

  const keywords = seoMetadata?.keywords
  const seoKeywords = keywords ?? title

  const {
    regularPriceOverride,
    price,
    compareAtPrice,
    onSale
  } = variant || {}

  const blockDetailOverrides = variant.productDetailBlocksOverrides?.items?.map(
    (override) => {
      if (!override) return null
      return {
        ...override,
        blocks: override.contentCollection?.items
          ?.filter((block) => block?.__typename === 'BlockContent')
          .map((block) => {
            if (!block) return null
            return {
              ...block,
              content: block.content?.json ? RichTextRenderer({ content: block.content.json }) : null,
            }
          }).filter(notEmpty),
      }
    }
  ).filter(notEmpty) ?? []

  const mergedDetails = mergeProductDetailsOverrides(
    details,
    blockDetailOverrides as ContentfulProductDetailBlocks
  )

  const showFallbackATC = Array.isArray(mergedDetails) && !mergedDetails.some(
    (detail) => detail?.type === 'AddtoCartButton' ||
    detail?.type === 'AddtoCartButtonOverride'
  )

  return (
    <Container
      as="section"
      flex
      flexRow
      gap="4"
      styleProp={styles.layout}
      contentGap
    >
      {/* MOBILE ONLY */}
      <Container
        as="div"
        styleProp={[
          styles.details,
          styles.detailsMobile
        ]}
      >
        <ProductDetailsTitle
          onSale={onSale}
          title={title}
          slug={slug}
          regularPriceOverride={regularPriceOverride}
          price={price}
          compareAtPrice={compareAtPrice}
          reviews={reviews.aggregate}
          calloutItems={calloutItems}
          product={product}
          groupProducts={groupProducts}
          productGroup={productGroup}
          showTooltip
          details={mergedDetails}
        />
      </Container>
      <Container as="div" styleProp={styles.gallery}>
        {/* TODO: we don't want as any here, instead it should support just ContentfulProductVariant */}
        <ProductGallery variant={variant as any} />
      </Container>
      <Container as="div" styleProp={styles.details}>
        <SEOHeader text={seoKeywords} />

        {/* Fallback product details if not added on Contentful */}
        {!mergedDetails.some((detail) => detail.type === 'ProductTitle') && (
          <ProductDetailsTitle
            onSale={onSale}
            title={title}
            slug={slug}
            regularPriceOverride={regularPriceOverride}
            price={price}
            compareAtPrice={compareAtPrice}
            reviews={reviews?.aggregate}
            calloutItems={calloutItems}
            desktop
            details={mergedDetails}
          />
        )}
        <ProductDetailsBlocks
          details={mergedDetails}
          data={{
            product: product as any,
            variant: variant as any,
            group,
            reviews,
            productGroup,
            groupProducts,
            details: mergedDetails,
            selectedSwatch,
          }}
        />

        {showFallbackATC && <AddToCart variant={variant} product={product} />}

        {/* Automatically add aggregated reviews disclaimer when applicable */}
        {reviews?.aggregated && reviews?.aggregate?.count > 0 && (
          <>
            <Spacer size="sm" />
            <Typography as="p" typographyTheme="captionLarge">
              *Reviews are an aggregate of bundle components
            </Typography>
          </>
        )}
      </Container>
    </Container>
  )
}

export default ProductDetails
