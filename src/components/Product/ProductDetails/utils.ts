import {
  CompareSet, ProductCardFieldsProps, ProductGroup
} from '../types'

import { ContentfulExtractedProductDetailBlock } from '@/lib/contentful/fetchProducts'
import { ContentfulProductDetailsBlock } from '@/lib/contentful/types/blocks'
import { removeSpaces } from '@/utils/regex'

function isValidGroup(group: ProductGroup | CompareSet | undefined): group is ProductGroup {
  return !!group && group.__typename === 'ProductsGroup'
}

export const getProductGroup = (
  group: string,
  groups: ProductCardFieldsProps['groups'],
) => {
  const validGroups = groups?.items.filter((g) => isValidGroup(g))

  if (!validGroups) {
    return undefined
  }

  const productGroup: ProductGroup | undefined = validGroups[0]

  if (!groups || group === 'default') {
    return productGroup
  }

  if (groups.items.length <= 1) {
    return productGroup
  }

  return validGroups.find(({ slug }) => slug === group)
}

export const mergeProductDetailsOverrides = (
  blocks: ContentfulExtractedProductDetailBlock[],
  overrides: ContentfulExtractedProductDetailBlock[]
) => {
  if (!overrides) {
    return blocks
  }

  return blocks.map((block) => {
    const override = overrides
      .map((item) => ({
        ...item,
        type: removeSpaces(item.type) as ContentfulProductDetailsBlock['type'],
      }))
      .find(({ type }) => type === removeSpaces(block.type))

    return override || block
  })
}
