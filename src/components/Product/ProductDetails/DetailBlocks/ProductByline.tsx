import { DetailBlockProps } from './types'

import Spacer from '@/components/layout/Spacer'
import RichTextRenderer from '@/utils/RichTextRenderer'

import * as stylex from '@stylexjs/stylex'

const LG = '@media (min-width: 992px)'

const styles = stylex.create({
  container: {
    display: {
      default: 'none',
      [LG]: 'block',
    }
  },
  showOnMobile: {
    display: {
      default: 'block',
      [LG]: 'none',
    }
  }
})

const ProductBylineBlock = ({ detail, showOnMobile = false }: DetailBlockProps & { showOnMobile?: boolean }) => {
  if (!detail?.description?.json) {
    return null
  }

  return (
    <div {...stylex.props([styles.container, showOnMobile && styles.showOnMobile])}>
      {!showOnMobile && <Spacer size="sm" />}
      <RichTextRenderer content={detail?.description?.json} />
    </div>
  )
}

export default ProductBylineBlock
