import { DetailBlockProps } from './types'
import ProductATFTest from './ProductATFTest'

import AddOnsATF from '../../AddOnsATF'

import { fetchVariantAvailability } from '@/lib/shopify/fetchAvailability'
import AddOns from '@components/Product/AddOns'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

const ProductAddOnsBlock = async ({ detail, data }: DetailBlockProps) => {
  const addOnsVariants = await Promise.all(detail.variants?.map(async (variant) => {
    const isVariantAvailable = await fetchVariantAvailability(variant.id)

    if (!isVariantAvailable) return null
    return {
      ...variant,
      description: documentToPlainTextString(variant?.description?.json),
    }
  }) || [])

  const addOnsBlocks = detail.blocks?.flatMap((block) => {
    const variant = block.variants?.[0]

    if (!block.title || !variant) {
      return []
    }

    return {
      __typename: block.typename,
      id: variant.id,
      price: variant.price,
      name: block.title,
      image: block?.assets?.[0]?.url,
      description:
        block?.contentRaw &&
        documentToPlainTextString(block?.contentRaw?.json),
      variant: variant.variant,
      product: variant.product,
    }
  }) || []

  const addOnsProducts =
    await Promise.all(detail.products
      ?.filter((product) => product.title && product.variants?.items?.length > 0)
      .map(async (product) => {
        const variant = product.variants?.items?.[0]

        const isVariantAvailable = await fetchVariantAvailability(variant.variantId)

        if (!isVariantAvailable) return null

        return {
          __typename: product.__typename,
          id: variant.variantId,
          price: variant.price,
          name: product.title,
          image: variant.primaryImage?.url,
          description:
            variant?.description &&
            documentToPlainTextString(variant?.description?.json),
          variant,
          product,
        }
      }) || [])

  const filteredAddOnsVariants = addOnsVariants.filter(Boolean)
  const filteredAddOnsProducts = addOnsProducts.filter(Boolean)

  const addOns = [...filteredAddOnsVariants, ...addOnsBlocks, ...filteredAddOnsProducts].filter((addOn) => addOn !== null)

  if (!addOns || !addOns.length) {
    return null
  }

  return (
    <ProductATFTest
      control={(
        <AddOns
          addOnsTitle={`${detail.title || 'Add-Ons:'}`}
          addOns={addOns as any}
          data={data}
        />
      )}
      test={(
        <AddOnsATF
          addOnsTitle={`${detail.title || 'Add-Ons:'}`}
          addOns={addOns as any}
          data={data}
        />
      )}
    />
  )
}

export default ProductAddOnsBlock
