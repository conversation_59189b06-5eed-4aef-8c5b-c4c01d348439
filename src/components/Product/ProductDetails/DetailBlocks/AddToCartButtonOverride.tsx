'use client'

import { DetailBlockProps } from './types'

import AddToCartOverride from '../../AddToCartOverride'

import Container from '@/components/layout/Container'

const AddToCartButtonBlockOverride = ({ detail, data }: DetailBlockProps) => {
  const { variant } = data || {}
  const cta = detail.contentCollection?.items[0]

  if (!cta || cta.__typename !== 'BlockCallToAction') {
    return null
  }

  if (!variant) {
    return null
  }

  return (
    <Container flex gap="2">
      <div id="add-to-cart-button">
        <AddToCartOverride cta={cta} variant={variant} fullWidth />
      </div>
    </Container>
  )
}

export default AddToCartButtonBlockOverride
