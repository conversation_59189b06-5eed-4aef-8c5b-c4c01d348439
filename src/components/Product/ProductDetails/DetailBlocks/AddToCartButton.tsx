import { DetailBlockProps } from './types'

import PurchaseLimitMessaging from '@/components/Product/PurchaseLimitMessaging'
import AddToCart from '@/components/Product/AddToCart'
import Container from '@/components/layout/Container'

import { Suspense } from 'react'

const AddToCartButtonBlock = ({ data }: DetailBlockProps) => {
  const { product, variant } = data || {}

  if (!product || !variant) {
    return null
  }

  return (
    <Container flex gap="2">
      <div id="add-to-cart-button">
        <AddToCart
          variant={variant as any}
          product={product as any}
        />
      </div>
      <Suspense fallback={null}>
        <PurchaseLimitMessaging variantId={variant.variantId} />
      </Suspense>
    </Container>
  )
}

export default AddToCartButtonBlock
