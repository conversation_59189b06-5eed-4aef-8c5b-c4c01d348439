import { DetailBlockProps } from './types'

import ProductToggles from '../../ProductToggles'
import { CompareSet } from '../../types'

const ProductTogglesBlock = ({ data }: DetailBlockProps) => {
  const {
    product,
    productGroup,
    groupProducts,
  } = data || {}

  if (!product || !productGroup || !groupProducts) {
    return null
  }

  const compareSetsDialog = product?.groups?.items.find(
    (group) => group.__typename === 'CompareSet'
  ) as CompareSet | undefined

  return (
    <ProductToggles
      products={groupProducts}
      productSlug={product.slug}
      group={productGroup?.slug}
      togglesV2
      openDialog={compareSetsDialog?.openDialog}
    />
  )
}

export default ProductTogglesBlock
