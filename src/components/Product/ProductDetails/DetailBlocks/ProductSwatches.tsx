import { DetailBlockProps } from './types'

import ProductSwatches from '@components/Product/ProductSwatches'
import ProductQuantityToggle from '@components/Product/ProductQuantityToggle'
import { ContentfulProductSwatch } from '@/lib/contentful/types/products'

// eslint-disable-next-line complexity
const ProductSwatchesBlock = ({ data }: DetailBlockProps) => {
  const {
    product,
    variant,
    selectedSwatch: urlSelectedSwatch
  } = data || {}
  const { swatches } = product || {}

  if (!product || !variant) {
    return null
  }

  const quantityToggles = product?.quantities?.items?.[0]?.toggles?.items || []
  const selectedSwatches =
    Number(product?.swatches?.items?.length) > 1 &&
    product?.swatches?.items.flatMap((_swatches) => _swatches?.swatchCollections?.items.flatMap(
      (swatchCollection) => swatchCollection?.swatches?.items
    )) as ContentfulProductSwatch[]
  // Find the actual ContentfulProductSwatch that matches the URL selected swatch
  let selectedSwatch = selectedSwatches || variant.swatch

  if (urlSelectedSwatch?.swatch?.slug) {
    // Find the matching swatch from the product's swatches
    const matchingSwatch = product?.swatches?.items
      ?.flatMap((swatchCollection) => swatchCollection?.swatchCollections?.items || [])
      ?.flatMap((collection) => collection?.swatches?.items || [])
      ?.find((swatch) => swatch?.slug === urlSelectedSwatch.swatch.slug)

    if (matchingSwatch) {
      selectedSwatch = matchingSwatch
    }
  }

  return (
    <>
      <ProductQuantityToggle
        quantityToggles={quantityToggles}
        product={product}
        variant={variant}
      />
      <ProductSwatches
        swatches={swatches?.items}
        product={product as any}
        variant={variant as any}
        selectedSwatch={selectedSwatch}
        includeMobileSwatches
      />
    </>
  )
}

export default ProductSwatchesBlock
