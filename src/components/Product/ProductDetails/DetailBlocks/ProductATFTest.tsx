'use client'

import FeatureFlag from '@/components/Generic/FeatureFlag'

import { ReactNode } from 'react'

export default function ProductATFTest({ control, test }: {
  control: ReactNode,
  test: ReactNode,
}) {
  // A/B Test: PDP ATF
  // This component is part of (GB TEST: PDP| V1 | New ATF Toggle Experience).
  // TODO: Consolidate ProductBylineBlock & ProductBylineABTest after test conclusion.
  // Owner: [Will]

  // const pathname = usePathname()
  // const isTestPDP = pathname.includes('/products/cookware-sets') || pathname.includes('/products/deluxe-cookware-set')

  return (
    <FeatureFlag name="addons-pdp-upsell" defaultValue="Control">
      {({ value }) => (value === 'Control' ? control : test)}
    </FeatureFlag>
  )
}
