'use client'

import { DetailBlockProps } from './types'

import {
  OverviewAccordion,
  NonToxicMaterialsAccordion,
} from '../../ProductAccordions'

import { SingleOpenedAccordion as Accordion } from '@/components/Generic/Accordion'
import LatestReviews from '@/components/Okendo/LatestReviews'
import { useSingleAccordion } from '@/hooks/useSingleAccordion'

import React from 'react'

const ONE = 1
const TWO = 2

/* eslint-disable complexity */
// TODO: this should be refactored to avoid all the ifs
const ProductAccordionsBlock = ({ detail, data }: DetailBlockProps) => {
  const { reviews } = data || {}

  const noReviews = Number.isNaN(reviews?.aggregate?.rating ?? NaN)

  const accordionState = useSingleAccordion('Overview', true)

  return detail.blocks?.map((block, index) => {
    if (block.title === 'Reviews' && reviews) {
      if (noReviews) return null

      return (
        <React.Fragment key={`Reviews-${block.title}-${index.toString()}`}>
          <Accordion
            {...accordionState}
            title={block.title}
            isLast={detail.blocks && index === detail.blocks.length - 1}
          >
            <LatestReviews reviews={reviews.latest} />
          </Accordion>
        </React.Fragment>
      )
    }
    if (block.title === 'Overview') {
      return (
        <React.Fragment key={`Overview-${block.title}-${index.toString()}`}>
          <OverviewAccordion accordionState={accordionState} block={block} />
        </React.Fragment>
      )
    }
    if (block.title === 'Non-Toxic Materials') {
      return (
        <React.Fragment key={`NonToxic-${block.title}-${index.toString()}`}>
          <NonToxicMaterialsAccordion accordionState={accordionState} block={block} />
        </React.Fragment>
      )
    }
    return (
      <Accordion
        {...accordionState}
        key={block.title}
        title={block.title || 'Missing accordion title'}
        isLast={detail.blocks && index === detail.blocks.length - (!noReviews ? ONE : TWO)}
      >
        {block.content}
      </Accordion>
    )
  })
}

export default ProductAccordionsBlock
