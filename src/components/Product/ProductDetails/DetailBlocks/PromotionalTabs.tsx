import { DetailBlockProps } from './types'

import { getOOSSwatches } from '../../utils'

import PromotionalBlockSlider from '@/components/Content/PromotionalBlockSlider/PromotionalBlockSlider'

import { ComponentProps } from 'react'

const PromotionalTabsBlock = async ({ detail, data }: DetailBlockProps) => {
  const slides = detail?.blocks as ComponentProps<typeof PromotionalBlockSlider>['slides']

  if (!slides || !data) {
    return null
  }

  const oosSwatches = await getOOSSwatches([data?.variant])

  const petiteCooker5thAnniversaryVariantIds = new Set([
    '41312872038482',
    '41312872071250',
    '41312872104018',
    '41312872136786',
    '41312872169554',
    '41312872202322',
    '41312872235090',
  ])

  const isOOS = petiteCooker5thAnniversaryVariantIds.has(data.variant.variantId) && oosSwatches.length === 1

  const oosVariantPromotionalSlide = (
    <div>
      This birthday exclusive is sold out! Want first dibs on future
      collections? |{' '}
      <a href="/forms/coming-soon" style={{ textDecoration: 'underline' }}>
        Sign Up
      </a>
    </div>
  )

  if (isOOS) {
    slides.unshift({
      content: oosVariantPromotionalSlide,
      // @ts-ignore
      settings: { theme: 'Gray 200' },
    })
  }

  return <PromotionalBlockSlider slides={slides} />
}

export default PromotionalTabsBlock
