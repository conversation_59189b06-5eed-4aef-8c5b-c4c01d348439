import { ProductCardFieldsProps, ProductGroup } from '../../types'

import { ContentfulExtractedProductDetailBlock } from '@/lib/contentful/fetchProducts'
import { ContentfulProduct } from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

export type DetailBlockProps = {
  detail: ContentfulExtractedProductDetailBlock;
  data?: {
    product: ContentfulProduct;
    variant: ContentfulProductVariant;
    group: string;
    productGroup?: ProductGroup;
    groupProducts?: ProductGroup['products']['items'];
    reviews: ProductCardFieldsProps['reviews'];
    details?: ContentfulExtractedProductDetailBlock[];
    selectedSwatch?: any;
  };
};
