import { DetailBlockProps } from './types'

import Spacer from '@/components/layout/Spacer'
import RichTextRenderer from '@/utils/RichTextRenderer'

const DisclaimerBlock = ({ detail }: DetailBlockProps) => {
  if (!detail?.description?.json) {
    return null
  }

  return (
    <>
      <Spacer size="sm" />
      <RichTextRenderer settings={{ typographyTheme: 'captionLarge' }} content={detail.description.json} />
    </>
  )
}

export default DisclaimerBlock
