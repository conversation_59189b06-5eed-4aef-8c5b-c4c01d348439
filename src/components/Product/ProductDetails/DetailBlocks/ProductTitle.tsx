import { DetailBlockProps } from './types'

import ProductDetailsTitle from '../ProductDetailsTitle'
import StickyProductTitle from '../../StickyProductTitle'
import { ProductDetailProps } from '../../types'

const ProductTitleBlock = ({ data }: DetailBlockProps) => {
  if (!data) {
    return null
  }

  const {
    product,
    variant,
    reviews,
    details
  } = data
  const {
    title,
    callouts,
    swatches
  } = product
  const {
    regularPriceOverride,
    price,
    compareAtPrice,
    onSale
  } = variant

  const calloutItems = callouts?.items.filter(
    (callout) => !callout.placement ||
      callout.placement.length === 0 ||
      callout.placement.includes('PDP')
  )

  return (
    <>
      <ProductDetailsTitle
        slug={product.slug}
        title={title}
        regularPriceOverride={regularPriceOverride}
        price={price}
        compareAtPrice={compareAtPrice}
        reviews={reviews?.aggregate}
        calloutItems={calloutItems}
        desktop
        showTooltip
        onSale={onSale}
        product={product as unknown as ProductDetailProps['product']}
        details={details}
      />
      <StickyProductTitle
        swatches={swatches?.items}
        product={product as any}
        variant={variant as any}
        selectedSwatch={variant.swatch}
        reviews={reviews?.aggregate}
      />
    </>
  )
}

export default ProductTitleBlock
