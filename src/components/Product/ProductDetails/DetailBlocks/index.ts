import ProductTitle from './ProductTitle'
import PromotionalTabs from './PromotionalTabs'
import ProductByline from './ProductByline'
import ProductSwatches from './ProductSwatches'
import ProductAccordions from './ProductAccordions'
import AddtoCartButton from './AddToCartButton'
import AddtoCartButtonOverride from './AddToCartButtonOverride'
import ProductAddOns from './ProductAddOns'
import ProductCrossSells from './ProductCrossSells'
import ProductToggles from './ProductToggles'
import Disclaimer from './Disclaimer'
import PlainText from './PlainText'
import TolstoyWidget from './TolstoyWidget'
import BuyWithZest from './BuyWithZest'
import { DetailBlockProps } from './types'

import { ContentfulProductDetailsBlock } from '@/lib/contentful/types/blocks'

import { ReactNode } from 'react'

const blocks: Record<
ContentfulProductDetailsBlock['type'],
(props: DetailBlockProps) => ReactNode
> = {
  PromotionalTabs,
  ProductTitle,
  ProductByline,
  ProductSwatches,
  ProductAccordions,
  ProductAddOns,
  ProductCrossSells,
  ProductToggles,
  AddtoCartButton,
  AddtoCartButtonOverride,
  Disclaimer,
  PlainText,
  TolstoyWidget,
  BuyWithZest
}

export default blocks
