import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import Callout from '@/components/Generic/Callout'
import Skeleton from '@/components/Generic/Skeleton'
import { colors } from '@/app/themeTokens.stylex'
import { calculateSavings } from '@/utils/price'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

type ProductSavingsProps = {
  slug: string;
  price: number;
  regularPriceOverride?: number;
  compareAtPrice: number;
}

const styles = stylex.create({
  container: { gap: 6 },
  red: { color: colors.red700 },
})

const ProductSavings = ({
  price,
  regularPriceOverride,
  compareAtPrice,
  slug
}: ProductSavingsProps) => {
  const regularPrice = regularPriceOverride || price
  const { savingsPercentage, savingsAmount } = calculateSavings({ price: regularPrice, compareAtPrice })
  const displaySavingsTitle = savingsAmount > 0 ? `SAVE $${savingsAmount.toLocaleString('en-US', {})}` : ''
  const savingsPercentTitle = `(${savingsPercentage}% off)`
  const isGiftCard = slug === 'gift-card'

  if (savingsAmount <= 0 || isGiftCard) return null

  return (
    <Container styleProp={styles.container} flex flexRow flexCentered>
      <Skeleton
        isLoaded={!!regularPrice}
        animation="shimmer"
        width={100}
        height={24}
      >
        <Typography
          as="p"
          typographyTheme="bodySmall"
          typographyThemeMobile="captionLarge"
          styleProp={styles.red}
        >
          {savingsPercentTitle}
        </Typography>
        <Callout
          key={`product-savings-${slug}`}
          title={displaySavingsTitle}
          theme="red700"
        />
      </Skeleton>
    </Container>
  )
}

export default ProductSavings
