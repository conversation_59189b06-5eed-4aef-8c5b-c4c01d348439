import { CalloutProps } from '../Generic/Callout/types'

import { ThemeColors } from '@/app/themeThemes.stylex'

import type { ContentfulProductDetailsBlock } from '@/lib/contentful/types/blocks'
import type { ContentfulProductSwatch, ContentfulProductSwatches, ContentfulProductType } from '@lib/contentful/types/products'
import type { ContentfulImage } from '@lib/contentful/types/generic'
import type { ProductDetailsProps, Review } from '@/lib/contentful/types'
import type { ContentfulProductDetailBlocks } from '@/lib/contentful/fetchProducts'

export type SwatchProps = ContentfulProductSwatch

export type VariantProps = {
  name: string,
  sku: string
  swatch: SwatchProps
  variantId: number
  price: number
  compareAtPrice: number
  regularPriceOverride?: number
  onSale: boolean
  primaryImage: {
    title: string
    url: string
  }
  gallery: {
    items: {
      title: string
      description: string
      contentType: string
      url: string
      width: number
      height: number
    }[]
  }
  productDetailBlocksOverrides: {
    items: ContentfulProductDetailsBlock[] | [];
  }
  estimatedShippingDate?: string
  variantAvailability:
  | 'Notify Me'
  | 'Sold Out'
  | 'Notify Me when unavailable'
  | 'Sold Out when unavailable';
  availableForSale: boolean;
}

export type VariantsProps = {
  items: VariantProps[]
}

export type ProductCardFieldsProps = {
  __typename: 'Product'
  productType: ContentfulProductType
  productDetailBlocksCollection: ProductDetailsProps['productDetailBlocksCollection']
  seoMetadata: {
    keywords: string
    description: string
  }
  groups?: {
    items: (ProductGroup | CompareSet)[]
  }
  productMetadata: {
    items: Array<{
      type: string
      // TODO: Type this properly
      references: Array<any>
    }>
  }
  title: string,
  slug: string,
  productId: string,
  swatches?: {
    items: ContentfulProductSwatches[]
  }
  variants: {
    items: {
      sku: string,
      swatch: {
        slug: string
        presentation: string
        icon: {
          url: string
        }
        style: string
        linkedFrom: {
          swatchCollectionCollection: {
            items: {
              linkedFrom: {
                swatchesCollection: {
                  items: {
                    slug: string
                  }
                }
              }
            }
          }
        }
      }
      variantId: number,
      price: number,
      compareAtPrice: number
      primaryImage: {
        url: string,
        title: string
      }
      estimatedShippingDate?: string;
      availableForSale: boolean;
      variantAvailability?: 'Notify Me' | 'Sold Out' | 'Notify Me when unavailable' | 'Sold Out when unavailable'
    }[]
  }
  callouts: {
    items: {
      title: string
      badge?: ContentfulImage,
      placement?: string[],
      settings: {
        theme: ThemeColors
      }
      sys: {
        id: string
      }
    }[]
  }
  reviews: {
    aggregate: {
      rating: number,
      count: number
    },
    latest: Review[],
    aggregated: boolean
  }
}

export type ProductGroup = {
  __typename: string
  name: string;
  slug: string;
  settings?: any
  products: {
    items: {
      slug: string;
      title: string;
      groups?: {
        items: ProductGroup[];
      }
      variants?: {
        items: {
          price: number | null;
          compareAtPrice: number | null;
        }[]
      }
    }[]
  }
  groupItem: {
    __typename: string;
    items: {
      title: string;
      subheader: string;
      callout: CalloutProps;
      product: any[];
      referencesCollection: {
        items: any[];
      };
    }[];
  };
}

export type CompareSet = {
  __typename: string
  openDialog: {
    text: string
    page: {
      sys: {
        id: string
      }
      text: string
    }
  }
}

export type ProductCardsProps = {
  products: ProductCardFieldsProps[]
}

export type ProductCardProps = {
  product: ProductCardFieldsProps
}

export type ProductCard = ProductCardFieldsProps & {
  __typename: 'ProductCard'
  size: 'Single' | 'Double'
  hoverImage: ContentfulImage
  gallery?: {
    items: ContentfulImage[]
  }
}

export type ProductDetailProps = {
  variant: VariantProps;
  // TODO: I'm keeping it as is but I'm not sure why we're using ProductCard types in the context of product details on the PDP
  product: ProductCardFieldsProps;
  details: ContentfulProductDetailBlocks;
  reviews: {
    aggregated: boolean
    aggregate: {
      rating: number;
      count: number;
    };
    latest: Review[];
  };
  group: string;
  selectedSwatch?: any;
};
