'use client'

import AddOnCard from '@/components/Product/AddOnsATF/AddOnCard'
import Container from '@/components/layout/Container'
import { ContentfulExtractedProductDetailBlock } from '@/lib/contentful/fetchProducts'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'
import { useAppDispatch, useAppSelector } from '@/redux/hooks'
import Typography from '@components/Typography'
import Wrapper from '@components/layout/Wrapper'
import { toggleAddOn, clearAddOns } from '@redux/features/product/productSlice'

import * as stylex from '@stylexjs/stylex'
import { useEffect } from 'react'

export type AddOnsWrapperProps = {
  addOnsTitle: string;
  addOns: {
    id: string;
    name: string;
    price: number;
    description?: string;
    image?: string;
    variant?: ContentfulProductVariant;
    product?: NonNullable<ContentfulExtractedProductDetailBlock['products']>[0];
  }[];
  data?: any;
};

const styles = stylex.create({
  wrapper: {
    marginBlock: 24,
    position: 'relative',
    zIndex: 0,
  },
})

const AddOnsATF = ({
  addOnsTitle,
  addOns = [],
  data
}: AddOnsWrapperProps) => {
  const dispatch = useAppDispatch()
  const selectedAddOns = useAppSelector((state) => state.product.addOns)

  useEffect(() => () => {
    dispatch(clearAddOns())
  }, [dispatch])

  const onSelectAddOn = (id: string) => {
    const item = addOns.find((addOn) => addOn.id === id)

    if (item) {
      dispatch(toggleAddOn(item))
    }
  }

  const onUpdateAddOn = (fromId: string, toId: string) => {
    const item = addOns.find((addOn) => addOn.id === fromId)
    const newVariant = item?.product?.variants?.items?.find((variant) => variant.variantId === toId)

    let newItem

    if (item?.variant && newVariant) {
      newItem = { ...item, variant: newVariant }
    }

    if (item && newItem) {
      dispatch(toggleAddOn(item))
      dispatch(toggleAddOn(newItem))
    }
  }

  return (
    <Wrapper styleProp={styles.wrapper}>
      <Typography
        as="h6"
        typographyTheme="bodyLarge"
        marginBottom="sm"
        fontBold
      >
        {addOnsTitle}
      </Typography>

      <Container flex gap="2">
        {addOns.map((addOn) => (
          <AddOnCard
            key={`add-on-${addOn.id}`}
            addOn={addOn as any}
            onSelect={onSelectAddOn}
            onUpdate={onUpdateAddOn}
            selected={!!selectedAddOns.find(({ id }) => id === addOn.id)}
            data={data}
          />
        ))}
      </Container>
    </Wrapper>
  )
}

export default AddOnsATF
