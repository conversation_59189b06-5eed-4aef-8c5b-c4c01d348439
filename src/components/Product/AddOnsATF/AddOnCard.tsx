'use client'

import AddOnsCheckbox from './AddOnsCheckbox'

import Container from '@/components/layout/Container'
import { colors, globalTokens } from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import SwatchesDropdown from '@/components/Generic/SwatchesDropdown'
import { formatDate } from '@/utils/date'
import useVariantAvailability from '@/hooks/getVariantAvailability'
import Skeleton from '@/components/Generic/Skeleton'
import { useAppDispatch } from '@/redux/hooks'
import { elementInteraction } from '@/redux/features/events/eventsSlice'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'

type AddOnCardProps = {
  addOn: {
    __typename: 'Variant' | 'Product' | 'BlockContent';
    id: string;
    name: string;
    price: number;
    description?: string;
    image?: string;
    product: any;
    variant: any;
  };
  onSelect: (id: string) => void;
  onUpdate: (fromId: string, toId: string, type: 'Product' | 'AddOn') => void;
  selected: boolean;
  data?: any;
}

const styles = stylex.create({
  wrapper: {
    position: 'relative',
  },
  main: {
    backgroundColor: colors.offWhite,
    borderColor: colors.gray300,
    // NOTE: there is no borderWidth: 1 in the themeTokens.stylex
    borderWidth: 1,
    borderRadius: globalTokens.borderRadiusSmall,
    borderStyle: 'solid',
    padding: {
      default: '12px 14px 17px 12px',
      '@media (min-width: 768px)': '12px 14px 17px 17px',
    },
    display: 'flex',
    flexDirection: 'row',
    gap: {
      default: 8,
      '@media (min-width: 768px)': 16,
    },
    cursor: 'pointer',
    transition: 'border-color 0.3s ease-in-out, background-color 0.3s ease-in-out',
    position: 'relative',
    zIndex: 10,
  },
  selected: {
    borderColor: colors.navy,
    backgroundColor: colors.gray100,
  },
  image: {
    alignSelf: 'center',
    objectFit: 'contain',
    mixBlendMode: 'darken',
  },
  details: {
    display: 'flex',
    flex: '1',
    flexDirection: 'column',
    justifyContent: 'center',
    gap: 4,
  },
  headerRow: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 4,
  },
  descriptionRow: {
    paddingInlineEnd: 40,
  },
  descriptionTypography: {
    fontSize: '14px'
  },
  priceContainer: {
    textAlign: 'right',
    minWidth: '60px',
    alignContent: 'start',
  },
  addOnDetailsWrapper: {
    transition: 'height 0.3s ease-in-out',
  },
  addOnDetailsPosition: {
    position: 'absolute',
    left: 0,
    right: 0,
    pointerEvents: 'none',
    zIndex: 5,
    transition: 'z-index 0.3s step-start',
  },
  addOnDetailsPostionActive: {
    pointerEvents: 'auto',
    zIndex: 15,
    transition: 'z-index 0.3s step-end',
  },
  addOnDetails: {
    transform: 'translateY(-100%)',
    transition: 'transform 0.3s ease-in-out',
    borderWidth: 1,
    borderColor: colors.gray300,
    borderStyle: 'solid',
    backgroundColor: colors.offWhite,
    borderRadius: `0 0 ${globalTokens.borderRadiusSmall} ${globalTokens.borderRadiusSmall}`,
    padding: {
      default: '17px 13px 12px 13px',
      '@media (min-width: 768px)': '19px 17px 14px 17px',
    },
    gap: {
      default: 8,
      '@media (min-width: 768px)': 16,
    },
  },
  addOnDetailsActive: {
    transform: 'translateY(-4px)',
  },
  swatchesSelectorWrapper: {
    width: '83px',
    '::before': {
      position: 'absolute',
      content: '""',
      top: '-6px',
      left: '-1px',
      right: '-1px',
      height: '8px',
      borderColor: colors.navy,
      borderStyle: 'solid',
      borderWidth: 1,
      borderTopWidth: 0,
      borderBottomRightRadius: globalTokens.borderRadiusSmall,
      borderBottomLeftRadius: globalTokens.borderRadiusSmall,
      backgroundColor: colors.gray100,
      zIndex: 20,
    }
  },
})

// eslint-disable-next-line complexity
const AddOnCard = ({
  addOn,
  onSelect,
  onUpdate,
  selected,
  data
}: AddOnCardProps) => {
  const detailsRef = useRef<HTMLDivElement>(null)
  const [currentVariant, setCurrentVariant] = useState(addOn.variant)

  const dispatch = useAppDispatch()

  const {
    data: isVariantAvailable,
    isLoading,
    isError,
    isSuccess,
  } = useVariantAvailability(currentVariant.variantId)

  const { variantAvailability } = currentVariant
  const available = isVariantAvailable && isSuccess
  const notifyMeOrSoldOut = variantAvailability === 'Sold Out' || variantAvailability === 'Notify Me'

  const showAddOn = addOn.__typename === 'Product' || (available && !notifyMeOrSoldOut)
  const loaded = !isLoading && !isError

  useEffect(() => {
    onUpdate(addOn.id, currentVariant?.variantId, 'Product')
  }, [currentVariant])

  const handleSelect = () => {
    onSelect(addOn.id)

    dispatch(elementInteraction({
      properties: {
        category: 'PDP Upsell',
        label: 'PDP Upsell',
        interaction_type: 'click',
        interaction_value: !selected ? 'open' : 'close',
        placement: 'PDP',
        product: {
          productName: data?.product?.title,
          addOnProductName: addOn.name,
          addOnProductPrice: addOn.price,
        }
      }
    }))
  }

  const addOnImage = addOn.__typename === 'Product' ? currentVariant?.primaryImage?.url : addOn.image
  const addOnName = addOn.name
  const addOnEstimatedShipDate =
    currentVariant.estimatedShippingDate &&
    formatDate(currentVariant.estimatedShippingDate, 'full', true)

  if (!showAddOn && !isLoading) {
    return null
  }

  return (
    <Container styleProp={styles.wrapper}>
      <Skeleton as="div" height={96} isLoaded={loaded}>
        <div
          onClick={handleSelect}
          {...stylex.props(styles.main, selected && styles.selected)}
        >
          {addOnImage && (
            <Image
              src={addOnImage}
              alt={addOnName}
              width={83}
              height={65}
              {...stylex.props(styles.image)}
            />
          )}
          <div {...stylex.props(styles.details)}>
            <div {...stylex.props(styles.headerRow)}>
              <Typography as="h6" typographyTheme="bodySmall" fontBold>
                {addOnName}
              </Typography>
              <Container flex flexRow gap="1" styleProp={styles.priceContainer}>
                <Typography
                  as="span"
                  typographyTheme="bodySmall"
                  fontBold
                  lineHeight="xl"
                >
                  {`$${currentVariant.price}`}
                </Typography>
                <AddOnsCheckbox
                  label={`Select Add On: ${addOnName}`}
                  handleSelect={handleSelect}
                  selected={selected}
                />
              </Container>
            </div>

            <div {...stylex.props(styles.descriptionRow)}>
              <Typography as="p" styleProp={styles.descriptionTypography}>
                {addOn.description}
              </Typography>
            </div>
          </div>
        </div>
        <Container
          styleProp={styles.addOnDetailsWrapper}
          style={{
            height: selected ? detailsRef.current?.clientHeight : 0,
          }}
        >
          <Container
            styleProp={[
              styles.addOnDetailsPosition,
              selected && styles.addOnDetailsPostionActive,
            ]}
          >
            <div ref={detailsRef}>
              <Container
                flex
                flexRow
                styleProp={[
                  styles.addOnDetails,
                  selected && styles.addOnDetailsActive,
                ]}
              >
                <Container
                  flex
                  flexRow
                  flexCentered
                  styleProp={styles.swatchesSelectorWrapper}
                >
                  <div style={{ zIndex: 1000 }}>
                    <SwatchesDropdown
                      product={
                        addOn.__typename === 'Product'
                          ? addOn.product
                          : addOn.variant
                      }
                      setCurrentVariant={setCurrentVariant}
                      currentVariant={currentVariant}
                      size="small"
                      focusable={selected}
                    />
                  </div>
                </Container>
                <Container flex justifyContentCenter>
                  <Typography as="h5" typographyTheme="captionLarge">
                    {currentVariant.swatch?.presentation}
                  </Typography>
                  {addOnEstimatedShipDate && (
                    <Typography as="p" typographyTheme="captionSmall">
                      Estimated Ship Date: {addOnEstimatedShipDate}
                    </Typography>
                  )}
                </Container>
              </Container>
            </div>
          </Container>
        </Container>
      </Skeleton>
    </Container>
  )
}

export default AddOnCard
