import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import Card from '@components/Generic/Card'
import { spacing } from '@/app/themeTokens.stylex'
import { imageProps, videoProps } from '@/components/Generic/Card/types'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { notEmpty } from '@/utils/checking'
import RenderIf from '@/components/Generic/RenderIf'

import * as stylex from '@stylexjs/stylex'
import React, { useState } from 'react'

const DESKTOP = '@media (min-width: 1024px)'

export type MultifunctionalChildProps = {
  id: string
  theme: ThemeColors,
  title: string
  description: any
  overlay: boolean
  image?: imageProps
  video?: {
    ratio: string
    playOnHover: boolean
    sources: {
      src: string
      type: string
    }[]
  }
  posterImage?: imageProps
}

const styles = stylex.create({
  card: {
    position: 'relative',
    borderRadius: spacing.sm,
    overflow: 'hidden',
    height: '100%',
  },
  content: {
    insetInline: '0',
    paddingBlock: `calc(${spacing.xxs} * 7)`,
    paddingInline: spacing.md,
    gap: spacing.xs,
    overflow: 'hidden',
  },
  contentAbsolute: {
    position: 'absolute',
    bottom: '0',
    height: 'auto',
    borderRadius: spacing.sm,
    paddingBlock: `calc(${spacing.xs} + ${spacing.md}) calc(${spacing.lg} - ${spacing.xs})`,
  },
  contentHover: {
    bottom: {
      default: '0',
      [DESKTOP]: '0'
    },
    height: 'auto',
    display: {
      default: 'block',
      [DESKTOP]: 'flex'
    },
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  description: {
    marginTop: spacing.xs,
    display: {
      default: 'block',
      [DESKTOP]: 'none'
    }
  },
  title: {
    width: '100%',
  },
  descriptionBlock: {
    width: '100%',
    display: 'block'
  },
})

const WhatsIncludedV2Card = (
  { item, hoverBehavior = true }:
  { item: MultifunctionalChildProps, hoverBehavior?: boolean }
) => {
  const [hover, setHover] = useState(
    !hoverBehavior
  )

  const textPresent = notEmpty(item.description) && notEmpty(item.title)

  return (
    <Card
      image={item.image}
      video={item.video as videoProps}
      posterImage={item.posterImage}
      theme={item.theme}
      styleProp={styles.card}
      setIsHovered={hoverBehavior ? setHover : () => {}}
      hover={hover}
    >
      <RenderIf condition={textPresent}>
        <Container
          as="div"
          theme={item.theme}
          styleProp={[
            styles.content,
            item.overlay && styles.contentAbsolute,
            hover && styles.contentHover,
          ]}
        >
          <Typography
            as="h3"
            typographyTheme="bodyLarge"
            lineHeight="xxs"
            fontBold
            styleProp={styles.title}
          >{item.title}
          </Typography>
          <Typography
            as="span"
            size="bodySmall"
            styleProp={[
              styles.description,
              hover && styles.descriptionBlock
            ]}
          >
            {item.description}
          </Typography>
        </Container>
      </RenderIf>
    </Card>
  )
}

export default WhatsIncludedV2Card
