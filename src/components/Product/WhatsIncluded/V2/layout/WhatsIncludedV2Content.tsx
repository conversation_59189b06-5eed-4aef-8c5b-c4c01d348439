import Icon from '@/components/Generic/Icon'
import Container from '@components/layout/Container'
import { colors, spacing } from '@/app/themeTokens.stylex'
import { AnchorProps } from '@/components/Generic/CallToAction'
import RenderIf from '@/utils/renderIf'
import { empty, notEmpty } from '@/utils/checking'
import Typography from '@/components/Typography'
import { useDialogContext } from '@/providers/config/DialogContext'
import RenderCTAS from '@/components/Generic/RenderCTAS'

import React from 'react'
import Link from 'next/link'
import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  content: {
    alignItems: 'flex-start',
    marginTop: spacing.lg,
    marginBlockEnd: spacing.xs,
  },
  description: {
    whiteSpace: 'pre-wrap'
  },
  hideMobile: {
    display: {
      default: 'none',
      [DESKTOP]: 'flex',
    },
  },
  underline: {
    textDecoration: 'underline',
  },
  link: {
    color: colors.navy,
    fontWeight: 400,
    fontSize: '1rem'
  }
})

type WhatsIncludedV2ContentProps = {
  description?: string,
  links?: AnchorProps[],
  dialog?: {
    text: string,
    id: string
  },
  firstIndex: boolean,
  lastIndex: boolean,
  hideNav?: boolean,
  handleNext: () => void,
  handlePrev: () => void
}

export const RenderLinks = ({ links = [] }:{ links: AnchorProps[] }) => {
  const { triggerDialog } = useDialogContext()

  if (links.length === 0) return null
  return (
    <Container as="ul" gap="1" flex>
      { links.map(({
        id,
        href,
        children,
      }) => (
        <li key={id}>
          <RenderIf condition={notEmpty(href)}>
            <Link
              href={href || '#'}
              target="_blank"
              rel="noreferrer"
            >
              <Typography
                as="span"
                typographyTheme="bodyLarge"
                typographyThemeMobile="bodySmall"
                underline
              >
                {children} →
              </Typography>
            </Link>
          </RenderIf>
          <RenderIf condition={empty(href)}>
            <button
              onClick={() => id && triggerDialog(id)}
              type="button"
            >
              <Typography
                as="span"
                typographyTheme="bodyLarge"
                typographyThemeMobile="bodySmall"
                underline
              >
                {children} →
              </Typography>
            </button>
          </RenderIf>
        </li>
      ))}
    </Container>
  )
}

const WhatsIncludedV2Content = ({
  description = '',
  links = [],
  dialog,
  firstIndex,
  lastIndex,
  hideNav,
  handleNext,
  handlePrev
}: WhatsIncludedV2ContentProps) => {
  const gap = dialog?.id !== '' ? '2' : undefined

  return (
    <Container as="div" flex gap="1" flexRow spaceBetween styleProp={styles.content}>
      <Container as="div" gap="2" flex>
        {description && (
        <Container as="ul" gap="2" flex flexRow styleProp={styles.description}>
          {description}
        </Container>
        )}
        <Container
          flex
          flexRow
          gap={gap}
        >
          {dialog && (
            <Typography
              as="p"
              typographyTheme="bodyLarge"
            >
              <RenderCTAS
                buttons={[
                  {
                    children: dialog.text,
                    id: dialog.id,
                    styleProp: styles.link,
                    variant: 'underlined'
                  },
                ]}
              />
            </Typography>
          )}
          <RenderLinks links={links} />
        </Container>
      </Container>
      {!hideNav && (
        <Container
          as="div"
          gap="2"
          flex
          flexRow
          styleProp={styles.hideMobile}
        >
          <button
            onClick={handlePrev}
            type="button"
            aria-label="Previous"
            disabled={firstIndex}
          >
            <Icon name="LeftArrowCircle" size="xxlarge" fill={firstIndex ? colors.gray300 : colors.navy300} />
          </button>
          <button
            onClick={handleNext}
            type="button"
            aria-label="Next"
            disabled={lastIndex}
          >
            <Icon name="RightArrowCircle" size="xxlarge" fill={lastIndex ? colors.gray300 : colors.navy300} />
          </button>
        </Container>
      )}
    </Container>
  )
}

export default WhatsIncludedV2Content
