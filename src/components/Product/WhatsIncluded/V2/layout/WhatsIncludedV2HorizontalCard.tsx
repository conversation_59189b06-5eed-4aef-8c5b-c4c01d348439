import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import Card from '@components/Generic/Card'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  card: {
    position: 'relative',
    borderRadius: spacing.sm,
    overflow: 'hidden',
    flexDirection: {
      default: 'column',
      [DESKTOP]: 'row-reverse',
    },
    gap: spacing.md,
  },
  content: {
    paddingBlock: `calc(${spacing.xxs} * 7)`,
    maxWidth: {
      [DESKTOP]: '35%',
    },
    justifyContent: 'center',
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: spacing.lg
    },
    borderRadius: spacing.sm,
    boxSizing: 'border-box',
    width: '100%',
    overflow: 'hidden',
  },
  description: {
    display: 'block'
  },
  video: {
    borderRadius: spacing.sm,
    overflow: 'hidden',
  },
})

const WhatsIncludedV2HorizontalCard = ({ item }: any) => (
  <Card
    video={item.video}
    image={item.image}
    theme={item.theme}
    styleProp={styles.card}
    styleVideoProp={styles.video}
  >
    <Container
      as="div"
      theme={item.theme}
      styleProp={styles.content}
      gap="2"
      flex
    >
      <Typography as="h3" size="md" fontBold>{item.title}</Typography>
      <Typography
        as="span"
        typographyTheme="bodyLarge"
        typographyThemeMobile="bodySmall"
        styleProp={styles.description}
      >
        {item.description}
      </Typography>
    </Container>
  </Card>
)

export default WhatsIncludedV2HorizontalCard
