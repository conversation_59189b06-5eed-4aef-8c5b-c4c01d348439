'use client'

import WhatsIncludedV2HorizontalCard from './WhatsIncludedV2HorizontalCard'
import WhatsIncludedV2Card from './WhatsIncludedV2Card'
import WhatsIncludedV2TextCard from './WhatsIncludedV2TextCard'

import Slider, { Slide } from '@/components/Generic/Slider'
import { spacing } from '@/app/themeTokens.stylex'
import { empty, notEmpty } from '@/utils/checking'
import RenderIf from '@/components/Generic/RenderIf'
import useIsMobile from '@/hooks/isMobile'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  slider: {
    height: 'auto'
  },
  sliderContainer: {
    gap: spacing.md,
  },
  slide: {
    flex: {
      default: '0 0 100%',
      [DESKTOP]: `0 0 calc(100% / 3 - ${spacing.md})`
    },
    position: 'relative',
  },
  threeColumns: {
    flex: {
      default: '0 0 85%',
      [DESKTOP]: `0 0 calc(100% / 3 - ${spacing.md})`
    },
  },
  horizontal: {
    flex: {
      default: '0 0 90vw',
      [DESKTOP]: `0 0 calc(100% - ${spacing.md})`
    },
    position: 'relative',
  },
})

type WhatsIncludedV2Props = {
  setEmblaApi: any
  items: any,
  layout?: string
}

const WhatsIncludedV2Cards = ({
  setEmblaApi,
  items = [],
  layout
}: WhatsIncludedV2Props) => {
  const { isMobile } = useIsMobile()
  return (
    <Slider
      setEmblaList={setEmblaApi}
      options={{ align: isMobile ? 'center' : 'start', }}
      styleProp={styles.slider}
      styleContainerProp={styles.sliderContainer}
    >
      {items.map((item: any) => {
        const onlyTextCard = empty(item.image) && empty(item.video)
        const onlyImageCard = (notEmpty(item.image) || notEmpty(item.video)) && empty(item.title)
        const imageAndText = !onlyTextCard && !onlyImageCard

        if (layout === 'horizontal') {
          return (
            <Slide styleProp={styles.horizontal} key={item.id}>
              <WhatsIncludedV2HorizontalCard item={item} />
            </Slide>
          )
        }

        return (
          <Slide
            styleProp={[
              styles.slide,
              (layout === 'three-column') && styles.threeColumns
            ]}
            key={item.id}
          >
            <RenderIf condition={onlyTextCard}>
              <WhatsIncludedV2TextCard item={item} />
            </RenderIf>
            <RenderIf condition={imageAndText || onlyImageCard}>
              <WhatsIncludedV2Card item={item} />
            </RenderIf>
          </Slide>
        )
      })}
    </Slider>
  )
}

export default WhatsIncludedV2Cards
