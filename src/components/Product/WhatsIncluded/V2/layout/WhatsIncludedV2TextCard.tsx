import { MultifunctionalChildProps } from './WhatsIncludedV2Card'

import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  card: {
    position: 'relative',
    borderRadius: spacing.sm,
    height: '100%',
    minHeight: {
      default: 'auto',
      [DESKTOP]: '400px',
    },
    overflow: 'hidden'
  },
  content: {
    paddingBlock: `calc(${spacing.xxs} * 7)`,
    justifyContent: 'center',
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: spacing.lg
    },
    borderRadius: spacing.sm,
    boxSizing: 'border-box',
    width: '100%',
    overflow: 'hidden',
  },
  description: {
    display: 'block'
  },
  video: {
    borderRadius: spacing.sm,
    overflow: 'hidden',
  },
})

const WhatsIncludedV2TextCard = ({ item }: { item: MultifunctionalChildProps }) => (
  <Container
    as="div"
    theme={item.theme}
    styleProp={[styles.card, styles.content]}
    gap="2"
    flex
  >
    <Typography as="h3" size="md" fontBold>{item.title}</Typography>
    <Typography
      as="span"
      size="bodyLarge"
      styleProp={styles.description}
    >
      {item.description}
    </Typography>
  </Container>
)

export default WhatsIncludedV2TextCard
