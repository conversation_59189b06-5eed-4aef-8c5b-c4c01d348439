'use client'

import WhatsIncludedV1 from './V1'
import WhatsIncludedV2Cards from './V2/layout/WhatsIncludedV2Cards'
import WhatsIncludedV2Content, { RenderLinks } from './V2/layout/WhatsIncludedV2Content'
import WhatsIncludedV1Cards from './V1/WhatsIncludedV1Cards'

import Wrapper from '@/components/layout/Wrapper'
import Container from '@components/layout/Container'
import { spacing } from '@/app/themeTokens.stylex'
import { AnchorProps } from '@/components/Generic/CallToAction'
import RenderIf from '@/components/Generic/RenderIf'
import { notEmpty } from '@/utils/checking'
import Typography from '@/components/Typography'

import React, { useState, useEffect } from 'react'
import * as stylex from '@stylexjs/stylex'

type WhatsIncludedProps = {
  layout: string
  header: string,
  items: any,
  content: any
  fixedLink?: AnchorProps | null,
  anchorTargeting?: string
}

const styles = stylex.create({
  wrapper: {
    padding: {
      default: spacing.md,
      '@media (min-width: 1024px)': spacing.lg
    }
  },
  fixedLink: {
    display: 'flex',
    justifyContent: 'flex-end',
    marginTop: {
      default: spacing.md,
      '@media (min-width: 1024px)': '0'
    }
  },
  header: {
    marginBottom: spacing.lg
  },
})

/* eslint-disable complexity */
const WhatsIncluded = ({
  layout,
  header,
  items = [],
  content,
  fixedLink = null,
  anchorTargeting
}: WhatsIncludedProps) => {
  const version = layout ? `v${layout.replace(/\D/g, '')}` : 'v1'

  const [emblaApi, setEmblaApi] = useState([])
  const [lastIndex, setLastIndex] = useState(false)
  const [firstIndex, setFirstIndex] = useState(true)
  const [currentPosition, setCurrentPosition] = useState(0)
  const [currentItem, setCurrentItem] = useState({
    description: '', links: [], items: [], layout: 'default'
  })
  const MIN_ITEMS = 3
  const { ctaDialog } = content || {}
  const dialog = Object.keys(ctaDialog).length > 0 ? ctaDialog : null

  const handleControls = (embla: any) => {
    setFirstIndex(!embla.canScrollPrev())
    setLastIndex(!embla.canScrollNext())
  }

  const handleNext = () => {
    emblaApi.forEach((embla: any) => {
      embla.scrollNext()
      handleControls(embla)
    })
  }

  const handlePrev = () => {
    emblaApi.forEach((embla: any) => {
      embla.scrollPrev()
      handleControls(embla)
    })
  }

  useEffect(() => {
    if (content?.items?.length === 0) return
    setCurrentItem(content?.items[currentPosition])
  }, [currentPosition, content])

  useEffect(() => {
    if (emblaApi.length === 0) return
    emblaApi.forEach((embla: any) => embla.on('select', () => handleControls(embla)))
  }, [emblaApi])

  if (version === 'v3') {
    return (
      <Wrapper size="xl" styleProp={styles.wrapper}>
        <span id={anchorTargeting} />
        <Container
          as="section"
          size="6"
        >
          {header && (
          <Typography
            as="h4"
            typographyTheme="h4Secondary"
            styleProp={styles.header}
          >{header}
          </Typography>
          )}
          {currentItem?.items && (
            <WhatsIncludedV2Cards
              setEmblaApi={setEmblaApi}
              items={currentItem.items}
              layout={currentItem.layout}
            />
          )}
          <WhatsIncludedV2Content
            description={currentItem?.description}
            links={currentItem?.links}
            dialog={dialog}
            firstIndex={firstIndex}
            lastIndex={lastIndex}
            handleNext={handleNext}
            handlePrev={handlePrev}
            hideNav={currentItem.items.length <= MIN_ITEMS}
          />
        </Container>
      </Wrapper>
    )
  }

  if (version === 'v2') {
    return (
      <Wrapper size="xl" styleProp={styles.wrapper}>
        <span id={anchorTargeting} />
        <WhatsIncludedV1
          title={header}
          items={items}
          size="6"
          currentPosition={currentPosition}
          setCurrentPosition={setCurrentPosition}
          mobileNav
          desktopNav
        />
        <Container
          as="section"
          size="6"
        >
          {currentItem?.items && (
            <WhatsIncludedV2Cards
              setEmblaApi={setEmblaApi}
              items={currentItem.items}
              layout={currentItem.layout}
            />
          )}
          <WhatsIncludedV2Content
            description={currentItem?.description}
            links={currentItem?.links}
            dialog={dialog}
            firstIndex={firstIndex}
            lastIndex={lastIndex}
            handleNext={handleNext}
            handlePrev={handlePrev}
            hideNav={currentItem.items.length <= MIN_ITEMS}
          />
        </Container>
      </Wrapper>
    )
  }
  if (version === 'v1') {
    return (
      <Wrapper size="xl" styleProp={styles.wrapper}>
        <span id={anchorTargeting} />
        <WhatsIncludedV1
          title={header}
          items={items}
          size="6"
          currentPosition={currentPosition}
          setCurrentPosition={setCurrentPosition}
          desktopNav
          borderTop
          popover
          mobileNav
        />
        {currentItem?.items && (
          <WhatsIncludedV1Cards
            setEmblaApi={setEmblaApi}
            items={currentItem.items}
            links={currentItem?.links}
          />
        )}
        <RenderIf condition={notEmpty(fixedLink)}>
          <Container styleProp={styles.fixedLink}>
            <RenderLinks links={fixedLink ? [fixedLink] : []} />
          </Container>
        </RenderIf>
      </Wrapper>
    )
  }
  return null
}

export default WhatsIncluded
