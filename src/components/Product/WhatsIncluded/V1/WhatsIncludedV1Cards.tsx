'use client'

import { RenderLinks } from '../V2/layout/WhatsIncludedV2Content'

import Slider, { Slide } from '@/components/Generic/Slider'
import { spacing, globalTokens as $ } from '@/app/themeTokens.stylex'
import RenderIf from '@/components/Generic/RenderIf'
import useIsMobile from '@/hooks/isMobile'
import Typography from '@/components/Typography'
import { AnchorProps } from '@/components/Generic/CallToAction'

import * as stylex from '@stylexjs/stylex'
import React from 'react'
import Image from 'next/image'

const styles = stylex.create({
  description: {
    marginBottom: spacing.md,
  },
  image: {
    display: 'block',
    width: '100%',
    height: 'auto',
    marginBottom: spacing.md,
    borderRadius: $.borderRadius
  },
  sliderContainer: {
    gap: spacing.md,
  },
  slide: {
    flex: '0 0 100%',
    position: 'relative',
  },
})

type WhatsIncludedV1Props = {
  setEmblaApi: any
  items: any,
  links: AnchorProps[]
}

const WhatsIncludedV1Cards = ({
  setEmblaApi,
  items = [],
  links = []
}: WhatsIncludedV1Props) => {
  const { isMobile } = useIsMobile()
  return (
    <RenderIf condition={isMobile}>
      <Slider
        setEmblaList={setEmblaApi}
        options={{ align: 'start', }}
        styleContainerProp={styles.sliderContainer}
      >
        {items.map((item: any) => (
          <Slide
            styleProp={[styles.slide]}
            key={item.id}
          >
            <Image src={item?.image?.url} width={item.image?.width} height={item.image.height} alt={item.name} {...stylex.props(styles.image)} />
            <Typography as="span" typographyTheme="bodySmall" styleProp={styles.description}>
              {item.description}
            </Typography>
          </Slide>
        ))}
      </Slider>
      <RenderLinks links={links} />
    </RenderIf>
  )
}

export default WhatsIncludedV1Cards
