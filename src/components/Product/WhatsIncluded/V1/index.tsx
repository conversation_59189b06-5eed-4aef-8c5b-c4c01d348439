'use client'

import Typography from '@components/Typography'
import { colors, spacing, globalTokens as $ } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container, { Size } from '@components/layout/Container'
import Wrapper from '@components/layout/Wrapper'
import Slider, { Slide } from '@components/Generic/Slider'
import Icon, { IconName } from '@/components/Generic/Icon'
import RenderIf from '@/components/Generic/RenderIf'
import Popover from '@/components/Generic/Popover'
import useIsMobile from '@/hooks/isMobile'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import { useEffect, useState } from 'react'

export type WhatsIncludedV1Props = {
  title?: string;
  theme?: ThemeColors;
  size?: Size;
  items: any;
  currentPosition: any
  setCurrentPosition: any
  desktopNav?: boolean
  borderTop?: boolean
  popover?: boolean
  mobileNav?: boolean
}

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  wrapper: {
    gap: spacing.md,
    overflow: 'hidden',
  },
  container: {
    gap: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    },
    paddingBlock: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    }
  },
  sliderParent: {
    width: '100%',
    display: 'flex',
    borderBottomWidth: '1px',
    borderBottomStyle: 'solid',
    borderBottomColor: colors.gray300,
  },
  topBorder: {
    borderTopWidth: '1px',
    borderTopStyle: 'solid',
    borderTopColor: colors.gray300
  },
  slider: {
    flex: '1',
  },
  list: {
  },
  item: {
    transform: 'translate3d(0, 0, 0)',
    minWidth: '0',
    flex: {
      default: '0 0 20%',
      [DESKTOP]: '0 0 20%',
    },
    opacity: {
      default: 0.5,
      [DESKTOP]: 1,
    },
  },
  img: {
    maxWidth: '100%',
    objectFit: 'contain',
    width: {
      default: '67px',
      [DESKTOP]: '97px',
    },
    height: 'auto',
  },
  btn: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    marginInline: 'auto',
    gap: 0,
    fontWeight: {
      default: 400,
      ':hover': 700,
    },
    paddingBlockEnd: spacing.xs,
    borderBottomWidth: '3px',
    borderBottomStyle: 'solid',
    borderBottomColor: {
      default: 'transparent',
      ':hover': colors.black,
    },
    textAlign: 'center',
    height: '100%',
  },
  itemActive: {
    opacity: '1',
  },
  btnActive: {
    fontWeight: 700,
    borderBottomColor: colors.black,
  },
  label: {
    color: colors.black,
  },
  hideMobile: {
    display: {
      default: 'none',
      [DESKTOP]: 'block',
    },
  },
  mobileNav: {
    display: {
      default: 'flex',
      [DESKTOP]: 'none',
    },
  },
  popover: {
    padding: '0',
    backgroundColor: 'transparent !important',
  },
  popoverImage: {
    display: 'block',
    borderRadius: $.borderRadius,
    width: '331px',
    height: '191px',
  },
})

type ArrowButtonProps = {
  ariaLabel?: string;
  disabled?: boolean;
  onClick?: () => void;
  iconName?: IconName;
}

const ArrowButton = ({
  ariaLabel = '',
  disabled = false,
  onClick = () => {},
  iconName = 'LeftArrow'
}: ArrowButtonProps) => (
  <button
    type="button"
    aria-label={ariaLabel}
    disabled={disabled}
    onClick={onClick}
  >
    <Icon
      name={iconName}
      size="medium"
      fill={disabled ? colors.gray300 : colors.black}
    />
  </button>
)

const NUMBER_OF_SLIDES = 5

const WhatsIncludedV1 = ({
  title,
  items = [],
  theme,
  size,
  currentPosition,
  setCurrentPosition,
  desktopNav = false,
  borderTop = false,
  popover = false,
  mobileNav = false,
}: WhatsIncludedV1Props) => {
  const { isMobile } = useIsMobile()
  const [emblaList, setEmblaList] = useState<any[]>([])

  const [emblaApi] = emblaList

  const [canScrollPrev, setCanScrollPrev] = useState(false)
  const [canScrollNext, setCanScrollNext] = useState(false)

  const [leftBoundary, setLeftBoundary] = useState(0)
  const [rightBoundary, setRightBoundary] = useState(NUMBER_OF_SLIDES - 1)

  useEffect(() => {
    if (!emblaApi) return

    const updateButtons = () => {
      setCanScrollPrev(emblaApi.canScrollPrev())
      setCanScrollNext(emblaApi.canScrollNext())
    }

    emblaApi.on('select', updateButtons)
    emblaApi.on('pointerUp', updateButtons)

    updateButtons()

    // eslint-disable-next-line consistent-return
    return () => {
      emblaApi.off('select', updateButtons)
      emblaApi.off('pointerUp', updateButtons)
    }
  }, [emblaApi])

  const handleMobilePosition = (type: string) => () => {
    if (type === 'prev' && currentPosition > 0) {
      setCurrentPosition(currentPosition - 1)
    }
    if (type === 'next' && currentPosition < items.length - 1) {
      setCurrentPosition(currentPosition + 1)
    }
  }

  useEffect(() => {
    if (currentPosition > rightBoundary) {
      setLeftBoundary(currentPosition - (NUMBER_OF_SLIDES - 1))
      setRightBoundary(currentPosition)
    }

    if (currentPosition < leftBoundary) {
      setLeftBoundary(currentPosition)
      setRightBoundary(currentPosition - (NUMBER_OF_SLIDES - 1))
    }
  }, [currentPosition])

  useEffect(() => {
    if (!emblaApi) return

    emblaApi.scrollTo(leftBoundary, true)
  }, [leftBoundary])

  return (
    <Wrapper as="section" theme={theme} styleProp={styles.wrapper}>
      <Container
        as="div"
        size={size}
        flex
        align="center"
        styleProp={styles.container}
      >
        {title && (
        <Typography
          as="h4"
          size="h5"
          fontSecondary
          fontBold
        >{title}
        </Typography>
        )}

        <Container styleProp={[
          styles.sliderParent,
          borderTop && styles.topBorder
        ]}
        >
          <RenderIf condition={desktopNav && !isMobile && items.length > NUMBER_OF_SLIDES}>
            <ArrowButton
              ariaLabel="Previous"
              disabled={!canScrollPrev}
              onClick={() => emblaApi.scrollPrev()}
              iconName="LeftArrow"
            />
          </RenderIf>
          <Slider
            options={{
              align: 'start',
              slidesToScroll: 1,
            }}
            styleProp={styles.slider}
            styleContainerProp={styles.list}
            setEmblaList={setEmblaList}
          >
            {items.map((item: any, i: number) => {
              const slideContent = (
                <button
                  type="button"
                  {...stylex.props(styles.btn, currentPosition === i && styles.btnActive)}
                  onClick={() => setCurrentPosition(i)}
                >
                  <Image
                    src={item.img}
                    alt={item.name}
                    width={95}
                    height={95}
                    {...stylex.props(styles.img)}
                  />
                  <span {...stylex.props(styles.hideMobile)}>{item.name}</span>
                </button>
              )

              const popupImage = item.items ? item.items[0]?.image : null

              const popContent = popupImage ? (
                <Image
                  src={popupImage.url}
                  width={popupImage.width}
                  height={popupImage.height}
                  alt={item.name}
                  {...stylex.props(styles.popoverImage)}
                />
              ) : null

              return (
                <Slide
                  key={item.id}
                  styleProp={[
                    styles.item,
                    currentPosition === i && styles.itemActive
                  ]}
                >
                  <RenderIf condition={popover && !isMobile}>
                    <Popover
                      content={
                        popContent
                    }
                      contentStyleProp={{ ...styles.popover }}
                    >
                      {slideContent}
                    </Popover>
                  </RenderIf>
                  <RenderIf condition={!popover || isMobile}>
                    {slideContent}
                  </RenderIf>
                </Slide>
              )
            })}

          </Slider>
          <RenderIf condition={desktopNav && !isMobile && items.length > NUMBER_OF_SLIDES}>
            <ArrowButton
              ariaLabel="Next"
              disabled={!canScrollNext}
              onClick={() => emblaApi.scrollNext()}
              iconName="RightArrow"
            />
          </RenderIf>
        </Container>

        <RenderIf condition={mobileNav}>
          <Container
            as="div"
            gap="1"
            flex
            flexRow
            styleProp={styles.mobileNav}
            flexCentered
            spaceBetween
          >
            <ArrowButton
              ariaLabel="Previous"
              disabled={currentPosition === 0}
              onClick={handleMobilePosition('prev')}
              iconName="LeftArrow"
            />
            <span>{items[currentPosition]?.name}</span>
            <ArrowButton
              ariaLabel="Next"
              disabled={currentPosition === items.length - 1}
              onClick={handleMobilePosition('next')}
              iconName="RightArrow"
            />
          </Container>
        </RenderIf>
      </Container>
    </Wrapper>
  )
}

export default WhatsIncludedV1
