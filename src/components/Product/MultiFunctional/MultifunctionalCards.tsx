'use client'

import MultifunctionalTextCard from './MultifunctionalTextCard'
import MultifunctionalMediaCard from './MultifunctionalMediaCard'
import MultifunctionalCard from './MultifunctionalCard'

import Slider, { Slide } from '@/components/Generic/Slider'
import { spacing } from '@/app/themeTokens.stylex'
import { empty, notEmpty } from '@/utils/checking'
import RenderIf from '@/utils/renderIf'
import useIsMobile from '@/hooks/isMobile'

import * as stylex from '@stylexjs/stylex'
import React from 'react'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  slider: {
    flex: '1',
    height: 'auto',
  },
  sliderContainer: {
    gap: spacing.md,
    paddingLeft: {
      default: spacing.md,
      [DESKTOP]: 0,
    },
    height: {
      default: 'auto',
      // [DESKTOP]: '100%',
    },
  },
  twoColumns: {
    aspectRatio: '466/556',
    flex: {
      default: `0 0 calc(100% - ${spacing.xl})`,
      [DESKTOP]: `0 0 calc(100% / 2 - ${spacing.md})`
    },
  },
  horizontal: {
    flex: {
      default: '0 0 90vw',
      [DESKTOP]: `0 0 calc(100% - ${spacing.md})`
    },
    display: 'flex',
    alignItems: 'center',
    position: 'relative',
  },
})

type MultifunctionalProps = {
  setEmblaApi: any
  items: any,
}

const MultifunctionalCards = ({ setEmblaApi, items = [] }: MultifunctionalProps) => {
  const { isMobile } = useIsMobile()
  return (
    <Slider
      setEmblaList={setEmblaApi}
      options={{ align: isMobile ? 'center' : 'start', }}
      styleProp={styles.slider}
      styleContainerProp={styles.sliderContainer}
    >
      {items.map((item: any) => {
        const onlyTextCard = empty(item.image) && empty(item.video)
        const onlyImageCard = (notEmpty(item.image) || notEmpty(item.video)) && empty(item.title)
        const imageAndText = !onlyTextCard && !onlyImageCard

        return (
          <Slide styleProp={styles.twoColumns} key={item.id}>
            <RenderIf condition={onlyTextCard}>
              <MultifunctionalTextCard item={item} />
            </RenderIf>
            <RenderIf condition={imageAndText}>
              <MultifunctionalCard item={item} hoverBehavior={false} />
            </RenderIf>
            <RenderIf condition={onlyImageCard}>
              <MultifunctionalMediaCard item={item} />
            </RenderIf>
          </Slide>
        )
      })}
    </Slider>
  )
}

export default MultifunctionalCards
