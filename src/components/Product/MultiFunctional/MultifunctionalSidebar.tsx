import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { colors, fontSizes, spacing } from '@/app/themeTokens.stylex'
import useIsMobile from '@/hooks/isMobile'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

type Item = {
  id: number
  name: string
}

type MultifunctionalSidebarProps = {
  title: string
  items: Item[]
  currentItem: Item
  setCurrentItem: React.Dispatch<React.SetStateAction<Item>>
  children: React.ReactNode
}

const DESKTOP = '@media (min-width: 1024px)'

const animationIn = stylex.keyframes({
  from: {
    width: '0%',
  },
  to: {
    width: '100%',
  },
})
const animationOut = stylex.keyframes({
  from: {
    width: '100%',
  },
  to: {
    width: '0%',
  },
})

const styles = stylex.create({
  root: {
    flexDirection: {
      default: 'column-reverse',
      [DESKTOP]: 'row',
    },
    gap: {
      default: spacing.lg,
      [DESKTOP]: 0,
    },
    alignItems: 'center',
  },
  sidebar: {
    gap: spacing.xl,
    width: '100%',
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: spacing.xl,
    },
    maxWidth: {
      default: '100%',
      [DESKTOP]: '300px',
    }
  },
  categories: {
    display: 'flex',
    flexDirection: {
      default: 'row',
      [DESKTOP]: 'column',
    },
    gap: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    },
    flexWrap: 'wrap',
  },
  content: {
    flex: '1',
    display: {
      default: 'block',
      [DESKTOP]: 'flex',
    },
    position: 'relative',
    maxWidth: '100%',
    alignItems: 'center',
  },
  title: {
    maxWidth: '100%',
    wordWrap: 'break-word',
    textWrap: 'balance',
    display: {
      default: 'none',
      [DESKTOP]: 'block',
    }
  },
  sidebarItem: {
    color: colors.gray,
    fontSize: fontSizes.sm,
    cursor: 'pointer',
    '::after': {
      animationName: animationOut,
      animationDuration: '0.3s',
      animationTimingFunction: 'ease-out',
      content: '""',
      display: 'block',
      height: '2px',
      width: '0',
      backgroundColor: colors.marigold,
    }
  },
  sidebarItemActive: {
    color: colors.navy,
    fontWeight: 'bold',
    '::after': {
      animationName: animationIn,
      width: '100%',
    }
  }
})

const MultifunctionalSidebar = ({
  title,
  items = [],
  currentItem,
  setCurrentItem,
  children
}: MultifunctionalSidebarProps) => {
  const { isMobile } = useIsMobile()

  return (
    <Container
      as="section"
      size="6"
      flex
      flexRow
      noWrap
      align="center"
      paddingBlock="6"
      styleProp={styles.root}
    >
      <Container as="aside" styleProp={styles.sidebar} flex>
        <Typography
          as="h4"
          size="h5"
          fontSecondary
          fontBold
          styleProp={styles.title}
        >
          {title}
        </Typography>
        <Container as="ul" styleProp={styles.categories}>
          {items.map((item: any) => (
            <li key={item.id}>
              <button
                type="button"
                aria-label="Previous"
                {...stylex.props(
                  styles.sidebarItem,
                  currentItem.id === item.id && styles.sidebarItemActive
                )}
                onMouseEnter={isMobile ? () => {} : () => setCurrentItem(item)}
                onClick={() => setCurrentItem(item)}
              >
                {item.name}
              </button>
            </li>
          ))}
        </Container>
      </Container>
      <Container as="div" gap="1" styleProp={styles.content}>
        {children}
      </Container>
    </Container>
  )
}

export default MultifunctionalSidebar
