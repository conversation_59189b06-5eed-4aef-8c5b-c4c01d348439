import { MultifunctionalChildProps } from '../WhatsIncluded/V2/layout/WhatsIncludedV2Card'

import Card from '@components/Generic/Card'
import { spacing } from '@/app/themeTokens.stylex'
import { videoProps } from '@/components/Generic/Card/types'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  card: {
    position: 'relative',
    borderRadius: spacing.sm,
    overflow: 'hidden',
    height: '100%',
  },
  video: {
    height: '100%',
  }
})

const MultifunctionalMediaCard = ({ item }: { item: MultifunctionalChildProps }) => (
  <Card
    image={item.image}
    video={item.video as videoProps}
    theme={item.theme}
    styleProp={styles.card}
    styleVideoProp={styles.video}
    styleImageProp={styles.video}
  />
)

export default MultifunctionalMediaCard
