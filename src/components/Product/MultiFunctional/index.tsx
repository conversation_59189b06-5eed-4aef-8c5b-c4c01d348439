'use client'

import MultifunctionalSidebar from './MultifunctionalSidebar'
import MultifunctionalCards from './MultifunctionalCards'

import Container from '@components/layout/Container'
// import WhatsIncluded from '@/components/Product/WhatsIncluded'
import { colors, spacing } from '@/app/themeTokens.stylex'
import { NextCircleButton, PrevCircleButton } from '@/components/Generic/Slider/SliderButtons'
import Typography from '@/components/Typography'
import { notEmpty } from '@/utils/checking'

import * as stylex from '@stylexjs/stylex'
import React, { useState, useEffect } from 'react'

type MultiFunctionalProps = {
  header: string,
  items: any,
}

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  controls: {
    position: 'absolute',
    width: '90%',
    insetInline: 0,
    marginInline: 'auto',
    top: '50%',
    transform: 'translateY(-50%)',
    pointerEvents: 'none',
    display: {
      default: 'none',
      [DESKTOP]: 'flex',
    }
  },
  mobileTitle: {
    display: {
      default: 'block',
      [DESKTOP]: 'none',
    },
    paddingInline: spacing.md,
    marginBottom: `calc(${spacing.md} + ${spacing.sm})`,
    textAlign: 'center',
  },
  button: {
    pointerEvents: 'auto',
    lineHeight: 0,
    background: colors.white,
    borderRadius: '50%',
    opacity: {
      default: 1,
      ':disabled': 0,
    }
  },
})

const MultiFunctional = ({ header, items = [] }: MultiFunctionalProps) => {
  const [emblaApi, setEmblaApi] = useState<any[]>([])
  const [lastIndex, setLastIndex] = useState(false)
  const [firstIndex, setFirstIndex] = useState(true)

  const [currentItem, setCurrentItem] = useState(items[0] || {
    description: '', links: [], items: [], layout: 'default'
  })

  const handleControls = (embla: any) => {
    setFirstIndex(!embla.canScrollPrev())
    setLastIndex(!embla.canScrollNext())
  }

  const handleNext = () => {
    emblaApi.forEach((embla: any) => {
      embla.scrollNext()
      handleControls(embla)
    })
  }

  const handlePrev = () => {
    emblaApi.forEach((embla: any) => {
      embla.scrollPrev()
      handleControls(embla)
    })
  }

  useEffect(() => {
    if (emblaApi.length === 0) return
    emblaApi.forEach((embla: any) => embla.on('select', () => handleControls(embla)))
  }, [emblaApi])

  useEffect(() => {
    if (notEmpty(emblaApi)) {
      handleControls(emblaApi[0])
      emblaApi[0]?.scrollTo(0)
    }
  }, [emblaApi, currentItem.id])

  return (
    <MultifunctionalSidebar
      title={header}
      items={items}
      currentItem={currentItem}
      setCurrentItem={setCurrentItem}
    >
      <Typography
        as="h4"
        size="h5"
        fontSecondary
        fontBold
        styleProp={styles.mobileTitle}
      >
        {header}
      </Typography>
      <MultifunctionalCards
        setEmblaApi={setEmblaApi}
        items={currentItem?.items}
      />
      <Container
        as="div"
        flex
        flexCentered
        flexRow
        spaceBetween
        styleProp={styles.controls}
      >
        <PrevCircleButton
          enabled={!firstIndex}
          fill={colors.navy}
          onClick={handlePrev}
          styleProp={styles.button}
        />

        <NextCircleButton
          enabled={!lastIndex}
          fill={colors.navy}
          onClick={handleNext}
          styleProp={styles.button}
        />
      </Container>
    </MultifunctionalSidebar>
  )
}

export default MultiFunctional
