import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import Card from '@components/Generic/Card'
import { spacing } from '@/app/themeTokens.stylex'
import { imageProps, videoProps } from '@/components/Generic/Card/types'
import { ThemeColors } from '@/app/themeThemes.stylex'

import * as stylex from '@stylexjs/stylex'
import React, { useState } from 'react'

const DESKTOP = '@media (min-width: 1024px)'

export type MultifunctionalChildProps = {
  id: string
  theme: ThemeColors,
  title: string
  description: any
  overlay: boolean
  image?: imageProps
  video?: {
    ratio: string
    playOnHover: boolean
    sources: {
      src: string
      type: string
    }[]
  }
}

const styles = stylex.create({
  card: {
    position: 'relative',
    borderRadius: spacing.sm,
    overflow: 'hidden',
    height: '100%',
  },
  content: {
    bottom: '0',
    insetInline: '0',
    paddingBlock: `calc(${spacing.xxs} * 7)`,
    paddingInline: spacing.md,
    gap: spacing.xs,
    height: 'auto',
    overflow: 'hidden',
  },
  contentAbsolute: {
    position: 'absolute',
    height: 'auto',
    borderRadius: spacing.sm,
    paddingBlock: {
      default: spacing.md,
      [DESKTOP]: `calc(${spacing.xxs} * 7)`
    },
  },
  description: {
    marginTop: spacing.xs,
    display: {
      default: 'block',
      [DESKTOP]: 'none'
    }
  },
  activeDescription: {
    display: 'block',
  }
})

const MultifunctionalCard = (
  { item, hoverBehavior = true }:
  { item: MultifunctionalChildProps, hoverBehavior?: boolean }
) => {
  const [hover, setHover] = useState(
    !hoverBehavior
  )

  return (
    <Card
      image={item.image}
      video={item.video as videoProps}
      theme={item.theme}
      styleProp={styles.card}
      setIsHovered={hoverBehavior ? setHover : () => {}}
    >
      <Container
        as="div"
        theme={item.theme}
        styleProp={{ ...styles.content, ...item.overlay && styles.contentAbsolute }}

      >
        <Typography as="h3" typographyTheme="bodyLarge" fontBold>{item.title}</Typography>
        <Typography
          as="p"
          size="bodySmall"
          styleProp={{
            ...styles.description,
            ...hover || !item.overlay ? styles.activeDescription : {}
          }}
        >
          {item.description}
        </Typography>
      </Container>
    </Card>
  )
}

export default MultifunctionalCard
