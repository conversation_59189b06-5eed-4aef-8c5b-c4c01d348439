'use client'

import RenderIf from '../Generic/RenderIf'

import Container from '@components/layout/Container'
import { SingleOpenedAccordion } from '@components/Generic/Accordion'
import Typography from '@components/Typography'
import { spacing, globalTokens as $ } from '@/app/themeTokens.stylex'
import MediaVideo from '@components/Generic/MediaVideo'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { useSingleAccordion } from '@/hooks/useSingleAccordion'
import getMediaType from '@/utils/getMediaType'

import * as stylex from '@stylexjs/stylex'
import React from 'react'
import Image from 'next/image'

type mediaProps = {
  url: string;
  height: number,
  width: number,
}

const DEFAULT_IMAGE_WIDTH = 500
const DEFAULT_IMAGE_HEIGHT = 500

export type accordionSplitProps = {
  title: string,
  theme: ThemeColors,
  items: Array<{
    id: string
    title: string
    content: string
    media: mediaProps,
    mobileMedia: mediaProps
  }>,
  anchorTargeting?: string
}

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  layout: {
    display: 'flex',
    gap: 0,
    paddingBlock: spacing.lg,
    margin: '0 auto',
    justifyContent: 'center',
  },
  accordionLayout: {
    maxWidth: {
      default: '100%',
      [DESKTOP]: '35%',
    },
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: spacing.xl,
    },
    minHeight: {
      default: 'auto',
      [DESKTOP]: '680px'
    }
  },
  mediaLayout: (height: string) => ({
    width: '100%',
    maxWidth: '50%',
    height,
    display: {
      default: 'none',
      [DESKTOP]: 'block',
    }
  }),
  title: {
    marginBlockEnd: {
      default: `calc(${spacing.xs} * 4)`,
      [DESKTOP]: spacing.xl,
    },
    display: 'block',
  },
  mobileContainer: {
    width: '100%',
    marginBlock: spacing.md,
    minHeight: '150px',
    display: {
      default: 'block',
      [DESKTOP]: 'none',
    }
  },
  mobileVideoContainer: {
    height: '350px',
  },
  mobileImage: {
    maxWidth: '100%',
    width: '100%',
    height: 'auto',
  },
  mediaWithRadius: {
    maxWidth: '100%',
    borderRadius: $.borderRadius,
    overflow: 'hidden',
  },
  mediaMaxWidth: {
    maxWidth: '670px',
  }
})

const AccordionSplit = ({
  title,
  theme,
  items,
  anchorTargeting
}: accordionSplitProps) => {
  const accordionState = useSingleAccordion(items[0]?.title)

  const currentItem = items.find((item) => item.title === accordionState.isOpenedId) || null

  const desktopMedia = currentItem?.media
  const mobileMedia = currentItem?.mobileMedia ?? currentItem?.media

  const maxDesktopHeight = 675
  const safeDesktopHeight = () => (
    (desktopMedia?.height || 0) > maxDesktopHeight
      ? maxDesktopHeight
      : desktopMedia?.height
  )

  return (
    <Container
      theme={theme}
      styleProp={styles.layout}
    >
      <span id={anchorTargeting} />
      <Container styleProp={styles.accordionLayout}>
        <Typography
          as="h4"
          typographyTheme="h4Secondary"
          fontSecondary
          styleProp={styles.title}
        >
          {title}
        </Typography>

        {items.length > 0 && items.map((item, index) => (
          <SingleOpenedAccordion
            titleTypographyTheme="h6Primary"
            isOpenedId={accordionState.isOpenedId}
            handleToggle={accordionState.handleToggle}
            key={item.id}
            title={item.title}
            open={index === 0}
            isLast={index === items.length - 1}
          >
            {item.content}
            {
                mobileMedia && (
                  <Container styleProp={styles.mobileContainer}>
                    <RenderIf condition={getMediaType(mobileMedia.url) === 'video'}>
                      <Container styleProp={styles.mobileVideoContainer}>
                        <MediaVideo
                          objectFit="contain"
                          media={mobileMedia?.url}
                          styleProp={styles.mediaWithRadius}
                        />
                      </Container>
                    </RenderIf>
                    <RenderIf condition={getMediaType(mobileMedia.url) === 'image'}>
                      <Image
                        src={mobileMedia.url}
                        alt={item.title}
                        width={mobileMedia.width || DEFAULT_IMAGE_WIDTH}
                        height={mobileMedia.height || DEFAULT_IMAGE_HEIGHT}
                        {...stylex.props(styles.mediaWithRadius, styles.mobileImage)}
                      />
                    </RenderIf>
                  </Container>
                )
              }
          </SingleOpenedAccordion>
        ))}

      </Container>
      {desktopMedia && (
        <Container
          styleProp={[
            styles.mediaLayout(`${safeDesktopHeight()}px` || ''),
            styles.mediaMaxWidth
          ]}
        >
          <RenderIf condition={getMediaType(desktopMedia.url) === 'video'}>
            <MediaVideo
              objectFit="contain"
              media={desktopMedia?.url}
              videoHeight={`${desktopMedia?.height}px`}
              imageHeight={`${desktopMedia?.height}px`}
              styleProp={styles.mediaWithRadius}
            />
          </RenderIf>
          <RenderIf condition={getMediaType(desktopMedia.url) === 'image'}>
            <Image
              src={desktopMedia.url}
              alt={desktopMedia.url}
              width={desktopMedia.width}
              height={desktopMedia.height}
              {...stylex.props(styles.mediaWithRadius, styles.mobileImage)}
            />
          </RenderIf>
        </Container>
      )}
    </Container>
  )
}

export default AccordionSplit
