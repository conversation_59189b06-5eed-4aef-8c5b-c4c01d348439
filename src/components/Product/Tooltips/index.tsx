'use client'

import styles from './tokens.stylex'

import Wrapper from '@/components/layout/Wrapper'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { CTAProps } from '@/components/Generic/CallToAction'
import useIsMobile from '@/hooks/isMobile'
import Slider, { Slide } from '@/components/Generic/Slider'
import { DotButtons, PrevButton, NextButton } from '@/components/Generic/Slider/SliderButtons'
import { colors } from '@/app/themeTokens.stylex'
import RenderCTAS from '@/components/Generic/RenderCTAS'

import Image from 'next/image'
import React, { useState, useCallback } from 'react'
import * as stylex from '@stylexjs/stylex'

export type popupProps = {
  id?: string
  title: string
  location?: 'popupLeft' | 'popupRight' | 'popupTop' | 'popupBottom'
  description: string
  cta?: CTAProps
}

export type HotspotProps = {
  position: {
    mobile: {
      x: number
      y: number
    },
    desktop: {
      x: number
      y: number
    }
  },
  popup: popupProps
}

export type toolTipsProps = {
  image: string
  mobileImage: string
  hotspots: HotspotProps[]
}

const Popup = ({
  id,
  title,
  description,
  cta,
  location = 'popupTop',
}: popupProps) => (
  <Container data-id={id} as="div" theme="white" styleProp={{ ...styles.popup, ...styles[location] }}>
    <Typography as="h6" size="md" fontBold>
      {title}
    </Typography>
    <Typography as="p" size="bodyLarge" marginBottom="md">
      {description}
    </Typography>
    {
      cta && (
      <RenderCTAS
        buttons={[cta]}
        mergeProps={{
          fullWidth: true,
          useArrow: true,
        }}
      />
      )
    }
  </Container>
)

const Tooltips = ({
  image,
  mobileImage,
  hotspots
}: toolTipsProps) => {
  const [emblaApi, setEmblaApi] = useState<any[]>([])
  const [currentTooltip, setCurrentTooltip] = useState<number | null>(null)
  const { isMobile } = useIsMobile()
  const [currentSlide, setCurrentSlide] = useState(0)
  const scrollSnaps = hotspots.map((_, i) => i)

  const setCurrent = (index: number) => {
    setCurrentTooltip(index)
    setCurrentSlide(index)
  }

  const scrollPrev = useCallback(() => {
    if (emblaApi.length > 0) {
      emblaApi[0].scrollPrev()
      setCurrent(currentSlide - 1)
    }
  }, [currentSlide, emblaApi])

  const scrollNext = useCallback(
    () => {
      if (emblaApi.length > 0) {
        emblaApi[0].scrollNext()
        setCurrent(currentSlide + 1)
      }
    },
    [currentSlide, emblaApi]
  )

  const handleScroll = (index: number) => () => {
    if (emblaApi.length > 0) {
      emblaApi[0].scrollTo(index)
      setCurrent(index)
    }
  }

  return (
    <Wrapper as="section">
      <Container as="div" styleProp={styles.hotspotContainer}>
        <Image
          src={mobileImage}
          objectFit="contain"
          alt="tooltips"
          width={375}
          height={600}
          {...stylex.props(styles.bgImage, styles.mobileImage)}
        />
        <Image
          src={image}
          objectFit="contain"
          alt="tooltips"
          width={1920}
          height={768}
          {...stylex.props(styles.bgImage)}
        />

        {hotspots.length > 0 && hotspots.map(({ position, popup }, i) => {
          const { x, y } = isMobile ? position.mobile : position.desktop
          return (
            <div
              key={popup.id}
              onMouseEnter={() => setCurrentTooltip(i)}
              onMouseLeave={() => setCurrentTooltip(null)}
              onClick={handleScroll(i)}
              {...stylex.props(styles.item)}
              style={{
                top: `${y}%`,
                left: `${x}%`,
              }}
            >
              <button
                type="button"
                {...stylex.props(
                  styles.hotspot,
                  currentTooltip === i && styles.hotspotActive
                )}
              >
                <div {...stylex.props(styles.bgHotspot)} />
              </button>
              {currentTooltip === i && !isMobile && (
                <Popup {...popup} />
              )}
            </div>
          )
        })}
      </Container>
      <Container as="div" styleProp={styles.mobileCarousel} paddingBlock="4" paddingInline="3">
        {hotspots.length > 0 && (
          <Slider setEmblaList={setEmblaApi}>
            {hotspots.map(({ popup }) => (
              <Slide key={popup.id}>
                <Typography
                  as="h6"
                  size="md"
                  fontBold
                  textCentered
                  marginBottom="xs"
                >
                  {popup.title}
                </Typography>
                <Typography
                  as="p"
                  size="bodyLarge"
                  marginBottom="md"
                  textCentered
                >
                  {popup.description}
                </Typography>
                {
      popup.cta && (
      <RenderCTAS
        buttons={[popup.cta]}
        mergeProps={{
          fullWidth: true,
          useArrow: true,
        }}
      />
      )
    }
              </Slide>
            ))}
          </Slider>
        )}
        <Container as="div" styleProp={styles.mobileCarouselDots}>
          <PrevButton
            enabled={currentSlide > 0}
            size="large"
            fill={colors.navy}
            onClick={scrollPrev}
          />
          <DotButtons
            scrollSnaps={scrollSnaps}
            handleScroll={handleScroll}
            styleProp={{ ...styles.dots }}
            currentSlide={currentSlide}
          />
          <NextButton
            enabled={currentSlide < hotspots.length - 1}
            size="large"
            fill={colors.navy}
            onClick={scrollNext}
          />
        </Container>
      </Container>
    </Wrapper>
  )
}

export default Tooltips
