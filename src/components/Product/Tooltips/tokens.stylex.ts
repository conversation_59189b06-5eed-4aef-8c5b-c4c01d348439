import { colors, spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const pulse = stylex.keyframes({
  '0%': {
    width: 'calc(100% + 2px)',
    height: 'calc(100% + 2px)'
  },
  '100%': {
    width: 'calc(100% + 16px)',
    height: 'calc(100% + 16px)'
  }
})

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  hotspotContainer: {
    position: 'relative',
  },
  bgImage: {
    width: '100%',
    height: 'auto',
    display: {
      default: 'none',
      [DESKTOP]: 'block',
    }
  },
  mobileImage: {
    display: {
      default: 'block',
      [DESKTOP]: 'none',
    }
  },
  item: {
    position: 'absolute',
    top: 0,
    left: 0,
    lineHeight: 0,
  },
  hotspot: {
    width: '24px',
    height: '24px',
    borderRadius: '50%',
    backgroundColor: colors.navy,
    borderWidth: '4px',
    borderStyle: 'solid',
    borderColor: colors.offWhite,
    cursor: 'pointer',
    lineHeight: 0,
  },
  hotspotActive: {
    backgroundColor: colors.white,
    borderColor: colors.navy,
  },
  bgHotspot: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '100%',
    height: '100%',
    margin: 'auto',
    background: colors.offWhite,
    borderRadius: '50%',
    content: '""',
    opacity: 0.35,
    animationName: pulse,
    animationDuration: '1.25s',
    animationTimingFunction: 'ease-in-out',
    animationIterationCount: 'infinite',
    animationDirection: 'alternate',
  },
  popup: {
    zIndex: 50,
    borderRadius: '20px',
    position: 'absolute',
    paddingBlock: spacing.md,
    paddingInline: spacing.lg,
    boxShadow: '1px 1px 6px 4px rgb(0 0 0 / 5%)',
    width: '345px',
    minHeight: '190px',
    lineHeight: 1.5,
    display: 'flex',
    flexDirection: 'column',
    gap: spacing.xs
  },
  popupLeft: {
    left: 'auto',
    right: '120%',
    top: '50%',
    transform: 'translateY(-50%)',
  },
  popupRight: {
    left: '120%',
    top: '50%',
    transform: 'translateY(-50%)',
  },
  popupTop: {
    top: 'auto',
    bottom: '120%',
    left: '50%',
    transform: 'translateX(-50%)',
  },
  popupBottom: {
    top: '120%',
    left: '50%',
    transform: 'translateX(-50%)',
  },
  mobileCarousel: {
    display: {
      default: 'block',
      [DESKTOP]: 'none',
    },
  },
  mobileCarouselDots: {
    display: 'flex',
    justifyContent: 'center',
    gap: spacing.lg,
    marginTop: spacing.lg,
  },
  dots: {
    display: 'flex',
    gap: spacing.md,
    justifyContent: 'center',
    paddingBlockStart: spacing.xs,
  },
})

export default styles
