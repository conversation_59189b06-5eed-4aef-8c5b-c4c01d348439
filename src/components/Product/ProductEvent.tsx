'use client'

import { useAppDispatch } from '@redux/hooks'
import { selectProduct } from '@redux/features/product/productSlice'

import { ReactNode } from 'react'

type ProductEventProps = {
  product: any
  children: ReactNode
}

// TODO: update payload before sending
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const ProductEvent = ({ product, children }: ProductEventProps) => {
  const dispatch = useAppDispatch()

  const handleClick = () => {
    dispatch(selectProduct('payload'))
  }

  return (
    <div onClick={handleClick}>
      {children}
    </div>
  )
}

export default ProductEvent
