import SwatchIcon from './SwatchIcon'
import SwatchText from './SwatchText'
import { LinkRenderFunction } from './types'

import ProductLink from '../ProductLink'

import { ProductCardFieldsProps, VariantProps } from '@components/Product/types'
import Container from '@components/layout/Container'
import {
  ContentfulProductSwatch,
  ContentfulProductSwatchCollection,
} from '@/lib/contentful/types/products'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  noIconGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(4, 1fr)',
    gap: spacing.sm,
    width: '100%',
  },
  textSwatchesContainer: {
    display: 'grid',
    gridTemplateColumns: 'repeat(4, 1fr)',
    gap: spacing.sm,
    width: '100%',
  },
  iconGridContainer: { gap: '9px' },
  swatch: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '34px',
    height: '34px',
    flexShrink: 0,
  },
  swatchText: {
    display: 'block',
    flex: `0 calc(25% - calc(${spacing.sm} / 2))`,
  },
  zIndex: {
    zIndex: 1,
  }
})

type SwatchesCollectionProps = {
  swatchCollection: ContentfulProductSwatchCollection;
  product: ProductCardFieldsProps;
  variant: VariantProps;
  attribute: string;
  activeSwatch?: ContentfulProductSwatch;
  renderLink?: (swatch: ContentfulProductSwatch) => LinkRenderFunction;
  oosSwatches?: string[]
};

const SwatchesCollection = ({
  swatchCollection,
  product,
  variant,
  attribute,
  activeSwatch,
  renderLink,
  oosSwatches
}: SwatchesCollectionProps) => {
  if (!swatchCollection.swatches) {
    return null
  }

  const swatches = swatchCollection.swatches.items

  const noIconGrid = swatches.every((swatch) => !swatch.icon)

  const containerProps = noIconGrid
    ? { styleProp: [styles.noIconGrid] }
    : {
      flex: true,
      flexRow: true,
      size: 'full',
      styleProp: [styles.zIndex, styles.iconGridContainer]
    }

  return (
    <Container {...containerProps as {}}>
      {swatches.map((swatch) => (
        <ProductLink
          key={swatch.slug}
          product={product as any}
          variant={variant as any}
          urlParams={{
            [attribute]: swatch.slug,
          }}
          styleProp={swatch.icon ? styles.swatch : styles.swatchText}
          renderLink={renderLink && ((props) => renderLink(swatch)(props))}
          scroll={false}
        >
          <SwatchIcon
            swatch={swatch}
            attribute={attribute}
            selectedSwatch={activeSwatch}
            oosSwatches={oosSwatches}
          />
          <SwatchText
            swatch={swatch}
            attribute={attribute}
            selectedSwatch={activeSwatch?.slug}
            oosSwatches={oosSwatches}
          />
        </ProductLink>
      ))}
    </Container>
  )
}

export default SwatchesCollection
