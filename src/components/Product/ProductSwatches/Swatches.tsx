import SwatchesCollection from './SwatchesCollection'
import SwatchesCurrentValue from './SwatchesCurrentValue'
import { getActiveSwatch, getBasePrice } from './utils'
import { LinkRenderFunction } from './types'

import { ProductCardFieldsProps, VariantProps } from '@components/Product/types'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import {
  ContentfulProductSwatch,
  ContentfulProductSwatches,
} from '@/lib/contentful/types/products'
import { colors } from '@/app/themeTokens.stylex'
import { empty, equals } from '@/utils/checking'
import { formatDate } from '@/utils/date'

import React from 'react'
import * as stylex from '@stylexjs/stylex'
import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

const styles = stylex.create({
  swatchesCollection: {
    flexWrap: {
      default: 'wrap',
      '@media (min-width: 768px)': 'nowrap',
    }
  },
  swatchesCollectionHeader: {
    minHeight: '22px',
  },
  redCustomMessage: {
    color: colors.red700
  },
})

type SwatchesProps = {
  productSwatches: ContentfulProductSwatches;
  product: ProductCardFieldsProps;
  variant: VariantProps;
  renderLink?: (swatch: ContentfulProductSwatch) => LinkRenderFunction;
  selectedSwatch?: ContentfulProductSwatch | ContentfulProductSwatch[];
  oosSwatches?: string[]
};

const Swatches = ({
  productSwatches,
  product,
  variant,
  renderLink,
  selectedSwatch,
  oosSwatches
}: SwatchesProps) => {
  const activeSwatch = getActiveSwatch(selectedSwatch || productSwatches)
  const collections = productSwatches.swatchCollections?.items

  const [firstCollection, secondCollection, ...otherCollections] =
    productSwatches.swatchCollections?.items || []

  const firstCollectionSwatches =
    firstCollection?.swatches?.items.map((swatch) => ({
      ...swatch,
      collection: firstCollection?.presentation,
    })) || []

  const secondCollectionSwatches =
    secondCollection?.swatches?.items.map((swatch) => ({
      ...swatch,
      collection: secondCollection?.presentation,
    })) || []

  const otherCollectionsWithCollectionName = otherCollections.map((collection) => ({
    ...collection,
    swatches: {
      items: collection.swatches?.items.map((swatch) => ({
        ...swatch,
        collection: collection.presentation,
      })) || [],
    },
  }))

  const combinedCollections = {
    __typename: 'SwatchCollection',
    slug: 'combined',
    presentation: null,
    swatches: {
      items: [
        ...firstCollectionSwatches,
        ...secondCollectionSwatches,
      ],
    },
  } as const

  const newCollections = [
    combinedCollections,
    ...otherCollectionsWithCollectionName,
  ]
  const flattenedCombinedCollections = combinedCollections.swatches?.items.map((swatch) => swatch.slug) || []

  const flattenedCollections = collections?.flatMap((collection) => collection.swatches?.items.map((swatch) => swatch.slug) || []) || []
  const flattenedVariants = product.variants.items.map((item) => item.swatch.slug)
  const matchingVariants = flattenedVariants.filter((item) => flattenedCollections.includes(item))

  const productMetadata = product.productMetadata?.items || []

  // Create a metadata object for each custom message and index by slug
  const customMsgEntries = productMetadata.reduce((acc: any, meta: any) => {
    const {
      type,
      references,
      description
    } = meta
    if (type.includes('Variant Custom Message')) {
      references.items.forEach(({ slug }: { slug: string }) => {
        if (empty(acc[slug])) {
          acc[slug] = []
        }

        acc[slug].push({
          type,
          description,
        })
      })
    }
    return acc
  }, {})

  const isGiftCard = equals(product.slug, 'gift-card')
  return (
    <Container flex gap="2" key={productSwatches.slug}>
      {/* eslint-disable-next-line complexity */}
      {newCollections?.map((swatchCollection) => {
        const availableVariants = swatchCollection.swatches?.items.filter((swatch) => matchingVariants.includes(swatch.slug)) || []

        if (availableVariants.length === 0) {
          return null
        }

        const { estimatedShippingDate } = variant || {}
        const displayCustomMsg =
          availableVariants.some(
            ({ slug }) => slug === activeSwatch?.slug && customMsgEntries[slug]
          ) || estimatedShippingDate
        const selectedCustomMsg = customMsgEntries[activeSwatch?.slug as string]
        const formattedDate = estimatedShippingDate ? formatDate(estimatedShippingDate, 'full', true) : null

        const customMsg =
          displayCustomMsg &&
          (formattedDate ? (
            <Typography
              as="span"
              key={formattedDate || ''}
              typographyTheme="bodySmall"
            >
              Estimated Ship Date: {formattedDate}
            </Typography>
          )
            : selectedCustomMsg?.map((msg: { type: string, description: { json: any } }) => (
              <Typography
                as="span"
                typographyTheme="bodySmall"
                key={msg?.type || ''}
                styleProp={
                  msg?.type?.includes('Alert') && styles.redCustomMessage
                }
              >
                {documentToPlainTextString(msg?.description?.json)}
              </Typography>
            ))
          )

        const basePrice = getBasePrice(product)
        const priceDiffMessage = isGiftCard
          ? null
          : basePrice && basePrice !== variant.price && (
            <Typography
              as="span"
              typographyTheme="bodySmall"
              styleProp={styles.redCustomMessage}
            >
              +${variant.price - basePrice}
            </Typography>
          )

        return (
          <React.Fragment key={swatchCollection.slug}>
            <Container
              flex
              gap="2"
              key={swatchCollection.slug}
              styleProp={styles.swatchesCollection}
            >
              {swatchCollection.presentation === null && (
                <SwatchesCurrentValue
                  swatches={productSwatches}
                  activeSwatch={activeSwatch?.slug}
                  withValue={flattenedCombinedCollections.includes(activeSwatch?.slug || '')}
                  customMessage={priceDiffMessage}
                />
              )}
              {swatchCollection.presentation && (
                <Container flex flexRow alignCentered gap="1">
                  {newCollections.length > 1 && (
                    <Typography as="span" typographyTheme="bodySmall" uppercase>
                      {swatchCollection.presentation}
                    </Typography>
                  )}
                  <SwatchesCurrentValue
                    swatches={productSwatches}
                    activeSwatch={activeSwatch?.slug}
                    withLabel={false}
                    withValue={!flattenedCombinedCollections.includes(activeSwatch?.slug || '')}
                    customMessage={priceDiffMessage}
                  />
                </Container>
              )}
              <SwatchesCollection
                swatchCollection={{ ...swatchCollection, swatches: { items: availableVariants } }}
                product={product}
                variant={variant}
                attribute={productSwatches.slug}
                activeSwatch={activeSwatch}
                renderLink={renderLink}
                oosSwatches={oosSwatches}
              />
              {customMsg}
            </Container>
          </React.Fragment>
        )
      })}
    </Container>
  )
}

export default Swatches
