import { ProductCardFieldsProps } from '../types'

import { ContentfulProductVariant } from '@/lib/contentful/types/variants'
import { ContentfulProductSwatch, ContentfulProductSwatches } from '@/lib/contentful/types/products'

export function getActiveSwatch(
  swatches: ContentfulProductSwatches | ContentfulProductSwatch | ContentfulProductSwatch[],
  productSwatches?: ContentfulProductSwatches,
) {
  if (productSwatches) {
    const matchingSwatchCollection = productSwatches.swatchCollections?.items.find((collection) => collection.swatches?.items.some((swatch) => {
      if (Array.isArray(swatches)) {
        return swatches.some((selectedSwatch) => selectedSwatch.slug === swatch.slug)
      }

      return false
    }))

    const matchingSwatch = matchingSwatchCollection?.swatches?.items.find((swatch) => {
      if (Array.isArray(swatches)) {
        return swatches.some((selectedSwatch) => selectedSwatch.slug === swatch.slug)
      }

      return false
    })

    if (matchingSwatch) {
      return matchingSwatch
    }
  }

  if (Array.isArray(swatches)) {
    return swatches[0]
  }

  if (swatches.__typename === 'Swatch') {
    return swatches
  }

  const firstCollection = swatches.swatchCollections?.items[0]
  const selectedSwatch = firstCollection?.swatches?.items[0]

  return selectedSwatch
}

export function getBasePrice(productData: ProductCardFieldsProps) {
  if (
    !productData.variants ||
    !productData.variants.items ||
    productData.variants.items.length === 0
  ) {
    return null
  }

  const lowestPrice = productData.variants.items.reduce((min, variant) => {
    if (!variant.price) {
      return min
    }

    return Math.min(min, variant.price)
  }, Infinity)

  return lowestPrice === Infinity ? null : lowestPrice
}

export function getDefaultOOSSwatches(variants?: ContentfulProductVariant[]) {
  if (!variants) {
    return []
  }

  return variants.map((variant) => {
    if (
      variant.availableForSale === false ||
      variant.variantAvailability === 'Sold Out' ||
      variant.variantAvailability === 'Notify Me'
    ) {
      return variant?.swatch?.slug
    }

    return false
  }).filter(Boolean) as string[]
}
