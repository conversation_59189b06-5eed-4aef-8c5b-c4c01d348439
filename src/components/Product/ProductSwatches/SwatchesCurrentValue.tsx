'use client'

import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ContentfulProductSwatches } from '@/lib/contentful/types/products'
import { colors } from '@/app/themeTokens.stylex'
import variantAttribute from '@/utils/variants'
import { slugify } from '@/utils/regex'

import { useSearchParams } from 'next/navigation'
import React, { ReactNode, Suspense } from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  presentation: {
    color: colors.gray,
  },
})

type SwatchesCurrentValueProps = {
  swatches: ContentfulProductSwatches;
  activeSwatch?: string;
  withLabel?: boolean;
  withValue?: boolean;
  customMessage?: ReactNode;
};

const SwatchesCurrentValueSuspense = ({
  swatches,
  activeSwatch,
  withLabel = true,
  withValue = true,
  customMessage,
}: SwatchesCurrentValueProps) => {
  const params = useSearchParams()
  const param = params.get(swatches.slug) || variantAttribute.get(swatches.slug) || activeSwatch
  // Ensure consistent comparison by comparing slugified values
  const slugifiedParam = param ? slugify(param) : null
  const matchingSwatch = swatches.swatchCollections?.items.find(
    (swatchCollection) => swatchCollection.swatches?.items.some((_swatch) => slugify(_swatch.slug) === slugifiedParam)
  )
  const swatch = matchingSwatch?.swatches?.items.find(
    (_swatch) => slugify(_swatch.slug) === slugifiedParam
  )

  if (!swatch) {
    return null
  }

  return (
    <Container
      flex
      flexRow
      gap="1"
      styleProp={{ minHeight: '22px' }}
    >
      {withLabel && <strong>Select {swatches.presentation}:</strong>}
      {withValue && (
        <>
          <Typography as="span" typographyTheme="bodySmall" styleProp={styles.presentation}>
            {swatch.presentation}
          </Typography>
          {customMessage}
        </>
      )}
    </Container>
  )
}

const SwatchesCurrentValue = (props: SwatchesCurrentValueProps) => (
  <Suspense>
    <SwatchesCurrentValueSuspense {...props} />
  </Suspense>
)

export default SwatchesCurrentValue
