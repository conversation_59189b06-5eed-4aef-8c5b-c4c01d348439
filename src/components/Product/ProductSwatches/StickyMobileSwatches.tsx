'use client'

/* eslint-disable complexity */
import { getActiveSwatch } from './utils'
import SwatchIcon from './SwatchIcon'

import { ProductCardFieldsProps, VariantProps } from '../types'
import ProductLink from '../ProductLink'
import AddToCartOverride from '../AddToCartOverride'

import AddToCart from '@components/Product/AddToCart'
import {
  ContentfulProductSwatch,
  ContentfulProductSwatches,
} from '@/lib/contentful/types/products'
import Container from '@/components/layout/Container'
import { colors, spacing } from '@/app/themeTokens.stylex'
import Typography from '@/components/Typography'
import RightArrow from '@/components/Generic/Icon/lib/RightArrow'
import { formatDate } from '@/utils/date'

import * as stylex from '@stylexjs/stylex'
import { useEffect, useState } from 'react'

const styles = stylex.create({
  sticky: {
    position: 'fixed',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 45,
    display: {
      default: 'flex',
      '@media (min-width: 992px)': 'none',
    },
    flexDirection: 'column',
    transform: 'translateY(100%)',
    transition: 'transform 0.5s',
    pointerEvents: 'none',
    maxHeight: '80dvh',
  },
  stickyVisible: {
    transform: 'translateY(0)',
  },
  stickyBar: {
    position: 'relative',
    zIndex: 10,
    paddingBlock: '12px',
    paddingInline: spacing.md,
    boxShadow: 'rgba(0, 0, 0, 0.15) 0px 4px 10px 0px',
    pointerEvents: 'all',
  },
  giftLabelColor: {
    color: colors.navy
  },
  picker: {
    position: 'relative',
    zIndex: 9,
    transform: 'translateY(100%)',
    transition: 'transform 0.3s',
    borderTopWidth: 1,
    borderTopStyle: 'solid',
    borderTopColor: colors.gray200,
    pointerEvents: 'none',
    overflowY: 'auto',
  },
  pickerExpanded: {
    transform: 'translateY(0)',
    pointerEvents: 'all',
  },
  expandButton: {
    display: 'flex',
    alignItems: 'center',
    gap: spacing.sm,
  },
  expandButtonArrow: {
    position: 'relative',
    top: 2,
    transform: 'rotate(90deg)',
    transition: 'transform 0.3s',
  },
  expandButtonArrowExpanded: {
    transform: 'rotate(-90deg)',
  },
  atcButton: {
    width: 'auto',
  },
  expandedSwatchItem: {
    paddingBlock: '12px',
    paddingInline: spacing.md,
    borderBottomWidth: 1,
    borderBottomStyle: 'solid',
    borderBottomColor: colors.gray200,
  },
  expandedSwatchItemActive: {
    backgroundColor: colors.gray100,
  },
  esdLabel: {
    color: colors.gray,
    fontSize: '12px',
  }
})

type StickyMobileSwatchesProps = {
  product: ProductCardFieldsProps;
  variant: VariantProps;
  productSwatches: ContentfulProductSwatches;
  selectedSwatch?: ContentfulProductSwatch | ContentfulProductSwatch[];
  oosSwatches?: string[]
};

const STICKY_VISIBLE_THRESHOLD = 500

const StickyMobileSwatches = ({
  product,
  variant,
  productSwatches,
  selectedSwatch,
  oosSwatches
}: StickyMobileSwatchesProps) => {
  const activeSwatch = getActiveSwatch(
    selectedSwatch || productSwatches,
    productSwatches
  )
  const activeSwatchSlug = activeSwatch?.slug
  const collections = productSwatches.swatchCollections?.items

  const [visible, setVisible] = useState(false)
  const [expanded, setExpanded] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY < STICKY_VISIBLE_THRESHOLD && visible) {
        setVisible(false)
      }

      if (window.scrollY >= STICKY_VISIBLE_THRESHOLD && !visible) {
        setVisible(true)
      }
    }

    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [visible])

  if (!collections) {
    return null
  }

  const atcOverride = product.productDetailBlocksCollection?.items.find(
    (item: { type: string }) => item.type === 'Add to Cart Button Override'
  )

  const variantSwatchSlugs = product.variants.items.flatMap((item) => item.swatch.slug)
  const swatches = collections
    .flatMap((collection) => collection.swatches?.items)
    .filter((swatch) => typeof swatch !== 'undefined')
    .filter((swatch) => variantSwatchSlugs.includes(swatch.slug)) || []

  const selectedVariant = product.variants.items.find(
    (v) => v.swatch.slug === activeSwatchSlug
  )

  const getEstimatedShippingDate = (swatch: ContentfulProductSwatch) => {
    const swatchVariant = product.variants.items.find((v) => v.swatch.slug === swatch.slug)
    const esd = swatchVariant?.estimatedShippingDate

    if (!esd) {
      return null
    }

    const date = formatDate(esd, 'short', true)

    if (!date) {
      return null
    }

    return (
      <Typography as="span" styleProp={styles.esdLabel}>
        Estimated Ship Date: {date}
      </Typography>
    )
  }

  return (
    <Container styleProp={[styles.sticky, visible && styles.stickyVisible]}>
      <Container styleProp={[styles.picker, expanded && styles.pickerExpanded]}>
        {swatches.map((swatch) => (
          <ProductLink
            key={swatch.slug}
            product={product as any}
            variant={variant as any}
            urlParams={{
              [productSwatches.slug]: swatch.slug,
            }}
            scroll={false}
            onClick={() => setExpanded(false)}
          >
            <Container
              flex
              flexRow
              alignCentered
              spaceBetween
              styleProp={[
                styles.expandedSwatchItem,
                swatch.slug === activeSwatchSlug &&
                  styles.expandedSwatchItemActive,
              ]}
              key={swatch.slug}
              theme="offWhite"
            >
              <Container flex flexRow alignCentered gap="1">
                {swatch.presentation}
                {getEstimatedShippingDate(swatch)}
              </Container>
              <SwatchIcon
                swatch={swatch}
                selectedSwatch={activeSwatch}
                attribute={productSwatches.slug}
                size={24}
                oosSwatches={oosSwatches}
              />
            </Container>
          </ProductLink>
        ))}
      </Container>
      <Container
        flex
        flexRow
        alignCentered
        spaceBetween
        theme="offWhite"
        styleProp={styles.stickyBar}
      >
        <Container flex gap="2" key={productSwatches.slug}>
          <button
            type="button"
            onClick={() => setExpanded(!expanded)}
            {...stylex.props([
              styles.expandButton,
              styles.giftLabelColor
            ])}
          >
            {activeSwatch && (
              <>
                <SwatchIcon
                  swatch={activeSwatch}
                  selectedSwatch={activeSwatch}
                  attribute={productSwatches.slug}
                  size={24}
                  oosSwatches={oosSwatches}
                />
                <Container flex flexRow alignCentered>
                  <Typography as="span" size="xs">
                    {activeSwatch?.presentation}
                  </Typography>
                  <RightArrow
                    dimensions="16"
                    styleProp={[
                      styles.expandButtonArrow,
                      expanded && styles.expandButtonArrowExpanded,
                    ]}
                  />
                </Container>
              </>
            )}
          </button>
        </Container>
        {selectedVariant && (
          atcOverride ? (
            <AddToCartOverride
              variant={selectedVariant as any}
              cta={{
                __typename: 'BlockCallToAction',
                anchor: '',
                settings: {},
                ...atcOverride.contentCollection?.items[0],
                text: 'Shop Now',
              }}
            />
          ) : (
            <AddToCart
              product={product}
              variant={selectedVariant as unknown as VariantProps}
              styleProp={styles.atcButton}
            />
          )
        )}
      </Container>
    </Container>
  )
}

export default StickyMobileSwatches
