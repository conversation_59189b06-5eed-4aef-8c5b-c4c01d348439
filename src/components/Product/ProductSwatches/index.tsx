'use client'

import Swatches from './Swatches'
import { LinkRenderFunction } from './types'
import StickyMobileSwatches from './StickyMobileSwatches'
import { getDefaultOOSSwatches } from './utils'

import { getOOSSwatches } from '../utils'
import { getOOSContentfulVariantAvailabilitySlugs } from '../ProductCard/utils'

import { ProductCardFieldsProps, VariantProps } from '@components/Product/types'
import {
  ContentfulProductSwatch,
  ContentfulProductSwatches,
} from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

import * as stylex from '@stylexjs/stylex'
import React, { useEffect, useState } from 'react'

const styles = stylex.create({
  wrapper: {
    marginBlockStart: '16px',
    marginBlockEnd: '12px',
  },
})

type ProductSwatchesProps = {
  swatches: ContentfulProductSwatches[] | undefined;
  product: ProductCardFieldsProps;
  variant: VariantProps;
  renderLink?: (swatch: ContentfulProductSwatch) => LinkRenderFunction;
  selectedSwatch?: ContentfulProductSwatch | ContentfulProductSwatch[];
  includeMobileSwatches?: boolean;
};

const ProductSwatches = ({
  swatches,
  product,
  variant,
  renderLink,
  selectedSwatch,
  includeMobileSwatches = false,
}: ProductSwatchesProps) => {
  const defaultOOSSwatches = getDefaultOOSSwatches(
    product?.variants?.items as unknown as ContentfulProductVariant[]
  )
  const [oosSwatches, setOOSSwatches] = useState<string[]>(defaultOOSSwatches)

  useEffect(() => {
    async function fetchData() {
      // Checks Shopify stock
      const response = await getOOSSwatches(product.variants.items)

      // Checks for Contentful Variant level updates
      const contentfulVariantAvailabilityToggle = getOOSContentfulVariantAvailabilitySlugs(product)

      const mergedOOSSwatches = Array.from(new Set([...response, ...contentfulVariantAvailabilityToggle]))

      setOOSSwatches(mergedOOSSwatches)
    }
    fetchData()
  }, [])

  if (!swatches) {
    return null
  }

  const mainSwatches = swatches[0]

  return (
    <div {...stylex.props(styles.wrapper)}>
      {swatches.map((productSwatches) => (
        <React.Fragment key={productSwatches.slug}>
          <Swatches
            productSwatches={productSwatches}
            product={product}
            variant={variant}
            renderLink={renderLink}
            selectedSwatch={selectedSwatch}
            oosSwatches={oosSwatches}
          />
        </React.Fragment>
      ))}
      {mainSwatches && includeMobileSwatches && (
        <StickyMobileSwatches
          product={product}
          variant={variant}
          productSwatches={mainSwatches}
          selectedSwatch={variant.swatch}
          oosSwatches={oosSwatches}
        />
      )}
    </div>
  )
}

export default ProductSwatches
