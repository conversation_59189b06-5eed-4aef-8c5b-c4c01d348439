import { colors, globalTokens, spacing } from '@/app/themeTokens.stylex'
import { ContentfulImage } from '@/lib/contentful/types/generic'
import variantAttribute from '@/utils/variants'
import { slugify } from '@/utils/regex'

import * as stylex from '@stylexjs/stylex'
import React, { Suspense } from 'react'

const styles = stylex.create({
  crossOutContainer: {
    position: 'relative'
  },
  swatchText: {
    display: 'block',
    paddingTop: spacing.xs,
    paddingBottom: spacing.xs,
    textAlign: 'center',
    borderColor: colors.gray300,
    borderWidth: '1px',
    borderStyle: 'solid',
    borderRadius: globalTokens.borderRadiusSmall,
    lineHeight: '1',
  },
  swatchTextActive: {
    backgroundColor: colors.gray200,
    borderColor: colors.navy,
  },
  oosStyle: {
    backgroundColor: colors.gray200,
    color: colors.gray,
    borderColor: colors.gray100
  },
  oosActiveStyle: {
    borderColor: colors.gray300,
  },
  crossLineSwatchText: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundImage: 'linear-gradient(to bottom left, transparent 49%, white 50%, transparent 51%)',
  },
})

type SwatchTextProps = {
  swatch: {
    slug: string;
    presentation: string;
    icon?: ContentfulImage;
  };
  attribute: string;
  selectedSwatch?: string;
  styleProp?: stylex.StyleXStyles;
  oosSwatches?: string[];
};

// eslint-disable-next-line complexity
const SwatchTextSuspense = ({
  swatch,
  attribute,
  selectedSwatch,
  styleProp,
  oosSwatches,
}: SwatchTextProps) => {
  // Prioritize selectedSwatch prop over cache for server-side rendering
  const param = selectedSwatch || variantAttribute.get(attribute)
  const slugifiedSwatchSlug = slugify(swatch.slug)
  // Ensure consistent comparison by comparing slugified values
  const slugifiedParam = param ? slugify(param) : null
  const isActive = slugifiedParam === slugifiedSwatchSlug

  const isOos = oosSwatches?.some((s) => slugify(s) === slugifiedSwatchSlug)

  if (swatch.icon) {
    return null
  }

  return (
    <div {...stylex.props(styles.crossOutContainer)}>
      <span
        {...stylex.props(
          styles.swatchText,
          isActive && styles.swatchTextActive,
          isOos && styles.oosStyle,
          isActive && isOos && styles.oosActiveStyle,
          styleProp
        )}
      >
        {swatch.presentation}
      </span>
      {isOos && <div {...stylex.props(styles.crossLineSwatchText)} />}
    </div>
  )
}

const SwatchText = (props: SwatchTextProps) => (
  <Suspense>
    <SwatchTextSuspense {...props} />
  </Suspense>
)

export default SwatchText
