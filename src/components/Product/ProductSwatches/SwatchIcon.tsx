import { ContentfulProductSwatch } from '@/lib/contentful/types/products'
import { colors } from '@/app/themeTokens.stylex'
import variantAttribute from '@/utils/variants'
import { slugify } from '@/utils/regex'

import React, { Suspense } from 'react'
import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  crossOutContainer: {
    position: 'relative',
  },
  swatchImage: {
    borderRadius: '50%',
  },
  swatchImageSquare: {
    borderRadius: '4px',
  },
  oosStyle: {
    filter: 'brightness(60%)'
  },
  crossLine: {
    position: 'absolute',
    top: '45%',
    height: '1.4px',
    backgroundColor: 'white',
  },
  crossLineCircle: {
    left: '11%',
    width: '101%',
    transform: 'translate(-10%, -50%) rotate(40deg)',
  },
  crossLineSquare: {
    left: '0%',
    width: '125%',
    transform: 'translate(-10%, -50%) rotate(45deg)',
  },
  swatchOutline: (size) => ({
    width: `${size}px`,
    height: `${size}px`,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    outline: `2px solid ${colors.navy}`,
    outlineOffset: '3px',
    zIndex: 2,
    opacity: 1,
    pointerEvents: 'none',
  }),
  swatchOutlineCircle: {
    borderRadius: '50%',
  },
  swatchOutlineSquare: {
    borderRadius: '4px',
  },
})

type SwatchIconProps = {
  swatch: ContentfulProductSwatch;
  attribute: string;
  selectedSwatch?: ContentfulProductSwatch;
  size?: number;
  oosSwatches?: (string | null)[];
};

const DEFAULT_SIZE = 32

// eslint-disable-next-line complexity
const SwatchIconSuspense = ({
  swatch,
  selectedSwatch,
  attribute,
  size = DEFAULT_SIZE,
  oosSwatches,
}: SwatchIconProps) => {
  // Prioritize selectedSwatch prop over cache for server-side rendering
  const param = selectedSwatch?.slug || variantAttribute.get(attribute)
  const slugifiedSwatchSlug = slugify(swatch.slug)
  // Ensure consistent comparison by comparing slugified values
  const slugifiedParam = param ? slugify(param) : null
  const isActive = slugifiedParam === slugifiedSwatchSlug

  const isOos = oosSwatches?.some((s) => s && slugify(s) === slugifiedSwatchSlug)

  if (!swatch.icon) {
    return null
  }

  return (
    <div {...stylex.props(styles.crossOutContainer)}>
      <Image
        src={swatch.icon.url}
        alt={swatch.presentation}
        width={size}
        height={size}
        {...stylex.props(
          swatch.style === 'Circle' && styles.swatchImage,
          swatch.style === 'Square' && styles.swatchImageSquare,
          isOos && styles.oosStyle,
        )}
      />
      {isOos && <div {...stylex.props(styles.crossLine, swatch.style === 'Circle' && styles.crossLineCircle, swatch.style === 'Square' && styles.crossLineSquare)} />}
      {isActive && <div {...stylex.props(styles.swatchOutline(size), swatch.style === 'Circle' && styles.swatchOutlineCircle, swatch.style === 'Square' && styles.swatchOutlineSquare)} />}
    </div>
  )
}

const SwatchIcon = (props: SwatchIconProps) => (
  <Suspense>
    <SwatchIconSuspense {...props} />
  </Suspense>
)

export default SwatchIcon
