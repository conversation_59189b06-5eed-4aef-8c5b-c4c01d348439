'use client'

import ProductDetailsTooltip from '../Content/AtomicLevelTooltip/ProductDetailsTooltip'

import formatCurrency from '@utils/formatCurrency'
import Typography, { ValidSize } from '@components/Typography'
import themes, { ThemeColors } from '@/app/themeThemes.stylex'
import { useAppSelector } from '@/redux/hooks'

import Image from 'next/image'
import stylex from '@stylexjs/stylex'
import { useState } from 'react'

type PriceProps = {
  regularPriceOverride?: number,
  onSale?: boolean,
  price: number,
  compareAtPrice: number,
  size?: ValidSize<'xs' | 'sm' | 'md' | 'lg'>;
  discountedSize?: ValidSize<'xs' | 'sm' | 'md' | 'lg'>;
  slug?: string,
  showTooltip?: boolean
  stylePropFinalPrice?: any
  stylePropCompareAtPrice?: any
}

const DISALLOW_LIST = ['gift-card']
const SPECIAL_OFFER_PRODUCTS = ['full-ceramic-cookware-bundle']

const calculatePricing = (price: number, compareAtPrice: number) => {
  const finalPrice = price
  const effectiveCompareAtPrice = compareAtPrice > finalPrice ? compareAtPrice : price
  const isDiscounted = finalPrice < effectiveCompareAtPrice

  return { finalPrice, effectiveCompareAtPrice, isDiscounted }
}

const styles = stylex.create({
  productIcon: {
    position: 'relative'
  },
  price: {
    fontSize: {
      default: '16px',
      '@media (min-width: 1024px)': '18px'
    }
  },
  strikethrough: {
    fontSize: {
      default: '14px',
      '@media (min-width: 1024px)': '16px'
    }
  }
})

// eslint-disable-next-line complexity
const ProductPrice = ({
  regularPriceOverride,
  price,
  onSale = false,
  compareAtPrice,
  size = 'md',
  discountedSize = 'sm',
  slug = '',
  showTooltip = false,
  stylePropFinalPrice = {},
  stylePropCompareAtPrice = {},
}: PriceProps) => {
  const [isHovered, setIsHovered] = useState(false)
  const quantity = useAppSelector((state) => state.product.quantity)
  const computedPrice = price * quantity
  const computedCompareAtPrice = compareAtPrice * quantity

  const {
    finalPrice,
    effectiveCompareAtPrice,
    isDiscounted
  } =
    calculatePricing(
      computedPrice,
      computedCompareAtPrice
    )

  return (
    <>
      {isDiscounted && (
        <>
          { ' ' }
          <Typography
            as="span"
            size={discountedSize}
            lineThrough
            colorSecondary
            styleProp={[stylePropCompareAtPrice, styles.strikethrough]}
          >
            {formatCurrency(effectiveCompareAtPrice)}
          </Typography>
        </>
      )}
      <Typography
        as="span"
        size={size}
        colorSecondary={onSale}
        styleProp={[
          onSale && themes['red700' as ThemeColors],
          stylePropFinalPrice,
          styles.price
        ]}
      >
        {regularPriceOverride ? formatCurrency(regularPriceOverride) : formatCurrency(finalPrice)}
      </Typography>
      {showTooltip && !DISALLOW_LIST.includes(slug) && !SPECIAL_OFFER_PRODUCTS.includes(slug) && compareAtPrice && (
        <div {...stylex.props(styles.productIcon)}>
          <Image
            src="/assets/Navy-Info-Icon-Moderat-Regular.svg"
            alt="Product Price Details Breakdown"
            width={12}
            height={12}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          />
          <ProductDetailsTooltip
            regularPriceOverride={regularPriceOverride}
            compareAtPrice={computedCompareAtPrice}
            price={computedPrice}
            theme="cream"
            isHovered={isHovered}
          />
        </div>
      )}
      {showTooltip && SPECIAL_OFFER_PRODUCTS.includes(slug) && (
        <div {...stylex.props(styles.productIcon)}>
          <Image
            src="/assets/Navy-Info-Icon-Moderat-Regular.svg"
            alt="Product Price Details Breakdown"
            width={12}
            height={12}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          />
          <ProductDetailsTooltip
            regularPriceOverride={regularPriceOverride}
            compareAtPrice={computedCompareAtPrice}
            customLaunchDiscountCopy="Special Offer"
            onSale={onSale}
            price={computedPrice}
            theme="cream"
            isHovered={isHovered}
          />
        </div>
      )}
    </>
  )
}

export default ProductPrice
