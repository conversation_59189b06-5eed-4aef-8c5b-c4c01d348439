'use client'

import { productListViewed } from '@redux/features/events/eventsSlice'
import { useAppDispatch } from '@redux/hooks'
import { ContentfulProduct } from '@lib/contentful/types/products'

import { useEffect, useState } from 'react'

const ProductListViewedEvent = ({
  id,
  title,
  products
}: {
  id?: string
  title?: string
  products: ContentfulProduct[]
}) => {
  const dispatch = useAppDispatch()
  const [isProductListViewed, setIsProductListViewed] = useState(false)

  useEffect(() => {
    if (isProductListViewed) return
    dispatch(productListViewed({ products, id, title }))
    setIsProductListViewed(true)
  }, [dispatch, id, isProductListViewed, products, title])

  return null
}

export default ProductListViewedEvent
