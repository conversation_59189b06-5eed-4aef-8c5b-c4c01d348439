import Anatomy from './Anatomy'
import {
  foodStorageDummy, knifeUtensilSetDummy, teaKettleDummy, petiteCookerDummy
} from './anatomy-dummy'

import { Suspense } from 'react'

type AnatomyProps = {
  variant: {
    swatch: {
      swatchType: string;
    }
  }
}

export const AnatomyTeaKettle = ({ variant }: AnatomyProps) => <Suspense fallback={<div>Loading...</div>}><Anatomy variant={variant} product={teaKettleDummy.product} /></Suspense>
export const AnatomyFoodStorage = ({ variant }: AnatomyProps) => <Suspense fallback={<div>Loading...</div>}><Anatomy variant={variant} product={foodStorageDummy.product} /></Suspense>
export const AnatomyKnifeUtensilSet = ({ variant }: AnatomyProps) => <Suspense fallback={<div>Loading...</div>}><Anatomy variant={variant} product={knifeUtensilSetDummy.product} /></Suspense>
export const AnatomyPetiteCooker = ({ variant }: AnatomyProps) => <Suspense fallback={<div>Loading...</div>}><Anatomy variant={variant} product={petiteCookerDummy.product} /></Suspense>
