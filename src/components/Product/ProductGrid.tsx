'use client'

import ProductCard from './ProductCard'
import ProductGridTitle from './ProductGridTitle'

import MarketingModule from '../Generic/MarketingModule'

import Container from '@components/layout/Container'
import Wrapper from '@components/layout/Wrapper'
import { ContentfulProduct } from '@lib/contentful/types/products'
import {
  spacing,
} from '@/app/themeTokens.stylex'
import { ExtractedMarketingModule } from '@/lib/contentful/extractMarketingModules'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

type ProductGridProps = {
  title?: string
  products: (ContentfulProduct | ExtractedMarketingModule)[]
  anchor?: string
}

const styles = stylex.create({
  wrapper: {
    paddingBlock: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    }
  },
  productGrid: {
    display: 'grid',
    gridTemplateColumns: {
      default: 'auto auto',
      [DESKTOP]: 'repeat(auto-fill, minmax(min(20%, 100%), 1fr))',
    },
    rowGap: {
      default: spacing.lg,
      [DESKTOP]: spacing.xl,
    }
  },
})

const ProductGrid = ({
  products,
  title,
  anchor
}: ProductGridProps) => (
  <Wrapper as="section" size="5" pageGap styleProp={styles.wrapper}>
    {anchor && <span id={anchor} />}
    {title && <ProductGridTitle title={title} />}
    <Container as="div" gap="3" styleProp={styles.productGrid}>
      {products.map((product, i) => {
        if (!product) {
          return null
        }

        if (product.__typename === 'BlockContent') {
          return (
            <MarketingModule
              key={`${product.id}-${i.toString()}`}
              content={product}
            />
          )
        }

        return (
          <ProductCard
            key={`${product.productId}-${i.toString()}`}
            product={product}
          />
        )
      })}
    </Container>
  </Wrapper>
)

export default ProductGrid
