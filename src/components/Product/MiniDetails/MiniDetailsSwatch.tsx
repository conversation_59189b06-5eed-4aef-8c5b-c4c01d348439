import {
  ContentfulProduct,
  ContentfulProductSwatch,
} from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

import * as stylex from '@stylexjs/stylex'
import { Dispatch, ReactNode, SetStateAction } from 'react'

type MiniDetailsSwatchProps = {
  swatch: ContentfulProductSwatch;
  product: ContentfulProduct;
  setCurrentVariant: Dispatch<SetStateAction<ContentfulProductVariant>>;
  children: ReactNode;
  styleProp: stylex.StyleXStyles;
};

const MiniDetailsSwatch = ({
  swatch,
  product,
  setCurrentVariant,
  children,
  styleProp,
}: MiniDetailsSwatchProps) => {
  const matchingVariant = product.variants.items.find(
    (_variant) => _variant.swatch.slug === swatch.slug
  )

  return (
    <button
      type="button"
      onClick={() => {
        if (matchingVariant) {
          setCurrentVariant(matchingVariant)
        }
      }}
      {...stylex.props(styleProp)}
    >
      {children}
    </button>
  )
}

export default MiniDetailsSwatch
