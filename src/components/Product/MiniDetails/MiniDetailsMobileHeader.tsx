import ProductPrice from './ProductPrice'
import { MiniDetailsMobileHeaderProps } from './types'

import Container from '@components/layout/Container'
import ProductDetailsTitle from '@components/Product/ProductDetails/ProductDetailsTitle'

import * as stylex from '@stylexjs/stylex'

const TABLET = '@media (min-width: 768px) and (max-width: 1023px)'
const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  details: {
    width: '100%',
    flex: {
      default: '1',
      [TABLET]: '0.45'
    }
  },
  detailsMobile: {
    display: {
      default: 'block',
      [DESKTOP]: 'none'
    }
  }
})

const MiniDetailsMobileHeader = ({
  currentVariant,
  product,
  calloutItems,
  price,
  compareAtPrice,
  isWeeklyDealsLandingPage
}: MiniDetailsMobileHeaderProps) => (
  <Container styleProp={[styles.details, styles.detailsMobile]}>
    <ProductDetailsTitle
      onSale={currentVariant?.onSale}
      title={product.title}
      slug={product.slug}
      price={price}
      compareAtPrice={compareAtPrice}
      regularPriceOverride={currentVariant?.regularPriceOverride}
      calloutItems={calloutItems}
      reviews={product.reviews}
      priceComponent={
        isWeeklyDealsLandingPage && (
          <ProductPrice
            price={price}
            compareAtPrice={compareAtPrice}
            swatch={currentVariant?.swatch?.slug}
          />
        )
      }
    />
  </Container>
)

export default MiniDetailsMobileHeader
