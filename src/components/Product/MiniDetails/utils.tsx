'use client'

import { ContentfulProduct, ContentfulProductSwatch } from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

import { Dispatch, ReactNode, SetStateAction } from 'react'
import * as stylex from '@stylexjs/stylex'

type SwatchRenderFunctionProps = {
  children: ReactNode
  styleProp: stylex.StyleXStyles
}

type SwatchRenderFunction = (props: SwatchRenderFunctionProps) => JSX.Element

const renderCustomSwatch = (
  product: ContentfulProduct,
  setCurrentVariant: Dispatch<SetStateAction<ContentfulProductVariant>>
) => (swatch: ContentfulProductSwatch): SwatchRenderFunction => ({ children, styleProp }: SwatchRenderFunctionProps) => (
  <button
    type="button"
    onClick={() => {
      const matchingVariant = product.variants.items.find(
        (_variant) => _variant.swatch.slug === swatch.slug
      )
      if (matchingVariant) {
        setCurrentVariant(matchingVariant)
      }
    }}
    {...stylex.props(styleProp)}
  >
    {children}
  </button>
)

export default renderCustomSwatch
