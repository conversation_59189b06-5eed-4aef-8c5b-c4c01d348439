import { MiniDetailsGalleryProps } from './types'

import Container from '@components/layout/Container'
import ProductGallery from '@components/Product/ProductGallery'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  gallery: {
    flex: '0.55',
    maxWidth: '100%',
  }
})

const MiniDetailsGallery = ({ currentVariant }: MiniDetailsGalleryProps) => (
  <Container styleProp={styles.gallery}>
    <ProductGallery variant={currentVariant} hideThumbnails />
  </Container>
)

export default MiniDetailsGallery
