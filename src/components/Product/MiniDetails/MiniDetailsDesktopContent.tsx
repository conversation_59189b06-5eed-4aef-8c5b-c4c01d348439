import ProductPrice from './ProductPrice'
import { MiniDetailsDesktopContentProps } from './types'
import renderCustomSwatch from './utils'

import Container from '@components/layout/Container'
import ProductDetailsTitle from '@components/Product/ProductDetails/ProductDetailsTitle'
import ProductSwatches from '@components/Product/ProductSwatches'
import AddToCart from '@components/Product/AddToCart'
import ProductLink from '@components/Product/ProductLink'
import Typography from '@components/Typography'
import RightArrow from '@components/Generic/Icon/lib/RightArrow'
import Spacer from '@components/layout/Spacer'
import RichTextRender from '@utils/RichTextRenderer'
import { spacing, colors } from '@/app/themeTokens.stylex'

import { Product } from '@shopify/hydrogen-react/storefront-api-types'
import { ProductProvider } from '@shopify/hydrogen-react'
import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const TABLET = '@media (min-width: 768px) and (max-width: 1023px)'

const styles = stylex.create({
  details: {
    width: '100%',
    maxWidth: '490px',
    flex: {
      default: '1',
      [TABLET]: '0.45'
    }
  },
  addToCart: {
    marginBlockStart: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    },
  },
  productLink: {
    display: 'inline-block',
    color: {
      default: colors.navy,
      ':hover': colors.gray,
    }
  }
})

const MiniDetailsDesktopContent = ({
  currentVariant,
  product,
  calloutItems,
  price,
  compareAtPrice,
  isWeeklyDealsLandingPage,
  byline,
  attribute,
  shopifyProduct,
  setCurrentVariant
}: MiniDetailsDesktopContentProps) => (
  <Container styleProp={styles.details}>
    <ProductDetailsTitle
      onSale={currentVariant?.onSale}
      slug={product.slug}
      title={product.title}
      price={price}
      compareAtPrice={compareAtPrice}
      regularPriceOverride={currentVariant?.regularPriceOverride}
      calloutItems={calloutItems}
      reviews={product.reviews}
      desktop
      priceComponent={
        isWeeklyDealsLandingPage && (
          <ProductPrice
            price={price}
            compareAtPrice={compareAtPrice}
            swatch={currentVariant?.swatch?.slug}
          />
        )
      }
    />
    {byline?.description?.json && (
      <>
        <Spacer size="sm" />
        <RichTextRender content={byline.description.json} />
      </>
    )}
    <ProductSwatches
      swatches={product.swatches?.items}
      product={product as any}
      variant={currentVariant as any}
      renderLink={renderCustomSwatch(product, setCurrentVariant)}
      selectedSwatch={currentVariant?.swatch}
    />
    <Container flex gap="1">
      {shopifyProduct && (
        <Container styleProp={styles.addToCart}>
          <ProductProvider data={shopifyProduct as unknown as Product}>
            <AddToCart
              variant={currentVariant as any}
              product={product as any}
            />
          </ProductProvider>
        </Container>
      )}
      <Container flex flexCentered>
        <ProductLink
          product={product as any}
          variant={currentVariant as any}
          urlParams={
            attribute
              ? {
                [attribute]: currentVariant?.swatch?.slug,
              }
              : undefined
          }
        >
          <Typography
            as="span"
            typographyTheme="bodyLarge"
            underline
            styleProp={styles.productLink}
          >
            View Full Details <RightArrow dimensions="10" />
          </Typography>
        </ProductLink>
      </Container>
    </Container>
  </Container>
)

export default MiniDetailsDesktopContent
