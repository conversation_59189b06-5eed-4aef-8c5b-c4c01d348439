'use client'

import MiniDetailsMobileHeader from './MiniDetailsMobileHeader'
import MiniDetailsGallery from './MiniDetailsGallery'
import MiniDetailsDesktopContent from './MiniDetailsDesktopContent'

import { mergeProductDetailsOverrides } from '../ProductDetails/utils'

import Container from '@components/layout/Container'
import { ContentfulProduct } from '@/lib/contentful/types/products'
import { ContentfulExtractedProductDetailBlock } from '@/lib/contentful/fetchProducts'
import { removeSpaces } from '@utils/regex'
import getShopifyData from '@lib/shopify/getShopifyData'

import { flattenConnection } from '@shopify/hydrogen-react'
import { usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  layout: {
    marginBlockStart: 'spacing.lg',
    marginBlockEnd: 'spacing.lg',
    flexDirection: {
      default: 'column',
      '@media (min-width: 1024px)': 'row'
    }
  }
})

type MiniDetailsContentProps = {
  product: ContentfulProduct
}

const MiniDetailsContent = ({ product }: MiniDetailsContentProps) => {
  const [currentVariant, setCurrentVariant] = useState(
    product?.variants?.items?.[0] ?? null
  )
  const [shopifyData, setShopifyData] = useState<any>(null)

  const path = usePathname()
  useEffect(() => {
    if (product?.productId) {
      const fetchShopifyData = async () => {
        const { data } = await getShopifyData(product.productId)
        setShopifyData(data)
      }
      fetchShopifyData()
    }
  }, [product?.productId])

  if (!product || !currentVariant) {
    return null
  }

  const { price, compareAtPrice } = currentVariant
  const attribute = product.swatches?.items?.[0]?.slug ?? ''

  const calloutItems = product.callouts?.items ?? []

  const blockDetailOverrides = currentVariant?.productDetailBlocksOverrides?.items ?? []

  const mergedDetails = mergeProductDetailsOverrides(
    product.productDetailBlocksCollection?.items as ContentfulExtractedProductDetailBlock[],
    blockDetailOverrides as ContentfulExtractedProductDetailBlock[]
  )

  const byline = mergedDetails?.find(
    (item) => removeSpaces(item.type) === 'ProductByline'
  )

  const shopifyProduct = shopifyData?.product ? flattenConnection(shopifyData.product) : {}

  const isWeeklyDealsLandingPage = [
    '/cf1-mbws',
    '/cf2-mnd',
    '/cf3-sqws',
    '/cf4-pws',
  ].includes(path)

  return (
    <Container flex flexRow gap="4" alignCentered styleProp={styles.layout}>
      <MiniDetailsMobileHeader
        currentVariant={currentVariant}
        product={product}
        calloutItems={calloutItems}
        price={price}
        compareAtPrice={compareAtPrice}
        isWeeklyDealsLandingPage={isWeeklyDealsLandingPage}
      />
      <MiniDetailsGallery currentVariant={currentVariant} />
      <MiniDetailsDesktopContent
        currentVariant={currentVariant}
        product={product}
        calloutItems={calloutItems}
        price={price}
        compareAtPrice={compareAtPrice}
        isWeeklyDealsLandingPage={isWeeklyDealsLandingPage}
        byline={byline}
        attribute={attribute}
        shopifyProduct={shopifyProduct}
        setCurrentVariant={setCurrentVariant}
      />
    </Container>
  )
}

export default MiniDetailsContent
