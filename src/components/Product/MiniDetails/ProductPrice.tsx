'use client'

// TODO: this whole component is a temporary file
// to override holiday pricing for the weekly deal
// It should be removed once the weekly deal is over

import ProductDetailsTooltip from '@components/Content/AtomicLevelTooltip/ProductDetailsTooltip'
import formatCurrency from '@utils/formatCurrency'
import Typography from '@components/Typography'
import themes, { ThemeColors } from '@/app/themeThemes.stylex'

import Image from 'next/image'
import stylex from '@stylexjs/stylex'
import { useState } from 'react'
import { usePathname } from 'next/navigation'

type PriceProps = {
  price: number,
  compareAtPrice: number,
  regularPriceOverride?: number,
  size?: 'sm' | 'md' | 'lg',
  slug?: string,
  showTooltip?: boolean,
  swatch?: string
}

const styles = stylex.create({
  productIcon: {
    position: 'relative'
  }
})

function getWeeklyDealPrice(lpSlug: string, swatch: string, price: number) {
  switch (lpSlug) {
    case '/cf1-mbws':
    case '/cf4-pws':
      // eslint-disable-next-line no-magic-numbers
      return price * 0.85
    case '/cf2-mnd':
      if (swatch !== 'black' && swatch !== 'white') {
        // eslint-disable-next-line no-magic-numbers
        return price * 0.85
      }
      return price
    case '/cf3-sqws':
      // eslint-disable-next-line no-magic-numbers
      return price * 0.8
    default:
      return price
  }
}

const ProductPrice = ({
  price,
  compareAtPrice,
  regularPriceOverride,
  size,
  showTooltip = false,
  swatch = ''
}: PriceProps) => {
  const [isHovered, setIsHovered] = useState(false)
  const path = usePathname()

  const finalPrice = getWeeklyDealPrice(path, swatch, price)
  const isDiscounted = finalPrice < price
  const fontSize = size === 'sm' ? 'sm' : 'md'

  return (
    <>
      <Typography
        as="span"
        size={fontSize}
        colorSecondary={isDiscounted}
        fontBold
        styleProp={isDiscounted && themes['red700' as ThemeColors]}
      >
        {regularPriceOverride ? formatCurrency(regularPriceOverride) : formatCurrency(finalPrice)}
      </Typography>
      <>
        {' '}
        <Typography as="span" size="sm" lineThrough colorSecondary>
          {formatCurrency(compareAtPrice)}
        </Typography>
      </>
      {showTooltip && (
        <div {...stylex.props(styles.productIcon)}>
          <Image
            src="/assets/Navy-Info-Icon-Moderat-Regular.svg"
            alt="Product Price Details Breakdown"
            width={12}
            height={12}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          />
          <ProductDetailsTooltip
            compareAtPrice={compareAtPrice}
            price={price}
            theme="cream"
            isHovered={isHovered}
          />
        </div>
      )}
    </>
  )
}

export default ProductPrice
