import MiniDetailsContent from './MiniDetailsContent'

import Container from '@components/layout/Container'
import { ContentfulProduct } from '@/lib/contentful/types/products'
import { breakpoints, spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  miniDetailsPadding: {
    padding: {
      default: `${spacing.lg} ${spacing.md} ${spacing.md} ${spacing.md}`,
      '@media (min-width: 1024px)': `${spacing.lg} 0`
    }
  },
  containerMaxWidth: {
    maxWidth: breakpoints.lg
  }
})

type MiniDetailsProps = {
  products: ContentfulProduct[];
  settings: {
    anchorTargeting: string;
  }
};

const MiniDetails = ({ products, settings }: MiniDetailsProps) => (
  <Container
    as="section"
    theme="offWhite"
    contentGap
    flexCentered
    styleProp={styles.miniDetailsPadding}
  >
    {settings?.anchorTargeting && (
      <span
        id={settings.anchorTargeting}
        style={{ position: 'absolute', top: '-120px' }}
      />
    )}
    <Container styleProp={styles.containerMaxWidth}>
      {products.map((product) => (
        <MiniDetailsContent key={product.productId} product={product} />
      ))}
    </Container>
  </Container>
)

export default MiniDetails
