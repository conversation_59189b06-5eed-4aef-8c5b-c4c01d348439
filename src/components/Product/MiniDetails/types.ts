import { ContentfulProduct } from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

import { Dispatch, SetStateAction } from 'react'

export type SharedMiniDetailsProps = {
  currentVariant: ContentfulProductVariant
  product: ContentfulProduct
  calloutItems: any[]
  price: number
  compareAtPrice: number
  isWeeklyDealsLandingPage: boolean
}

export type MiniDetailsMobileHeaderProps = SharedMiniDetailsProps

export type MiniDetailsGalleryProps = {
  currentVariant: ContentfulProductVariant
}

export type MiniDetailsDesktopContentProps = SharedMiniDetailsProps & {
  byline: any
  attribute: string
  shopifyProduct: any
  setCurrentVariant: Dispatch<SetStateAction<ContentfulProductVariant>>
}
