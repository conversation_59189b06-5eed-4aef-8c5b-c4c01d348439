import { ContentfulProduct } from '@lib/contentful/types/products'
import {
  spacing
} from '@/app/themeTokens.stylex'
import formatCurrency from '@/utils/formatCurrency'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gridGap: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
  }
})

const getSelectedVariantData = (product: ContentfulProduct) => {
  const selectedVariant = product?.variants?.items?.[0]

  if (!selectedVariant) {
    return null
  }

  return {
    price: selectedVariant.price ?? null,
    compareAtPrice: selectedVariant.compareAtPrice ?? null,
    image: selectedVariant.primaryImage ?? null,
  }
}

const getFeatureCardData = (product: ContentfulProduct | null | undefined) => {
  if (!product) {
    return { status: 'no-product' as const }
  }

  const title = product.title ?? 'Product'
  const variantData = getSelectedVariantData(product)

  if (!variantData) {
    return { status: 'no-variant' as const, title }
  }

  const {
    price,
    compareAtPrice,
    image
  } = variantData

  const displayPrice = price !== null ? formatCurrency(price) : 'N/A'
  const displayCompareAtPrice = compareAtPrice !== null ? formatCurrency(compareAtPrice) : null
  const onSale = price !== null && compareAtPrice !== null && price < compareAtPrice

  return {
    status: 'ok' as const,
    title,
    image,
    onSale,
    displayPrice,
    displayCompareAtPrice
  }
}

const FeatureCardImage = ({ image, title }: { image: any, title: string }) => {
  if (image?.url) {
    return (
      <Image
        src={image.url}
        alt={image.title ?? title}
        width={240}
        height={180}
        priority
      />
    )
  }
  return (
    <div style={{
      width: 240, height: 180, background: '#eee', display: 'flex', alignItems: 'center', justifyContent: 'center'
    }}
    >No Image
    </div>
  )
}

const FeatureCardPrice = ({
  onSale,
  displayPrice,
  displayCompareAtPrice
}: {
  onSale: boolean;
  displayPrice: string;
  displayCompareAtPrice: string | null;
}) => (
  <span>
    {onSale ? (
      <>
        { displayPrice }
        {displayCompareAtPrice && <span style={{ textDecoration: 'line-through', marginLeft: '8px' }}>{ displayCompareAtPrice }</span>}
      </>
    )
      : displayPrice}
  </span>
)

interface ProductFeatureCardProps {
  product: ContentfulProduct | null | undefined;
}

const ProductFeatureCard = ({ product }: ProductFeatureCardProps) => {
  const cardData = getFeatureCardData(product)

  switch (cardData.status) {
    case 'no-product':
      return null
    case 'no-variant':
      return (
        <div {...stylex.props(styles.wrapper)}>
          <h2>{cardData.title}</h2>
          <span>Price not available</span>
        </div>
      )
    case 'ok': {
      const {
        title,
        image,
        onSale,
        displayPrice,
        displayCompareAtPrice
      } = cardData
      return (
        <div {...stylex.props(styles.wrapper)}>
          <FeatureCardImage image={image} title={title} />
          <h2>{title}</h2>
          <FeatureCardPrice
            onSale={onSale}
            displayPrice={displayPrice}
            displayCompareAtPrice={displayCompareAtPrice}
          />
        </div>
      )
    }
    default:
      return null
  }
}

export default ProductFeatureCard
