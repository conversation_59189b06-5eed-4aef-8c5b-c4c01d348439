import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@components/layout/Container'
import CategoriesLarge from '@components/Generic/CategoriesLarge'
import { getProductCardByID, getProductFields } from '@/lib/contentful/fetchProducts'
import { ProductCardData } from '@/components/Product/ProductCard/utils'

type ProductCarouselProps = {
  title: string;
  theme?: ThemeColors;
  productsCollection?: any;
};

const getProductsCard = async (item: any) => getProductCardByID(item.sys.id)
const getProductsSlug = async (item: any) => getProductFields(item.slug)

const fetchProducts = async (productsCollection: any): Promise<ProductCardData[]> => Promise.all(
  productsCollection.items.map(async (item: any) => {
    let product: any = {}
    if (item.__typename === 'ProductCard') {
      product = await getProductsCard(item)
      return {
        __typename: item.__typename,
        size: product.size,
        hoverImage: product.hoverImage,
        gallery: product.gallery,
        product: product.product,
        variantCards: product.variantCards,
      }
    }
    if (item.__typename === 'Product') {
      product = await getProductsSlug(item)
      return {
        __typename: item.__typename,
        slug: product.slug,
        title: product.title,
        seoMetadata: product.seoMetadata,
        productId: product.productId,
        productMetadata: product.productMetadata,
        swatches: product.swatches,
        variants: product.variants,
        callouts: product.callouts,
      }
    }
    return product
  })
)

const ProductCarousel = async ({
  theme = 'white',
  title,
  productsCollection,
}: ProductCarouselProps) => {
  const products: ProductCardData[] = await fetchProducts(productsCollection)

  return (
    <Container as="section" theme={theme}>
      <CategoriesLarge products={products} header={title} />
    </Container>
  )
}

export default ProductCarousel
