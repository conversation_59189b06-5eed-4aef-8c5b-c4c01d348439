'use client'

import { ProductSearchParams } from './utils'
import { LinkRenderFunction } from './ProductSwatches/types'

import { getProductUrl, ProductVariantParams, parseVariantParams } from '@/utils/urls'
import { useAppDispatch } from '@redux/hooks'
import { selectProduct, selectVariant } from '@redux/features/product/productSlice'
import { ContentfulProduct } from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

import { ReactNode, Suspense } from 'react'
import Link from 'next/link'
import * as stylex from '@stylexjs/stylex'
import { useParams } from 'next/navigation'

type ProductLinkProps = {
  product: ContentfulProduct
  variant?: ContentfulProductVariant | null
  children: ReactNode
  scroll?: boolean
  urlParams?: ProductSearchParams
  styleProp?: stylex.StyleXStyles
  renderLink?: LinkRenderFunction
  onClick?: () => void
}

const styles = stylex.create({
  linkLoading: {
    opacity: 0.5,
  }
})

const ProductLink = ({
  product,
  variant,
  children,
  urlParams,
  styleProp,
  renderLink,
  onClick,
  scroll = true,
}: ProductLinkProps) => {
  const dispatch = useAppDispatch()
  const params = useParams()

  const handleClick = () => {
    // I'm removing these properties from the product object
    // because they contain non serializable values
    //  - pageSectionsCollection has React components in it
    //  - reviews has the result of an async server function in it
    // TODO: add type for pageSectionsCollection in the product type
    delete (product as any).pageSectionsCollection
    delete product.reviews

    if (variant) {
      dispatch(selectVariant({ product, variant, quantity: 1 }))
    } else {
      dispatch(selectProduct({ product, quantity: 1 }))
    }

    if (onClick) {
      onClick()
    }
  }

  if (variant && !urlParams) {
    const attribute = product.swatches?.items?.[0]?.slug

    if (attribute) {
      const variantSwatchSlug = variant.swatch?.slug
      if (variantSwatchSlug) {
        urlParams = { [attribute]: variantSwatchSlug }
      }
    }
  }

  // Get current variant parameters from URL path segments
  const currentVariantParams = parseVariantParams(params.variants as string)

  // Combine current variant parameters with new urlParams
  const combinedParams: ProductVariantParams = {
    ...currentVariantParams,
    ...urlParams
  }

  const href = getProductUrl(product.slug, combinedParams)

  if (renderLink) {
    return renderLink({
      children,
      styleProp,
      variant,
      href,
      handleClickEvents: handleClick,
    })
  }

  return (
    <Link
      onClick={handleClick}
      scroll={scroll}
      href={href}
      {...stylex.props(styleProp)}
    >
      {children}
    </Link>
  )
}

// eslint-disable-next-line func-names
export default function (props: ProductLinkProps) {
  const { children, styleProp } = props

  return (
    <Suspense
      fallback={(
        <span {...stylex.props(styleProp, styles.linkLoading)}>
          {children}
        </span>
      )}
    >
      <ProductLink {...props} />
    </Suspense>
  )
}
