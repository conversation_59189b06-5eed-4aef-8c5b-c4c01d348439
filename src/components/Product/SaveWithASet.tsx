import ProductPrice from './ProductPrice'

import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import CallToAction from '@components/Generic/CallToAction'
import Rating from '@components/Generic/Rating'
import CheckMark from '@components/Generic/Icon/lib/CheckMark'
import generateSlug from '@utils/generateSlug'
import {
  spacing,
  fontSizes,
  breakpoints,
} from '@/app/themeTokens.stylex'
import themes, { ThemeColors } from '@/app/themeThemes.stylex'
import RichTextRender from '@utils/RichTextRenderer'
import { ExtractSaveWithASetModule } from '@/lib/contentful/extractSaveWithASet'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'
import Link from 'next/link'

const MOBILE = '@media (max-width: 1024px)'

const styles = stylex.create({
  headerDesktop: {
    display: {
      default: 'block',
      [MOBILE]: 'none'
    },
  },
  headerMobile: {
    display: {
      default: 'none',
      [MOBILE]: 'block'
    }
  },
  mainContainer: {
    maxWidth: breakpoints.xl,
    margin: '0 auto',
    flexDirection: {
      default: 'row',
      [MOBILE]: 'column-reverse'
    },
    flexWrap: null,
    gap: {
      default: spacing.xxl,
      [MOBILE]: spacing.md
    },
  },
  fullWrapperContainer: { width: '100%' },
  descriptionContainer: {
    justifyContent: 'center',
    gap: spacing.md,
  },
  listContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: spacing.sm,
  },
  comparePrice: {
    fontSize: {
      default: fontSizes.md,
      [MOBILE]: fontSizes.sm,
    },
  },
  itemContainerCustom: { alignItems: 'baseline' },
  itemTitleCustom: {
    maxWidth: {
      default: '420px',
      [MOBILE]: '305px'
    },
  },
  itemImage: {
    width: '100%',
    height: 'auto',
    maxHeight: '100%',
    borderRadius: spacing.sm,
    overflow: 'hidden',
    textAlign: 'center',
    marginBottom: {
      default: 0,
      [MOBILE]: spacing.md
    }
  },
  mainImage: {
    objectFit: 'cover',
    borderRadius: spacing.sm,
    width: '100%',
    height: 'auto',
  },
  contentList: {
    display: 'flex',
    alignItems: 'center',
    gap: spacing.sm
  },
  checkMark: {
    minHeight: '24px',
    minWidth: '25px',
  },
  cta: {
    width: '100%',
    maxWidth: {
      default: breakpoints.xxs,
      [MOBILE]: '100%'
    },
  },
  ratingText: {
    fontSize: fontSizes.xxs,
  },
})

type SaveWithSetProps = {
  title: string;
  products: ExtractSaveWithASetModule;
  theme?: ThemeColors;
};

const SaveWithSet = async ({
  title,
  products,
  theme = 'offWhite',
}: SaveWithSetProps) => {
  const themeStyles = themes[theme]

  return (
    <Container as="section" theme={theme}>
      <Container
        as="div"
        flex
        paddingBlock="5"
        paddingInline="4"
        gap="5"
        styleProp={[styles.mainContainer, themeStyles]}
      >
        <Container
          as="div"
          flex
          styleProp={[styles.descriptionContainer, styles.fullWrapperContainer]}
        >
          {products.blockContentCustomTitle ? (
            <Typography
              as="h4"
              typographyTheme="h4Secondary"
              fontBold
              styleProp={styles.headerDesktop}
            >
              {products.blockContentCustomTitle}
            </Typography>
          ) : (
            <Typography
              as="h4"
              typographyTheme="h4Secondary"
              fontBold
              styleProp={styles.headerDesktop}
            >
              {title}
            </Typography>
          )}
          <Container as="div">
            {products.mainDescription ? (
              <Typography as="p" typographyTheme="bodyLarge">
                {products.mainDescription}
              </Typography>
            ) : (
              <RichTextRender content={products.overviewDescription?.content?.json} />
            )}
          </Container>
          {products.keyProps && (
            <Container as="div">
              <ul {...stylex.props(styles.listContainer)}>
                {products.keyProps.map((keyProp: string) => (
                  <li key={keyProp} {...stylex.props(styles.contentList)}>
                    <CheckMark
                      dimensions="24px"
                      fill="#1F3438"
                      {...stylex.props(styles.checkMark)}
                    />
                    <Typography
                      as="p"
                      typographyTheme="bodyLarge"
                    >
                      {keyProp}
                    </Typography>
                  </li>
                ))}
              </ul>
            </Container>
          )}
          {products.mainProduct?.slug && (
            <CallToAction
              useArrow
              href={generateSlug(products.mainProduct?.slug, 'Product')}
              variant="primary"
              styleProp={styles.cta}
            >
              Shop Now
            </CallToAction>
          )}
        </Container>
        <Container as="div" styleProp={styles.fullWrapperContainer}>
          {products.blockContentCustomTitle ? (
            <Typography
              as="h4"
              marginBottom="md"
              typographyTheme="h4Secondary"
              fontBold
              styleProp={styles.headerMobile}
            >
              {products.blockContentCustomTitle}
            </Typography>
          ) : (
            <Typography
              as="h4"
              marginBottom="md"
              typographyTheme="h4Secondary"
              fontBold
              styleProp={styles.headerMobile}
            >
              {title}
            </Typography>
          )}
          {products.primaryImage?.url && (
            <Link href={generateSlug(products.mainProduct?.slug, 'Product')}>
              <Container
                as="div"
                styleProp={styles.itemImage}
              >
                <Image
                  src={products.primaryImage.url}
                  alt={products.primaryImage.title}
                  width={600}
                  height={356}
                  {...stylex.props(styles.mainImage)}
                />
              </Container>
            </Link>
          )}
          <Container
            as="div"
            flex
            flexRow
            flexCentered
            spaceBetween
            gap="1"
            paddingBlock="1"
            styleProp={styles.itemContainerCustom}
          >
            {products.mainProduct.title && (
              <Typography
                as="h6"
                typographyTheme="h6Secondary"
                styleProp={styles.itemTitleCustom}
              >
                {products.mainProduct.title}
              </Typography>
            )}
            <Container
              as="div"
            >
              <ProductPrice
                onSale={products.mainProduct?.variants?.items[0]?.onSale}
                price={products.mainProduct?.variants?.items[0]?.price}
                compareAtPrice={products.mainProduct?.variants?.items[0]?.compareAtPrice}
              />
            </Container>
          </Container>
          <Rating
            rating={products.mainProduct?.reviews?.rating}
            reviewCount={products.mainProduct?.reviews?.count}
            textContentOn="right"
            textStyleProps={styles.ratingText}
            isAggregated
            iconColor="marigold"
          />
        </Container>
      </Container>
    </Container>
  )
}

export default SaveWithSet
