'use client'

import { useDotButton } from '../Content/PromotionalBlockSlider/EmblaCarouselDotButton'

import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { colors, spacing } from '@/app/themeTokens.stylex'
import Icon from '@/components/Generic/Icon'
import { empty, notEmpty } from '@/utils/checking'

import { EmblaOptionsType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'
import {
  useEffect, useRef, useState, useCallback
} from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

const fadeIn = stylex.keyframes({
  from: {
    opacity: 0
  },
  to: {
    opacity: 1
  }
})

const MD = '@media (min-width: 768px)'
const LG = '@media (min-width: 992px)'
const XL = '@media (min-width: 1100px)'

const PULSING = stylex.keyframes({
  '0%': {
    boxShadow: '0 0 0 0 white',
  },
  '70%': {
    boxShadow: '0 0 0 10px rgba(0, 0, 0, 0)',
  },
  '100%': {
    boxShadow: '0 0 0 0 rgba(0, 0, 0, 0)',
  },
})

const styles = stylex.create({
  root: {
    display: 'grid',
    justifyItems: 'center',
    paddingBlock: 45,
    paddingInline: spacing.sm,
    rowGap: '1rem',
    transition: 'background-color 0.5s ease-out',
    position: 'relative',
    minHeight: {
      default: 500,
      [LG]: 530
    },
    overflow: 'hidden',
    minWidth: '375px',
  },
  rootBg: (color: string) => ({
    backgroundColor: color,
  }),
  empty: {
    display: 'none'
  },
  iconButtonWrapper: {
    display: 'flex',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'flex-start',
    columnGap: 14,
    rowGap: '1rem',
    maxWidth: '100%',
  },
  icon: {
    cursor: 'pointer',
    position: 'relative',
    width: 30,
    height: 30,
    backgroundColor: 'transparent',
    borderRadius: '50%',
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: colors.gray300,
  },
  iconActive: {
    borderWidth: 0,
  },
  iconSquare: {
    borderRadius: 8
  },
  imageWrapper: {
    position: 'relative',
    width: {
      default: 'min(100% - 2rem, 500px)',
      [MD]: 350,
      [LG]: 500,
    },
    height: {
      default: 'auto',
      [MD]: 350,
      [LG]: 500,
    },
    marginInline: 'auto',
    aspectRatio: {
      default: 1
    }
  },
  kettleDetailDesktopWrapper: {
    position: 'absolute',
    top: '17.5%',
    left: '0',
    right: '0',
    width: 530,
    height: 350,
    margin: 'auto',
    display: {
      default: 'none',
      [MD]: 'block'
    }
  },
  DetailVisible: {
    animationName: fadeIn,
    animationDuration: '2s',
    transform: 'translateX(0)',
  },
  kettleDetail1: {
    display: {
      default: 'none',
      [MD]: 'block',
    },
    position: 'absolute',
    textAlign: 'right',
    transition: 'transform 1s ease-out',
    transitionDelay: '0.2s',
    transform: 'translateX(-85%)',
    top: {
      [MD]: '40%',
      [LG]: '56%',
    },
    left: {
      [MD]: '-23%',
      [LG]: '-42%',
      [XL]: '-46.5%',
    },
    right: {
      [MD]: '-21%',
      [LG]: '-39%',
      [XL]: '-45%',
    },
    maxWidth: {
      [MD]: '18ch',
      [LG]: '23ch',
      [XL]: '25ch',
    },
  },
  kettleDetail1ImgContainer: {
    position: 'absolute',
    height: 10,
    width: 104,
    left: {
      [MD]: '183px',
      [LG]: '240px',
      [XL]: '262px'
    },
    top: '5px'
  },
  kettleDetail2: {
    display: 'flex',
    flexDirection: 'column',
    position: 'absolute',
    textAlign: 'left',
    transition: 'transform 1.2s ease-out',
    transform: 'translateX(120%)',
    transitionDelay: '0.2s',
    top: {
      [MD]: '20%',
    },
    right: {
      [MD]: '-27.5%',
      [LG]: '-37.5%',
    },
    maxWidth: {
      [MD]: '22ch',
    },
  },
  kettleDetail2ImgContainer: {
    height: 10,
    width: 117,
    top: '5px',
    position: 'absolute',
    right: {
      [MD]: '232px',
      [LG]: '260px',
    }
  },
  kettleDetail3: {
    display: 'flex',
    flexDirection: 'column',
    position: 'absolute',
    textAlign: 'right',
    transitionDelay: '0.5s',
    transition: 'transform 800ms ease-out 0.5s',
    transform: 'translateX(-85%)',
    top: {
      [MD]: '69%',
      [LG]: '98%',
    },
    left: {
      [MD]: '-21%',
      [LG]: '-39%',
      [XL]: '-45%',
    },
    maxWidth: {
      [MD]: '18ch',
      [LG]: '23ch',
      [XL]: '26ch',
    },
  },
  kettleDetail3ImgContainer: {
    position: 'absolute',
    top: '6px',
    height: 68,
    width: 101,
    left: {
      [MD]: '182px',
      [LG]: '236px',
      [XL]: '268px',
    }
  },
  kettleDetail4: {
    display: 'flex',
    flexDirection: 'column',
    position: 'absolute',
    textAlign: 'left',
    transitionDelay: '0.5s',
    transition: 'transform 800ms ease-out 0.4s',
    transform: 'translateX(120%)',
    top: {
      [MD]: '55%',
      [LG]: '75%',
    },
    right: {
      [MD]: '-25%',
      [LG]: '-39%',
    },
    maxWidth: {
      [MD]: '22ch',
    },
  },
  kettleDetail4ImgContainer: {
    position: 'absolute',
    top: '2px',
    height: 10,
    width: 63,
    right: {
      [MD]: '230px',
      [LG]: '256px',
    }
  },
  foodStorageDetail1: {
    display: {
      default: 'none',
      [MD]: 'block',
    },
    position: 'absolute',
    textAlign: 'right',
    transition: 'transform 1s ease-out',
    transitionDelay: '0.2s',
    transform: 'translateX(-85%)',
    top: {
      [MD]: '8%',
      [LG]: '3%',
    },
    left: {
      [MD]: '-16%',
      [LG]: '-33%',
      [XL]: '-36%',
    },
    maxWidth: {
      [MD]: '18ch',
      [LG]: '23ch',
      [XL]: '26ch',
    },
  },

  foodStorageDetail1ImgContainer: {
    position: 'absolute',
    height: 10,
    width: 104,
    left: {
      [MD]: '183px',
      [LG]: '240px',
      [XL]: '252px'
    },
    top: '5px'
  },
  foodStorageDetail2: {
    display: 'flex',
    flexDirection: 'column',
    position: 'absolute',
    textAlign: 'left',
    transition: 'transform 1.2s ease-out',
    transform: 'translateX(120%)',
    transitionDelay: '0.2s',
    top: {
      [MD]: '55%',
      [LG]: '70%',
    },
    right: {
      [MD]: '-31.5%',
      [LG]: '-43.5%',
    },
    maxWidth: {
      [MD]: '22ch',
      [LG]: '25ch',
    },
  },
  foodStorageDetail2ImgContainer: {
    height: 10,
    width: 117,
    top: '5px',
    position: 'absolute',
    right: {
      [MD]: '232px',
      [LG]: '260px',
    }
  },
  foodStorageDetail3: {
    display: 'flex',
    flexDirection: 'column',
    position: 'absolute',
    textAlign: 'right',
    transitionDelay: '0.5s',
    transition: 'transform 800ms ease-out 0.5s',
    transform: 'translateX(-85%)',
    top: {
      [MD]: '45%',
      [LG]: '48%',
    },
    left: {
      [MD]: '-20%',
      [LG]: '-44%',
      [XL]: '-50%',
    },
    maxWidth: {
      [MD]: '17ch',
      [LG]: '22ch',
      [XL]: '25ch',
    },

  },
  foodStorageDetail3ImgContainer: {
    position: 'absolute',
    top: '6px',
    height: 48,
    width: 62,
    left: {
      [MD]: '182px',
      [LG]: '236px',
      [XL]: '268px',
    }
  },
  knifeUtensilSetDetail1: {
    display: {
      default: 'none',
      [MD]: 'block',
    },
    position: 'absolute',
    textAlign: 'right',
    transition: 'transform 1s ease-out',
    transitionDelay: '0.2s',
    transform: 'translateX(-85%)',
    top: {
      [MD]: '20%',
    },
    left: {
      [MD]: '-22%',
      [LG]: '-41.5%',
      [XL]: '-45.5%',
    },
    maxWidth: {
      [MD]: '18ch',
      [LG]: '23ch',
      [XL]: '25ch',
    },
  },
  knifeUtensilSetDetail1ImgContainer: {
    position: 'absolute',
    height: 10,
    width: 104,
    left: {
      [MD]: '183px',
      [LG]: '240px',
      [XL]: '262px'
    },
    top: '5px'
  },
  knifeUtensilSetDetail2: {
    display: 'flex',
    flexDirection: 'column',
    position: 'absolute',
    textAlign: 'left',
    transition: 'transform 1.2s ease-out',
    transform: 'translateX(120%)',
    transitionDelay: '0.2s',
    top: {
      [MD]: '-1%',
    },
    right: {
      [MD]: '-22.5%',
      [LG]: '-36%',
    },
    maxWidth: {
      [MD]: '22ch',
      [LG]: '25ch',
    },
  },
  knifeUtensilSetDetail2ImgContainer: {
    height: 155,
    width: 54,
    top: '5px',
    position: 'absolute',
    right: {
      [MD]: '232px',
      [LG]: '260px',
    }
  },
  knifeUtensilSetDetail3: {
    display: 'flex',
    flexDirection: 'column',
    position: 'absolute',
    textAlign: 'right',
    transitionDelay: '0.5s',
    transition: 'transform 800ms ease-out 0.5s',
    transform: 'translateX(-85%)',
    top: {
      [MD]: '72%',
      [LG]: '95%',
    },
    left: {
      [MD]: '-23%',
      [LG]: '-43%',
      [XL]: '-50%',
    },
    maxWidth: {
      [MD]: '18ch',
      [LG]: '22ch',
      [XL]: '25ch',
    },

  },
  knifeUtensilSetDetail3ImgContainer: {
    position: 'absolute',
    top: '6px',
    height: 10,
    width: 98,
    left: {
      [MD]: '182px',
      [LG]: '236px',
      [XL]: '268px',
    }
  },
  partTitle: {
    marginBlockEnd: '0.5rem',
    display: 'block',
    width: '100%',
  },
  partTitleMobile: {
    textAlign: 'center'
  },
  partDesc: {
    display: 'block',
    width: '100%',
  },
  partDescMobile: {
    textAlign: 'center'
  },
  kettleDetailMobileWrapper: {
    display: {
      default: 'flex',
      [MD]: 'none'
    },
    justifyContent: 'center',
    maxWidth: {
      default: '300px',
      '@media (min-width: 400px)': '400px',
    },

  },
  kettleDetailDotWrapper: {
    width: 'min(100% - 2rem, 500px)',
    maxWidth: '500px',
    position: 'absolute',
    top: '96px',
    left: '0',
    right: '0',
    bottom: '0',
    marginInline: 'auto',
    aspectRatio: '1',
  },
  DetailDot: {
    margin: '10px',
    height: '25px',
    width: '25px',
    transform: 'scale(1)',
    position: 'absolute',
    cursor: 'pointer',
    display: 'block',
    backgroundColor: colors.navy,
    borderStyle: 'solid',
    borderWidth: '3px',
    borderColor: colors.white,
    borderRadius: '50%',
    outlineWidth: '10px',
    outlineStyle: 'solid',
    outlineColor: 'rgba(255,255,255,0.65)',
  },
  kettleDetailDot1: {
    top: '40%',
    left: '18%',
  },
  kettleDetailDot2: {
    top: '29%',
    left: '66%',
  },
  kettleDetailDot3: {
    top: '81%',
    left: '17%',
  },
  kettleDetailDot4: {
    top: '50%',
    left: '75%',
  },
  foodStorageDetailDot1: {
    top: '5%',
    left: '23%',
  },
  foodStorageDetailDot2: {
    top: '45%',
    left: '10%',
  },
  foodStorageDetailDot3: {
    top: '48%',
    left: '72%',
  },
  knifeUtensilSetDetailDot1: {
    top: '15%',
    left: '15%',
  },
  knifeUtensilSetDetailDot2: {
    top: '29%',
    left: '74%',
  },
  knifeUtensilSetDetailDot3: {
    top: '71%',
    left: '6%',
  },
  DetailActiveDot: {
    backgroundColor: '#fcfcfa',
    borderColor: '#1f3438',
    animationName: `${PULSING}`,
    animationDuration: '2s',
    animationIterationCount: 'infinite',
    borderRadius: '50%',
    margin: '10px',
    height: '25px',
    width: '25px',
    position: 'absolute',
    cursor: 'pointer',
  },
  AnatomyDetailSliderWrapper: {
    // width: 'min(100% - 2rem, 375px)',
    margin: 'auto',
    display: {
      default: 'block',
    },
  },
  AnatomyDetailSliderViewPort: {
    overflow: 'hidden',
    position: 'relative'
  },
  AnatomyDetailSliderContainer: {
    display: 'flex',
    touchAction: 'pan-y pinch-zoom',
    paddingBlock: spacing.md,
  },
  AnatomyDetailSliderControlContainer: {
    gap: spacing.md,
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: spacing.md,
  },
  AnatomyDetailSlider: {
    transform: 'translate3d(0, 0, 0)',
    flex: '0 0 100%',
    textAlign: 'center',
  },
  smallDot: {
    display: 'block',
    width: '12px',
    height: '12px',
    borderRadius: '50%',
    backgroundColor: colors.gray300,
  },
  smallSelectedDot: {
    backgroundColor: colors.navy,
  },
  petiteCookerDetail1: {
    display: 'flex',
    flexDirection: 'column',
    position: 'absolute',
    textAlign: 'right',
    transition: 'transform 1s ease-out',
    transitionDelay: '0.2s',
    transform: 'translateX(-85%)',
    top: {
      [MD]: '20%',
    },
    left: {
      [MD]: '-30%',
      [LG]: '-38%',
      [XL]: '-42.5%',
    },
    maxWidth: {
      [MD]: '23ch',
      [LG]: '23ch',
      [XL]: '25ch',
    },
  },
  petiteCookerDetail1ImgContainer: {
    position: 'absolute',
    top: '6px',
    height: 10,
    width: 104,
    left: {
      [MD]: '235px',
      [LG]: '240px',
      [XL]: '262px'
    }
  },
  petiteCookerDetail2: {
    display: 'flex',
    flexDirection: 'column',
    position: 'absolute',
    textAlign: 'left',
    transition: 'transform 1.2s ease-out',
    transform: 'translateX(120%)',
    transitionDelay: '0.2s',
    top: {
      [MD]: '40%',
      [LG]: '46%',
    },
    right: {
      [MD]: '-25%',
      [LG]: '-41%',
    },
    maxWidth: {
      [MD]: '20ch',
      [LG]: '23ch',
    },
  },
  petiteCookerDetail2ImgContainer: {
    position: 'absolute',
    top: '5px',
    height: 10,
    width: 117,
    right: {
      [MD]: '210px',
      [LG]: '237px',
    }
  },
  petiteCookerDetail3: {
    display: 'flex',
    flexDirection: 'column',
    position: 'absolute',
    textAlign: 'right',
    transitionDelay: '0.5s',
    transition: 'transform 800ms ease-out 0.5s',
    transform: 'translateX(-85%)',
    top: {
      [MD]: '53%',
      [XL]: '63%'
    },
    left: {
      [MD]: '-23%',
      [LG]: '-32%'
    },
    maxWidth: {
      [MD]: '18ch',
    },
  },
  petiteCookerDetail3ImgContainer: {
    position: 'absolute',
    height: 48,
    width: 100,
    top: {
      [MD]: '-18%',
    },
    left: {
      [MD]: '182px',
      [LG]: '186px',
    }
  },
  petiteCookerDetailDot1: {
    top: '20%',
    left: '16%',
  },
  petiteCookerDetailDot2: {
    top: '53%',
    left: '10%',
  },
  petiteCookerDetailDot3: {
    top: '50%',
    left: '74%',
  },
})

export type AnatomyVariant = {
  id: string;
  name: string;
  image: string;
  backgroundColor: string;
  slug: string;
  icon: string;
}

export type AnatomyPart = {
  id: string;
  title: string;
  description: string;
  media: string;
};

export type AnatomyProps = {
  variant: {
    swatch: {
      swatchType: string;
    }
  }
  product: {
    id: string;
    name: string;
    slug: string;
    variants: AnatomyVariant[],
    parts: AnatomyPart[]
  }
}

const FIRST_INDEX = 0
const SECOND_INDEX = 1
const THIRD_INDEX = 2
const FOURTH_INDEX = 3

// eslint-disable-next-line complexity
const Anatomy = ({ product, variant }: AnatomyProps) => {
  const searchParams = useSearchParams()
  const router = useRouter()

  const [isDesktopContentVisible, setIsDesktopContentVisible] = useState(false)
  const contentRef = useRef<HTMLDivElement | null>(null)
  const [selected, setSelected] = useState<number>(0)
  const OPTIONS: EmblaOptionsType = { loop: true }
  const { variants, parts } = product
  const [emblaRef, emblaApi] = useEmblaCarousel(OPTIONS)

  const { selectedIndex, onDotButtonClick } = useDotButton(emblaApi)

  const [prevBtnDisabled, setPrevBtnDisabled] = useState(true)
  const [nextBtnDisabled, setNextBtnDisabled] = useState(true)

  const onPrevButtonClick = useCallback(() => {
    if (!emblaApi) return
    emblaApi.scrollPrev()
  }, [emblaApi])

  const onNextButtonClick = useCallback(() => {
    if (!emblaApi) return
    emblaApi.scrollNext()
  }, [emblaApi])

  const onSelect = useCallback((embla: any) => {
    setPrevBtnDisabled(!embla.canScrollPrev())
    setNextBtnDisabled(!embla.canScrollNext())
  }, [])

  useEffect(() => {
    if (!emblaApi) return

    onSelect(emblaApi)
    emblaApi.on('reInit', onSelect).on('select', onSelect)
  }, [emblaApi, onSelect])

  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      const entry = entries[0]
      setIsDesktopContentVisible(entry.isIntersecting)
    })
    observer.observe(contentRef.current as Element)
  }, [])

  // SYNC WITH CURRENT VARIANT
  const paramValue = searchParams.get(variant?.swatch?.swatchType)
  const swatchValue = paramValue ?? null

  useEffect(() => {
    if (empty(variants)) {
      return
    }

    if (swatchValue) {
      const index = variants.findIndex(({ slug }) => slug === swatchValue)
      if (index !== -1) {
        setSelected(index)
      }
    }
  }, [swatchValue, variants])

  // TODO: Check with TEO and Dawid this implementation
  function selectedSwatch({ slug }: { slug: string }) {
    return () => {
      if (notEmpty(variant)) {
        // TODO: Check this potential issue
        const targetUrl = new URL(window.location.href)
        targetUrl.searchParams.set(variant.swatch.swatchType, slug)

        router.push(
          targetUrl.toString(),
          { scroll: false },
        )
      } else {
        setSelected(variants.findIndex((v) => v.slug === slug))
      }
    }
  }

  return (
    <div {...stylex.props(styles.root, styles.rootBg(variants[selected].backgroundColor))}>
      <Container styleProp={styles.iconButtonWrapper}>
        {variants.length > 1 && variants.map((v: AnatomyVariant, index: number) => (
          <button
            key={`variant-${v.slug}`}
            {...stylex.props(
              styles.icon,
              index === selected && styles.iconActive,
              product.slug === 'food-storage-set' && styles.iconSquare,
            )}
            type="button"
            onClick={selectedSwatch({ slug: v.slug })}
          >
            <Image alt="icon" fill src={v.icon} style={{ objectFit: 'contain' }} />
          </button>
        ))}
      </Container>
      <Container as="div" styleProp={[styles.imageWrapper]}>
        <Image alt="image" fill src={variants[selected].image} />
      </Container>
      {/* Desktop view */}
      <div {...stylex.props(styles.kettleDetailDesktopWrapper)} ref={contentRef}>
        {/* eslint-disable-next-line complexity, no-magic-numbers */}
        {product.slug === 'tea-kettle' && parts.map((part: AnatomyPart, index: number) => (
          <div
            key={part.id}
            {...stylex.props(
              index === FIRST_INDEX && styles.kettleDetail1,
              index === FIRST_INDEX && isDesktopContentVisible && styles.DetailVisible,
              index === SECOND_INDEX && styles.kettleDetail2,
              index === SECOND_INDEX && isDesktopContentVisible && styles.DetailVisible,
              index === THIRD_INDEX && styles.kettleDetail3,
              index === THIRD_INDEX && isDesktopContentVisible && styles.DetailVisible,
              index === FOURTH_INDEX && styles.kettleDetail4,
              index === FOURTH_INDEX && isDesktopContentVisible && styles.DetailVisible,
            )}
          >
            <Typography as="span" typographyTheme="bodySmall" fontBold styleProp={styles.partTitle}>{part.title}</Typography>
            <Typography as="span" typographyTheme="bodySmall" styleProp={styles.partDesc}>{part.description}</Typography>

            <div {...stylex.props(
              index === FIRST_INDEX && styles.kettleDetail1ImgContainer,
              index === SECOND_INDEX && styles.kettleDetail2ImgContainer,
              index === THIRD_INDEX && styles.kettleDetail3ImgContainer,
              index === FOURTH_INDEX && styles.kettleDetail4ImgContainer,

            )}
            >
              <Image
                alt="icon"
                fill
                src={part.media}
              />
            </div>
          </div>
        ))}
        {/* eslint-disable-next-line complexity, no-magic-numbers */}
        {product.slug === 'food-storage-set' && parts.map((part: AnatomyPart, index: number) => (
          <div
            key={part.id}
            {...stylex.props(
              index === FIRST_INDEX && styles.foodStorageDetail1,
              index === FIRST_INDEX && isDesktopContentVisible && styles.DetailVisible,
              index === SECOND_INDEX && styles.foodStorageDetail2,
              index === SECOND_INDEX && isDesktopContentVisible && styles.DetailVisible,
              index === THIRD_INDEX && styles.foodStorageDetail3,
              index === THIRD_INDEX && isDesktopContentVisible && styles.DetailVisible,
            )}
          >
            <Typography as="span" typographyTheme="bodyLarge" fontBold styleProp={styles.partTitle}>{part.title}</Typography>
            <Typography as="span" typographyTheme="bodyLarge" styleProp={styles.partDesc}>{part.description}</Typography>

            <div {...stylex.props(
              index === FIRST_INDEX && styles.foodStorageDetail1ImgContainer,
              index === SECOND_INDEX && styles.foodStorageDetail2ImgContainer,
              index === THIRD_INDEX && styles.foodStorageDetail3ImgContainer,

            )}
            >
              <Image
                alt="icon"
                fill
                src={part.media}
              />
            </div>
          </div>
        ))}
        {/* eslint-disable-next-line complexity, no-magic-numbers */}
        {product.slug === 'knife-utensil-set' && parts.map((part: AnatomyPart, index: number) => (
          <div
            key={part.id}
            {...stylex.props(
              index === FIRST_INDEX && styles.knifeUtensilSetDetail1,
              index === FIRST_INDEX && isDesktopContentVisible && styles.DetailVisible,
              index === SECOND_INDEX && styles.knifeUtensilSetDetail2,
              index === SECOND_INDEX && isDesktopContentVisible && styles.DetailVisible,
              index === THIRD_INDEX && styles.knifeUtensilSetDetail3,
              index === THIRD_INDEX && isDesktopContentVisible && styles.DetailVisible,
            )}
          >
            <Typography as="span" typographyTheme="bodyLarge" fontBold styleProp={styles.partTitle}>{part.title}</Typography>
            <Typography as="span" typographyTheme="bodyLarge" styleProp={styles.partDesc}>{part.description}</Typography>

            <div {...stylex.props(
              index === FIRST_INDEX && styles.knifeUtensilSetDetail1ImgContainer,
              index === SECOND_INDEX && styles.knifeUtensilSetDetail2ImgContainer,
              index === THIRD_INDEX && styles.knifeUtensilSetDetail3ImgContainer,

            )}
            >
              <Image
                alt="icon"
                fill
                src={part.media}
              />
            </div>
          </div>
        ))}
        {/* eslint-disable-next-line complexity, no-magic-numbers */}
        {product.slug === 'ceramic-petite-cooker' && parts.map((part: AnatomyPart, index: number) => (
          <div
            key={part.id}
            {...stylex.props(
              index === FIRST_INDEX && styles.petiteCookerDetail1,
              index === FIRST_INDEX && isDesktopContentVisible && styles.DetailVisible,
              index === SECOND_INDEX && styles.petiteCookerDetail2,
              index === SECOND_INDEX && isDesktopContentVisible && styles.DetailVisible,
              index === THIRD_INDEX && styles.petiteCookerDetail3,
              index === THIRD_INDEX && isDesktopContentVisible && styles.DetailVisible,
            )}
          >
            <Typography as="span" typographyTheme="bodySmall" fontBold styleProp={styles.partTitle}>{part.title}</Typography>
            <Typography as="span" typographyTheme="bodySmall" styleProp={styles.partDesc}>{part.description}</Typography>

            <div {...stylex.props(
              index === FIRST_INDEX && styles.petiteCookerDetail1ImgContainer,
              index === SECOND_INDEX && styles.petiteCookerDetail2ImgContainer,
              index === THIRD_INDEX && styles.petiteCookerDetail3ImgContainer,
            )}
            >
              <Image
                alt="icon"
                fill
                src={part.media}
              />
            </div>
          </div>
        ))}
      </div>
      {/* Mobile view */}
      <div {...stylex.props(styles.kettleDetailMobileWrapper)}>
        {/* eslint-disable-next-line complexity, no-magic-numbers */}
        {product.slug === 'tea-kettle' && (
          <div {...stylex.props(styles.kettleDetailDotWrapper)}>
            <div {...stylex.props([styles.DetailDot, styles.kettleDetailDot1, selectedIndex === FIRST_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(FIRST_INDEX)} />
            <div {...stylex.props([styles.DetailDot, styles.kettleDetailDot2, selectedIndex === SECOND_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(SECOND_INDEX)} />
            <div {...stylex.props([styles.DetailDot, styles.kettleDetailDot3, selectedIndex === THIRD_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(THIRD_INDEX)} />
            <div {...stylex.props([styles.DetailDot, styles.kettleDetailDot4, selectedIndex === FOURTH_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(FOURTH_INDEX)} />
          </div>
        )}
        {/* eslint-disable-next-line complexity, no-magic-numbers */}
        {product.slug === 'food-storage-set' && (
          <div {...stylex.props(styles.kettleDetailDotWrapper)}>
            <div {...stylex.props([styles.DetailDot, styles.foodStorageDetailDot1, selectedIndex === FIRST_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(FIRST_INDEX)} />
            <div {...stylex.props([styles.DetailDot, styles.foodStorageDetailDot2, selectedIndex === SECOND_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(SECOND_INDEX)} />
            <div {...stylex.props([styles.DetailDot, styles.foodStorageDetailDot3, selectedIndex === THIRD_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(THIRD_INDEX)} />
          </div>
        )}
        {/* eslint-disable-next-line complexity, no-magic-numbers */}
        {product.slug === 'knife-utensil-set' && (
          <div {...stylex.props(styles.kettleDetailDotWrapper)}>
            <div {...stylex.props([styles.DetailDot, styles.knifeUtensilSetDetailDot1, selectedIndex === FIRST_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(FIRST_INDEX)} />
            <div {...stylex.props([styles.DetailDot, styles.knifeUtensilSetDetailDot2, selectedIndex === SECOND_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(SECOND_INDEX)} />
            <div {...stylex.props([styles.DetailDot, styles.knifeUtensilSetDetailDot3, selectedIndex === THIRD_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(THIRD_INDEX)} />
          </div>
        )}
        {/* eslint-disable-next-line complexity, no-magic-numbers */}
        {product.slug === 'ceramic-petite-cooker' && (
          <div {...stylex.props(styles.kettleDetailDotWrapper)}>
            <div {...stylex.props([styles.DetailDot, styles.petiteCookerDetailDot1, selectedIndex === FIRST_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(FIRST_INDEX)} />
            <div {...stylex.props([styles.DetailDot, styles.petiteCookerDetailDot2, selectedIndex === SECOND_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(SECOND_INDEX)} />
            <div {...stylex.props([styles.DetailDot, styles.petiteCookerDetailDot3, selectedIndex === THIRD_INDEX && styles.DetailActiveDot])} onClick={() => onDotButtonClick(THIRD_INDEX)} />
          </div>
        )}

        <Container as="div" styleProp={[styles.AnatomyDetailSliderWrapper]}>
          <div ref={emblaRef} {...stylex.props([styles.AnatomyDetailSliderViewPort])}>
            <Container as="div" styleProp={[styles.AnatomyDetailSliderContainer]}>
              {parts.map((part) => (
                <Container key={part.id} as="div" styleProp={[styles.AnatomyDetailSlider]}>
                  <Typography as="span" typographyTheme="h5Primary" fontBold styleProp={[styles.partTitle, styles.partTitleMobile]}>{part.title}</Typography>
                  <Typography as="span" typographyTheme="h6Primary" styleProp={[styles.partDesc, styles.partDescMobile]}>{part.description}</Typography>
                </Container>
              ))}
            </Container>
          </div>
          <Container as="div" styleProp={styles.AnatomyDetailSliderControlContainer}>
            <button
              type="button"
              onClick={onPrevButtonClick}
              disabled={prevBtnDisabled}
              aria-label="Previous Slide"
            >
              <Icon name="LeftArrow" size="large" />
            </button>
            {
                parts.map(({ id }, index) => (
                  <button
                    key={id}
                    type="button"
                    {...stylex.props([styles.smallDot, selectedIndex === index && styles.smallSelectedDot])}
                    onClick={() => onDotButtonClick(index)}
                  />
                ))
              }
            <button
              type="button"
              onClick={onNextButtonClick}
              disabled={nextBtnDisabled}
              aria-label="Next Slide"
            >
              <Icon name="RightArrow" size="large" />
            </button>
          </Container>
        </Container>
      </div>
    </div>
  )
}

export default Anatomy
