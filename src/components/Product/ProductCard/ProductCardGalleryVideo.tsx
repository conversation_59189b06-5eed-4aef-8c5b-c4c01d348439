import sharedStyles from './styles'

import { ContentfulImage } from '@/lib/contentful/types/generic'
import { ContentfulProductCard } from '@/lib/contentful/types/products'

import stylex from '@stylexjs/stylex'

const ProductCardGalleryVideo = ({ galleryItem, size }: {
  galleryItem: ContentfulImage;
  size: ContentfulProductCard['size'];
}) => galleryItem.contentType.startsWith('video') && (
<video
  src={galleryItem.url}
  width={363}
  height={482}
  muted
  loop
  autoPlay
  playsInline
  {...stylex.props(
    sharedStyles.cardImage,
    sharedStyles.galleryImage,
    size === 'Double' && sharedStyles.cardImageDouble
  )}
/>
)

export default ProductCardGalleryVideo
