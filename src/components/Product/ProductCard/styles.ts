import * as stylex from '@stylexjs/stylex'

const sharedStyles = stylex.create({
  cardImage: {
    width: '100%',
    height: '100%',
    aspectRatio: '286/382',
    objectFit: 'contain',
  },
  cardImageDouble: {
    aspectRatio: '591/382',
  },
  galleryImage: {
    objectFit: 'cover',
  },
  hoverImage: {
    objectFit: 'cover',
  },
  primaryImageLink: {
    display: 'block',
    backgroundColor: '#f7f7f4',
  },
})

export default sharedStyles
