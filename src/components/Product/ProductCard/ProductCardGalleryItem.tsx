import ProductCardGalleryImage from './ProductCardGalleryImage'
import ProductCardGalleryVideo from './ProductCardGalleryVideo'

import ProductLink from '../ProductLink'

import { ContentfulImage } from '@/lib/contentful/types/generic'
import {
  ContentfulProduct,
  ContentfulProductCard,
} from '@/lib/contentful/types/products'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  emblaSlide: {
    flex: '0 0 100%',
    minWidth: 0,
  },
  primaryImageLink: {
    display: 'block',
    backgroundColor: '#f7f7f4',
  },
})

const ProductCardGalleryItem = ({
  galleryItem,
  cardProduct,
  size = 'Single',
}: {
  galleryItem: ContentfulImage;
  cardProduct: ContentfulProduct;
  size: ContentfulProductCard['size'];
}) => (
  <div {...stylex.props(styles.emblaSlide)}>
    <ProductLink product={cardProduct} styleProp={styles.primaryImageLink}>
      <ProductCardGalleryVideo galleryItem={galleryItem} size={size} />
      <ProductCardGalleryImage galleryItem={galleryItem} size={size} />
    </ProductLink>
  </div>
)

export default ProductCardGalleryItem
