import sharedStyles from './styles'

import ProductLink from '../ProductLink'

import { ContentfulProduct, ContentfulProductCard } from '@/lib/contentful/types/products'
import { ContentfulImage } from '@/lib/contentful/types/generic'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

const styles = stylex.create({
  primaryImage: {
    mixBlendMode: 'darken'
  }
})

const ProductCardImage = ({
  product,
  variant,
  primaryImage,
  size,
  altImage
}: {
  product: ContentfulProduct;
  variant: ContentfulProductVariant | null;
  primaryImage: ContentfulImage;
  size: ContentfulProductCard['size'];
  altImage: boolean;
}) => (
  <ProductLink
    product={product}
    variant={variant}
    styleProp={!altImage && sharedStyles.primaryImageLink}
  >
    <Image
      src={primaryImage.url}
      alt={primaryImage.url}
      width={363}
      height={482}
      {...stylex.props(
        sharedStyles.cardImage,
        styles.primaryImage,
        size === 'Double' && sharedStyles.cardImageDouble
      )}
    />
  </ProductLink>
)

export default ProductCardImage
