import sharedStyles from './styles'

import { ContentfulImage } from '@/lib/contentful/types/generic'
import { ContentfulProductCard } from '@/lib/contentful/types/products'

import stylex from '@stylexjs/stylex'
import Image from 'next/image'

const ProductCardGalleryImage = ({ galleryItem, size }: {
  galleryItem: ContentfulImage;
  size: ContentfulProductCard['size'];
}) => galleryItem.contentType.startsWith('image') && (
<Image
  src={galleryItem.url}
  alt={galleryItem.url}
  width={363}
  height={482}
  {...stylex.props(
    sharedStyles.cardImage,
    sharedStyles.galleryImage,
    size === 'Double' && sharedStyles.cardImageDouble
  )}
/>
)

export default ProductCardGalleryImage
