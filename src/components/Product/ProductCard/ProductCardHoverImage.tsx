import { getHoverImage } from './utils'

import ProductLink from '../ProductLink'

import {
  ContentfulProduct,
  ContentfulProductCard,
} from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'
import useIsMobile from '@/hooks/isMobile'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

const styles = stylex.create({
  primaryImageLink: {
    display: 'block',
    backgroundColor: '#f7f7f4',
  },
  cardImage: {
    width: '100%',
    height: 'auto',
    aspectRatio: '286/382',
    objectFit: 'contain',
  },
  cardImageDouble: {
    aspectRatio: '591/382',
  },
  hoverImageWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
    opacity: 0,
    willChange: 'opacity',
  },
  hoverImageWrapperVisible: {
    opacity: 1,
    transition: 'opacity .25s linear',
  },
  hoverImage: {
    objectFit: 'cover',
  },
  badge: {
    position: 'absolute',
    top: '8px',
    right: '8px',
    zIndex: 1,
  }
})

type ProductCardHoverImageProps = {
  card?: ContentfulProductCard;
  product: ContentfulProduct;
  variant: ContentfulProductVariant | null;
  isHovered: boolean;
  badge?: {
    url: string;
    title: string;
    width: number;
    height: number;
  }
};

const ProductCardHoverImage = ({
  card,
  product,
  variant,
  isHovered,
  badge,
}: ProductCardHoverImageProps) => {
  const { isMobile } = useIsMobile()
  const hoverImage = getHoverImage(card || product)

  if (!hoverImage) {
    return null
  }

  const BADGE_MAX_SIZE = 72

  return (
    <div
      {...stylex.props(
        styles.hoverImageWrapper,
        isHovered && styles.hoverImageWrapperVisible
      )}
    >
      <ProductLink
        product={product}
        variant={variant}
        styleProp={styles.primaryImageLink}
      >
        <Image
          src={hoverImage.url}
          alt={hoverImage.url}
          width={363}
          height={482}
          {...stylex.props(
            styles.cardImage,
            styles.hoverImage,
            card?.size === 'Double' && styles.cardImageDouble
          )}
        />
        {!isMobile && badge && (
          <Image
            src={badge.url}
            alt={badge.title}
            width={Math.min(badge.width, BADGE_MAX_SIZE)}
            height={Math.min(badge.height, BADGE_MAX_SIZE)}
            unoptimized
            {...stylex.props(styles.badge)}
          />
        )}
      </ProductLink>
    </div>
  )
}

export default ProductCardHoverImage
