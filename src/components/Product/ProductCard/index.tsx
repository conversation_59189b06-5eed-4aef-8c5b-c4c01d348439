'use client'

import ProductCardHoverImage from './ProductCardHoverImage'
import ProductCardGallery from './ProductCardGallery'
import ProductCardSwatches from './ProductCardSwatches'
import ProductCardReviews from './ProductCardReviews'
import { useProductCard } from './hooks'
import ProductCardImage from './ProductCardImage'
import { getCurrentVariantCard, getOOSContentfulVariantAvailabilitySlugs, ProductCardData } from './utils'
import ProductCardCallToAction from './ProductCardCallToAction'

import ProductPrice from '../ProductPrice'
import { getOOSSwatches } from '../utils'
import { getDefaultOOSSwatches } from '../ProductSwatches/utils'
import ProductSavings from '../ProductDetails/ProductSavings'

import ProductLink from '@components/Product/ProductLink'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import CallToAction from '@components/Generic/CallToAction'
import Callouts from '@components/Generic/Callout/Callouts'
import { colors, spacing } from '@/app/themeTokens.stylex'
import useIntersectionObserver from '@/hooks/useIntersectionObserver'
import useIsMobile from '@/hooks/isMobile'
import { ContentfulProductCard } from '@/lib/contentful/types/products'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'
import * as stylex from '@stylexjs/stylex'
import { ReactNode, useState, useEffect } from 'react'

const styles = stylex.create({
  card: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'start',
    gap: spacing.sm,
    gridColumnStart: 'span 1',
    minWidth: 0,
  },
  cardDouble: {
    gridColumnStart: 'span 2',
  },
  imageWrapper: {
    position: 'relative',
    borderRadius: '8px',
    overflow: 'hidden',
    order: {
      default: -1,
      '@media (min-width: 768px)': 'unset',
    },
  },
  placeCallouts: {
    position: 'absolute',
    top: '8px',
    left: '8px',
    right: '8px',
    alignItems: 'flex-start',
    zIndex: 1
  },
  swatchesSliderContainer: {
    order: {
      default: -1,
      '@media (min-width: 768px)': 'unset',
    },
  },
  description: {
    fontSize: 14,
    lineHeight: '22px',
  },
  shopNowButtonSingle: {
    borderWidth: 1,
    alignSelf: 'start',
    textDecoration: 'underline',
    fontSize: 14,
    color: {
      default: colors.navy,
      ':hover': colors.gray,
    }
  },
  shopNowButtonDouble: {
    borderWidth: 1,
    alignSelf: 'start',
    padding: '8px 32px',
    fontSize: 16,
    color: {
      default: colors.navy,
      ':hover': colors.white,
    }
  },
  disabledOOS: {
    color: colors.gray
  },
  plpSpacing: {
    marginRight: spacing.xxs,
  },
  notifyMeLink: {
    fontWeight: 'bold',
    lineHeight: 1.5
  },
})

type ProductCardProps = {
  product: ProductCardData
  styleProp?: any
  onSwatchHover?: (isHovered: boolean) => void
  onClick?: () => void
};

const customCallToActionRenderer = (
  children: ReactNode,
  ctaStyles: stylex.StyleXStyles
) => (
  <ProductCardCallToAction styles={ctaStyles}>
    {children}
  </ProductCardCallToAction>
)

// eslint-disable-next-line complexity -- This component is complex and needs to be refactored
const ProductCard = ({
  product,
  styleProp = false,
  onSwatchHover = () => {},
  onClick
}: ProductCardProps) => {
  const { isMobile } = useIsMobile()
  const options = { loop: true }
  const productCard = useProductCard(product, options)
  const { ref, isIntersecting } = useIntersectionObserver()
  const _product = 'variants' in product ? product : productCard?.variant.cardProduct

  const defaultOOSSwatches = getDefaultOOSSwatches(
    _product?.variants.items
  )

  const [oosSwatches, setOOSSwatches] = useState<string[]>(defaultOOSSwatches)
  const [isSwatchesLoaded, setIsSwatchesLoaded] = useState(false)

  useEffect(() => {
    async function fetchData() {
      if (!_product || isSwatchesLoaded) return

      // Checks Shopify stock
      const response = await getOOSSwatches(_product.variants.items)

      // Checks for Contentful Variant level updates
      const contentfulVariantAvailabilityToggle = getOOSContentfulVariantAvailabilitySlugs(_product)

      const mergedOOSSwatches = Array.from(new Set([...response, ...contentfulVariantAvailabilityToggle]))

      setOOSSwatches(mergedOOSSwatches)
      setIsSwatchesLoaded(true)
    }

    if (isIntersecting) {
      fetchData()
    }
  }, [_product, isIntersecting])

  if (!productCard) {
    return null
  }

  const {
    variant,
    productGallery,
    hover,
  } = productCard

  const {
    card,
    cardProduct,
    primaryImage,
    gallery,
    size,
    description,
    currentVariant,
    setCurrentVariant,
  } = variant

  const { availableForSale, variantAvailability } = currentVariant || {}
  const currentNonDefaultVariant =
    currentVariant.sku !== cardProduct?.variants.items[0].sku
      ? currentVariant
      : null

  if (!cardProduct) {
    return null
  }

  const available = !(availableForSale === false || variantAvailability === 'Notify Me' || variantAvailability === 'Sold Out')
  const reviews = card ? card.reviews : cardProduct.reviews
  const callouts = cardProduct.callouts ? cardProduct.callouts.items : []
  const productCardCallouts = callouts.filter(
    (callout) => !callout.placement ||
      callout.placement.length === 0 ||
      callout.placement.includes('Product Card')
  )
  const textCallouts = productCardCallouts.filter((callout) => !callout.badge)
  const badges = productCardCallouts.filter((callout) => callout.badge)
  const { badge } = badges[0] || {}

  const currentVariantCard = getCurrentVariantCard(currentVariant || currentNonDefaultVariant, card)
  const hasAltImage = !!currentVariantCard?.image
  const canScrollPrev = productGallery.emblaApi?.canScrollPrev?.() ?? false
  const displayCallouts = isMobile ? !canScrollPrev : !hover.isHovered
  const attribute = cardProduct?.swatches?.items[0].slug || 'color'

  const mobileCard = card && {
    ...card,
    gallery: {
      items: [
        ...currentVariantCard?.image ? [currentVariantCard.image] : [],
        ...card?.gallery?.items || [],
      ]
    }
  } as ContentfulProductCard

  return (
    <div
      ref={ref}
      {...stylex.props(
        styles.card,
        size === 'Double' && styles.cardDouble,
        styleProp && styleProp
      )}
    >
      <Container
        styleProp={styles.imageWrapper}
        onMouseEnter={hover.enableHover}
        onMouseLeave={hover.disableHover}
        onClick={onClick}
      >
        {callouts && displayCallouts && (
          <Container styleProp={styles.placeCallouts}>
            <Callouts callouts={isMobile ? callouts : textCallouts} />
          </Container>
        )}
        <ProductCardImage
          product={cardProduct}
          variant={currentNonDefaultVariant}
          primaryImage={currentVariantCard?.image || primaryImage}
          size={size}
          altImage={hasAltImage}
        />
        {(!gallery || gallery.items.length === 0) && (
          <ProductCardHoverImage
            card={card}
            product={cardProduct}
            variant={currentNonDefaultVariant}
            isHovered={hover.isHovered}
            badge={badge}
          />
        )}
        {gallery && gallery.items.length > 0 && (
          <ProductCardGallery
            card={isMobile ? mobileCard : card}
            product={cardProduct}
            isHovered={isMobile ? true : hover.isHovered}
            gallery={productGallery}
          />
        )}
      </Container>
      <Container as="div" flex gap="1">
        <ProductLink product={cardProduct} variant={currentNonDefaultVariant}>
          <Typography
            as="h2"
            typographyTheme="h6Primary"
            typographyThemeMobile="bodyLarge"
          >
            {cardProduct.title}
          </Typography>
        </ProductLink>
        <ProductCardReviews reviews={reviews} />
        <Container as="div" flex flexRow gap="1">
          <Container flex flexRow>
            <ProductPrice
              onSale={currentVariant.onSale}
              slug={cardProduct.slug}
              size="sm"
              price={currentVariant.price}
              compareAtPrice={currentVariant.compareAtPrice}
              regularPriceOverride={currentVariant.regularPriceOverride}
              stylePropCompareAtPrice={[styles.disabledOOS, styles.plpSpacing]}
            />
          </Container>
          <ProductSavings
            slug={cardProduct.slug}
            price={currentVariant.price}
            compareAtPrice={currentVariant.compareAtPrice}
            regularPriceOverride={currentVariant.regularPriceOverride}
          />
        </Container>
      </Container>
      <div {...stylex.props(styles.swatchesSliderContainer)}>
        <ProductCardSwatches
          product={card || cardProduct}
          currentVariant={currentVariant}
          setCurrentVariant={setCurrentVariant}
          oosSwatches={oosSwatches}
          isHover={onSwatchHover}
        />
      </div>
      {description && (
        <Container styleProp={styles.description}>
          {typeof description === 'string'
            ? description
            : documentToPlainTextString(description.json)}
        </Container>
      )}
      {available ? (
        <ProductLink
          product={cardProduct}
          variant={currentNonDefaultVariant}
          onClick={onClick}
        >
          <CallToAction
            variant={size === 'Single' ? 'underlined' : 'transparentBorder'}
            theme="navy300"
            styleProp={[
              size === 'Single' && styles.shopNowButtonSingle,
              size === 'Double' && styles.shopNowButtonDouble,
            ]}
            custom={customCallToActionRenderer}
          >
            Shop Now →
          </CallToAction>
        </ProductLink>
      ) : (
        <ProductLink
          product={cardProduct}
          variant={currentVariant}
          urlParams={{
            [attribute]: currentVariant.swatch.slug,
          }}
          styleProp={[
            size === 'Single' && styles.shopNowButtonSingle,
            size === 'Double' && styles.shopNowButtonDouble,
            styles.notifyMeLink
          ]}
        >
          Notify Me →
        </ProductLink>
      )}
    </div>
  )
}

export default ProductCard
