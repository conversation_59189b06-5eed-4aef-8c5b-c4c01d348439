import ProductCardGalleryItem from './ProductCardGalleryItem'
import { getHoverImage } from './utils'
import sharedStyles from './styles'
import { useProductCardGallery } from './hooks'

import ProductLink from '../ProductLink'

import LeftArrow from '@components/Generic/Icon/lib/LeftArrow'
import RightArrow from '@components/Generic/Icon/lib/RightArrow'
import { ContentfulProductCard, ContentfulProduct } from '@lib/contentful/types/products'
import useIsMobile from '@/hooks/isMobile'
import useDotButton from '@/hooks/useDotButton'
import { colors } from '@/app/themeTokens.stylex'
import { DotButtons } from '@/components/Generic/Slider/SliderButtons'

import stylex from '@stylexjs/stylex'
import Image from 'next/image'

const styles = stylex.create({
  embla: {
    overflow: 'hidden',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0,
    willChange: 'opacity',
  },
  emblaVisible: {
    transition: 'opacity .25s linear',
    opacity: 1,
  },
  emblaContainer: {
    display: 'flex',
  },
  emblaSlide: {
    flex: '0 0 100%',
    minWidth: 0,
  },
  emblaButton: {
    position: 'absolute',
    top: '50%',
    transform: 'translateY(-50%)',
    zIndex: 1,
    background: 'none',
    borderWidth: 0,
    cursor: 'pointer',
    padding: 0,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: 32,
    height: 32,
    opacity: 0.8,
    backgroundColor: 'white',
    borderRadius: '50%',
    color: 'black',
  },
  emblaButtonPrev: {
    left: 10,
  },
  emblaButtonNext: {
    right: 10,
  },
  dotsNavigation: {
    display: {
      default: 'flex',
      '@media (min-width: 1024px)': 'none',
    },
    justifyContent: 'center',
    gap: '16px',
    marginTop: -20,
  },
  dotsNavigationButton: {
    background: colors.gray300,
    zIndex: '1'
  },
  dotsNavigationButtonSelected: {
    background: colors.navy,
  },
})

const DesktopArrows = ({ gallery }: { gallery: ReturnType<typeof useProductCardGallery> }) => {
  const { isMobile } = useIsMobile()

  if (isMobile) {
    return null
  }

  return (
    <>
      <button
        {...stylex.props(styles.emblaButton, styles.emblaButtonPrev)}
        type="button"
        onClick={gallery.onPrevButtonClick}
      >
        <LeftArrow dimensions="14" />
      </button>
      <button
        {...stylex.props(styles.emblaButton, styles.emblaButtonNext)}
        type="button"
        onClick={gallery.onNextButtonClick}
      >
        <RightArrow dimensions="14" />
      </button>
    </>
  )
}

const ProductCardGallery = ({
  card,
  product,
  gallery,
  isHovered = false,
}: {
  card?: ContentfulProductCard;
  product: ContentfulProduct;
  gallery: ReturnType<typeof useProductCardGallery>;
  isHovered: boolean;
}) => {
  const { emblaApi } = gallery
  const { isMobile } = useIsMobile()
  const hoverImage = getHoverImage(card || product)
  const galleryImages = card?.gallery

  const { selectedIndex, scrollSnaps } = useDotButton(emblaApi)

  if (!galleryImages || galleryImages.items.length === 0) {
    return null
  }

  const handleScroll = (index: number) => () => {
    if (emblaApi) {
      emblaApi.scrollTo(index)
    }
  }

  return (
    <div
      {...stylex.props(styles.embla, isHovered && styles.emblaVisible)}
      ref={gallery.emblaRef}
    >
      <div {...stylex.props(styles.emblaContainer)}>
        {hoverImage && !isMobile && (
          <div {...stylex.props(styles.emblaSlide)}>
            <ProductLink
              product={product as any}
              styleProp={sharedStyles.primaryImageLink}
            >
              <Image
                src={hoverImage.url}
                alt={hoverImage.url}
                width={363}
                height={482}
                {...stylex.props(
                  sharedStyles.cardImage,
                  sharedStyles.hoverImage,
                  card?.size === 'Double' && sharedStyles.cardImageDouble
                )}
              />
            </ProductLink>
          </div>
        )}
        {galleryImages.items.map((galleryItem) => (
          <ProductCardGalleryItem
            key={galleryItem.url}
            galleryItem={galleryItem}
            cardProduct={product}
            size={card?.size}
          />
        ))}
      </div>
      <DesktopArrows gallery={gallery} />
      <DotButtons
        scrollSnaps={scrollSnaps}
        currentSlide={selectedIndex}
        handleScroll={handleScroll}
        styleProp={styles.dotsNavigation}
        dotsStyleProp={styles.dotsNavigationButton}
        dotsSelectedStyleProp={styles.dotsNavigationButtonSelected}
      />
    </div>
  )
}

export default ProductCardGallery
