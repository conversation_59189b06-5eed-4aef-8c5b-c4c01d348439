import {
  getCardProduct, getProductCardDescription, ProductCardData,
  sortAvailableSwatches
} from './utils'

import usePrevNextButtons from '@/hooks/prevNextButtons'
import { isVariantLimited } from '@/hooks/usePurchaseLimit'
import {
  ContentfulProductCard,
  ContentfulProductSwatch,
} from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

import { EmblaOptionsType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import { useState } from 'react'

export function useProductCardHover(gallery: ReturnType<typeof useProductCardGallery>) {
  const [isHovered, setIsHovered] = useState(false)

  const enableHover = () => {
    setIsHovered(true)
  }

  const disableHover = () => {
    setIsHovered(false)
    gallery.emblaApi?.scrollTo(0)
  }

  return {
    isHovered,
    enableHover,
    disableHover,
  }
}

function getDefaultVariant(product: ProductCardData, defaultSwatch?: ContentfulProductSwatch | null) {
  let variant: ContentfulProductVariant | undefined

  if (product.__typename === 'Product') {
    variant = defaultSwatch
      ? product.variants.items.find((item) => item.swatch.slug === defaultSwatch.slug)
      : product.variants.items[0]
  }

  if (product.__typename === 'ProductCard') {
    variant = defaultSwatch
      ? product.product.variants.items.find((item) => item.swatch.slug === defaultSwatch.slug)
      : product.product.variants.items[0]
  }

  return variant
}

export function useCurrentVariant(
  product: ProductCardData,
  defaultSwatch?: ContentfulProductSwatch | null
) {
  let card: ContentfulProductCard | undefined
  let size: ContentfulProductCard['size'] = 'Single'
  let cardProduct: ProductCardData | null = null
  let gallery: ContentfulProductCard['gallery'] | { items: [] } = { items: [] }

  const variant = getDefaultVariant(product, defaultSwatch)

  if (product.__typename === 'Product') {
    cardProduct = product
  }

  if (product.__typename === 'ProductCard') {
    card = product
    cardProduct = product.product
    size = product.size
    gallery = product.gallery || { items: [] }
  }

  const [currentVariant, setCurrentVariant] = useState(variant)

  if (!currentVariant) {
    return null
  }

  const { primaryImage } = currentVariant

  return {
    card,
    cardProduct,
    currentVariant,
    setCurrentVariant,
    size,
    gallery,
    primaryImage,
    description: getProductCardDescription(card || product),
  }
}

export function useProductCardGallery(options?: EmblaOptionsType) {
  const [emblaRef, emblaApi] = useEmblaCarousel(options)
  const { onPrevButtonClick, onNextButtonClick } = usePrevNextButtons(emblaApi)

  return {
    emblaRef,
    emblaApi,
    onPrevButtonClick,
    onNextButtonClick,
  }
}

// eslint-disable-next-line complexity
function getDefaultProductCardSwatch(product: ProductCardData): ContentfulProductSwatch | null {
  const cardProduct = getCardProduct(product)
  const emptyProduct = Object.keys(product).length === 0

  if (emptyProduct) {
    return null
  }

  if (product.__typename === 'Product' || product.variantCards.items.length === 0) {
    const cardDefaultSwatch = cardProduct.productMetadata?.items.find(
      (item) => item.type === 'Card Default Swatch'
    )?.references?.items?.[0]

    const defaultSwatchVariant = cardProduct.variants.items.find(
      (item) => item.swatch.slug === cardDefaultSwatch?.slug
    )

    if (defaultSwatchVariant && defaultSwatchVariant.availableForSale === false) {
      const swatches = cardProduct.variants.items.filter(
        (item) => item.availableForSale === true
      ) as unknown as ContentfulProductVariant[]

      if (swatches.length === 0) {
        return cardProduct.variants.items[0].swatch
      }

      const sortedSwatches = sortAvailableSwatches(swatches)

      return sortedSwatches[0].swatch
    }

    return cardDefaultSwatch || null
  }

  const firstVariantCard = product.variantCards.items[0]

  if (firstVariantCard.__typename === 'VariantCard') {
    return firstVariantCard.swatch
  }

  return firstVariantCard
}

export function useProductCard(product: ProductCardData, options?: EmblaOptionsType) {
  const defaultSwatch = getDefaultProductCardSwatch(product)
  const variant = useCurrentVariant(product, defaultSwatch)
  const productGallery = useProductCardGallery(options)
  const hover = useProductCardHover(productGallery)
  const isLimited = isVariantLimited(variant?.currentVariant?.variantId)

  if (!variant) {
    return null
  }

  return {
    variant,
    productGallery,
    hover,
    isLimited,
  }
}
