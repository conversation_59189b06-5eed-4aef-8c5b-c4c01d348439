import { ContentfulProduct } from '@/lib/contentful/types/products'
import Rating from '@components/Generic/Rating'

export type ProductCardReviewsProps = {
  reviews?: ContentfulProduct['reviews']
};

const ProductCardReviews = ({ reviews }: ProductCardReviewsProps) => {
  if (!reviews) return null

  const rating = reviews ? reviews.rating : 0
  const reviewCount = reviews ? reviews.count : 0

  return <Rating rating={rating} reviewCount={reviewCount} withoutReviews />
}

export default ProductCardReviews
