import { useProductCardGallery } from './hooks'
import { getSwatches, ProductCardData } from './utils'

import { ContentfulProductVariant } from '@/lib/contentful/types/variants'
import useIsMobile from '@/hooks/isMobile'
import {
  spacing,
  colors,
  globalTokens as $
} from '@/app/themeTokens.stylex'
import { slugify } from '@/utils/regex'

import * as stylex from '@stylexjs/stylex'
import {
  Dispatch, SetStateAction, useEffect, useState
} from 'react'
import Image from 'next/image'

const styles = stylex.create({
  swatchesSliderContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  swatchesSlider: {
    overflow: 'hidden',
  },
  swatchesContainer: {
    display: 'flex',
    justifyContent: 'start',
    flexWrap: 'nowrap',
    gap: '5px',
  },
  swatchesSliderButton: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: 0,
    height: 32,
    borderWidth: 0,
    cursor: 'pointer',
    flexShrink: 0,
    opacity: 0,
    transition: 'width 0.1s ease-in-out',
    willChange: 'width',
  },
  swatchesSliderButtonVisible: {
    width: {
      default: 34,
      '@media (min-width: 394px)': 32
    },
    opacity: 1,
    // eslint-disable-next-line max-len
    backgroundImage: "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' viewBox='0 0 18 18' fill='none'%3E%3Cpath d='M5.28341 15.7695L12.2578 9.02567L5.28341 2.38492' stroke='%239F9F9F' stroke-width='1.48718' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E\")",
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
  },
  swatchesSliderButtonPrevious: {
    transform: 'rotate(180deg)',
  },
  swatch: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '38px',
    height: '38px',
    flexShrink: 0,
  },
  swatchImage: {
    borderRadius: '50%',
  },
  swatchTextSpacing: {
    gap: spacing.xs,
  },
  swatchText: {
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.gray300,
    paddingBlock: spacing.xs,
    paddingInline: spacing.sm,
    borderRadius: $.borderRadiusSmall,
    color: colors.navy,
  },
  giftSwatchText: { color: colors.navy },
  swatchImageSquare: {
    borderRadius: '4px',
  },
  swatchActive: {
    outline: '2px solid #1f3438',
    outlineOffset: '3px',
  },
  crossOutContainer: {
    position: 'relative',
  },
  crossLine: {
    position: 'absolute',
    top: '45%',
    height: '1.4px',
    backgroundColor: 'white',
  },
  crossLineCircle: {
    left: '8%',
    width: '105%',
    transform: 'translate(-10%, -50%) rotate(40deg)',
  },
  crossLineSquare: {
    left: '0%',
    width: '125%',
    transform: 'translate(-10%, -50%) rotate(45deg)',
  },
  oosStyle: {
    filter: 'brightness(60%)',
  },
  swatchOutline: {
    width: '25px',
    height: '25px',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    outline: `2px solid ${colors.navy}`,
    outlineOffset: '3px',
    zIndex: 2,
    opacity: 1,
    pointerEvents: 'none',
  },
  swatchOutlineCircle: {
    borderRadius: '50%',
  },
  swatchOutlineSquare: {
    borderRadius: '4px',
  },
  oosStyleTextSwatch: {
    backgroundColor: colors.gray300,
    color: colors.gray,
    borderColor: colors.gray200
  },
  crossLineSwatchText: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundImage: 'linear-gradient(to bottom left, transparent 49%, white 50%, transparent 51%)',
  },
})

const MAX_SWATCHES_MOBILE = 3
const MAX_SWATCHES_DESKTOP = 6

const ProductCardSwatches = ({
  product,
  currentVariant,
  setCurrentVariant,
  oosSwatches,
  isHover = () => {},
}: {
  product: ProductCardData;
  currentVariant: ContentfulProductVariant;
  setCurrentVariant: Dispatch<
  SetStateAction<ContentfulProductVariant | undefined>
  >;
  oosSwatches?: string[]
  isHover?: (isHovered: boolean) => void;
}) => {
  const { isMobile } = useIsMobile()
  const MAX_SWATCHES = isMobile ? MAX_SWATCHES_MOBILE : MAX_SWATCHES_DESKTOP
  const {
    emblaRef: emblaSwatchesRef,
    emblaApi: emblaSwatchesApi,
    onPrevButtonClick: onSwatchesPrevButtonClick,
    onNextButtonClick: onSwatchesNextButtonClick,
  } = useProductCardGallery({
    slidesToScroll: MAX_SWATCHES,
    duration: 10,
  })

  const [showNextButton, setShowNextButton] = useState(true)
  const [showPreviousButton, setShowPreviousButton] = useState(false)

  /* eslint-disable no-magic-numbers */
  const HUNDRED = 100
  const MIN_SCROLL_PROGRESS = isMobile ? 0.13 : 0.1
  const MAX_SCROLL_PROGRESS = isMobile ? 0.88 : 0.9

  useEffect(() => {
    if (emblaSwatchesApi) {
      emblaSwatchesApi.on('slidesInView', () => {
        const scrollProgress =
          Math.round(emblaSwatchesApi.scrollProgress() * HUNDRED) / HUNDRED

        if (scrollProgress === 0) {
          setShowNextButton(Object.is(emblaSwatchesApi?.scrollProgress(), -0))
          return
        }

        if (scrollProgress <= MIN_SCROLL_PROGRESS) {
          setShowNextButton(true)
          setShowPreviousButton(false)
        }

        if (scrollProgress >= MAX_SCROLL_PROGRESS) {
          setShowNextButton(false)
          setShowPreviousButton(true)
        }
      })
    }
  }, [emblaSwatchesApi])

  const swatches = getSwatches(product)
  const showSwatchesGallery = swatches && swatches.length >= MAX_SWATCHES

  const mouseEnter = () => {
    isHover(true)
  }

  const mouseLeave = () => {
    isHover(false)
  }

  return (
    <div
      {...stylex.props(styles.swatchesSliderContainer)}
      onTouchStart={mouseEnter}
      onTouchEnd={mouseLeave}
      onMouseEnter={mouseEnter}
      onMouseLeave={mouseLeave}
    >
      <button
        type="button"
        onClick={onSwatchesPrevButtonClick}
        {...stylex.props(
          styles.swatchesSliderButton,
          styles.swatchesSliderButtonPrevious,
          showPreviousButton && styles.swatchesSliderButtonVisible
        )}
      />
      <div
        ref={emblaSwatchesRef}
        {...stylex.props(styles.swatchesSlider)}
      >
        <div {...stylex.props(styles.swatchesContainer)}>
          {/* eslint-disable-next-line complexity */}
          {swatches.map((swatchVariant) => {
            const slugifiedSwatchSlug = slugify(swatchVariant.swatch.slug)
            const isOos = oosSwatches?.some((s) => slugify(s) === slugifiedSwatchSlug)

            return (
              <button
                key={swatchVariant.variantId}
                type="button"
                onMouseEnter={() => {
                  setCurrentVariant(swatchVariant)
                }}
                {...stylex.props(
                  swatchVariant.swatch.icon ? styles.swatch : styles.swatchTextSpacing,
                )}
              >
                {swatchVariant.swatch.icon ? (
                  <div {...stylex.props(styles.crossOutContainer)}>
                    <Image
                      src={swatchVariant.swatch.icon.url}
                      alt={swatchVariant.swatch.presentation}
                      width={25}
                      height={25}
                      {...stylex.props(
                        swatchVariant.swatch.style === 'Circle' && styles.swatchImage,
                        swatchVariant.swatch.style === 'Square' && styles.swatchImageSquare,
                        isOos && styles.oosStyle,
                      )}
                    />
                    {swatchVariant.variantId === currentVariant.variantId && (
                      <div {...stylex.props(
                        styles.swatchOutline,
                        swatchVariant.swatch.style === 'Circle' && styles.swatchOutlineCircle,
                        swatchVariant.swatch.style === 'Square' && styles.swatchOutlineSquare
                      )}
                      />
                    )}
                    {isOos && (
                      <div {...stylex.props(
                        styles.crossLine,
                        swatchVariant.swatch.style === 'Circle' && styles.crossLineCircle,
                        swatchVariant.swatch.style === 'Square' && styles.crossLineSquare
                      )}
                      />
                    )}
                  </div>
                ) : (
                  <div {...stylex.props(styles.crossOutContainer)}>
                    <div
                      {...stylex.props(
                        styles.swatchText,
                        styles.giftSwatchText,
                        isOos && styles.oosStyleTextSwatch,
                      )}
                    >
                      {swatchVariant.swatch.presentation}
                    </div>
                    {isOos && <div {...stylex.props(styles.crossLineSwatchText)} />}
                  </div>
                )}
              </button>
            )
          })}
        </div>
      </div>
      <button
        type="button"
        onClick={onSwatchesNextButtonClick}
        {...stylex.props(
          styles.swatchesSliderButton,
          showNextButton &&
            showSwatchesGallery &&
            styles.swatchesSliderButtonVisible
        )}
      />
    </div>
  )
}

export default ProductCardSwatches
