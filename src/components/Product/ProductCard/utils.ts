import { ProductCardFieldsProps } from '../types'

import {
  ContentfulProduct,
  ContentfulProductCard,
  ContentfulProductSwatch,
} from '@/lib/contentful/types/products'
import { ContentfulProductVariant, ContentfulVariantCard } from '@/lib/contentful/types/variants'

import { Document } from '@contentful/rich-text-types'

export type ProductCardData = ContentfulProduct | ContentfulProductCard

// eslint-disable-next-line complexity -- This function is complex by nature
export function getProductCardDescription(card?: ProductCardData) {
  if (!card) {
    return null
  }

  let cardProduct: ProductCardData | null = null
  let description: string | { json: Document } | null = null

  if (card.__typename === 'ProductCard') {
    cardProduct = card.product
    description = card.description
  }

  if (card.__typename === 'Product') {
    cardProduct = card
  }

  if (!cardProduct) {
    return null
  }

  if (description) {
    return description
  }

  // Extract product short description from metadata
  const shortDescription = cardProduct.productMetadata?.items.find(
    (item) => item.type === 'Card Short Description'
  )?.description

  // If no description found until this point, use short description
  if (shortDescription) {
    return shortDescription
  }

  // Extract product byline from detail blocks
  const detailBlocks = cardProduct.productDetailBlocksCollection?.items || []
  const byline = detailBlocks.find((block) => block.type === 'ProductByline')

  // Use byline as last resort fallback for description
  if (byline) {
    return byline.description
  }

  return null
}

export function getCardProduct(product: ProductCardData) {
  return product.__typename === 'Product' ? product : product.product
}

export function getHoverImage(card: ProductCardData) {
  if (card.__typename === 'ProductCard' && card.hoverImage) {
    return card.hoverImage
  }

  const proudct = getCardProduct(card)
  const { productMetadata } = proudct

  return productMetadata?.items.find(
    (item) => item.type === 'Card Hover Image'
  )?.media?.items[0]
}

export function getCurrentVariantCard(variant: ContentfulProductVariant, card?: ContentfulProductCard) {
  const variantCards = card?.variantCards?.items
  const swatch = variant?.swatch?.slug

  return variantCards?.find((item: any) => item.swatch?.slug === swatch)
}

type VariantOrSwatch = ContentfulVariantCard | ContentfulProductSwatch

function filterSwatchesByVariants(
  swatches: ContentfulProductVariant[],
  variantCards: VariantOrSwatch[]
) {
  return swatches.filter((swatch) => variantCards.some((variantCard) => {
    if (variantCard.__typename === 'VariantCard') {
      return variantCard.swatch.slug === swatch.swatch.slug
    }

    if (variantCard.__typename === 'Swatch') {
      return variantCard.slug === swatch.swatch.slug
    }

    return false
  }))
}

function sortSwatches(
  swatches: ContentfulProductVariant[],
  variantCards: VariantOrSwatch[]
) {
  return swatches.toSorted((a, b) => {
    const aIndex = variantCards.findIndex((variantCard) => {
      if (variantCard.__typename === 'VariantCard') {
        return variantCard.swatch.slug === a.swatch.slug
      }

      if (variantCard.__typename === 'Swatch') {
        return variantCard.slug === a.swatch.slug
      }

      return false
    })
    const bIndex = variantCards.findIndex((variantCard) => {
      if (variantCard.__typename === 'VariantCard') {
        return variantCard.swatch.slug === b.swatch.slug
      }

      if (variantCard.__typename === 'Swatch') {
        return variantCard.slug === b.swatch.slug
      }

      return false
    })

    return aIndex - bIndex
  })
}

export const isAvailable = (swatch: ContentfulProductVariant) => swatch.availableForSale === true &&
    swatch.variantAvailability !== 'Notify Me' &&
    swatch.variantAvailability !== 'Sold Out'

export function sortAvailableSwatches(swatches: ContentfulProductVariant[]) {
  return swatches.toSorted((swatch, nextSwatch) => (isAvailable(swatch) > isAvailable(nextSwatch) ? -1 : 1))
}

function changeDefaultSwatch(
  swatches: ContentfulProductVariant[],
  cardProduct: ContentfulProduct
) {
  const defaultMetadataSwatch = cardProduct.productMetadata?.items.find(
    (item) => item.type === 'Card Default Swatch'
  )?.references?.items?.[0]

  if (!defaultMetadataSwatch) {
    return swatches
  }

  let newSwatches = [...swatches]

  const defaultSwatch = newSwatches.find(
    (swatch) => swatch.swatch.slug === defaultMetadataSwatch.slug
  )

  if (defaultSwatch && isAvailable(defaultSwatch)) {
    // Remove default swatch from the swatches array
    newSwatches = swatches.filter(
      (swatch) => swatch.swatch.slug !== defaultMetadataSwatch.slug
    )
    // Add default swatch to the beginning of the swatches array
    newSwatches.unshift(defaultSwatch)
  }

  return newSwatches
}

export function getSwatches(product: ProductCardData) {
  const cardProduct = getCardProduct(product)
  let variantCards: VariantOrSwatch[] | [] = []

  if (product.__typename === 'ProductCard') {
    variantCards = product.variantCards?.items
  }

  // By default create swatches from product variants
  let swatches = cardProduct.variants.items

  if (product.__typename === 'ProductCard' && variantCards.length > 0) {
    swatches = filterSwatchesByVariants(
      sortSwatches(sortAvailableSwatches(swatches), variantCards),
      variantCards
    )
  }

  if (product.__typename === 'Product' || variantCards.length === 0) {
    swatches = changeDefaultSwatch(
      sortAvailableSwatches(swatches),
      cardProduct
    )
  }

  return swatches
}

// Returns an array of Variant slugs that have their VariantAvailability property set to "Notify Me" or "Sold Out"
export function getOOSContentfulVariantAvailabilitySlugs(product: ProductCardData | ProductCardFieldsProps) {
  const slugs = product.variants.items
    .filter(
      (item) => item.variantAvailability === 'Notify Me' ||
        item.variantAvailability === 'Sold Out'
    )
    .map((item) => item.swatch.slug) || []

  return slugs
}
