import Typography from '@components/Typography'
import Spacer from '@components/layout/Spacer'
import { defaultTheme as $T, spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  dividerSpacing: {
    marginBlock: spacing.md,
    borderTopColor: $T.indicatorInactive,
  },
})

type ProductGridTitleProps = {
  title: string
}

const ProductGridTitle = ({ title }: ProductGridTitleProps) => (
  <>
    <Typography as="h2" typographyTheme="h4Secondary">
      {title}
    </Typography>
    <hr {...stylex.props(styles.dividerSpacing)} />
    <Spacer size="md" />
  </>
)

export default ProductGridTitle
