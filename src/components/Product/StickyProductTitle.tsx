'use client'

import ProductLink from './ProductLink'
import SwatchIcon from './ProductSwatches/SwatchIcon'
import SwatchText from './ProductSwatches/SwatchText'
import { ProductCardFieldsProps, VariantProps } from './types'
import ProductPrice from './ProductPrice'
import AddToCart from './AddToCart'
import { getActiveSwatch } from './ProductSwatches/utils'
import { getOOSSwatches } from './utils'
import { getOOSContentfulVariantAvailabilitySlugs } from './ProductCard/utils'
import AddToCartOverride from './AddToCartOverride'

import Skeleton from '../Generic/Skeleton'
import Typography from '../Typography'
import Container from '../layout/Container'
import Rating, { MIN_RATING } from '../Generic/Rating'
import useStickyNav from '../layout/Header/hooks'

import { ContentfulProductSwatches, ContentfulProductSwatch } from '@/lib/contentful/types/products'
import { globalTokens as $, colors, spacing } from '@/app/themeTokens.stylex'
import { sanitize } from '@/utils/regex'

import * as stylex from '@stylexjs/stylex'
import React, { useEffect, useState } from 'react'

const styles = stylex.create({
  sticky: {
    position: 'fixed',
    top: 0,
    left: 0,
    zIndex: 10,
    width: '100%',
    transform: 'translateY(-100%)',
    transition: $.transitionSmooth,
    paddingInline: spacing.lg,
    paddingBlock: spacing.sm,
    boxShadow: '0px 3.769px 10.366px 0px rgba(0, 0, 0, 0.08)',
    backgroundColor: colors.white,
    display: {
      default: 'none',
      '@media (min-width: 992px)': 'flex',
    },
  },
  visible: {
    transform: 'translateY(0)',
  },
  swatch: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '34px',
    height: '34px',
    flexShrink: 0,
  },
  swatchText: {
    display: 'block',
    paddingInline: spacing.xs,
  },
  activeSwatchLabel: {
    display: {
      default: 'none',
      '@media (min-width: 1150px)': 'flex',
    },
  }
})

type StickyProductTitleProps = {
  swatches: ContentfulProductSwatches[] | undefined;
  product: ProductCardFieldsProps;
  variant: VariantProps;
  selectedSwatch?: ContentfulProductSwatch;
  reviews?: {
    rating: number;
    count: number;
  };
};

// eslint-disable-next-line complexity
const StickyProductTitle = ({
  swatches,
  product,
  variant,
  selectedSwatch,
  reviews,
}: StickyProductTitleProps) => {
  const { slug } = product
  const {
    price,
    compareAtPrice,
    onSale
  } = variant

  const flatSwatches = swatches
    ?.flatMap((collection) => collection.swatchCollections?.items.flatMap(
      (swatch) => swatch.swatches?.items || []
    ))
    .filter((swatch) => typeof swatch !== 'undefined') || []
  const flattenedVariants = product.variants.items.flatMap((item) => item.swatch.slug)
  const matchingVariants = flattenedVariants.filter((item) => flatSwatches.find((swatch) => swatch?.slug === item))
  const availableVariants = flatSwatches
    .filter((swatch) => swatch && matchingVariants.includes(swatch.slug))
    .filter(Boolean) || []

  const {
    isSticky: isNavHidden,
    isProductTitleSticky,
    mainNavHeight
  } = useStickyNav()

  const [oosSwatches, setOOSSwatches] = useState<string[]>([])

  useEffect(() => {
    async function fetchData() {
      // Checks Shopify stock
      const response = await getOOSSwatches(product.variants.items)

      // Checks for Contentful Variant level updates
      const contentfulVariantAvailabilityToggle = getOOSContentfulVariantAvailabilitySlugs(product)

      const mergedOOSSwatches = Array.from(new Set([...response, ...contentfulVariantAvailabilityToggle]))

      setOOSSwatches(mergedOOSSwatches)
    }
    fetchData()
  }, [])

  if (!availableVariants) {
    return null
  }

  const activeSwatch = getActiveSwatch(selectedSwatch || availableVariants)
  const attribute = swatches?.[0]?.slug || 'color'

  const atcOverride = (product as any).productDetailBlocksCollection?.items.find(
    (item: any) => item.type === 'Add to Cart Button Override'
  )

  return (
    <Container
      flexCentered
      styleProp={[styles.sticky, isProductTitleSticky && styles.visible]}
      style={{
        transform: !isNavHidden && isProductTitleSticky && `translateY(${mainNavHeight}px)`,
      }}
    >
      <Container size="6" flex flexRow alignCentered spaceBetween>
        <Container flex flexRow alignCentered gap="4">
          <div>
            <Typography
              as="h3"
              typographyTheme="h6Secondary"
              typographyThemeMobile="h6Secondary"
            >
              {product.title}
            </Typography>
            {reviews && reviews.rating >= MIN_RATING && (
              <Rating
                rating={reviews.rating}
                reviewCount={reviews.count}
                textContentOn="right"
                iconColor="marigold"
                textSize="xxs"
                isAggregated
              />
            )}
          </div>
          <Container flex flexRow gap="1">
            {availableVariants.map(
              (swatch, i) => swatch.slug && (
              <React.Fragment key={`${sanitize(swatch.slug)}-${String(i)}`}>
                <ProductLink
                  key={swatch.slug}
                  product={product as any}
                  variant={variant as any}
                  urlParams={{
                    [attribute]: swatch.slug,
                  }}
                  styleProp={!!swatch.icon && styles.swatch}
                  scroll={false}
                >
                  <SwatchIcon
                    swatch={swatch}
                    attribute={attribute}
                    selectedSwatch={selectedSwatch}
                    size={28}
                    oosSwatches={oosSwatches}
                  />
                  <SwatchText
                    swatch={swatch}
                    attribute={attribute}
                    selectedSwatch={selectedSwatch?.slug}
                    styleProp={styles.swatchText}
                    oosSwatches={oosSwatches}
                  />
                </ProductLink>
              </React.Fragment>
              )
            )}
          </Container>
          {activeSwatch && (
            <Container
              flex
              flexRow
              gap="1"
              styleProp={styles.activeSwatchLabel}
            >
              <Typography as="strong">
                {activeSwatch.swatchTypeLabel}:
              </Typography>
              {activeSwatch.presentation}
            </Container>
          )}
        </Container>
        <Container flex flexRow alignCentered gap="2">
          <Skeleton
            isLoaded={price !== undefined}
            animation="shimmer"
            width={100}
            height={18}
          >
            <Container flex flexRow alignCentered gap="1">
              <ProductPrice
                onSale={onSale}
                slug={slug}
                price={price!}
                compareAtPrice={compareAtPrice!}
              />
            </Container>
          </Skeleton>
          <Container>
            {atcOverride ? (
              <AddToCartOverride
                variant={variant as any}
                cta={{
                  ...atcOverride.contentCollection?.items[0],
                  text: 'Shop Now',
                }}
              />
            ) : (
              <AddToCart variant={variant as any} product={product as any} />
            )}
          </Container>
        </Container>
      </Container>
    </Container>
  )
}

export default StickyProductTitle
