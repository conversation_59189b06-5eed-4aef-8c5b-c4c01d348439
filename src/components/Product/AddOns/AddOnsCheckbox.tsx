import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

const DESKTOP = '@media screen and (min-width: 600px)'

const styles = stylex.create({
  wrapper: {
    position: 'relative',
    width: {
      default: 20,
      [DESKTOP]: 25
    },
    height: {
      default: 20,
      [DESKTOP]: 25
    },
    alignSelf: 'center',
    pointerEvents: 'none',
  },
  checkbox: {
    appearance: 'none',
  },
})

export type AddOnsCheckboxProps = {
  label: string;
  selected: boolean;
  handleSelect: () => void;
}

// eslint-disable-next-line max-len
const CHECKED_SVG = "data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='24' height='24' rx='2' fill='%231F3438'/%3E%3Cpath d='M5 11.7727L9.30769 16.5455L19 7' stroke='white' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E"
// eslint-disable-next-line max-len
const UNCHECKED_SVG = "data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='0.5' y='0.5' width='23' height='23' rx='1.5' stroke='%23C7C7C7'/%3E%3C/svg%3E%0A"

const AddOnsCheckbox = ({
  label,
  selected,
  handleSelect
}: AddOnsCheckboxProps) => (
  <div
    onClick={handleSelect}
    {...stylex.props(styles.wrapper)}
  >
    <input
      title={label}
      aria-label={label}
      type="checkbox"
      {...stylex.props(styles.checkbox)}
      checked={selected}
      onChange={handleSelect}
    />
    <Image
      fill
      alt="Checkmark"
      src={selected ? CHECKED_SVG : UNCHECKED_SVG}
    />
  </div>
)

export default AddOnsCheckbox
