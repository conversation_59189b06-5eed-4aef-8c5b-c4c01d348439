import AddOnsCheckbox from './AddOnsCheckbox'

import { colors, globalTokens } from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import { useAppDispatch } from '@/redux/hooks'
import { elementInteraction } from '@/redux/features/events/eventsSlice'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

type AddOnCardProps = {
  addOn: {
    id: string;
    name: string;
    price: number;
    description?: string;
    image?: string;
  };
  onSelect: (id: string) => void;
  selected: boolean;
  data?: any;
}

const styles = stylex.create({
  main: {
    borderColor: colors.gray300,
    // NOTE: there is no borderWidth: 1 in the themeTokens.stylex
    borderWidth: 1,
    borderRadius: globalTokens.borderRadiusSmall,
    borderStyle: 'solid',
    paddingInlineStart: {
      default: 8,
      '@media (min-width: 768px)': 20,
    },
    paddingInlineEnd: {
      default: 8,
      '@media (min-width: 768px)': 16,
    },
    paddingBlock: 8,
    display: 'flex',
    flexDirection: 'row',
    gap: {
      default: 8,
      '@media (min-width: 768px)': 16,
    },
    cursor: 'pointer',
  },
  selected: {
    borderColor: colors.navy,
    backgroundColor: colors.gray200,
  },

  image: {
    alignSelf: 'center',
    objectFit: 'contain',
  },
  details: {
    display: 'flex',
    flex: '1',
    flexDirection: 'column',
    justifyContent: 'center',
    gap: 4,
  },
  headerRow: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 4,
  },
  descriptionRow: {
    paddingInlineEnd: 40,
  },
  priceContainer: {
    width: 40,
    gap: 4,
    textAlign: 'right',
  }
})

const AddOnCard = ({
  addOn,
  onSelect,
  selected,
  data
}: AddOnCardProps) => {
  const dispatch = useAppDispatch()

  const handleSelect = () => {
    onSelect(addOn.id)

    dispatch(elementInteraction({
      properties: {
        category: 'PDP Upsell',
        label: 'PDP Upsell',
        interaction_type: 'click',
        interaction_value: !selected ? 'open' : 'close',
        placement: 'PDP',
        product: {
          productName: data?.product?.title,
          addOnProductName: addOn.name,
          addOnProductPrice: addOn.price,
        }
      }
    }))
  }

  return (
    <div onClick={handleSelect} {...stylex.props(styles.main, selected && styles.selected)}>
      <AddOnsCheckbox
        label={`Select Add On: ${addOn.name}`}
        handleSelect={handleSelect}
        selected={selected}
      />
      {addOn.image && (
        <Image
          src={addOn.image}
          alt={addOn.name}
          width={57}
          height={43}
          {...stylex.props(styles.image)}
        />
      )}
      <div {...stylex.props(styles.details)}>
        <div {...stylex.props(styles.headerRow)}>
          <Typography
            as="h6"
            typographyTheme="bodyLarge"
            fontBold
          >
            {addOn.name}
          </Typography>
          <div {...stylex.props(styles.priceContainer)}>
            <Typography
              as="span"
              typographyTheme="bodySmall"
              fontBold
              lineHeight="xl"
            >
              {`$${addOn.price}`}
            </Typography>
          </div>
        </div>

        <div {...stylex.props(styles.descriptionRow)}>
          <Typography
            as="p"
            typographyTheme="bodySmall"
          >
            {addOn.description}
          </Typography>
        </div>
      </div>
    </div>
  )
}

export default AddOnCard
