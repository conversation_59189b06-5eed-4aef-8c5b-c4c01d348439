'use client'

import Container from '@/components/layout/Container'
import { ContentfulProduct } from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'
import { useAppDispatch, useAppSelector } from '@/redux/hooks'
import AddOnCard from '@components/Product/AddOns/AddOnCard'
import Typography from '@components/Typography'
import Wrapper from '@components/layout/Wrapper'
import { toggleAddOn, clearAddOns } from '@redux/features/product/productSlice'

import * as stylex from '@stylexjs/stylex'
import { useEffect } from 'react'

export type AddOnsWrapperProps = {
  addOnsTitle: string;
  addOns: {
    id: string;
    name: string;
    price: number;
    description?: string;
    image?: string;
    variant?: ContentfulProductVariant;
    product?: ContentfulProduct;
  }[];
  data?: any;
};

const styles = stylex.create({
  wrapper: {
    marginBlock: 24,
  },
})

const AddOns = ({
  addOnsTitle,
  addOns = [],
  data
}: AddOnsWrapperProps) => {
  const dispatch = useAppDispatch()
  const selectedAddOns = useAppSelector((state) => state.product.addOns)

  useEffect(() => () => {
    dispatch(clearAddOns())
  }, [dispatch])

  const onSelectAddOn = (id: string) => {
    const item = addOns.find((addOn) => addOn.id === id)

    if (item) {
      dispatch(toggleAddOn(item))
    }
  }

  return (
    <Wrapper styleProp={styles.wrapper}>
      <Typography
        as="h6"
        typographyTheme="bodyLarge"
        marginBottom="sm"
        fontBold
      >
        {addOnsTitle}
      </Typography>

      <Container flex gap="2">
        {addOns.map((addOn) => (
          <AddOnCard
            key={`add-on-${addOn.id}`}
            addOn={addOn}
            onSelect={onSelectAddOn}
            selected={!!selectedAddOns.find(({ id }) => id === addOn.id)}
            data={data}
          />
        ))}
      </Container>
    </Wrapper>
  )
}

export default AddOns
