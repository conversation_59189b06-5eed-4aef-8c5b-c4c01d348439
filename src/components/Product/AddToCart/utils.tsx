'use client'

import en from '@/locales/en-US.json'
import styles from '@components/Generic/CallToAction/tokens.stylex'

export const renderCtaStyles = (availability: string | null) => {
  switch (availability) {
    case 'Notify Me when unavailable':
    case 'Notify Me':
      return styles.primary
    case 'Sold Out when unavailable':
    case 'Sold Out':
    case 'Loading':
      return styles.disabled
    default:
      return styles.primary
  }
}

export const renderCtaType = (variantAvailability: (string | null)) => {
  switch (variantAvailability) {
    case 'Notify Me when unavailable':
    case 'Notify Me':
      return en.product.notify_me
    case 'Sold Out when unavailable':
    case 'Sold Out':
      return en.product.sold_out
    default:
      return en.product.notify_me
  }
}

export const renderText = (
  available: boolean | undefined,
  isLoading: boolean,
  variantAvailability: (string | null)
) => {
  if (isLoading) {
    return 'Loading...'
  }
  return available ? en.product.add_to_cart : renderCtaType(variantAvailability)
}

export const calculateCartItems = (items: any[]) => items.reduce((sum, item) => sum + item.quantity, 0)
