'use client'

/* eslint-disable complexity */

// This is a slightly modified version of the AddToCartButton component from the Hydrogen React library.
// The original component only accepts a single variantId string.
// This modified version accepts also an array of variant IDs.
// It's 1:1 drop in replacement for the original AddToCartButton component.
// Passing a single variantId string will work the same as before.

import { useCart, useProduct } from '@shopify/hydrogen-react'
import {
  BaseButton,
  type BaseButtonProps,
} from '@shopify/hydrogen-react/BaseButton'
import {
  ElementType, useCallback, useEffect, useState
} from 'react'

export type Attribute = {
  key: string;
  value: string;
};

export type VariantAttributes = {
  variantId: string;
  attributes?: Attribute[];
}

export interface SingleAddToCartButtonPropsBase {
  mode: 'single';
  /** An array of cart line attributes that belong to the item being added to the cart. */
  attributes?: Attribute[];
  /** The ID of the variant. */
  variantId?: string;
  /** The item quantity. */
  quantity?: number;
}

export interface MultiAddToCartButtonPropsBase {
  mode: 'multiple';
  /** An array of cart line attributes that belong to the item being added to the cart. */
  attributes?: VariantAttributes[];
  /** The ID of the variant. */
  variantId?: string[];
  /** The item quantity. */
  quantity?: { variantId: number; quantity: number }[];
}

export type AddToCartButtonPropsBase = {
  /** The text that is announced by the screen reader when the item is being added to the cart. Used for accessibility purposes only and not displayed on the page. */
  accessibleAddingToCartLabel?: string;
  /** The selling plan ID of the subscription variant */
  sellingPlanId?: string;
} & (
  SingleAddToCartButtonPropsBase | MultiAddToCartButtonPropsBase
);

export type AddToCartButtonProps<AsType extends ElementType = 'button'> =
  AddToCartButtonPropsBase & BaseButtonProps<AsType>;

/**
 * The `AddToCartButton` component renders a button that adds an item to the cart when pressed.
 * It must be a descendent of the `CartProvider` component.
 */
export function AddToCartButton<AsType extends ElementType = 'button'>(
  props: AddToCartButtonProps<AsType>,
): JSX.Element {
  const [addingItem, setAddingItem] = useState<boolean>(false)
  const {
    variantId: explicitVariantId,
    sellingPlanId,
    onClick,
    children,
    accessibleAddingToCartLabel,
    ...passthroughProps
  } = props
  const { status, linesAdd } = useCart()
  const { selectedVariant } = useProduct()
  const variantId = explicitVariantId ?? selectedVariant?.id ?? ''
  const disabled =
    explicitVariantId === null ||
    variantId === '' ||
    selectedVariant === null ||
    addingItem ||
    // Only certain 'as' types such as 'button' contain `disabled`
    (passthroughProps as { disabled?: boolean }).disabled

  useEffect(() => {
    if (addingItem && status === 'idle') {
      setAddingItem(false)
    }
  }, [status, addingItem])

  const handleAddItem = useCallback(() => {
    const {
      variantId: itemVariantId,
      mode,
      quantity,
      attributes
    } = props

    if (!itemVariantId) {
      return
    }

    setAddingItem(true)

    if (mode === 'single') {
      linesAdd([
        {
          quantity: quantity || 1,
          merchandiseId: itemVariantId,
          attributes,
          sellingPlanId,
        },
      ])
    }

    if (mode === 'multiple') {
      linesAdd(
        itemVariantId.map((id) => {
          let itemQuantity: number = 1

          if (quantity) {
            const quantityForVariant = quantity.find((item) => id.endsWith(item.variantId.toString()))
            itemQuantity = quantityForVariant?.quantity || 1
          }

          const variantAttributes = attributes?.find(
            (attr) => attr.variantId === id
          )

          return {
            quantity: itemQuantity,
            merchandiseId: id,
            attributes: variantAttributes?.attributes,
            sellingPlanId,
          }
        })
      )
    }
  }, [
    linesAdd,
    props,
    sellingPlanId,
  ])

  return (
    <>
      <BaseButton
        {...passthroughProps}
        disabled={disabled}
        onClick={onClick}
        defaultOnClick={handleAddItem}
      >
        {children}
      </BaseButton>
      {accessibleAddingToCartLabel ? (
        <p
          style={{
            position: 'absolute',
            width: '1px',
            height: '1px',
            padding: '0',
            margin: '-1px',
            overflow: 'hidden',
            clip: 'rect(0, 0, 0, 0)',
            whiteSpace: 'nowrap',
            borderWidth: '0',
          }}
          role="alert"
          aria-live="assertive"
        >
          {addingItem ? accessibleAddingToCartLabel : null}
        </p>
      ) : null}
    </>
  )
}
