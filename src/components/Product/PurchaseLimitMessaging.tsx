'use client'

import { colors } from '@/app/themeTokens.stylex'
import usePurchaseLimit from '@/hooks/usePurchaseLimit'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  purchaseLimitMessaging: {
    color: colors.red700,
    textAlign: 'center',
    fontWeight: 'bold',
  }
})

type PurchaseLimitMessagingProps = {
  variantId: string;
};

export default function PurchaseLimitMessaging({ variantId }: PurchaseLimitMessagingProps) {
  const { isPurchaseLimited } = usePurchaseLimit(variantId)

  if (!isPurchaseLimited) {
    return null
  }

  return (
    <div {...stylex.props(styles.purchaseLimitMessaging)}>
      Purchase of Limited Edition Petite Cookers is restricted to 2 per person.
    </div>
  )
}
