import * as ArticleContent from './Article/ArticleContent'

import React from 'react'

type SectionType = {
  default: React.ComponentType<any>,
  extractor: (section: any) => any,
}

export type ArticleSectionType = {
  sys: {
    id: string;
  };
  sectionType: 'PageSectionsContent';
  richtext: {
    json: any
  },
  markdown: string;
}

type ArticleSectionsProps = {
  sections: ArticleSectionType[];
  articleInfo: {
    authors?: {
      items: {
        name: string
      }[]
    }
    datePublished?: string
  }
}

const RegisteredSections = {
  PageSectionsContent: ArticleContent,
}

const ArticleSections = ({ sections = [], articleInfo }: ArticleSectionsProps) => {
  const sectionsMap = sections.map((section) => {
    const { sectionType, sys } = section
    const SectionHandler = RegisteredSections[sectionType] as unknown as SectionType
    const Component = SectionHandler?.default
    const Props = SectionHandler?.extractor

    if (!Component && !Props) {
      // eslint-disable-next-line no-console
      console.error(`No component registered for section type: ${sectionType}`)
      return null
    }

    return (
      <Component key={sys.id} articleInfo={articleInfo} {...Props(section)} />
    )
  })

  return sectionsMap
}

export default ArticleSections
