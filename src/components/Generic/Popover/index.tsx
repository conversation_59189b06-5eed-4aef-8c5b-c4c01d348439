'use client'

import { colors } from '@/app/themeTokens.stylex'
import {
  <PERSON>overContainer, PopoverTrigger, PopoverContent, type PopoverContentProperties
} from '@components/ui/Popover'

import stylex from '@stylexjs/stylex'
import { ReactNode } from 'react'

type Props = {
  children: ReactNode
  content: ReactNode
  styleProp?: {}
  contentStyleProp?: {}
} & PopoverContentProperties

const styles = stylex.create({
  triggerWrapper: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
  },
  content: {
    backgroundColor: colors.cream,
    color: colors.navy,
    padding: '15px',
    borderRadius: '3px',
    textAlign: 'center',
    width: {
      default: '250px',
      '@media (max-width: 768px)': 'fit-content',
    },
    maxWidth: {
      '@media (max-width: 768px)': '90vw',
    },
  },
})

export default function Popover({
  children,
  content,
  styleProp,
  contentStyleProp,
  hideArrow = true,
}: Props) {
  if (!content) return null

  return (
    <PopoverContainer>
      <PopoverTrigger>
        <div {...stylex.props(styles.triggerWrapper, styleProp)}>
          {children}
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="center"
        hideArrow={hideArrow}
        arrowProps={{
          width: 10,
          height: 5,
          fill: colors.cream,
        }}
        side="bottom"
        sideOffset={5}
        contentStyleProps={{ ...styles.content, ...contentStyleProp }}
        {...stylex.props(styles.content, contentStyleProp)}
      >
        {content}
      </PopoverContent>
    </PopoverContainer>
  )
}
