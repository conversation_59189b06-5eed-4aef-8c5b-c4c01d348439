import Container from '@/components/layout/Container'
import useVariantAvailability from '@/hooks/getVariantAvailability'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import { KeyboardEvent, useEffect } from 'react'

const styles = stylex.create({
  swatchItem: {
    cursor: 'pointer',
    padding: '8px 12px',
    height: '40px',
    width: '100%',
    fontSize: '13px',
  },
  swatchItemSmall: {
    padding: '6px 8px',
    height: '30px',
    fontSize: '10px',
  },
  swatchItemDisabled: {
    cursor: 'not-allowed',
  },
  swatchImage: {
    borderRadius: '50%',
  },
  swatchImageSquare: {
    borderRadius: '4px',
  },
  swatch: {
    outline: '1px solid #1f3438',
    outlineOffset: '2px',
  },
})

const SMALL_SWATCH_SIZE = 16
const REGULAR_SWATCH_SIZE = 24

type SwatchDropdownItemProps = {
  size: 'small' | 'regular';
  swatches: any[];
  swatchVariant: any;
  currentVariant: any;
  setCurrentVariant: (variant: any) => void;
  swatchesOpen: boolean;
  removeFromAvailableVariants: (variantId: string) => void;
};

// eslint-disable-next-line complexity
const SwatchDropdownItem = ({
  size,
  swatches,
  swatchVariant,
  currentVariant,
  setCurrentVariant,
  swatchesOpen,
  removeFromAvailableVariants,
}: SwatchDropdownItemProps) => {
  const {
    data: isVariantAvailable,
    isLoading,
    isError,
  } = useVariantAvailability(swatchVariant.variantId)

  useEffect(() => {
    if (!isError && !isLoading && !isVariantAvailable) {
      removeFromAvailableVariants(swatchVariant.variantId)
    }
  }, [isError, isLoading, isVariantAvailable, removeFromAvailableVariants, swatchVariant.variantId])

  if (isLoading || isError || !isVariantAvailable) {
    return null
  }

  return (
    <Container
      flex
      flexRow
      spaceBetween
      alignCentered
      styleProp={[
        styles.swatchItem,
        size === 'small' && styles.swatchItemSmall,
        swatches.length === 1 && styles.swatchItemDisabled,
      ]}
      onClick={() => {
        setCurrentVariant(swatchVariant)
      }}
      onKeyDown={(e: KeyboardEvent<HTMLDivElement>) => {
        if (e.key === 'Spacebar' || e.key === ' ') {
          setCurrentVariant(swatchVariant)
        }
      }}
      tabIndex={swatchesOpen ? 0 : -1}
      data-slide-id={swatchVariant.variantId}
    >
      {swatchVariant?.swatch?.icon ? (
        <Image
          src={swatchVariant.swatch.icon.url}
          alt={swatchVariant.swatch.presentation}
          width={size === 'small' ? SMALL_SWATCH_SIZE : REGULAR_SWATCH_SIZE}
          height={size === 'small' ? SMALL_SWATCH_SIZE : REGULAR_SWATCH_SIZE}
          {...stylex.props(
            styles.swatch,
            swatchVariant.swatch.style === 'Circle' && styles.swatchImage,
            swatchVariant.swatch.style === 'Square' && styles.swatchImageSquare
          )}
        />
      ) : (
        <div>{swatchVariant.swatch.presentation}</div>
      )}
      {swatchesOpen && swatchVariant.variantId === currentVariant.variantId && (
        <Image
          alt=""
          src="/assets/checkmark.svg"
          width={16}
          height={16}
        />
      )}
    </Container>
  )
}

export default SwatchDropdownItem
