'use client'

import SwatchSelectorDropdownItem from './SwatchesDropdownItem'

import { colors } from '@/app/themeTokens.stylex'
import RightArrow from '@/components/Generic/Icon/lib/RightArrow'
import { getSwatches } from '@/components/Product/ProductCard/utils'
import useClickOutside from '@/hooks/useClickOutside'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

import * as stylex from '@stylexjs/stylex'
import useEmblaCarousel from 'embla-carousel-react'
import { WheelGesturesPlugin } from 'embla-carousel-wheel-gestures'
import {
  Dispatch, SetStateAction, useEffect, useRef, useState
} from 'react'

const styles = stylex.create({
  sliderItem: {
    width: '100%',
    height: 'auto',
    flexShrink: 0,
    backgroundColor: colors.white,
    borderRadius: '10px',
    padding: '16px',
    justifyContent: 'center',
  },
  data: {
    flex: '1',
  },
  slideHeaderData: {
    flexDirection: {
      default: 'column',
      '@media (min-width: 568px)': 'row',
    },
  },
  cta: {
    flex: '1',
  },
  ctaButton: {
    display: 'block',
    width: '100%',
    height: '100%',
    color: 'inherit',
  },
  toggleWrapper: {
    position: 'relative',
    width: '80px',
    height: '42px',
    flexShrink: 0,
    userSelect: 'none',
  },
  toggleWrapperSmall: {
    width: '60px',
    height: '32px',
  },
  toggle: {
    position: 'absolute',
    bottom: '0',
    borderRadius: '20px',
    borderWidth: '1px',
    borderColor: colors.navy,
    borderStyle: 'solid',
    width: '100%',
    height: '100%',
    backgroundColor: colors.white,
    overflow: 'hidden',
  },
  toggleSmall: {
    borderRadius: '16px',
  },
  toggleDisabled: {
    borderColor: colors.gray300,
  },
  toggleOpen: {
    width: '100%',
    height: '142px',
  },
  toggleOpenSmall: {
    height: '110px',
  },
  togglesLowerLengthHeight: {
    height: 'auto'
  },
  toggleArrow: {
    position: 'absolute',
    top: '50%',
    right: '12px',
    transform: 'translateY(-50%) rotate(90deg)',
  },
  toggleArrowSmall: {
    right: '8px',
  },
  toggleArrowDisabled: {
    opacity: 0.2,
  },
  sliderRef: {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
  },
  sliderContainer: {
    height: '100%',
    width: '100%',
  },
  swatchItem: {
    cursor: 'pointer',
    padding: '8px 12px',
    height: '40px',
    width: '100%',
    fontSize: '13px',
  },
  swatchItemSmall: {
    padding: '6px 8px',
    height: '30px',
    fontSize: '10px',
  },
  swatchItemDisabled: {
    cursor: 'not-allowed',
  },
  swatchImage: {
    borderRadius: '50%',
  },
  swatchImageSquare: {
    borderRadius: '4px',
  },
  swatch: {
    outline: '1px solid #1f3438',
    outlineOffset: '2px',
  },
})

type SwatchesDropdownProps = {
  product: any;
  setAvailableVariants?: Dispatch<SetStateAction<ContentfulProductVariant[]>>;
  setCurrentVariant: any;
  currentVariant: any;
  size?: 'regular' | 'small';
  focusable?: boolean;
  onOpenToggle?: () => void;
};

// eslint-disable-next-line complexity
const SwatchesDropdown = ({
  product,
  setAvailableVariants,
  setCurrentVariant,
  currentVariant,
  size = 'regular',
  focusable = true,
  onOpenToggle,
}: SwatchesDropdownProps) => {
  const dropdownRef = useRef<HTMLDivElement>(null)
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      axis: 'y',
    },
    [
      WheelGesturesPlugin({
        forceWheelAxis: 'y',
      }),
    ]
  )

  const [swatchesOpen, setSwatchesOpen] = useState(false)

  const removeFromAvailableVariants = (variantId: string) => {
    if (setAvailableVariants) {
      setAvailableVariants((prevVariants) => prevVariants.filter((variant) => variant.variantId !== variantId))
    }
  }

  useEffect(() => {
    const slides = emblaApi?.slideNodes()
    const slideIndex = slides?.findIndex((slide) => slide.dataset.slideId === currentVariant.variantId)

    emblaApi?.reInit()
    emblaApi?.scrollTo(slideIndex || 0, true)
  }, [swatchesOpen, currentVariant, emblaApi])

  useClickOutside(dropdownRef, () => {
    if (swatchesOpen) {
      setSwatchesOpen(false)
    }
  }, [swatchesOpen])

  if (!product) {
    return null
  }

  let swatches: any[] = []

  if (product.__typename === 'Product') {
    swatches = getSwatches(product)
  }

  if (product.__typename === 'Variant') {
    swatches = [product]
  }

  const disabled = swatches.length === 1

  const openDropdown = () => {
    if (onOpenToggle) {
      onOpenToggle()
    }

    if (swatches.length > 1) {
      setSwatchesOpen(!swatchesOpen)
    }
  }

  const SMALLER_TOGGLE_SIZE_TRIGGER_LENGTH = 5

  return (
    <div
      ref={dropdownRef}
      {...stylex.props(
        styles.toggleWrapper,
        size === 'small' && styles.toggleWrapperSmall
      )}
    >
      <div
        tabIndex={focusable && !disabled ? 0 : -1}
        {...stylex.props(
          styles.toggle,
          size === 'small' && styles.toggleSmall,
          swatchesOpen && styles.toggleOpen,
          swatchesOpen && size === 'small' && styles.toggleOpenSmall,
          swatchesOpen && swatches.length < SMALLER_TOGGLE_SIZE_TRIGGER_LENGTH && styles.togglesLowerLengthHeight,
          disabled && styles.toggleDisabled
        )}
        onClick={openDropdown}
        onKeyDown={(e) => {
          if (e.key === 'Spacebar' || e.key === ' ') {
            e.preventDefault()
            openDropdown()
          }
        }}
      >
        <div ref={emblaRef} {...stylex.props(styles.sliderRef)}>
          <div {...stylex.props(styles.sliderContainer)}>
            {swatches.map(
              (swatchVariant) => (
                <SwatchSelectorDropdownItem
                  size={size}
                  swatches={swatches}
                  swatchVariant={swatchVariant}
                  currentVariant={currentVariant}
                  setCurrentVariant={setCurrentVariant}
                  removeFromAvailableVariants={removeFromAvailableVariants}
                  swatchesOpen={swatchesOpen}
                  key={swatchVariant.variantId}
                />
              )
            )}
          </div>
        </div>
        {!swatchesOpen && (
          <RightArrow
            dimensions="15"
            {...stylex.props(
              styles.toggleArrow,
              disabled && styles.toggleArrowDisabled,
              size === 'small' && styles.toggleArrowSmall
            )}
          />
        )}
      </div>
    </div>
  )
}

export default SwatchesDropdown
