'use client'

import { ThemeColors } from '@/app/themeThemes.stylex'
import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  button: {
    background: 'none',
    borderWidth: '0',
    borderStyle: 'none',
    borderColor: 'transparent',
    cursor: 'pointer',
    padding: '0',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  svg: {
    aspectRatio: '1',
  },
  svgWidth: (width) => ({
    width,
  }),
})

type PlayPauseButtonProps = {
  width?: string | number | undefined | null
  play?: boolean
  iconColor?: ThemeColors
  styleProp?: {}
  onClick?: () => void
}

const PlayPauseButton = ({
  width = '24px',
  play = false,
  iconColor = colors.navy as ThemeColors,
  styleProp,
  onClick,
}: PlayPauseButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    {...stylex.props(styles.button, styleProp)}
  >
    <svg
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      {...stylex.props(styles.svg, styles.svgWidth(width))}
    >
      {play ? (
        <path
          d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"
          fill={iconColor}
        />
      ) : (
        <path
          d="M8 5v14l11-7z"
          fill={iconColor}
        />
      )}
    </svg>
  </button>
)

export default PlayPauseButton
