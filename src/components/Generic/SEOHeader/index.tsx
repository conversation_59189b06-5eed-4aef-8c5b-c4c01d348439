import React from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  hidden: {
    position: 'absolute',
    width: '1px',
    height: '1px',
    whiteSpace: 'nowrap',
    fontSize: '0',
    overflow: 'hidden',
    clip: 'rect(1px, 1px, 1px, 1px)',
    display: 'inline-block',
    padding: '0!important',
    margin: '0!important',
    borderWidth: '0 !important',
  }
})

const SEOHeader = ({ text } : { text: string }) => (
  <h1 {...stylex.props(styles.hidden)}>{text}</h1>
)

export default SEOHeader
