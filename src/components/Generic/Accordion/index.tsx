'use client'

import {
  globalTokens as $,
  defaultTheme as $T,
  spacing
} from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import { equals } from '@/utils/checking'
import { TypographyThemes } from '@/app/typographyThemes.stylex'
import Container from '@/components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import React, {
  useState,
  ReactNode,
  forwardRef,
  useRef,
} from 'react'

export type AccordionProps = {
  title: string | null | undefined;
  // eslint-disable-next-line
  open?: boolean;
  children: ReactNode;
  isLast?: boolean;
  titleTypographyTheme?: TypographyThemes
}

export type AccordionChildsProps = AccordionProps & {
  isOpenedId?: string | null | undefined;
  handleToggle?: Function
}

const styles = stylex.create({
  details: {
    borderTopWidth: '1px',
    borderTopStyle: 'solid',
    borderTopColor: `rgb(from ${$T.primaryText} r g b / 0.2)`,
    overflow: 'hidden'
  },
  last: {
    borderBottomWidth: '1px',
    borderBottomStyle: 'solid',
    borderBottomColor: `rgb(from ${$T.primaryText} r g b / 0.2)`,
  },
  summary: {
    display: 'flex',
    gap: spacing.sm,
    justifyContent: 'space-between',
    paddingBlock: spacing.md,
    cursor: 'pointer',
    '::marker': {
      display: 'none',
    },
  },
  content: {
    height: 0,
    transitionProperty: 'height',
    transitionDuration: '0.45s',
    transitionTimingFunction: $.timingFunction,
    overflow: 'hidden',
    boxSizing: 'content-box',
  },
  contentOpen: {
    height: 'calc-size(auto, size)',
    transitionProperty: 'height',
    transitionDuration: '0.25s',
    transitionTimingFunction: $.timingFunction,
    paddingBlockEnd: spacing.md,
  },
  contentDisplayOpen: {
    display: 'block',
    transitionProperty: 'display',
    transitionDuration: '0.25s',
    transitionTimingFunction: $.timingFunction,
  },
  icon: {
    width: spacing.sm,
    aspectRatio: '1',
    marginBlockStart: '7px',
    alignSelf: 'start',
    flexShrink: 0,
  },
  line: {
    stroke: $T.primaryText,
    strokeWidth: '4px',
  },
  verticalLine: {
    transformOrigin: 'center',
    transition: 'transform .2s ease-out',
  },
  verticalLineOpen: {
    transform: 'rotate(90deg)',
  },
  iconOpen: {
    '::after': {
      transform: 'rotate(90deg)',
    },
  },
  title: {
    display: 'block',
    paddingRight: spacing.lg,
  }
})

const UncontrolledAccordion = forwardRef<HTMLDetailsElement, AccordionChildsProps>(({
  titleTypographyTheme = 'bodyLarge',
  title,
  open = false,
  children,
  isLast,
  handleToggle = () => {}
}: AccordionChildsProps, ref) => (
  <Container>
    <details
      ref={ref}
      open={open}
      {...stylex.props(
        styles.details,
      )}
    >
      <summary {...stylex.props(styles.summary)} onClick={(e) => handleToggle(e, { title, open, ref })}>
        <Typography
          as="span"
          typographyTheme={titleTypographyTheme}
          fontBold
          styleProp={styles.title}
        >
          {title}
        </Typography>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 25 25"
          {...stylex.props(styles.icon)}
        >
          <g {...stylex.props(styles.line)}>
            <line x1="0" y1="12.5" x2="25" y2="12.5" />
            <line
              x1="12.5"
              y1="0"
              x2="12.5"
              y2="25"
              {...stylex.props(
                styles.verticalLine,
                open && styles.verticalLineOpen
              )}
            />
          </g>
        </svg>
      </summary>
    </details>
    <div
      id={title || ''}
      {...stylex.props(
        styles.content,
        open && styles.contentOpen,
        isLast && styles.last,
      )}
    >
      {children}
    </div>
  </Container>
))

const Accordion = ({
  titleTypographyTheme = 'bodyLarge',
  title,
  children,
  isLast,
  open = false,
}: AccordionProps) => {
  const [isOpen, setIsOpen] = useState(open)

  const handleToggle = (): void => {
    const nextState = !isOpen
    setIsOpen(nextState)
  }

  return (
    <UncontrolledAccordion
      titleTypographyTheme={titleTypographyTheme}
      title={title}
      isLast={isLast}
      open={isOpen}
      handleToggle={handleToggle}
    >
      {children}
    </UncontrolledAccordion>
  )
}

export const SingleOpenedAccordion = ({
  isOpenedId,
  titleTypographyTheme = 'bodyLarge',
  title,
  children,
  isLast,
  handleToggle = () => {}
}: AccordionChildsProps) => {
  const isOpen = equals(title, isOpenedId)
  const ref = useRef<HTMLDetailsElement>(null)

  return (
    <UncontrolledAccordion
      titleTypographyTheme={titleTypographyTheme}
      title={title}
      isLast={isLast}
      open={isOpen}
      handleToggle={handleToggle}
      ref={ref}
    >
      {children}
    </UncontrolledAccordion>
  )
}

export default Accordion
