'use client'

import SubmittedPrivacyRequestForm from './SubmittedPrivacyRequestForm'
import StateSpecificFields from './StateSpecificFields/StateSpecificFields'

import { spacing, fontSizes, colors } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Typography from '@components/Typography'

import * as stylex from '@stylexjs/stylex'
import { useForm } from 'react-hook-form'
import { useState } from 'react'

export type FormFields = {
  firstName: string;
  lastName: string;
  email: string;
  streetAddress: string;
  city: string;
  selectedState: string;
  zipcode: string;
  requestType: string;
  consentConfirmation?: boolean;
  rightToAppeal?: boolean;
  appealDetails?: string;
  verificationMethod?: string;
};

const US_STATES = [
  'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut', 'Delaware', 'Florida', 'Georgia',
  'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland',
  'Massachusetts', 'Michigan', 'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire', 'New Jersey',
  'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina',
  'South Dakota', 'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia', 'Wisconsin', 'Wyoming'
]

const REQUEST_TYPES = [
  'Delete my information',
  'Do not sell my personal information',
  'Request to correct my information',
  'Opt out of marketing communications'
]

const styles = stylex.create({
  parentWrapper: {
    paddingBlock: spacing.lg,
    paddingInline: spacing.md,
    width: 'min(100%, 800px)',
    marginInline: 'auto',
  },
  input: {
    minHeight: '40px',
    borderRadius: '4px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.gray500,
    paddingInline: spacing.xs,
    fontSize: fontSizes.sm,
    color: colors.navy,
    outlineStyle: {
      default: 'none',
      ':focus': 'solid',
    },
  },
  label: {
    display: 'flex',
    flexDirection: 'column',
    gap: spacing.xs,
    color: colors.navy,
    flex: '1 1 343px',
  },
  errorMessage: {
    color: colors.red700,
  },
  submitButton: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '48px',
    paddingInline: '32px',
    backgroundColor: colors.navy,
    color: colors.white,
    borderRadius: '24px',
    borderWidth: '0',
    borderStyle: 'none',
    borderColor: 'transparent',
    fontSize: fontSizes.sm,
    cursor: {
      default: 'pointer',
      ':disabled': 'not-allowed',
    },
    opacity: {
      default: 1,
      ':hover': 0.9,
      ':disabled': 0.7,
    },
  },
  fullWidthInput: {
    flex: '1 1 100%',
  },
  dividerContainer: {
    width: '100%',
    paddingBlock: spacing.md,
  },
  divider: {
    width: '100%',
    borderTopWidth: '1px',
    borderTopStyle: 'solid',
    borderTopColor: colors.navy,
  }
})

const ErrorMessage = () => (
  <Typography
    as="span"
    typographyTheme="bodySmall"
    styleProp={styles.errorMessage}
  >
    This field is required
  </Typography>
)

// eslint-disable-next-line complexity
const PrivacyRequestForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [, setFormError] = useState<string | null>(null)
  const [selectedState, setSelectedState] = useState('')
  const [formData, setFormData] = useState({})

  const handleInputChange = (e:any) => {
    const {
      name,
      value,
      type,
      checked
    } = e.target
    const newValue = type === 'checkbox' ? checked : value

    setFormData((prev) => {
      const updatedFormData = {
        ...prev,
        [name]: newValue
      }
      return updatedFormData
    })
  }

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormFields>()

  const isValidEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return regex.test(email)
  }

  const onSubmit = async (data: FormFields) => {
    const {
      firstName,
      lastName,
      email,
      requestType
    } = data
    if (!firstName || !lastName || !email || !requestType) {
      setFormError('Please fill in all required fields')
      return
    }

    if (!isValidEmail(email)) {
      setFormError('Please enter a valid email address')
      return
    }

    setIsSubmitting(true)

    try {
      const combinedData = {
        ...data,
        ...formData
      }

      const response = await fetch('/api/privacy-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(combinedData)
      })

      if (!response.ok) {
        throw new Error('Failed to submit form')
      }

      setIsSubmitted(true)
    } catch (error) {
      setFormError('An error occurred while submitting the form. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubmitted) {
    return (
      <SubmittedPrivacyRequestForm />
    )
  }

  return (
    <Container flex gap="4" styleProp={styles.parentWrapper}>
      <Typography as="h2" typographyTheme="h2Secondary">Privacy Request</Typography>
      <Typography as="p" typographyTheme="bodyLarge">
        Caraway values your privacy and believes in your right to make informed decisions about your personal data.
        Please fill out this form to submit a request regarding your personal information.
        For additional information about our privacy practices, please review our Privacy Policy.
      </Typography>
      <Container
        as="form"
        flex
        flexRow
        flexWrap="wrap"
        gap="2"
        onSubmit={handleSubmit(onSubmit)}
      >
        <label {...stylex.props(styles.label)} htmlFor="firstName">
          First Name
          <input
            id="firstName"
            type="text"
            required
            {...register('firstName', { required: 'Please enter your first name' })}
            {...stylex.props(
              styles.input
            )}
          />
          {errors.firstName && <ErrorMessage />}
        </label>

        <label {...stylex.props(styles.label)} htmlFor="lastName">
          Last Name
          <input
            id="lastName"
            type="text"
            required
            {...register('lastName', { required: 'Please enter your last name' })}
            {...stylex.props(
              styles.input
            )}
          />
          {errors.lastName && <ErrorMessage />}
        </label>

        <label {...stylex.props(styles.label)} htmlFor="streetAddress">
          Street Address
          <input
            id="streetAddress"
            type="text"
            required
            {...register('streetAddress', { required: 'Please enter your street address' })}
            {...stylex.props(styles.input)}
          />
          {errors.streetAddress && <ErrorMessage />}
        </label>

        <label {...stylex.props(styles.label)} htmlFor="city">
          City
          <input
            id="city"
            type="text"
            required
            {...register('city', { required: 'Please enter your city' })}
            {...stylex.props(styles.input)}
          />
          {errors.city && <ErrorMessage />}
        </label>

        <label {...stylex.props(styles.label)} htmlFor="selectedState">
          State
          <select
            id="selectedState"
            required
            {...register('selectedState', { required: 'Please select a state' })}
            {...stylex.props(styles.input)}
            onChange={(e) => setSelectedState(e.target.value)}
          >
            <option value="">Select a state</option>
            {US_STATES.map((state) => (
              <option key={state} value={state}>
                {state}
              </option>
            ))}
          </select>
          {errors.selectedState && <ErrorMessage />}
        </label>

        <label {...stylex.props(styles.label)} htmlFor="zipcode">
          Zipcode
          <input
            id="zipcode"
            type="text"
            required
            {...register('zipcode', { required: 'Please enter your zipcode' })}
            {...stylex.props(styles.input)}
          />
          {errors.zipcode && <ErrorMessage />}
        </label>
        {selectedState && (
        <StateSpecificFields
          state={selectedState}
          formData={formData}
          onChange={handleInputChange}
        />
        )}
        <label {...stylex.props(styles.label, styles.fullWidthInput)} htmlFor="requestType">
          Request Type
          <select
            id="requestType"
            required
            {...register('requestType', { required: 'Please select a request type' })}
            {...stylex.props(
              styles.input,
            )}
          >
            <option value="">Select a request type</option>
            {REQUEST_TYPES.map((type) => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
          {errors.requestType && <ErrorMessage />}
        </label>

        <label {...stylex.props(styles.label, styles.fullWidthInput)} htmlFor="email">
          Email
          <input
            id="email"
            type="email"
            required
            {...register('email', {
              required: 'Please enter your email',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Please enter a valid email address'
              }
            })}
            {...stylex.props(
              styles.input,

            )}
          />
          {errors.email && <ErrorMessage />}
        </label>
        <Container flex gap="1" styleProp={styles.dividerContainer}>
          <hr {...stylex.props(styles.divider)} />
          <hr {...stylex.props(styles.divider)} />
        </Container>
        <button
          type="submit"
          disabled={isSubmitting || Object.keys(errors).length > 0}
          {...stylex.props(styles.submitButton, styles.fullWidthInput)}
        >
          {isSubmitting ? 'Submitting...' : 'Submit →'}
        </button>
        <Typography as="p" typographyTheme="bodySmall">
          To exercise your rights or for any inquiries, please call our toll-free number: 1-855-944-4909.
        </Typography>
      </Container>
    </Container>
  )
}

export default PrivacyRequestForm
