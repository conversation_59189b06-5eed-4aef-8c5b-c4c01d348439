export type FieldConfig = {
  type: 'select' | 'checkbox' | 'textarea';
  label: string;
  options?: string[];
  dependsOn?: string;
}

export type StateConfig = {
  name: string;
  fields: {
    [key: string]: FieldConfig;
  };
}

export type StateConfigs = {
  [key: string]: StateConfig;
}

export type FormFieldProps = {
  fieldKey: string;
  field: FieldConfig;
  value: any;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
}

export type StateSpecificFieldsProps = {
  state: string;
  formData: Record<string, any>;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
}
