import { FormFieldProps } from './types'

import { spacing, fontSizes, colors } from '@/app/themeTokens.stylex'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  input: {
    minHeight: '40px',
    borderRadius: '4px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.gray500,
    paddingInline: spacing.xs,
    fontSize: fontSizes.sm,
    color: colors.navy,
    outlineStyle: {
      default: 'none',
      ':focus': 'solid',
    },
  },
  label: {
    display: 'flex',
    flexDirection: 'column',
    gap: spacing.xs,
    color: colors.navy,
    flex: '1 1 343px',
  },
  errorMessage: {
    color: colors.red700,
  },
  fullWidthInput: {
    flex: '1 1 100%',
  },
  checkbox: {
    flexDirection: 'row',
    marginBlockStart: spacing.xs,
  },
  textAreaLabel: {
    marginBlockStart: spacing.xs,
  },
  textarea: {
    marginBlockStart: spacing.xs,
    height: '100px',
  }
})

const FormField = ({
  fieldKey,
  field,
  value,
  onChange
}: FormFieldProps) => {
  switch (field.type) {
    case 'select':
      return (
        <label {...stylex.props(styles.label)} htmlFor={fieldKey}>{field.label}
          <select
            id={fieldKey}
            name={fieldKey}
            value={value || ''}
            onChange={onChange}
            {...stylex.props(styles.input)}
          >
            <option value="">Select {field.label}</option>
            {field.options?.map((option) => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </label>
      )

    case 'checkbox':
      return (
        <label {...stylex.props(styles.label, styles.checkbox)} htmlFor={fieldKey}>{field.label}
          <input
            type="checkbox"
            id={fieldKey}
            name={fieldKey}
            checked={value || false}
            onChange={onChange}
          />
        </label>
      )

    case 'textarea':
      return (
        <label {...stylex.props(styles.label, styles.textAreaLabel)} htmlFor={fieldKey}>{field.label}
          <textarea
            id={fieldKey}
            name={fieldKey}
            value={value || ''}
            onChange={onChange}
            {...stylex.props(styles.input, styles.textarea)}
          />
        </label>
      )

    default:
      return null
  }
}

export default FormField
