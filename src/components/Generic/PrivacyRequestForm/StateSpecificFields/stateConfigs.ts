import { StateConfigs } from './types'

export const statesWithAdditionalFields: StateConfigs = {
  California: {
    name: 'California (CCPA)',
    fields: {
      verificationMethod: {
        type: 'select',
        label: 'Verification Method',
        options: ['Email', 'Phone', 'Mail']
      }
    }
  },
  Virginia: {
    name: 'Virginia (VCDPA)',
    fields: {
      consentConfirmation: {
        type: 'checkbox',
        label: 'I confirm that I am a resident of Virginia'
      },
      rightToAppeal: {
        type: 'checkbox',
        label: 'I would like to exercise my right to appeal'
      },
      appealDetails: {
        type: 'textarea',
        label: 'Appeal Details',
        dependsOn: 'rightToAppeal'
      }
    }
  },
  Colorado: {
    name: 'Colorado (CPA)',
    fields: {
      consentConfirmation: {
        type: 'checkbox',
        label: 'I confirm that I am a resident of Colorado'
      },
      rightToAppeal: {
        type: 'checkbox',
        label: 'I would like to exercise my right to appeal'
      },
      appealDetails: {
        type: 'textarea',
        label: 'Appeal Details',
        dependsOn: 'rightToAppeal'
      }
    }
  },
  Connecticut: {
    name: 'Connecticut (CTDPA)',
    fields: {
      consentConfirmation: {
        type: 'checkbox',
        label: 'I confirm that I am a resident of Connecticut'
      },
      rightToAppeal: {
        type: 'checkbox',
        label: 'I would like to exercise my right to appeal'
      },
      appealDetails: {
        type: 'textarea',
        label: 'Appeal Details',
        dependsOn: 'rightToAppeal'
      }
    }
  },
  Oregon: {
    name: 'Oregon (CDPA)',
    fields: {
      consentConfirmation: {
        type: 'checkbox',
        label: 'I confirm that I am a resident of Oregon'
      },
      rightToAppeal: {
        type: 'checkbox',
        label: 'I would like to exercise my right to appeal'
      },
      appealDetails: {
        type: 'textarea',
        label: 'Appeal Details',
        dependsOn: 'rightToAppeal'
      }
    }
  },
  Texas: {
    name: 'Texas (TCPA)',
    fields: {
      consentConfirmation: {
        type: 'checkbox',
        label: 'I confirm that I am a resident of Texas'
      },
      rightToAppeal: {
        type: 'checkbox',
        label: 'I would like to exercise my right to appeal'
      },
      appealDetails: {
        type: 'textarea',
        label: 'Appeal Details',
        dependsOn: 'rightToAppeal'
      }
    }
  }
}

export const hasAdditionalFields = (state: string): boolean => state in statesWithAdditionalFields

export const getStateAbbreviation = (state: string): string => {
  const stateAbbreviations: { [key: string]: string } = {
    California: 'CA',
    Virginia: 'VA',
    Colorado: 'CO',
    Connecticut: 'CT',
    Oregon: 'OR',
    Texas: 'TX'
  }
  return stateAbbreviations[state] || state
}
