import { statesWithAdditionalFields } from './stateConfigs'
import FormField from './FormField'
import { StateSpecificFieldsProps } from './types'

import Typography from '@components/Typography'

import React from 'react'

const StateSpecificFields = ({
  state,
  formData,
  onChange
}: StateSpecificFieldsProps) => {
  const stateConfig = statesWithAdditionalFields[state]
  if (!stateConfig) return null

  return (
    <div style={{ width: '100%' }}>
      <Typography as="h3" fontBold marginBottom="xs" typographyTheme="bodyLarge">
        {stateConfig.name} Additional Requirements
      </Typography>
      {Object.entries(stateConfig.fields).map(([fieldKey, field]) => {
        if (field.dependsOn === 'rightToAppeal' && !formData.rightToAppeal) {
          return null
        }

        return (
          <FormField
            key={fieldKey}
            fieldKey={fieldKey}
            field={field}
            value={formData[fieldKey]}
            onChange={onChange}
          />
        )
      })}
    </div>
  )
}

export default StateSpecificFields
