import CallToAction from '@components/Generic/CallToAction'
import { spacing } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Typography from '@components/Typography'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  wrapper: {
    width: 'min(100%, 500px)',
    marginInline: 'auto',
    paddingInline: spacing.md,
  },
  content: {
    textAlign: 'center',
  },
  button: {
    marginBlockStart: spacing.md,
  }
})

function SubmittedPrivacyRequestForm() {
  const defaultCta = {
    ctaText: 'Shop Now →',
    url: '/collections#cookware'
  }
  return (
    <Container flexCentered gap="2" paddingBlock="6" styleProp={styles.wrapper}>
      <Typography styleProp={styles.content} as="h2" typographyTheme="h2Primary">
        Thank you for your request.
      </Typography>
      <Typography styleProp={styles.content} as="p" typographyTheme="bodySmall">
        Please note that your request is being processed and we will make every effort to complete
        it as quickly as possible. The typical timeline for data removal is 30 days, but it may
        take longer depending on the complexity of the data involved.
      </Typography>
      <CallToAction
        href={defaultCta.url}
        styleProp={styles.button}
      >
        {defaultCta.ctaText}
      </CallToAction>
    </Container>
  )
}

export default SubmittedPrivacyRequestForm
