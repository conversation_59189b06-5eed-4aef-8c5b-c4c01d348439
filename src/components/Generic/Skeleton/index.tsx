import {
  spacing,
  colors,
  globalTokens as $
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const pulse = stylex.keyframes({
  '50%': {
    opacity: 0.5
  }
})

const shimmer = stylex.keyframes({
  to: {
    backgroundPosition: '180% 0'
  }
})

const styles = stylex.create({
  base: {
    height: spacing.xl,
    display: 'inline-block',
    borderRadius: `clamp(4px, 1vh, ${$.borderRadius})`,
    animationDuration: '1.15s',
    animationIterationCount: 'infinite',
    animationTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)'
  },
  pulse: {
    animationName: pulse,
    backgroundColor: colors.gray200,
  },
  shimmer: {
    animationName: shimmer,
    background: `linear-gradient(
      to right,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 0.75) 50%,
      rgba(255, 255, 255, 0) 80%),
      ${colors.gray200}`,
    backgroundRepeat: 'repeat-y',
    backgroundSize: 'clamp(50px, 2vw, 250px)',
    backgroundPosition: '0 0',
  },
  size: (width: number | string, height: number | string) => ({
    width,
    height
  })
})

const Skeleton = ({
  as: Component = 'span',
  children,
  animation = 'pulse',
  isLoaded = true,
  width = '100%',
  height = '100%',
  ...props
}: {
  as?: React.ElementType,
  children?: React.ReactNode,
  isLoaded?: boolean,
  animation?: 'pulse' | 'shimmer',
  width?: number | string,
  height?: number | string,
  [key: string]: any
}) => {
  if (isLoaded) {
    return children
  }
  return <Component {...props} {...stylex.props(styles.base, styles[animation], styles.size(width, height))} />
}

export default Skeleton
