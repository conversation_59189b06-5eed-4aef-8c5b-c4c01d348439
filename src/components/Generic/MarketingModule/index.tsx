import modules from './Layouts'

import Container from '@/components/layout/Container'
import { spacing } from '@/app/themeTokens.stylex'
import { ExtractedMarketingModule } from '@/lib/contentful/extractMarketingModules'

import React, { ReactNode } from 'react'
import * as stylex from '@stylexjs/stylex'

type MarketingModuleProps = {
  content: ExtractedMarketingModule
};

const style = stylex.create({
  container: {
    display: {
      default: 'none',
      '@media (min-width: 375px)': 'flex',
    },
    flexDirection: 'column',
    justifyContent: 'start',
    gap: spacing.sm,
    gridColumnStart: {
      default: 'span 1',
      '@media (min-width: 414px)': 'span 1',
    },
    minWidth: 0,
  }
})

const MarketingModule = ({ content }: MarketingModuleProps) => {
  if (!content?.layout) {
    return null
  }

  const Module = modules[content.layout] as (props: any) => ReactNode

  return (
    <Container styleProp={style.container}>
      <Module content={content} />
    </Container>
  )
}

export default MarketingModule
