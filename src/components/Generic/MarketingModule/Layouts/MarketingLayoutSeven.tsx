import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { ContentfulImage } from '@/lib/contentful/types/generic'
import getMediaType from '@/utils/getMediaType'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

export type MarketingLayoutSevenProps = {
  content: {
    theme?: ThemeColors,
    title: string | null
    fullLayout: boolean
    assetsBlock: ContentfulImage[] | null
  }
}

const styles = stylex.create({
  container: {
    position: 'relative',
    minHeight: {
      default: '360px',
      '@media (min-width: 1024px)': '488px',
    },
    padding: '40px 20px',
    borderRadius: '44px',
    overflow: 'hidden',
  },
  image: {
    borderRadius: {
      default: '11.5px',
      '@media (min-width: 1024px)': '20px',
    },
    width: '100%',
    height: 'auto',
    aspectRatio: '243 / 170',
  },
  fullImage: {
    height: '100%',
    width: '100%',
    position: 'absolute',
    objectFit: 'cover',
    aspectRatio: 'unset'
  }
})

const MarketingLayoutSeven = ({ content }: MarketingLayoutSevenProps) => {
  const {
    title,
    assetsBlock,
    fullLayout,
    theme
  } = content || {}

  return (
    <Container
      flex
      flexCentered
      gap="md"
      theme={theme}
      styleProp={styles.container}
    >
      {title && (
        <Typography
          as="h4"
          typographyTheme="h4Secondary"
          typographyThemeMobile="h6Secondary"
          textCentered
        >
          {title}
        </Typography>
      )}
      {assetsBlock && assetsBlock.map((asset) => {
        const mediaType = getMediaType(asset.url)

        if (mediaType === 'image') {
          return (
            <Image
              src={asset.url}
              alt={asset.title}
              key={asset.title}
              width={243}
              height={170}
              {...stylex.props(
                styles.image,
                fullLayout && styles.fullImage
              )}
            />
          )
        } if (mediaType === 'video') {
          return (
            <video
              key={asset.title}
              loop
              muted
              playsInline
              autoPlay
              width="100%"
              height="auto"
              {...stylex.props(
                styles.image,
                fullLayout && styles.fullImage
              )}
            >
              <source src={asset.url} type="video/mp4" />
            </video>
          )
        }
        return null
      })}
    </Container>
  )
}

export default MarketingLayoutSeven
