import { MarketingModuleQuoteBlock } from '../types'

import Icon from '@components/Generic/Icon'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { colors, spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

export type MarketingLayoutFourProps = {
  content: {
    theme?: ThemeColors
    copy: string | null
    quoteBlock: MarketingModuleQuoteBlock | null
  }
}

const styles = stylex.create({
  container: {
    minHeight: {
      default: '360px',
      '@media (min-width: 1024px)': '488px',
    },
    paddingBlock: {
      default: spacing.xl,
      '@media (min-width: 1024px)': spacing.lg,
    },
    paddingInline: {
      default: spacing.sm,
      '@media (min-width: 1024px)': spacing.md,
    },
    borderRadius: '44px',
  },
  desktopQuote: {
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'block',
    }
  },
  mobileQuote: {
    maxWidth: '275px',
    display: {
      default: 'block',
      '@media (min-width: 1024px)': 'none',
    }
  }
})

const starLength = 5
const iconColor = 'marigold'

const MarketingLayoutFour = ({ content }: MarketingLayoutFourProps) => {
  const {
    copy,
    quoteBlock,
    theme
  } = content
  const {
    logo,
    quote,
    author
  } = quoteBlock || {}

  return (
    <Container
      flex
      flexCentered
      gap="md"
      theme={theme}
      styleProp={styles.container}
    >
      <Container flex flexRow>
        {Array.from({ length: starLength }, (_, i) => i).map((key) => (
          <Icon
            key={key}
            name="Star"
            size="large"
            fill={colors[iconColor] as string}
          />
        ))}
      </Container>
      <Typography
        as="h6"
        typographyTheme="h6Secondary"
        textCentered
        styleProp={styles.mobileQuote}
      >
        {quote}
      </Typography>
      {copy && (
        <Typography
          as="h5"
          typographyTheme="h6Secondary"
          textCentered
          styleProp={styles.desktopQuote}
        >
          {copy}
        </Typography>
      )}
      <Container
        flex
        flexRow
        flexCentered
        gap="xs"
      >
        {logo?.url && (
          <Image
            src={logo.url}
            alt="testing"
            width={22}
            height={22}
          />
        )}
        <Typography
          as="p"
          typographyTheme="bodySmall"
          textCentered
          fontBold
        >
          {author}
        </Typography>
      </Container>
    </Container>
  )
}
export default MarketingLayoutFour
