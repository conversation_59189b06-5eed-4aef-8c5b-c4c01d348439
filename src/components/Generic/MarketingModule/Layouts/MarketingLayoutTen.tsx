import { MarketingModuleCtaBlock } from '../types'

import CallToAction from '@components/Generic/CallToAction'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'

import * as stylex from '@stylexjs/stylex'

export type MarketingLayoutTenProps = {
  content: {
    theme?: ThemeColors
    heading?: string
    copy: string | null
    ctaBlock: MarketingModuleCtaBlock | null
  }
}

const styles = stylex.create({
  container: {
    minHeight: {
      default: '360px',
      '@media (min-width: 1024px)': '488px',
    },
    padding: {
      default: '54px 10px',
      '@media (min-width: 1024px)': '40px 20px',
    },
    borderRadius: '44px',
  },
  copy: {
    maxWidth: '250px',
  }
})

const MarketingLayoutTen = ({ content }: MarketingLayoutTenProps) => {
  const {
    copy,
    ctaBlock,
    heading,
    theme
  } = content
  const {
    text,
    linkTo,
    variant
  } = ctaBlock || {}

  return (
    <Container
      flex
      flexCentered
      gap="md"
      theme={theme}
      styleProp={styles.container}
    >
      {copy && (
        <Typography
          as="h4"
          typographyTheme="h4Secondary"
          typographyThemeMobile="h6Primary"
          fontBold
          textCentered
        >
          {heading}
        </Typography>
      )}
      {copy && (
        <Typography
          as="p"
          typographyTheme="bodyLarge"
          typographyThemeMobile="captionLarge"
          textCentered
          styleProp={styles.copy}
        >
          {copy}
        </Typography>
      )}
      {ctaBlock && (
        <CallToAction
          variant={variant}
          href={linkTo}
        >
          {text}
        </CallToAction>
      )}
    </Container>
  )
}
export default MarketingLayoutTen
