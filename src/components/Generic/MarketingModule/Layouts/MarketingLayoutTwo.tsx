import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

export type MarketingLayoutTwoProps = {
  content: {
    theme?: ThemeColors
    title?: string
    blocks: {
      title: string | undefined;
    }[] | null
  }
}

const styles = stylex.create({
  container: {
    minHeight: {
      default: '0',
      '@media (min-width: 1024px)': '488px'
    },
    borderRadius: '44px',
    paddingBlock: {
      default: spacing.xl,
      '@media (min-width: 1024px)': spacing.lg
    },
    paddingInline: {
      default: '0',
      '@media (min-width: 1024px)': spacing.md
    },
  },
  heading: {
    marginBottom: '32px',
    maxWidth: {
      default: '126px',
      '@media (min-width: 1024px)': '228px'
    }
  },
  list: {
    listStyle: 'none',
    padding: 0,
    margin: 0,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'start',
    gap: '22px'
  },
  listItem: {
    display: 'flex',
    alignItems: 'center',
    flexWrap: 'nowrap',
    gap: spacing.xs
  },
  caption: {
    fontSize: {
      default: '10px',
      '@media (min-width: 1024px)': 'inherit'
    },
    maxWidth: {
      default: '80px',
      '@media (min-width: 1024px)': 'inherit'
    },
    marginLeft: {
      default: spacing.xxs,
      '@media (min-width: 1024px)': 'inherit'
    }
  },
  svgContainer: {
    display: 'flex',
    flexWrap: 'nowrap',
    gap: spacing.xs,
    alignItems: 'center'
  },
  check: {
    flexShrink: 0,
    width: '24px',
    height: '24px'
  }
})

const MarketingLayoutTwo = ({ content }: MarketingLayoutTwoProps) => {
  const {
    blocks,
    title,
    theme
  } = content

  return (
    <Container
      flex
      flexCentered
      gap="3"
      styleProp={styles.container}
      theme={theme}
    >
      <Container flex flexCentered>
        <Typography
          as="h5"
          typographyTheme="h5Secondary"
          typographyThemeMobile="bodyLarge"
          textCentered
          fontBold
          styleProp={styles.heading}
        >
          {title}
        </Typography>
        <ul {...stylex.props(styles.list)}>
          {blocks && blocks.map((item) => (
            <li
              key={item.title || ''}
              {...stylex.props(styles.listItem)}
            >
              <Container styleProp={styles.svgContainer}>
                <svg {...stylex.props(styles.check)} viewBox="0 0 24 24" stroke="currentColor" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M23.25 12C23.25 18.2132 18.2132 23.25 12 23.25C5.7868 23.25 0.75 18.2132 0.75 12C0.75 5.7868 5.7868 0.75 12 0.75C18.2132 0.75 23.25 5.7868 23.25 12Z" strokeWidth="1.5" />
                  <path d="M6.62109 11.9996L10.1497 15.9785L18.0356 8.04492" strokeWidth="1.5" />
                </svg>
                <Typography
                  as="p"
                  typographyTheme="bodyLarge"
                  typographyThemeMobile="captionSmall"
                  styleProp={styles.caption}
                >
                  {item.title}
                </Typography>
              </Container>
            </li>
          ))}
        </ul>
      </Container>
    </Container>
  )
}

export default MarketingLayoutTwo
