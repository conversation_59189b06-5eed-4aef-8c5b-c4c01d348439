import { MarketingModuleCtaBlock } from '../types'

import Icon from '@components/Generic/Icon'
import CallToAction from '@components/Generic/CallToAction'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

export type MarketingLayoutFiveProps = {
  content: {
    theme?: ThemeColors
    copy: string | null
    ctaBlock?: MarketingModuleCtaBlock | null
  }
}

const styles = stylex.create({
  container: {
    minHeight: {
      default: '360px',
      '@media (min-width: 1024px)': '488px',
    },
    padding: {
      default: '40px 6px',
      '@media (min-width: 1024px)': '40px 20px',
    },
    borderRadius: '44px',
  },
  cta: {
    width: 'fit-content',
    fontWeight: 'bold',
    fontSize: {
      default: '14px',
      '@media (min-width: 1024px)': 'inherit',
    }
  }
})

const starLength = 5
const iconColor = 'marigold'

const MarketingLayoutFive = ({ content }: MarketingLayoutFiveProps) => {
  const {
    copy,
    ctaBlock,
    theme
  } = content

  return (
    <Container
      flex
      flexCentered
      gap="md"
      theme={theme as ThemeColors}
      styleProp={styles.container}
    >
      <Container flex flexRow>
        {Array.from({ length: starLength }, (_, i) => i).map((key) => (
          <Icon
            key={key}
            name="Star"
            size="large"
            fill={colors[iconColor] as string}
          />
        ))}
      </Container>
      {copy && (
        <Typography
          as="h5"
          typographyTheme="h5Secondary"
          typographyThemeMobile="h6Secondary"
          textCentered
        >
          {copy}
        </Typography>
      )}
      {ctaBlock && (
        <CallToAction
          variant="transparent"
          href={ctaBlock.linkTo}
          {...stylex.props(styles.cta)}
        >
          {ctaBlock.text}
        </CallToAction>
      )}
    </Container>
  )
}
export default MarketingLayoutFive
