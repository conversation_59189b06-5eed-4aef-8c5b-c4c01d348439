import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing } from '@/app/themeTokens.stylex'
import { ContentfulImage } from '@/lib/contentful/types/generic'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

export type MarketingLayoutSixProps = {
  content: {
    theme?: ThemeColors
    copy: string | null
    asset: ContentfulImage | null
  }
}

const styles = stylex.create({
  container: {
    minHeight: {
      default: '360px',
      '@media (min-width: 1024px)': '488px',
    },
    paddingBlock: {
      default: spacing.xl,
      '@media (min-width: 1024px)': spacing.lg,
    },
    paddingInline: {
      default: spacing.xs,
      '@media (min-width: 1024px)': spacing.md,
    },
    borderRadius: '44px',
  },
  copy: {
    maxWidth: '275px',
  }
})

const MarketingLayoutSix = ({ content }: MarketingLayoutSixProps) => {
  const {
    copy,
    asset,
    theme
  } = content
  const {
    width,
    height,
    url,
    title
  } = asset || {}

  return (
    <Container
      flex
      flexCentered
      gap="md"
      theme={theme}
      styleProp={styles.container}
    >
      {copy && (
        <Typography
          as="h5"
          typographyTheme="h5Secondary"
          typographyThemeMobile="bodyLarge"
          textCentered
          fontBold
          styleProp={styles.copy}
        >
          {copy}
        </Typography>
      )}
      <Container
        flex
        flexRow
        flexCentered
        gap="xs"
      >
        {url && (
          <Image
            src={url}
            alt={title || ''}
            width={width || '120'}
            height={height || '24'}
          />
        )}
      </Container>
    </Container>
  )
}

export default MarketingLayoutSix
