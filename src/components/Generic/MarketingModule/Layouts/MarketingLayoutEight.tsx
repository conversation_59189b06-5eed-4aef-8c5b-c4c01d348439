import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing } from '@/app/themeTokens.stylex'
import { ContentfulImage } from '@/lib/contentful/types/generic'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

export type MarketingLayoutEightProps = {
  content: {
    theme?: ThemeColors;
    title: string | null;
    assetsBlock: ContentfulImage[] | null;
  };
};

const styles = stylex.create({
  container: {
    minHeight: {
      default: '360px',
      '@media (min-width: 1024px)': '488px',
    },
    padding: {
      default: '40px 6px',
      '@media (min-width: 407px)': '40px 18px',
      '@media (min-width: 1024px)': '40px 20px',
    },
    borderRadius: '44px',
  },
  responsiveContainer: {
    gap: {
      default: spacing.xs,
      '@media (min-width: 1024px)': '16px',
    },
    maxWidth: {
      default: '136px',
      '@media (min-width: 1024px)': '100%',
    }
  },
  image: {
    borderRadius: '50%',
    width: {
      default: '64px',
      '@media (min-width: 1024px)': '112px',
    },
    height: 'auto',
    aspectRatio: '1 / 1',
  },
})

const MarketingLayoutEight = ({ content }: MarketingLayoutEightProps) => {
  const {
    title,
    assetsBlock,
    theme
  } = content

  return (
    <Container
      flex
      flexCentered
      gap="md"
      theme={theme}
      styleProp={styles.container}
    >
      {title && (
        <Typography
          as="h4"
          typographyTheme="h4Secondary"
          typographyThemeMobile="h6Secondary"
          textCentered
        >
          {title}
        </Typography>
      )}
      <Container
        flex
        flexRow
        flexCentered
        styleProp={styles.responsiveContainer}
      >
        {assetsBlock && assetsBlock.map((asset) => (
          <Image
            src={asset.url}
            alt={asset.title}
            key={asset.title}
            height={112}
            width={112}
            {...stylex.props(styles.image)}
          />
        ))}
      </Container>
    </Container>
  )
}

export default MarketingLayoutEight
