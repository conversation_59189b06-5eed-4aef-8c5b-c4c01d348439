import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

export type MarketingLayoutOneProps = {
  content: {
    theme?: ThemeColors
    blocks?: {
      title?: string
      description?: string | null
      asset: {
        title: string
        url: string
      } | null
    }[] | null
  } | null
}

const styles = stylex.create({
  container: {
    paddingInline: {
      default: spacing.sm,
      '@media (min-width: 1024px)': '3'
    },
    borderRadius: '44px',
    justifyContent: {
      default: 'space-evenly',
      '@media (min-width: 1024px)': 'center'
    }
  },
  copy: {
    fontWeight: {
      default: 'bold',
      '@media (min-width: 1024px)': 'inherit'
    }
  },
  mobileImage: {
    display: {
      default: 'block',
      '@media (min-width: 1024px)': 'none'
    },
    marginBottom: spacing.xxs
  },
  desktopImage: {
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'block'
    }
  }
})

export const MobileImage = () => (
  <Image
    src="/circledCheck.svg"
    alt="checkmark"
    width={24}
    height={24}
    {...stylex.props(styles.mobileImage)}
  />
)

const MarketingLayoutOne = ({ content }: MarketingLayoutOneProps) => {
  if (!content) {
    return null
  }

  const { blocks, theme } = content

  return (
    <Container
      flex
      paddingBlock="5"
      gap="3"
      styleProp={styles.container}
      theme={theme}
    >
      {blocks && blocks.map((block) => {
        const {
          title,
          description,
          asset
        } = block

        return (
          <Container
            flex
            flexCentered
            gap="1"
          >
            <MobileImage />
            {asset && (
            <Image
              src={asset.url}
              alt={asset.title || ''}
              width={80}
              height={80}
              {...stylex.props(styles.desktopImage)}
            />
            )}
            <Container flex>
              <Typography
                as="h6"
                typographyTheme="h6Secondary"
                typographyThemeMobile="bodyLarge"
                textCentered
                fontBold
              >
                {title}
              </Typography>
              {description && (
              <Typography
                as="h6"
                typographyTheme="h6Primary"
                typographyThemeMobile="bodyLarge"
                textCentered
                styleProp={styles.copy}
              >
                {description}
              </Typography>
              )}
            </Container>
          </Container>
        )
      })}
    </Container>
  )
}

export default MarketingLayoutOne
