import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { spacing } from '@/app/themeTokens.stylex'
import { ContentfulImage } from '@/lib/contentful/types/generic'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

export type MarketingLayoutNineProps = {
  content: {
    theme?: ThemeColors
    title: string | null
    assetsBlock: ContentfulImage[] | null
    contentBlocks: {
      title: string | null;
      copy: string | null;
    }[] | null
  }
}

const styles = stylex.create({
  container: {
    borderRadius: '44px',
    paddingBlock: {
      default: '46px',
      '@media (min-width: 1024px)': spacing.lg,
    },
    paddingInline: {
      default: spacing.md,
      '@media (min-width: 1024px)': spacing.md,
    }
  },
  mobileContainer: {
    display: {
      default: 'flex',
      '@media (min-width: 1024px)': 'none',
    },
    gap: '28px'
  },
  desktopContainer: {
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'flex',
    }
  },
  image: {
    maxWidth: '100%',
    height: 'auto'
  }
})

const MarketingLayoutNine = ({ content }: MarketingLayoutNineProps) => {
  const {
    title,
    assetsBlock,
    contentBlocks,
    theme
  } = content

  return (
    <Container
      flex
      flexCentered
      theme={theme}
      gap="md"
      styleProp={styles.container}
    >
      <Container
        flex
        flexCentered
        styleProp={styles.mobileContainer}
      >
        {contentBlocks && contentBlocks.map((block) => (
          <Container flex gap="xs">
            <Typography
              as="h6"
              textCentered
              typographyTheme="h6Secondary"
            >
              {block.title}
            </Typography>
            <Typography
              as="p"
              textCentered
              typographyTheme="captionLarge"
            >
              {block.copy}
            </Typography>
          </Container>
        ))}
      </Container>
      <Container
        flex
        flexCentered
        gap="md"
        styleProp={styles.desktopContainer}
      >
        <Typography
          as="h6"
          typographyTheme="h6Primary"
          textCentered
        >
          {title}
        </Typography>
        {assetsBlock && assetsBlock.map((asset) => (
          <Image
            src={asset.url}
            alt={asset.title}
            height={330}
            width={238}
            {...stylex.props(
              styles.image
            )}
          />
        ))}
      </Container>
    </Container>
  )
}

export default MarketingLayoutNine
