import { imageProps } from '@components/Generic/Card/types'
import Typography from '@components/Typography'
import Card from '@components/Generic/Card'
import Container from '@components/layout/Container'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import Link from 'next/link'
import React from 'react'

export const styles = stylex.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'flex-start',
    paddingBlock: spacing.md,
    opacity: 0,
    zIndex: 1,
    transition: 'opacity 0.3s',
    // eslint-disable-next-line @stylexjs/valid-styles
    ':hover': {
      opacity: 1
    },
    // eslint-disable-next-line @stylexjs/valid-styles
    ':hover + a': {
      opacity: 0
    }
  },
  overlayText: {
    color: '#fff',
    display: 'flex',
    flexDirection: 'column',
    gap: spacing.xs,
    fontSize: '1.5rem',
    fontWeight: 700,
    transition: 'opacity 0.3s',
    opacity: 1,
    height: '100%',
  },
  bottomText: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    display: 'flex',
    flexDirection: 'column',
    gap: spacing.xs,
    padding: spacing.md,
    fontWeight: 700,
  },
  bottomLink: {
    marginTop: 'auto',
  }
})

type CategoryProductCardProps = {
  headliner?: string,
  title: string,
  product_name: string,
  link: string,
  image: imageProps
  ratio?: string
}

const CategoryProductCard = ({
  headliner,
  title,
  product_name,
  link,
  image,
  ratio
}: CategoryProductCardProps) => (
  <Card image={image} ratio={ratio}>
    <Link href={link} {...stylex.props(styles.overlay)}>
      <Container as="div" styleProp={styles.overlayText}>
        <Typography as="span" size="bodySmall">{headliner}</Typography>
        <Typography as="span" size="h6" fontSecondary>{title}</Typography>
        <Typography as="span" size="bodyLarge" styleProp={styles.bottomLink}>Shop Now <span>→</span></Typography>
      </Container>
    </Link>
    <Link href={link} {...stylex.props(styles.bottomText)}>
      <Typography as="span" size="h6" fontSecondary>{product_name}</Typography>
      <Typography as="span" size="bodyLarge">Shop Now <span>→</span></Typography>
    </Link>
  </Card>
)

export default CategoryProductCard
