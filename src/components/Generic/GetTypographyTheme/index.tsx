type GetTypographyThemeProps = {
  fontSizes: string;
  fontFamily: string;
  typographyTheme?: string;
};

// Maps font sizes to primary or secondary typography themes based on the font family.
const getPrimarySecondaryMap = (fontFamily: string): Record<string, string> => ({
  h1: fontFamily.toLowerCase().includes('secondary') ? 'h1Secondary' : 'h1Primary',
  h2: fontFamily.toLowerCase().includes('secondary') ? 'h2Secondary' : 'h2Primary',
  h3: fontFamily.toLowerCase().includes('secondary') ? 'h3Secondary' : 'h3Primary',
  h4: fontFamily.toLowerCase().includes('secondary') ? 'h4Secondary' : 'h4Primary',
  h5: fontFamily.toLowerCase().includes('secondary') ? 'h5Secondary' : 'h5Primary',
  h6: fontFamily.toLowerCase().includes('secondary') ? 'h6Secondary' : 'h6Primary',
})

// Defines a set of typography themes for other font sizes.
const otherThemes: Record<string, string> = {
  subheading: 'subheading',
  bodyLarge: 'bodyLarge',
  bodySmall: 'bodySmall',
  bodyXSmall: 'bodyXSmall',
  captionLarge: 'captionLarge',
  captionSmall: 'captionSmall',
}

// Determines the appropriate typography theme based on font size and font family.
const determineTypographyTheme = (fontSize: string, fontFamily: string): string => {
  const primarySecondaryMap = getPrimarySecondaryMap(fontFamily)
  return primarySecondaryMap[fontSize] || otherThemes[fontSize] || 'bodyLarge'
}

// Main function to get typography theme settings based on provided properties.
const GetTypographyTheme = (settings: GetTypographyThemeProps) => {
  if (settings) {
    const { fontSizes, fontFamily } = settings
    const typographyTheme = fontSizes && fontFamily
      ? determineTypographyTheme(fontSizes, fontFamily)
      : 'bodyLarge'
    return { ...settings, typographyTheme }
  }
  return null
}

export default GetTypographyTheme
