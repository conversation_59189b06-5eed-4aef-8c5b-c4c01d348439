import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  strikethrough: { textDecoration: 'line-through' },
  button: {
    width: '100%',
    padding: '15px 40px',
    outline: '1px solid blue',
    borderRadius: '50px',
  },
})

type VariantInfoProps = {
  title: string
  price: number
  compareAtPrice?: number
  styleProp?: {}
}

const VariantInfo = ({
  title,
  price,
  compareAtPrice,
  styleProp = {}
}: VariantInfoProps) => (
  <Container
    flex
    flexCentered
    styleProp={styleProp && styleProp}
  >
    <Typography
      as="h5"
      typographyTheme="h5Primary"
      typographyThemeMobile="bodyLarge"
    >
      {title}
    </Typography>
    <Container
      flex
      flexRow
      gap="1"
    >
      <span>${price}</span>
      {compareAtPrice && <span {...stylex.props(styles.strikethrough)}>${compareAtPrice}</span>}
    </Container>
  </Container>
)

export default VariantInfo
