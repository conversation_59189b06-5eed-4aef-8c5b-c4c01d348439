import GenerateUTMData from './utils/generateUTMData'

import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@/components/layout/Container'
import { VariantProps } from '@/components/Product/types'
import CallToAction from '@components/Generic/CallToAction'

import { ComponentProps, ReactNode } from 'react'
import * as stylex from '@stylexjs/stylex'

type CtaContainerProps = {
  theme?: ThemeColors
  styleProp?: {}
  variant: VariantProps
  slug: string
}

const renderLinkwithStyles = (
  children: ReactNode,
  ctaStyles: stylex.StyleXStyles,
  props: ComponentProps<typeof CallToAction>
) => (
  <a
    href={props.href}
    role="button"
    {...stylex.props(ctaStyles)}
  >
    {children}
  </a>
)

const CtaContainer = ({
  theme = 'gray200',
  styleProp = {},
  variant,
  slug
}: CtaContainerProps) => {
  const data = GenerateUTMData(variant, slug)

  return (
    <Container
      flex
      flexCentered
      alignCentered
      gap="2"
      theme={theme}
      styleProp={styleProp && styleProp}
    >
      <CallToAction
        theme="navy"
        fullWidth
        href={data?.email.link}
        custom={renderLinkwithStyles}
      >
        {data?.email.label}
      </CallToAction>
      <CallToAction
        theme="navy"
        fullWidth
        href={data?.sms.link}
        custom={renderLinkwithStyles}
      >
        {data?.sms.label}
      </CallToAction>
    </Container>
  )
}

export default CtaContainer
