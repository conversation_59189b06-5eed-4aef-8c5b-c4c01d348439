import { VariantProps } from '@/components/Product/types'

type SMSData = {
  link: string
  label:string
}

type EmailData = {
  link: string
  label: string
}

type UTMData = {
  sms: SMSData
  email: EmailData
};

const GenerateUTMData = (variant: VariantProps, slug: string): UTMData | null => {
  if (variant === undefined) {
    return null
  }
  const { swatch } = variant
  const { swatchType } = swatch
  const variantSlug = swatch.slug

  const productLink = variantSlug ? `https://www.carawayhome.com/products/${slug}?${swatchType}=${variantSlug}` : `https://www.carawayhome.com/products/${slug}`

  const utmInfoSms = '&utm_source=site&utm_medium=sms&utm_campaign=drop-a-hint'
  const textMessageContent = `Hey! I decided to take the hard work off your plate this holiday season. Here’s what I’ve been eyeing from Caraway: ${productLink}${utmInfoSms}`
  const smsLink = `sms:?&body=${encodeURIComponent(textMessageContent)}`

  const utmInfoEmail = '&utm_source=site&utm_medium=email&utm_campaign=drop-a-hint'
  const subjectLine = encodeURIComponent('Your Hint Is Here')
  const emailContent = encodeURIComponent(
    'Hey!\n\n' +
    `I decided to take the hard work off your plate this holiday season. Here’s what I’ve been eyeing from Caraway: ${productLink}${utmInfoEmail}`
  )
  const mailLink = `mailTo:?&subject=${subjectLine}&body=${emailContent}`

  return {
    sms: {
      link: smsLink,
      label: 'SEND VIA SMS →'
    },
    email: {
      link: mailLink,
      label: 'SEND VIA EMAIL →'
    }
  }
}

export default GenerateUTMData
