import Container from '@/components/layout/Container'
import SwatchIcon from '@/components/Product/ProductSwatches/SwatchIcon'
import { SwatchProps } from '@/components/Product/types'
import Typography from '@/components/Typography'

type SwatchInfoProps = {
  swatch: SwatchProps
  styleProp?: {}
}

const SwatchInfo = ({ swatch, styleProp = {} }: SwatchInfoProps) => (
  <Container
    flex
    flexRow
    flexCentered
    gap="2"
    styleProp={styleProp && styleProp}
  >
    <SwatchIcon
      swatch={swatch}
      attribute={swatch.swatchType}
      selectedSwatch={swatch}
    />
    <Typography
      as="p"
      typographyTheme="bodySmall"
    >
      {swatch.presentation}
    </Typography>
  </Container>
)

export default SwatchInfo
