import { spacing } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  heading: {
    marginBottom: {
      default: spacing.xs,
      '@media (min-width: 414)': spacing.sm,
    }
  }
})

type HeaderBlockProps = {
  styleProp?: {}
}

const HeaderBlock = ({ styleProp = {} }: HeaderBlockProps) => (
  <Container
    flex
    styleProp={[styleProp && styleProp]}
  >
    <Typography
      as="h5"
      typographyTheme="h5Secondary"
      typographyThemeMobile="h6Secondary"
      fontBold
      textCentered
      styleProp={styles.heading}
    >
      Like What You See?
    </Typography>
    <Typography
      as="p"
      typographyTheme="bodyLarge"
      textCentered
    >
      Drop a hint and let that special someone know what you’ve got on your wish list this season.
    </Typography>
  </Container>
)

export default HeaderBlock
