import VariantInfo from './Components/VariantInfo'
import Header<PERSON>lock from './Components/HeaderBlock'
import SwatchInfo from './Components/SwatchInfo'
import CtaContainer from './Components/CtaContainer'

import { CloseDialogButton } from '@components/Generic/Dialog/Layout1/DialogWrapper'
import { spacing } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import { VariantProps } from '@/components/Product/types'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  desktopOnly: {
    display: {
      default: 'none',
      '@media (min-width: 650px)': 'flex',
    },
    position: 'relative',
    minHeight: '450px',
    width: '98vw',
    maxWidth: '905px',
  },
  variantCard: {
    minWidth: '240px',
    height: '100%',
    flex: '3 1 325px'
  },
  body: {
    paddingInline: '1rem',
    flex: '10 3 595px'
  },
  header: {
    maxWidth: '375px',
    marginBottom: spacing.lg
  },
  productImage: {
    mixBlendMode: 'darken',
    marginBottom: spacing.md,
    maxWidth: '240px'
  },
  swatch: {
    width: '100%',
    justifyContent: 'center',
  },
  button: { maxWidth: '230px' }
})

type DropAHintDesktopProps = {
  title: string
  variant: VariantProps
  slug: string
}

const DropAHintDesktop = ({
  title,
  variant,
  slug
}: DropAHintDesktopProps) => {
  const {
    swatch,
    price,
    compareAtPrice,
    gallery
  } = variant

  const displayImage = gallery?.items?.[0]

  return (
    <Container
      flexRow
      noWrap
      alignCentered
      styleProp={styles.desktopOnly}
    >
      <CloseDialogButton />
      <Container
        flex
        flexCentered
        theme="offWhite"
        styleProp={styles.variantCard}
      >
        <VariantInfo
          title={title}
          price={price}
          compareAtPrice={compareAtPrice}
        />
        <Image
          src={displayImage?.url}
          alt={displayImage?.title}
          layout="responsive"
          width={240}
          height={180}
          {...stylex.props(styles.productImage)}
        />
        <SwatchInfo
          swatch={swatch}
          styleProp={styles.swatch}
        />
      </Container>
      <Container
        flex
        alignCentered
        styleProp={styles.body}
        theme="gray200"
      >
        <HeaderBlock styleProp={styles.header} />
        <CtaContainer
          styleProp={styles.button}
          variant={variant}
          slug={slug}
        />
      </Container>
    </Container>
  )
}

export default DropAHintDesktop
