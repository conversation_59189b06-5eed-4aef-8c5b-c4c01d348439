import HeaderBlock from './Components/HeaderBlock'
import VariantInfo from './Components/VariantInfo'
import SwatchInfo from './Components/SwatchInfo'
import CtaContainer from './Components/CtaContainer'

import { CloseDialogButton } from '@components/Generic/Dialog/Layout1/DialogWrapper'
import Container from '@/components/layout/Container'
import { VariantProps } from '@/components/Product/types'
import { colors, spacing } from '@/app/themeTokens.stylex'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 650px)'

const styles = stylex.create({
  outerContainer: {
    height: '100%',
    padding: '10px',
    background: 'none',
    display: {
      default: 'flex',
      [DESKTOP]: 'none',
    }
  },
  mobileOnly: {
    display: {
      default: 'flex',
      [DESKTOP]: 'none',
    },
    width: '100%',
    flexDirection: 'column',
    justifyContent: {
      default: 'flex-start',
      '@media (min-width: 414px)': 'center',
    },
    overflow: {
      default: 'auto',
      '@media (min-width: 375px)': 'hidden',
    },
    paddingTop: spacing.lg,
    backgroundColor: colors.offWhite,
    paddingInline: spacing.md
  },
  productImage: {
    mixBlendMode: 'darken',
    marginBottom: {
      default: spacing.xxs,
      '@media (min-width: 414px)': spacing.sm
    },
    maxWidth: '240px'
  },
  header: {
    marginBottom: {
      default: spacing.xs,
      '@media (min-width: 414px)': spacing.lg
    }
  },
  variantTitle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: {
      default: spacing.sm,
      '@media (min-width: 414px)': spacing.md
    }
  },
  swatch: {
    width: '100%',
    justifyContent: 'flex-start',
    marginBottom: spacing.md
  },
  ctaContainer: {
    display: {
      default: 'flex',
      '@media (min-width: 650px)': 'none',
    },
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.md,
    paddingInline: spacing.md,
    paddingBlock: spacing.lg
  }
})

type DropAHintMobileProps = {
  variant: VariantProps
  title: string
  slug: string
}

const DropAHintMobile = ({
  variant,
  title,
  slug
}: DropAHintMobileProps) => {
  const {
    gallery,
    price,
    compareAtPrice,
    swatch
  } = variant || null

  const displayImage = gallery?.items?.[0]

  return (
    <Container
      flex
      noWrap
      styleProp={styles.outerContainer}
    >
      <Container
        flex
        noWrap
        alignCentered
        styleProp={styles.mobileOnly}
      >
        <CloseDialogButton />
        <HeaderBlock styleProp={styles.header} />
        <Image
          src={displayImage?.url}
          alt={displayImage?.title}
          layout="responsive"
          width={240}
          height={180}
          {...stylex.props(styles.productImage)}
        />
        <VariantInfo
          title={title}
          price={price}
          compareAtPrice={compareAtPrice}
          styleProp={styles.variantTitle}
        />
        <SwatchInfo swatch={swatch} styleProp={styles.swatch} />
      </Container>
      <CtaContainer
        styleProp={styles.ctaContainer}
        variant={variant}
        slug={slug}
      />
    </Container>

  )
}

export default DropAHintMobile
