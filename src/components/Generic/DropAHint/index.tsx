'use client'

import DropAHintDesktop from './DropAHintDesktop'
import DropAHintMobile from './DropAHintMobile'

import Dialog from '@components/Generic/Dialog'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { useDialogContext } from '@/providers/config/DialogContext'
import { colors, spacing } from '@/app/themeTokens.stylex'
import { VariantProps } from '@/components/Product/types'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  container: {
    marginTop: spacing.md
  },
  link: {
    color: colors.perracotta,
    cursor: 'pointer',
  },
})

type DropAHintProps = {
  variant: VariantProps,
  title: string
  slug: string
}

const DropAHint = ({
  variant,
  title,
  slug,
}: DropAHintProps) => {
  const { triggerDialog } = useDialogContext()

  const handleClick = () => {
    triggerDialog('DropAHint')
  }

  return (
    <Container
      flex
      flexRow
      noWrap
      alignCentered
      gap="2"
      styleProp={styles.container}
    >
      <Image
        src="https://images.ctfassets.net/dfp1t53x5luq/2iZJeTjzqYp1Y9UQ22tmoM/804126af54040781d8cf4e3477adbfa9/icon-gift.svg"
        alt="Drop a hint"
        width={22}
        height={22}
      />
      <Typography
        as="p"
        typographyTheme="bodySmall"
      >
        Drop a Hint: Let someone know this is on your wishlist
        <span onClick={handleClick} {...stylex.props(styles.link)}> here.</span>
      </Typography>
      <Dialog
        layout="DropAHint"
        id="DropAHint"
        header=""
        content={(
          <DropAHintDesktop
            variant={variant}
            title={title}
            slug={slug}
          />
        )}
        mobileContent={(
          <DropAHintMobile
            variant={variant}
            title={title}
            slug={slug}
          />
        )}
        subheader=""
      />
    </Container>
  )
}

export default DropAHint
