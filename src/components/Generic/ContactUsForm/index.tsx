'use client'

import SubmittedContactUs from './SubmitedContactUsForm'

import { spacing, fontSizes, colors } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Typography from '@components/Typography'

import { useForm } from '@formspree/react'
import { useForm as reactUseForm } from 'react-hook-form'
import * as stylex from '@stylexjs/stylex'

import type { ReactNode } from 'react'

export type FormFields = {
  fullName: string;
  email: string;
  phoneNumber: string;
  helpTopic: string;
  orderNumber: string;
  message: string;
};

export type CallToActionType = {
  ctaText: string;
  buttonStyle: 'primary' | 'secondary';
  url: string;
};

export type Section = {
  id: string;
  callToAction?: CallToActionType;
};

export type Props = {
  id: string;
  children?: ReactNode;
};

const HELP_TOPICS = [
  'Accessibility Support',
  'Feedback & Suggestions',
  'General',
  'Influencer & Ambassador Program',
  'Marketing & Promotions',
  'Product Inquiry',
  'Shipping & Returns',
  'Trade Program/Corporate Sales',
  'Warranty'
]

export const styles = stylex.create({
  formWrapper: {
    paddingInline: spacing.md,
    marginInline: 'auto',
    maxWidth: '500px',
    background: colors.white,
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.gray200,
  },
  inputGroup: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
  },
  label: {
    display: 'flex',
    flexDirection: 'column',
    gap: spacing.xs,
    fontSize: fontSizes.sm,
    color: colors.navy,
    width: '100%',
  },
  input: {
    height: '40px',
    padding: '8px 12px',
    borderRadius: '4px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.gray200,
    fontSize: fontSizes.sm,
    width: '100%',
    outlineColor: {
      default: 'transparent',
      ':focus': colors.navy,
    },
    outlineWidth: {
      default: '0px',
      ':focus': '2px',
    },
    outlineStyle: {
      default: 'none',
      ':focus': 'solid',
    },
  },
  errorInput: {
    borderColor: colors.red700,
  },
  select: {
    height: '40px',
    padding: '8px 12px',
    borderRadius: '4px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.gray200,
    fontSize: fontSizes.sm,
    width: '100%',
    backgroundColor: colors.white,
  },
  textarea: {
    padding: '12px',
    borderRadius: '4px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.gray200,
    fontSize: fontSizes.sm,
    width: '100%',
    minHeight: '120px',
    resize: 'vertical',
  },
  errorMessage: {
    color: colors.red700,
    marginTop: spacing.xs,
  },
  submitButton: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '48px',
    paddingInline: '32px',
    backgroundColor: colors.marigold,
    color: colors.navy,
    borderRadius: '24px',
    borderWidth: '0',
    borderStyle: 'none',
    borderColor: 'transparent',
    fontSize: fontSizes.sm,
    cursor: {
      default: 'pointer',
      ':disabled': 'not-allowed',
    },
    marginTop: spacing.md,
    opacity: {
      default: 1,
      ':hover': 0.9,
      ':disabled': 0.7,
    },
  },
  spinner: {
    width: '24px',
    height: '24px',
  },

})

// eslint-disable-next-line complexity
function ContactUsForm({ id }: Props) {
  const [formspreeState, formSpreeSubmit] = useForm(id || process.env.NEXT_PUBLIC_FORMSPREE_ID || '')
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = reactUseForm<FormFields>()

  const onSubmit = (data: FormFields) => {
    const formData = new FormData()
    Array.from(Object.entries(data)).forEach(([key, value]) => {
      formData.append(key, value)
    })

    formSpreeSubmit(formData)
  }

  if (formspreeState.succeeded) {
    return (
      <Container paddingBlock="6">
        <SubmittedContactUs />
      </Container>
    )
  }

  return (
    <Container paddingBlock="6">
      <Container flex paddingBlock="4" styleProp={styles.formWrapper}>
        <Container as="form" flex gap="2" onSubmit={handleSubmit(onSubmit)}>
          <Container>
            <label {...stylex.props(styles.label)} htmlFor="fullName">
              Full Name
              <input
                id="fullName"
                type="text"
                aria-invalid={!!errors.fullName}
                {...register('fullName', { required: 'Please enter your name' })}
                {...stylex.props(
                  styles.input,
                  errors.fullName && styles.errorInput
                )}
              />
            </label>
            {errors.fullName && (
              <Typography as="span" typographyTheme="bodySmall" styleProp={styles.errorMessage}>
                {errors.fullName.message}
              </Typography>
            )}
          </Container>

          <Container>
            <label {...stylex.props(styles.label)} htmlFor="email">
              Email
              <input
                id="email"
                type="email"
                aria-invalid={!!errors.email}
                {...register('email', {
                  required: 'Please enter your email',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Please enter a valid email address'
                  }
                })}
                {...stylex.props(
                  styles.input,
                  errors.email && styles.errorInput
                )}
              />
            </label>
            {errors.email && (
            <Typography as="span" typographyTheme="bodySmall" styleProp={styles.errorMessage}>
              {errors.email.message}
            </Typography>
            )}
          </Container>

          <Container>
            <label {...stylex.props(styles.label)} htmlFor="phoneNumber">
              Phone Number
              <input
                id="phoneNumber"
                type="tel"
                {...register('phoneNumber')}
                {...stylex.props(styles.input)}
              />
            </label>
          </Container>

          <Container>
            <label {...stylex.props(styles.label)} htmlFor="helpTopic">
              What Do You Need Help With?
              <select
                id="helpTopic"
                aria-invalid={!!errors.helpTopic}
                {...register('helpTopic', { required: 'Please select a topic' })}
                {...stylex.props(
                  styles.select,
                  errors.helpTopic && styles.errorInput
                )}
              >
                <option value="">Select a topic</option>
                {HELP_TOPICS.map((topic) => (
                  <option key={topic} value={topic}>
                    {topic}
                  </option>
                ))}
              </select>
            </label>
            {errors.helpTopic && (
            <Typography as="span" typographyTheme="bodySmall" styleProp={styles.errorMessage}>
              {errors.helpTopic.message}
            </Typography>
            )}
          </Container>

          <Container>
            <label {...stylex.props(styles.label)} htmlFor="orderNumber">
              Order Number (if applicable)
              <input
                id="orderNumber"
                type="text"
                {...register('orderNumber')}
                {...stylex.props(styles.input)}
              />
            </label>
          </Container>

          <Container>
            <label {...stylex.props(styles.label)} htmlFor="message">
              Message
              <textarea
                id="message"
                aria-invalid={!!errors.message}
                {...register('message', { required: 'Please enter your message' })}
                {...stylex.props(
                  styles.textarea,
                  errors.message && styles.errorInput
                )}
                placeholder="Type your message here"
              />
            </label>
            {errors.message && (
            <Typography as="span" typographyTheme="bodySmall" styleProp={styles.errorMessage}>
              {errors.message.message}
            </Typography>
            )}
          </Container>

          <button
            type="submit"
            disabled={formspreeState.submitting}
            {...stylex.props(styles.submitButton)}
          >
            {formspreeState.submitting ? (
              <span {...stylex.props(styles.spinner)} />
            )
              : 'Submit →'}
          </button>
        </Container>
      </Container>
    </Container>
  )
}

export default ContactUsForm
