import CallToAction from '@components/Generic/CallToAction'
import { spacing } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import Typography from '@components/Typography'

import * as stylex from '@stylexjs/stylex'

export const styles = stylex.create({
  wrapper: {
    alignItems: 'center',
    maxWidth: '500px',
    marginInline: 'auto',
    paddingBlock: spacing.lg,
    textAlign: 'center',
  }
})

function SubmittedContactUsForm() {
  const defaultCta = {
    ctaText: 'Shop Now →',
    buttonStyle: 'secondary',
    url: '/collections#cookware'
  }

  return (
    <Container flexCentered gap="2" styleProp={styles.wrapper}>
      <Typography as="h2" typographyTheme="h2Primary">
        Thank you for contacting us!
      </Typography>
      <Typography as="p" typographyTheme="bodyLarge">
        Thank you for reaching out to us! We have received your message
        and our team is currently reviewing your request and will get
        back to you as soon as possible.
      </Typography>
      <CallToAction
        href={defaultCta.url}
      >
        {defaultCta.ctaText}
      </CallToAction>
    </Container>
  )
}

export default SubmittedContactUsForm
