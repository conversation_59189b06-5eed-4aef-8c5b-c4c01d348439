'use client'

import { spacing } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  container: {
    width: '95%',
    height: '100dvh',
    display: {
      default: 'flex',
      '@media(min-width: 520px)': 'none'
    },
    flexDirection: 'column',
    overflowY: 'auto',
    paddingInline: {
      default: spacing.xs,
      '@media(min-width: 520px)': 0
    },
    marginBottom: {
      default: spacing.lg,
      '@media(min-width: 520px)': 0
    }
  }
})

type TableProps = {
  mobileContent: []
}

const TableMobile = ({ mobileContent }: TableProps) => (
  <Container
    flex
    noWrap
    styleProp={styles.container}
  >
    {mobileContent}
  </Container>
)

export default TableMobile
