'use client'

import Container from '@/components/layout/Container'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  container: {
    display: {
      default: 'none',
      '@media(min-width: 520px)': 'flex'
    },
    height: '100%',
    width: '98%',
    flexDirection: 'column',
    overflowY: 'auto'
  }
})

type TableProps = {
  content: []
}

const TableDesktop = ({ content }: TableProps) => (
  <Container
    flex
    noWrap
    styleProp={styles.container}
  >
    {content}
  </Container>
)

export default TableDesktop
