import TableMobile from './Components/TableMobile'
import TableDesktop from './Components/TableDesktop'

import Container from '@/components/layout/Container'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

const styles = stylex.create({
  container: {
    flexWrap: 'nowrap',
    width: '94vw',
    height: '100%',
    minWidth: {
      default: 'none',
      '@media (min-width: 520px)': '500px'
    },
    maxWidth: {
      default: 'none',
      '@media (min-width: 520px)': '750px'
    },
    marginTop: {
      default: spacing.md,
      '@media (min-width: 520px)': '0'
    },
    padding: {
      default: '0',
      '@media (min-width: 520px)': spacing.md
    }
  },
  imageContainer: {
    marginBlockEnd: {
      default: '16px',
      '@media (min-width: 375)': spacing.md
    },
  }
})

const WeightAndDimTable = ({
  content,
  mobileContent,
  asset
}: any) => (
  <Container
    flex
    flexCentered
    theme="offWhite"
    alignCentered
    styleProp={styles.container}
  >
    {asset?.url && (
    <Container styleProp={styles.imageContainer}>
      <Image
        src={asset.url}
        alt={asset?.fileName || ''}
        width={350}
        height={180}
      />
    </Container>
    )}
    <TableMobile mobileContent={mobileContent} />
    <TableDesktop content={content} />
  </Container>
)

export default WeightAndDimTable
