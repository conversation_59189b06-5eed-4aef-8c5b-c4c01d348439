'use client'

import { spacing } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import BeforeAfterSlider, { BeforeAfterSliderProps } from '@components/Generic/BeforeAfterSlider'
import CallToAction from '@components/Generic/CallToAction'
import TextBanner from '@components/Content/TextBanner'
import Wrapper from '@components/layout/Wrapper'
import Container, { Size } from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'

type BeforeAfterProps = {
  header: {
    subheader?: string,
    header: string,
    body: string,
  }
  containerSize?: Size
  theme: ThemeColors,
  beforeAfterItems: BeforeAfterSliderProps[],
};

const DEKSTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: spacing.lg,
    paddingTop: {
      default: spacing.lg,
      [DEKSTOP]: `calc(9 * ${spacing.xs})`
    },
    paddingBottom: {
      default: spacing.lg,
      [DEKSTOP]: spacing.xxl
    }
  },
  container: {
    display: 'grid',
    gridTemplateColumns: {
      default: '1fr',
      [DEKSTOP]: '1fr 1fr',
    },
    paddingInline: {
      default: spacing.md,
      [DEKSTOP]: 0,
    },
    textAlign: 'center',
    gap: {
      default: spacing.lg,
      [DEKSTOP]: spacing.xl,
    },
    flexDirection: {
      default: 'column',
      [DEKSTOP]: 'row',
    },
  },
  buttonContainer: {
    display: {
      default: 'none',
      [DEKSTOP]: 'flex',
    }
  },
})

const BeforeAfter = ({
  header,
  beforeAfterItems,
  theme,
  containerSize = '5'
}: BeforeAfterProps) => (
  <Wrapper styleProp={styles.wrapper} theme={theme}>
    <TextBanner
      {...header}
      theme={theme}
      paddingBlock="1"
    />
    <Container
      as="div"
      size={containerSize}
      styleProp={styles.container}
    >
      {beforeAfterItems.length > 0 && beforeAfterItems.map((beforeAfter, index) => (
        <BeforeAfterSlider
        // TODO swap for ID
          // eslint-disable-next-line react/no-array-index-key
          key={`${beforeAfter.beforeImage.src}-${index}`}
          {...beforeAfter}
        />
      ))}
    </Container>
    {beforeAfterItems.length > 0 && (
      <Container as="div" gap="2" flex flexRow styleProp={styles.buttonContainer}>
        {beforeAfterItems.map((beforeAfter) => {
          const { button } = beforeAfter
          if (!button) return null
          return (
            <CallToAction
              useArrow={button.useArrow}
              variant={button.variant}
              href={button.href}
              theme={button.theme}
              key={button.id}
            >
              {button.text}
            </CallToAction>
          )
        })}
      </Container>
    )}
  </Wrapper>
)

export default BeforeAfter
