'use client'

import Typography from '@/components/Typography'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

type LogoSlidesProps = {
  id?: string;
  mediaURL?: any;
  mediaFileName?: any;
  textIcon?: string;
  layout?: string;
}

const MOBILE = '@media (max-width: 1024px)'

const styles = stylex.create({
  textIcon: {
    maxWidth: '115px',
  },
})

const layout1 = stylex.create({
  imageLayout: {
    width: 'auto',
    height: 'auto',
    maxWidth: '100px',
    maxHeight: '100px',
  },
})

const layout2 = stylex.create({
  imageLayout: {
    transition: 'all 0.3s ease-in-out',
    width: 'auto',
    height: 'auto',
    maxWidth: '130px',
    maxHeight: '70px',
    cursor: 'pointer',
    opacity: {
      default: 0.7,
      ':hover': 1,
      [MOBILE]: 1,
    },
  },
})

const layouts = new Map<string, any>([
  ['Layout 1', layout1],
  ['Layout 2', layout2],
])

const LogoSlides = ({
  id,
  mediaURL,
  mediaFileName,
  textIcon,
  layout = 'Layout 1',
}: LogoSlidesProps) => {
  const mainLayoutStyles = (layouts.get(layout) || layout1) as typeof layout1 & typeof layout2
  return (
    <Container
      key={id}
      as="div"
      flex
      alignCentered
      gap="2"
    >
      {mediaURL && (
        <Image
          key={id}
          src={mediaURL}
          alt={mediaFileName}
          width="150"
          height="150"
          {...stylex.props(mainLayoutStyles.imageLayout)}
        />
      )}
      {textIcon && (
        <Typography
          as="p"
          textCentered
          size="bodySmall"
          styleProp={styles.textIcon}
        >
          {textIcon}
        </Typography>
      )}
    </Container>
  )
}

export default LogoSlides
