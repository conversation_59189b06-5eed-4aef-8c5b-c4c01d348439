import Option from './option'

import { ThemeColors } from '@/app/themeThemes.stylex'
import { colors } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'

import * as stylex from '@stylexjs/stylex'

type option = {
  title: string;
  id: string;
}

type ToggleSwitcherProps = {
  optionsList: option[];
  handleChange: (index: number) => void;
  activeOption: string | undefined;
  outline?: boolean;
  secondary?: boolean;
  theme?: ThemeColors;
  styleProp?: stylex.StyleXStyles
  fontBold?: boolean;
  outlineWhite?: boolean;
}

const styles = stylex.create({
  wrapper: {
    width: 'calc(100% - 40px)',
    maxWidth: '580px',
    borderRadius: '40px',
    margin: '0 auto 0.5rem'
  },
  outlined: {
    outline: `1px solid ${colors.navy}`,
    margin: '2px auto 0.5rem'
  },
  outlineWhite: {
    outline: `2px solid ${colors.white}`
  }
})

const ToggleSwitcher = ({
  optionsList,
  handleChange,
  activeOption,
  outline = false,
  outlineWhite = false,
  secondary = false,
  theme,
  fontBold = false,
  styleProp
}: ToggleSwitcherProps) => (
  <Container
    as="div"
    flex
    flexRow
    spaceBetween
    noWrap
    theme={theme}
    styleProp={[
      styles.wrapper,
      outline && styles.outlined,
      outlineWhite && styles.outlineWhite,
      styleProp
    ]}
  >
    {optionsList && optionsList.map((option, i) => (
      <Option
        key={option.id}
        title={option.title}
        handleChange={() => handleChange(i)}
        activeOption={activeOption || ''}
        aria-label={option.title}
        secondary={secondary}
        fontBold={fontBold}
      />
    ))}
  </Container>
)

export default ToggleSwitcher
