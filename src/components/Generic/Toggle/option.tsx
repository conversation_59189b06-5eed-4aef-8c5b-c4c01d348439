import {
  defaultTheme as $T,
  colors
} from '@/app/themeTokens.stylex'
import CallToAction from '@/components/Generic/CallToAction'
import Typography from '@/components/Typography'

import stylex from '@stylexjs/stylex'

type OptionProps = {
  title: string;
  handleChange: (option: string) => void;
  activeOption: string;
  secondary?: boolean;
  fontBold?: boolean;
}

const styles = stylex.create({
  wrapper: {
    flexGrow: 1,
    height: '100%',
    padding: '8px'
  },
  active: {
    backgroundColor: $T.primaryText,
    color: $T.primaryCTAText,
  },
  secondary: {
    backgroundColor: colors.perracotta,
    color: $T.primaryCTAText,
  }
})

const Option = ({
  title,
  handleChange,
  activeOption,
  secondary = false,
  fontBold = false
}: OptionProps) => (
  <CallToAction
    onClick={() => handleChange(title)}
    variant="transparent"
    size="inline"
    styleProp={[
      styles.wrapper,
      activeOption === title && !secondary && styles.active,
      activeOption === title && secondary && styles.secondary,
    ]}
  >
    <Typography
      as="p"
      typographyTheme="bodySmall"
      fontBold={fontBold}
    >
      {title}
    </Typography>
  </CallToAction>
)

export default Option
