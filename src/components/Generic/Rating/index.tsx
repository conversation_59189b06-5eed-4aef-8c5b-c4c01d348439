import { FormattedCount, FormattedRating, Stars } from './formatters'

import Container from '@components/layout/Container'
import { StarsProps } from '@components/Generic/Stars'
import Typography, { Size } from '@components/Typography'
import RenderIf from '@components/Generic/RenderIf'
import { equals } from '@utils/checking'
import { ThemeColors } from '@/app/themeThemes.stylex'

import * as stylex from '@stylexjs/stylex'

export type RatingProps = StarsProps & {
  withoutRating?: boolean;
  withoutReviews?: boolean;
  isAggregated?: boolean;
  iconColor?: ThemeColors;
  rating?: number;
  reviewCount?: number;
  textContentOn?: 'left' | 'right' | 'none';
  textStyleProps?: object | undefined;
  textSize?: Size;
  styleProp?: {};
};

const styles = stylex.create({
  container: {
    alignItems: 'center',
    gap: {
      default: '4px',
      '@media (min-width: 1024px)': '8px',
    },
  },
  ratingText: {
    display: {
      default: 'none',
      '@media (min-width: 414px)': 'inline',
    }
  },
})

export const MIN_RATING = 4.25
const MAX_RATING = 5

const Rating = ({
  withoutRating = false,
  withoutReviews = false,
  isAggregated = false,
  textContentOn = 'right',
  textStyleProps,
  textSize = 'xs',
  reviewCount = 0,
  rating = MAX_RATING,
  styleProp = {},
  ...props
}: RatingProps) => {
  const { size } = props
  const ratingNumber = Math.min(rating, MAX_RATING)
  const numberOfStars = ratingNumber >= MIN_RATING ? MAX_RATING : 0

  const justStars = withoutRating && withoutReviews

  const textContent = justStars
    ? null
    : (
      <Typography as="span" size={textSize} styleProp={textStyleProps}>
        <RenderIf condition={!withoutRating && ratingNumber >= MIN_RATING}>
          <FormattedRating rating={ratingNumber} styleProp={styles.ratingText} />
        </RenderIf>
        <RenderIf condition={!withoutReviews}>
          <FormattedCount count={reviewCount} />
          {isAggregated ? 'Aggregated Reviews*' : 'Reviews'}
        </RenderIf>
      </Typography>
    )

  return (
    <Container
      as="div"
      flex
      flexRow
      noWrap
      styleProp={[styles.container, styleProp]}
    >
      <RenderIf condition={equals(textContentOn, 'left')}>
        {textContent}
      </RenderIf>
      <Stars {...props} starLength={numberOfStars} size={size} />
      <RenderIf condition={equals(textContentOn, 'right')}>
        {textContent}
      </RenderIf>
    </Container>
  )
}

export default Rating
