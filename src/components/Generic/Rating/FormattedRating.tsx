'use client'

import * as stylex from '@stylexjs/stylex'

type FormattedCountProps = {
  rating: number;
  styleProp?: stylex.StyleXStyles;
};

const FormattedRating = ({ rating, styleProp }: FormattedCountProps) => {
  const formattedRating = Intl.NumberFormat(undefined, {
    maximumFractionDigits: 1,
  }).format(rating)

  return <span {...stylex.props(styleProp)}>({formattedRating}) </span>
}

export default FormattedRating
