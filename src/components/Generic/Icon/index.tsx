import { IconProps, iconSizes } from './types'
import <PERSON><PERSON>ong<PERSON>rrow, { LeftLongArrow } from './lib/LongArrows'
import BigRight<PERSON>ongArrow, { BigLeftLongArrow } from './lib/BigLongArrows'

import {
  colors,
  defaultTheme as $T
} from '@/app/themeTokens.stylex'
import Star from '@components/Generic/Icon/lib/Star'
import RightArrow from '@components/Generic/Icon/lib/RightArrow'
import LeftArrow from '@components/Generic/Icon/lib/LeftArrow'
import LeftArrowCircle from '@components/Generic/Icon/lib/LeftArrowCircle'
import RightArrowCircle from '@components/Generic/Icon/lib/RightArrowCircle'
import { empty } from '@utils/checking'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  root: {
    color: $T.primaryText,
  },
})

const Icons = {
  Star,
  LeftArrow,
  RightArrow,
  RightLongArrow,
  LeftLongArrow,
  BigRightLongArrow,
  BigLeftLongArrow,
  LeftArrowCircle,
  RightArrowCircle
}

export type IconName = keyof typeof Icons

const Icon = ({
  name,
  fill = colors.navy,
  size = 'small',
  theme,
  ...props
}: IconProps) => {
  const IconComponent = Icons[name as IconName]
  const iconSize = iconSizes[size]

  if (empty(IconComponent)) {
    return null
  }

  const xFill = theme ? $T.indicatorActive : fill

  return (
    <IconComponent fill={xFill} dimensions={iconSize} {...stylex.props(styles.root)} {...props} />
  )
}

export default Icon
