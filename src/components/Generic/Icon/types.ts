import { ThemeColors } from '@/app/themeThemes.stylex'

import * as stylex from '@stylexjs/stylex'

export const iconSizes = {
  xsmall: '10px',
  small: '16px',
  medium: '20px',
  large: '24px',
  xlarge: '32px',
  xxlarge: '45px',
}
export type IconsSize = keyof typeof iconSizes;

export type ChildIconProps = {
  fill?: string
  dimensions?: string
  alt?: string
  styleProp?: stylex.StyleXStyles
}

export type IconProps = ChildIconProps & {
  name: 'Star' | 'LeftArrow' | 'RightArrow' | 'LeftArrowCircle' | 'RightArrowCircle' | 'LeftLongArrow' | 'RightLongArrow' | 'BigLeftLongArrow' | 'BigRightLongArrow'
  size?: IconsSize
  onClick?: Function
  theme?: ThemeColors
}
