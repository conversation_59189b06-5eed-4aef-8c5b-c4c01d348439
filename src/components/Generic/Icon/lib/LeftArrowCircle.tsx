import { ChildIconProps } from '../types'

const LeftArrowCircle = ({
  fill = 'currentColor',
  dimensions,
  ...rest
}: ChildIconProps) => (
  <svg
    width={dimensions}
    height={dimensions}
    fill="none"
    viewBox="0 0 40 40"
    {...rest}
  >
    <g clipPath="url(#a)" opacity={0.8}>
      <rect width={40} height={40} fill="#FCFCFA" rx={20} strokeWidth={2} stroke={fill} />
      <path
        stroke={fill}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.667}
        d="m23.333 30-10-9.67 10-9.521"
      />
    </g>
    <defs>
      <clipPath id="a">
        <rect width={40} height={40} fill="#fff" rx={20} />
      </clipPath>
    </defs>
  </svg>
)

export default LeftArrowCircle
