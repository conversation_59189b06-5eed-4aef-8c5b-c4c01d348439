import { ChildIconProps } from '../types'

const StarIcon = ({
  fill,
  dimensions,
  ...props
}: ChildIconProps) => (
  <svg viewBox="0 0 200 200" width={dimensions} height={dimensions} {...props}>
    <path
      fill={fill}
      // eslint-disable-next-line max-len
      d="M98.16 9.13c1.22-2.47 3.21-2.47 4.42 0l26.48 53.65a10.72 10.72 0 0 0 7.16 5.2l59.2 8.6c2.73.4 3.34 2.29 1.37 4.21L154 122.55a10.66 10.66 0 0 0-2.73 8.42l10.11 59c.47 2.71-1.15 3.88-3.58 2.6l-53-27.88a10.68 10.68 0 0 0-8.85 0L43 192.53c-2.43 1.28-4 .11-3.58-2.6l10.12-59a10.71 10.71 0 0 0-2.74-8.42L4 80.79C2 78.87 2.6 77 5.32 76.58L64.52 68a10.7 10.7 0 0 0 7.16-5.2Z"
    />
  </svg>
)

export default StarIcon
