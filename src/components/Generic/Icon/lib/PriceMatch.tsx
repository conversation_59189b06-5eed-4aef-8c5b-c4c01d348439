import stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  svgStyle: {
    maxWidth: '100%',
    minWidth: {
      default: 'initial',
      [DESKTOP]: '58px',
    },
    minHeight: {
      default: 'initial',
      [DESKTOP]: '32px',
    },
    width: 'auto',
    height: 'auto',
  }
})

const PriceMatchIcon = () => (
  <svg {...stylex.props(styles.svgStyle)} width="70" height="71" viewBox="0 0 60 61" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M45.0211 39.9531L49.7446 50.2577C50.574 52.0572 50.1101 53.6457 48.9011 53.1396L43.8964 51.045C42.5047 50.4686 41.0004 51.1574
      40.5365 52.5914L38.7652 58.0459C38.3997 59.1565 37.0501 58.5379 36.3191 56.9353L31.1738 45.7029"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
    <path
      d="M29.8383 45.6608L25.9864 57.1603C25.3538 59.0441 23.9339 59.8876 23.3997 58.6927L21.1786 53.7442C20.56 52.3806 18.9855 51.8464
      17.664 52.5633L12.6312 55.3328C11.605 55.8951 11.0427 54.5315 11.605 52.8445L15.7381 40.4874"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
    <path
      d="M30.0774 45.7452C42.3213 45.7452 52.247 35.8195 52.247 23.5755C52.247 11.3316 42.3213 1.40588 30.0774 1.40588C17.8334 1.40588
      7.90771 11.3316 7.90771 23.5755C7.90771 35.8195 17.8334 45.7452 30.0774 45.7452Z"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
    <path
      d="M30.0633 41.373C39.7529 41.373 47.6078 33.518 47.6078 23.8285C47.6078 14.1389 39.7529 6.28394 30.0633 6.28394C20.3737 6.28394
      12.5188 14.1389 12.5188 23.8285C12.5188 33.518 20.3737 41.373 30.0633 41.373Z"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
    <path
      d="M21.9658 26.3872L27.1392 31.42L37.9499 17.8258"
      stroke="#1F3438"
      strokeMiterlimit="10"
      strokeLinecap="round"
    />
  </svg>
)

export default PriceMatchIcon
