import { colors } from '@/app/themeTokens.stylex'

import stylex from '@stylexjs/stylex'
import { StyleXVar } from '@stylexjs/stylex/lib/StyleXTypes'

type ArrowUpIconProps = {
  color?: StyleXVar<string>
  styleProp?: {}
}
const ArrowUpIcon = ({ color = colors.gray, styleProp }: ArrowUpIconProps) => (
  <svg
    {...stylex.props(styleProp)}
    width="12"
    height="7"
    viewBox="0 0 12 7"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0.308834 5.68295L0.308937 5.68285L5.6281 0.247477C5.62811 0.247459 5.62813 0.247441 5.62815 0.247422C5.68261 0.191949 5.73897 0.155167 5.79697
      0.133894C5.85886 0.111192 5.9261 0.0996347 5.99949 0.10001L6 0.10001C6.07373 0.10001 6.14132 0.111619 6.20352 0.13407C6.26113 0.154864 6.31727
      0.191444 6.37167 0.24724L6.37191 0.247477L11.707 5.68295C11.835 5.81339 11.9 5.97577 11.9 6.17742C11.9 6.377 11.8311 6.5454 11.6912 6.68802C11.5502
      6.83165 11.3906 6.9 11.2084 6.9C11.0262 6.9 10.8667 6.83165 10.7257 6.68802L6.07137 1.94609L6 1.87338L5.92863 1.94609L1.27428 6.68801C1.14588 6.81883
      0.989686 6.88387 0.799156 6.88387C0.61072 6.88387 0.448892 6.81496 0.308926 6.67198L0.308833 6.67189C0.167838 6.52824 0.1 6.36483 0.1 6.17742C0.1 5.99001
      0.167838 5.8266 0.308834 5.68295ZM0.237467 6.74194C0.0791556 6.58065 -2.79424e-07 6.39247 -2.70024e-07 6.17742C-2.60623e-07 5.96237 0.0791557 5.77419 0.237467
      5.6129L0.237467 6.74194Z"
      fill={color}
      stroke={color}
      strokeWidth="0.5"
    />
  </svg>
)

export default ArrowUpIcon
