import { ChildIconProps } from '../types'

const CheckmarkYes = ({
  fill,
  dimensions,
  ...props
}: ChildIconProps) => (
  <svg viewBox="0 0 12 20" width={dimensions} height={dimensions} {...props} fill="none">
    <path d="M95,50.4C94.89,75.68,75.51,95,50.21,95A44.65,44.65,0,0,1,5,49.62,44.68,44.68,0,0,1,50.09,5C75.68,5,95.11,24.6,95,50.4Z" />
    <path d="M40.91,69.33c-4-4.06-8.84-8.85-13.63-13.63l2.08-2.56C33.35,56.61,36.43,60,41,64c9.73-9.49,19.72-19.8,29.59-29.44.46.53,1.7,1.83,2.16,2.36Z" fill="#fff" />
  </svg>
)

export default CheckmarkYes
