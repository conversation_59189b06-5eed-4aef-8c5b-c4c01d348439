import { ChildIconProps } from '../types'

const LeftArrow = ({
  fill = 'currentColor',
  dimensions,
  ...rest
}: ChildIconProps) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    alt="arrow"
    width={dimensions}
    height={dimensions}
    {...rest}
  >
    <path
      d="M16.3372 20L8 11.9384L16.3372 4"
      stroke={fill}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export default LeftArrow
