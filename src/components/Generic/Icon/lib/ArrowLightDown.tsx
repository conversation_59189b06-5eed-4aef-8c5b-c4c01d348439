import { colors } from '@/app/themeTokens.stylex'

import stylex from '@stylexjs/stylex'
import { StyleXVar } from '@stylexjs/stylex/lib/StyleXTypes'

type ArrowUpIconProps = {
  color?: StyleXVar<string>
  styleProp?: {}
}
const ArrowLightDown = ({ color = colors.white, styleProp }: ArrowUpIconProps) => (
  <svg
    {...stylex.props(styleProp)}
    width="9"
    height="11"
    viewBox="0 0 9 11"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="ArrowLightDown"
  >
    <path
      d="M8.12207 6.75L4.4343 10.4397L0.7446 6.75"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.4668 10.5V0.5"
      stroke={color}
      strokeLinecap="round"
    />
  </svg>

)

export default ArrowLightDown
