import stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  svgStyle: {
    maxWidth: '100%',
    minWidth: {
      default: 'initial',
      [DESKTOP]: '58px',
    },
    minHeight: {
      default: 'initial',
      [DESKTOP]: '32px',
    },
    width: 'auto',
    height: 'auto',
  }
})

const CustomerSupportIcon = () => (
  <svg {...stylex.props(styles.svgStyle)} width="80" height="80" viewBox="0 0 60 61" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M37.0239 22.5746C38.0035 22.5746 38.7977 21.7804 38.7977 20.8007C38.7977 19.821 38.0035 19.0269 37.0239 19.0269C36.0442 19.0269 35.25 19.821 35.25 20.8007C35.25
      21.7804 36.0442 22.5746 37.0239 22.5746Z"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
    <path
      d="M42.9716 22.5746C43.9513 22.5746 44.7455 21.7804 44.7455 20.8007C44.7455 19.821 43.9513 19.0269 42.9716 19.0269C41.9919 19.0269 41.1978 19.821 41.1978 20.8007C41.1978
      21.7804 41.9919 22.5746 42.9716 22.5746Z"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
    <path
      d="M48.9194 22.5746C49.8991 22.5746 50.6932 21.7804 50.6932 20.8007C50.6932 19.821 49.8991 19.0269 48.9194 19.0269C47.9397 19.0269 47.1455 19.821 47.1455 20.8007C47.1455
      21.7804 47.9397 22.5746 48.9194 22.5746Z"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
    <path
      d="M27.0007 22.393V12.0656C27.0007 12.0656 26.9268 10.5941 28.2908 10.5269H51.4116C52.8024 10.5269 54.2 10.4664 55.5909 10.5269C56.3703 10.5538 57.2035 10.8494 57.2102
      11.7699V35.7373C57.2438 36.1875 57.0826 36.631 56.7735 36.9535C56.33 37.323 55.6984 37.3634 55.2079 37.0543L50.8673 33.2848L48.912 31.5781C48.529 31.2422 47.9445 31.3228
      47.4607 31.3228H33.5788"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
    <path
      d="M18.1195 22.4736C13.6445 22.4736 9.16953 22.4736 4.69454 22.4736C4.51313 22.4736 4.33171 22.4803 4.15029 22.5072C3.51868 22.5609 3.02818 23.085 3.00802 23.7166C3.00131 23.8107
      3.00802 23.9115 3.00802 24.0056V47.6975C2.96099 48.1476 3.12225 48.5978 3.45149 48.9136C3.98231 49.3571 4.76845 49.3235 5.25896 48.833C6.83125 47.4287 8.43042 46.0513 10.0229
      44.6671C10.5067 44.2438 10.9972 43.8272 11.4742 43.3972C11.6288 43.2493 11.8438 43.1754 12.0588 43.1822C18.61 43.1822 25.1545 43.1822 31.7057 43.2023C32.4919 43.2493 33.1705
      42.6513 33.2243 41.8652C33.2243 41.8047 33.2243 41.7375 33.2243 41.6771C33.1974 35.7978 33.1974 29.9117 33.2243 24.0324C33.3116 23.2597 32.7606 22.5609 31.9879 22.4736C31.8804
      22.4601 31.7729 22.4601 31.6654 22.4736C27.1501 22.487 22.6415 22.4736 18.1262 22.4736H18.1195Z"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
    <path
      d="M19.2752 36.6108C17.0981 36.6108 15.6737 34.3195 15.6737 32.2567C15.6737 31.0741 15.9424 30.0999 16.6345 29.4347C17.2796 28.8366 18.1329 28.5141 19.0131 28.5343C19.7993 28.5813
      20.5317 28.9576 21.0289 29.5758C21.3111 29.8916 21.553 30.2477 21.7411 30.6307L21.7747 30.7113L22.8095 30.0528C22.6415 29.8109 22.4533 29.5825 22.2518 29.3742C21.4455 28.4469 20.2494
      27.9564 19.0266 28.0438C17.7633 28.0169 16.5539 28.5746 15.761 29.5623C15.0555 30.4425 14.6725 31.5378 14.6658 32.6599C14.6456 33.8021 14.9749 34.9175 15.6065 35.8649C16.3926 36.9467
      17.676 37.5582 19.0131 37.4977C20.6593 37.4775 22.1443 36.5167 22.8431 35.025L22.3727 34.8235C21.5194 36.0262 20.4981 36.6108 19.2617 36.6108H19.2752Z"
      fill="#1F3438"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
  </svg>
)

export default CustomerSupportIcon
