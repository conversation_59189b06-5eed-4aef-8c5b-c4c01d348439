import React from 'react'
import * as stylex from '@stylexjs/stylex'

const ldsDefaultKeyframes = stylex.keyframes({
  '0%, 20%, 80%, 100%': { transform: 'scale(1)' },
  '50%': { transform: 'scale(1.5)' },
})

const styles: { [key: string]: any } = stylex.create({
  spinnerWrapper: {
    display: 'inline-block',
    position: 'relative',
    width: '40px',
    height: '40px',
  },
  spinnerDot: {
    boxSizing: 'border-box',
    position: 'absolute',
    width: '3.2px',
    height: '3.2px',
    backgroundColor: 'currentColor',
    borderRadius: '50%',
    animationName: ldsDefaultKeyframes,
    animationDuration: '1.2s',
    animationTimingFunction: 'linear',
    animationIterationCount: 'infinite',
  },
  dot1: { animationDelay: '0s', top: '18.4px', left: '33.12px' },
  dot2: { animationDelay: '-0.1s', top: '11.04px', left: '31.1479px' },
  dot3: { animationDelay: '-0.2s', top: '5.6521px', left: '25.76px' },
  dot4: { animationDelay: '-0.3s', top: '3.68px', left: '18.4px' },
  dot5: { animationDelay: '-0.4s', top: '5.6521px', left: '11.04px' },
  dot6: { animationDelay: '-0.5s', top: '11.04px', left: '5.6521px' },
  dot7: { animationDelay: '-0.6s', top: '18.4px', left: '3.68px' },
  dot8: { animationDelay: '-0.7s', top: '25.76px', left: '5.6521px' },
  dot9: { animationDelay: '-0.8s', top: '31.1479px', left: '11.04px' },
  dot10: { animationDelay: '-0.9s', top: '33.12px', left: '18.4px' },
  dot11: { animationDelay: '-1s', top: '31.1479px', left: '25.76px' },
  dot12: { animationDelay: '-1.1s', top: '25.76px', left: '31.1479px' },
})

const Spinner = ({ ...props }) => (
  <div {...stylex.props(styles.spinnerWrapper)} {...props}>
    {Array.from({ length: 12 }).map((_, index) => (
      // eslint-disable-next-line react/no-array-index-key
      <div key={`spinner-dot-${index}`} {...stylex.props(styles.spinnerDot, styles[`dot${index + 1}`])} />
    ))}
  </div>
)

export default Spinner
