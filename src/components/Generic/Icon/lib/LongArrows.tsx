import { ChildIconProps } from '../types'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  right: {},
  left: {
    transform: 'rotate(180deg)',
  },
})

type ArrowProps = ChildIconProps & {
  direction?: 'right' | 'left'
}

const LongArrows = ({
  direction = 'right',
  fill,
  dimensions,
  ...props
} : ArrowProps) => (
  <svg
    width={26}
    height={8}
    {...props}
    {...stylex.props(styles[direction])}
  >
    <path
      fill={fill}
      d="M25.354 4.354a.5.5 0 0 0 0-.708L22.172.464a.5.5 0 1 0-.707.708L24.293 4l-2.828 2.828a.5.5 0 1 0 .707.708l3.182-3.182ZM0 4.5h25v-1H0v1Z"
    />
  </svg>
)

const RightLongArrow = ({
  ...props
}: ArrowProps) => (
  <LongArrows {...props} direction="right" />
)

export const LeftLongArrow = (props: ArrowProps) => (
  <LongArrows {...props} direction="left" />
)

export default RightLongArrow
