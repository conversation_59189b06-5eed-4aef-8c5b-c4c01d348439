import stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  svgStyle: {
    maxWidth: '100%',
    minWidth: {
      default: 'initial',
      [DESKTOP]: '58px',
    },
    minHeight: {
      default: 'initial',
      [DESKTOP]: '32px',
    },
    width: 'auto',
    height: 'auto',
  }
})

const ReturnsIcon = () => (
  <svg {...stylex.props(styles.svgStyle)} width="80" height="81" viewBox="0 0 80 81" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M76.37 21.44L40.6 28.96M76.37 21.44V59.12V59.13L40.6 67.77L3 59.13V21.44M76.37 21.44L37.26
      13.5L3 21.44M40.6 28.96V67.31M40.6 28.96L3 21.44M15.72 43.11L12.86 39.25L16.5 36.56M13.3 39.28L27.99
      42.65C30.21 43.13 31.79 45.1 31.79 47.37C31.79 50.45 28.94 52.75 25.93 52.09L13.38 49.42M21.29 25.04L53.68 16.81"
      stroke="#1F3438"
      strokeMiterlimit="10"
    />
  </svg>
)

export default ReturnsIcon
