import { ChildIconProps } from '../types'

const CheckMark = ({
  fill,
  dimensions,
  ...props
}: ChildIconProps) => (
  <svg
    width={dimensions}
    height={dimensions}
    {...props}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1093_68484)">
      <path
        d="M23.5 12C23.5 18.3513 18.3513 23.5 12 23.5C5.64873 23.5 0.5 18.3513 0.5 12C0.5 5.64873 5.64873 0.5 12 0.5C18.3513 0.5 23.5 5.64873 23.5 12Z"
        stroke={fill}
      />
      <path
        d="M6 12.2955L9.87692 16.5909L18.6 8"
        stroke={fill}
        strokeLinecap="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_1093_68484">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

export default CheckMark
