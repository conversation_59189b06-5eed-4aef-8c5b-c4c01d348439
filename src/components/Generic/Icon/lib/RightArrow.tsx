import { ChildIconProps } from '../types'

import * as stylex from '@stylexjs/stylex'

const RightArrow = ({
  fill = 'currentColor',
  dimensions,
  styleProp,
  ...rest
}: ChildIconProps) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    alt="arrow"
    width={dimensions}
    height={dimensions}
    {...stylex.props(styleProp)}
    {...rest}
  >
    <path
      d="M8.00019 20L16.3374 11.9384L8.00019 4"
      stroke={fill}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)

export default RightArrow
