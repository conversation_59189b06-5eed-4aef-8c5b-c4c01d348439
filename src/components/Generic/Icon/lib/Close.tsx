import {
  defaultTheme as $T
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  close: {
    cursor: 'pointer',
    display: 'block',
    width: 18,
    height: 18,
    position: 'relative',
  },
  cross: {
    width: 2,
    height: '100%',
    position: 'absolute',
    borderRadius: 10,
    backgroundColor: $T.primaryText,
    margin: 'auto',
    inset: 0,
  },
  extraSmall: {
    width: 10,
    height: 10,
  },
  small: {
    width: 14,
    height: 14,
  },
  medium: {
    width: 24,
    height: 24,
  },
  right: {
    transform: 'rotate(45deg)',
  },
  left: {
    transform: 'rotate(-45deg)',
  },
})

const crossStyles = stylex.create({
  small: {
    width: 1,
  },
  extraSmall: {
    width: 1,
  },
})

type CloseProps = {
  size?: 'extraSmall' | 'small' | 'medium'
}

const Close = ({ size = 'small' }: CloseProps) => {
  const sizeClass = styles[size]
  const crossWidth = size !== 'medium' ? crossStyles[size] : null

  return (
    <span
      {...stylex.props(
        styles.close,
        sizeClass,
      )}
    >
      <span {...stylex.props(
        styles.cross,
        styles.right,
        crossWidth && crossWidth
      )}
      />
      <span {...stylex.props(
        styles.cross,
        styles.left,
        crossWidth && crossWidth
      )}
      />
    </span>
  )
}

export default Close
