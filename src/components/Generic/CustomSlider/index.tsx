'use client'

import styles from './styles'
import DotButton from './DotsButton'

import Icon from '@components/Generic/Icon'
import Container from '@components/layout/Container'
import { condition, equals } from '@utils/checking'
import RenderIf from '@components/Generic/RenderIf'
import useDotButton from '@hooks/useDotButton'
import usePrevNextButtons from '@hooks/prevNextButtons'
import PlayPauseButton from '@components/Generic/PlayPauseButton'
import { colors } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'

import React, { useEffect, useRef } from 'react'
import { EmblaOptionsType, EmblaPluginType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import * as stylex from '@stylexjs/stylex'

export type RenderNextPrevButtonsProps = {
  onPrevButtonClick: () => void;
  onNextButtonClick: () => void;
  prevBtnDisabled: boolean;
  nextBtnDisabled: boolean;
  selectedIndex: number;
}

export type RenderDotsProps = {
  onPrevButtonClick: () => void;
  onNextButtonClick: () => void;
  prevBtnDisabled: boolean;
  nextBtnDisabled: boolean;
  scrollSnaps: number[];
  onDotButtonClick: (index: number) => void;
  selectedIndex: number;
}

export type RenderDotsFunction = (props: RenderDotsProps) => React.ReactNode
export type RenderPrevNextButtons = (props: RenderNextPrevButtonsProps) => React.ReactNode

export type CustomSliderProps = {
  getEmblaApi?: (api: any) => void,
  extractSlideKey?: (slide: any) => string,
  renderSlide: <T>(slide: T | any, isSelected: boolean) => React.ReactNode,
  renderDotsButtons?: RenderDotsFunction,
  renderPrevNextButtons?: RenderPrevNextButtons,
  dotsStyles?: {},
  dotsEachContainerStyles?: {},
  selectedDotsStyles?: {},
  buttonsContainerStyles?: {},
  withSimpleDots?: boolean,
  slidesPerPage?: number,
  slides: any[],
  options?: EmblaOptionsType,
  plugins?: EmblaPluginType[],
  renderDotsWithArrows?: boolean,
  renderArrowsCondition?: boolean,
  dotsContainerStyles?: {},
  gap?: number,
  playIcon?: boolean,
  playOnClick?: () => void,
  videoPlaying?: boolean,
  iconSize?: number;
  slideStyleProp?: {},
  arrowsSize?: 'small' | 'medium' | 'large',
  desktopContentVerticalAlignment?: string,
  desktopContentHorizontalAlignment?: string,
  tabletContentVerticalAlignment?: string,
  tabletContentHorizontalAlignment?: string,
  mobileContentVerticalAlignment?: string,
  mobileContentHorizontalAlignment?: string,
}

type mainProps = CustomSliderProps & {
  theme?: ThemeColors
}
const ONE_HUNDRED_PERCENT = 100

const CustomSlider: React.FC<mainProps> = ({
  theme,
  ...props
}) => {
  const {
    getEmblaApi = () => {},
    renderDotsWithArrows = false,
    renderArrowsCondition = true,
    renderSlide = () => (<div />),
    extractSlideKey = () => '',
    renderDotsButtons,
    renderPrevNextButtons,
    dotsStyles,
    dotsEachContainerStyles = {},
    dotsContainerStyles = {},
    selectedDotsStyles,
    buttonsContainerStyles,
    withSimpleDots = false,
    slidesPerPage = 1,
    slides,
    options,
    plugins = [],
    gap = 0,
    playIcon,
    playOnClick,
    videoPlaying = true,
    iconSize = '40px',
    slideStyleProp,
    arrowsSize = 'large',
  } = props

  const containerRef = useRef<HTMLDivElement>(null)
  const [emblaRef, emblaApi] = useEmblaCarousel(options, plugins)

  const gapsPerPage = slidesPerPage - 1
  const gapPercentages = containerRef.current
    ? (gap / containerRef.current.offsetWidth) * ONE_HUNDRED_PERCENT
    : 1

  const gapOffset = gapPercentages * gapsPerPage
  const slideWidth = (ONE_HUNDRED_PERCENT - gapOffset) / slidesPerPage

  const {
    selectedIndex,
    scrollSnaps,
    onDotButtonClick
  } =
    useDotButton(emblaApi)

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick
  } = usePrevNextButtons(emblaApi)

  const Dots = renderDotsButtons
    ? renderDotsButtons({
      onPrevButtonClick,
      onNextButtonClick,
      prevBtnDisabled,
      nextBtnDisabled,
      scrollSnaps,
      onDotButtonClick,
      selectedIndex
    })

    : (
      <>
        <RenderIf condition={Boolean(renderDotsWithArrows && renderArrowsCondition)}>
          <button onClick={onPrevButtonClick} disabled={prevBtnDisabled} type="button" aria-label="Go to previous slide">
            <Icon name="LeftArrow" size={arrowsSize} theme={theme} />
          </button>
        </RenderIf>
        <Container as="div" styleProp={[styles.dotsGrid, dotsEachContainerStyles]}>
          {
            scrollSnaps.map((bullet: number, index) => (
              <DotButton
                withSimpleDots={withSimpleDots}
                isSelected={equals(index, selectedIndex)}
                styleProp={dotsStyles}
                selectedDotsStyles={selectedDotsStyles}
                key={`${bullet.toString()}-${extractSlideKey(slides[index])}`}
                onClick={() => onDotButtonClick(index)}
                title={`Go to slide page ${index + 1}`}
                aria-label={`Go to slide page ${index + 1}`}
                theme={theme}
              >
                page { index + 1 }
              </DotButton>
            ))
          }
        </Container>
        <RenderIf condition={Boolean(renderDotsWithArrows && renderArrowsCondition)}>
          <button onClick={onNextButtonClick} disabled={nextBtnDisabled} type="button" aria-label="Go to next slide">
            <Icon name="RightArrow" size={arrowsSize} theme={theme} />
          </button>
        </RenderIf>
      </>
    )

  const nextPrevButtons = renderPrevNextButtons
    ? renderPrevNextButtons({
      onPrevButtonClick,
      onNextButtonClick,
      prevBtnDisabled,
      nextBtnDisabled,
      selectedIndex
    })

    : (
      <Container as="div" styleProp={styles.buttons} aria-label="Pagination Navigation">
        <button onClick={onPrevButtonClick} disabled={prevBtnDisabled} type="button" aria-label="Go to previous slide">
          <Icon name="LeftArrow" size="large" />
        </button>
        <button onClick={onNextButtonClick} disabled={nextBtnDisabled} type="button" aria-label="Go to next slide">
          <Icon name="RightArrow" size="large" />
        </button>
      </Container>
    )

  useEffect(() => {
    if (emblaApi) {
      getEmblaApi(emblaApi)
    }
  }, [emblaApi, getEmblaApi])

  const iconsStyles = [styles.iconBBG, styles.iconOn]

  return (
    <Container
      as="section"
      styleProp={styles.mainContainer}
    >
      <div ref={containerRef}>
        <div {...stylex.props(styles.viewport)} ref={emblaRef}>
          <Container as="div" styleProp={styles.container(gap)}>
            {slides.map((slide, i) => (
              <React.Fragment key={extractSlideKey(slide)}>
                <Container as="div" styleProp={[styles.slide(slideWidth), slideStyleProp]}>
                  { renderSlide(slide, selectedIndex === i) }
                </Container>
              </React.Fragment>
            ))}
          </Container>
        </div>

        <Container as="div" styleProp={buttonsContainerStyles}>
          <RenderIf condition={Boolean(nextPrevButtons)}>
            { nextPrevButtons }
          </RenderIf>

          <RenderIf condition={Boolean(Dots)}>
            <Container
              as="div"
              styleProp={[
                condition<{}>(renderDotsWithArrows, styles.dotsContainer, styles.dotsContainerCenter),
                dotsContainerStyles,
              ]}
            >
              { Dots }
            </Container>
          </RenderIf>
        </Container>
        <RenderIf condition={Boolean(playIcon)}>
          <PlayPauseButton
            play={videoPlaying}
            iconColor={colors.white as ThemeColors}
            width={iconSize}
            styleProp={iconsStyles}
            onClick={playOnClick}
          />
        </RenderIf>
      </div>
    </Container>
  )
}

export default CustomSlider
