import styles from './styles'

import { condition, notEmpty } from '@/utils/checking'
import themes, { ThemeColors } from '@/app/themeThemes.stylex'
import { defaultTheme as $T } from '@/app/themeTokens.stylex'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

const _styles = stylex.create({
  rootActive: { backgroundColor: $T.indicatorActive },
  rootInactive: { backgroundColor: $T.indicatorInactive }
})

type PropType = {
  withSimpleDots?: boolean,
  isSelected: boolean,
  styleProp?: {},
  selectedDotsStyles?: {},
  children: React.ReactNode,
  onClick?: () => void,
  title?: string,
  theme?: ThemeColors
}

const DotButton = ({
  styleProp,
  withSimpleDots = false,
  selectedDotsStyles,
  children,
  isSelected,
  onClick = () => {},
  title = '',
  theme
}: PropType) => {
  const style = styleProp ||
  condition<{}>(withSimpleDots, styles.simpleDotbase, styles.dots)

  const selectedStyle = isSelected
    ? condition<{}>(notEmpty(selectedDotsStyles), selectedDotsStyles, styles.dotSelected)
    : {}
  return (
    <button
      title={title}
      type="button"
      onClick={onClick}
      {...stylex.props([
        style,
        isSelected && selectedStyle,
        theme && [_styles.rootInactive, themes[theme]],
        theme && isSelected && [_styles.rootActive, themes[theme]]
      ])
      }
    >
      {children}
    </button>
  )
}

export default DotButton
