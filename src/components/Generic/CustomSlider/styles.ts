import {
  defaultTheme as $T,
  fontSizes,
  spacing
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  mainContainer: {
    position: 'relative',
    width: {
      default: '100%',
      [DESKTOP]: 'auto',
    }
  },
  viewport: {
    overflow: 'hidden',
  },
  container: (gap: number) => ({
    display: 'flex',
    touchAction: 'pan-y pinch-zoom',
    gap: `${gap}px`,
  }),
  slide: (slideWidth: number) => ({
    transform: 'translate3d(0, 0, 0)',
    flex: `0 0 ${slideWidth}%`,
    minWidth: '0',
  }),
  buttons: {
    position: 'relative',
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: '0.6rem',
    alignItems: 'center',
  },
  dotsContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dotsContainerCenter: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dotsGrid: {
    gap: '0.5rem',
    display: 'flex',
    flexWrap: 'wrap',
  },
  dotSelected: {
    backgroundColor: $T.indicatorActive,
    borderColor: $T.indicatorActive,
  },
  dots: {
    display: 'inline-block',
    width: spacing.sm,
    height: spacing.sm,
    borderRadius: '50%',
    borderStyle: 'solid',
    borderWidth: '2px',
    borderColor: {
      default: $T.indicatorInactive,
      ':hover': $T.indicatorHover,
    },
    backgroundColor: {
      default: $T.indicatorInactive,
      ':hover': $T.indicatorHover,
    },
    textIndent: '-9999px',
    cursor: 'pointer',

  },
  simpleDotbase: {
    display: 'inline-block',
    width: spacing.xs,
    height: spacing.xs,
    borderRadius: '50%',
    textIndent: '-9999px',
    cursor: 'pointer',
    backgroundColor: $T.indicatorInactive,
  },
  simpleSelectedDots: {
    backgroundColor: $T.indicatorActive,
  },
  iconBBG: {
    position: 'absolute',
    cursor: 'pointer',
    right: {
      default: fontSizes.h4,
      '@media (max-width: 1024px)': '30px',
    },
    bottom: {
      default: '17px',
      '@media (max-width: 1024px)': '37px',
    },
    display: {
      default: 'none',
      '@media (max-width: 1024px)': {
        default: 'block',
      }
    },
    zIndex: 1,
    width: {
      '@media (max-width: 1024px)': {
        default: fontSizes.sm,
      },
    },
    height: {
      '@media (max-width: 1024px)': {
        default: fontSizes.sm,
      },
    },
  },
  iconOn: {
    display: 'block',
  },
})

export default styles
