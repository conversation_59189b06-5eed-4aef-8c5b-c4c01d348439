import {
  defaultTheme as $T
} from '@/app/themeTokens.stylex'
import Icon from '@components/Generic/Icon'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import React, { MouseEvent } from 'react'

const styles = stylex.create({
  button: {
    display: 'inline-block',
    padding: '0',
    margin: '0',
    borderWidth: '0',
    borderStyle: 'none',
    borderColor: 'transparent',
    background: 'transparent',
    cursor: 'pointer',
  },
  dot: {
    width: '8px',
    height: '8px',
    borderRadius: '50%',
    margin: '0',
    background: $T.primaryCTAText,
    transition: 'background 0.3s',
  },
  isSelected: {
    background: $T.primaryCTASurface,
  },
})

type DotButtonProps = {
  selected: boolean,
  onClick: (event: MouseEvent<HTMLButtonElement>) => void,
}

export const DotButton = ({ selected, onClick }: DotButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    {...stylex.props({ ...styles.dot, ...selected && styles.isSelected })}
  />
)

type PrevButtonProps = {
  enabled: boolean,
  onClick: (event: MouseEvent<HTMLButtonElement>) => void,
}

export const PrevButton = ({ enabled, onClick }: PrevButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    disabled={!enabled}
    aria-label="Previous Slide"
  >
    <Icon name="LeftArrow" size="medium" />
  </button>
)

type NextButtonProps = {
  enabled: boolean,
  onClick: (event: MouseEvent<HTMLButtonElement>) => void,
}

export const NextButton = ({ enabled, onClick }: NextButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    disabled={!enabled}
    aria-label="Next Slide"
  >
    <Icon name="RightArrow" size="medium" />
  </button>
)

type DotButtonsProps = {
  scrollSnaps: Array<number>,
  handleScroll: (index: number) => (event: MouseEvent<HTMLButtonElement>) => void,
  styleProp: object | undefined,
  currentSlide: number,
}

export const DotButtons = ({
  scrollSnaps,
  handleScroll,
  styleProp,
  currentSlide
}: DotButtonsProps) => (
  <Container as="div" styleProp={styleProp}>
    {scrollSnaps.map((snap, index) => (
      <DotButton
        key={snap}
        onClick={handleScroll(index)}
        selected={index === currentSlide}
      />
    ))}
  </Container>
)
