import {
  globalTokens,
  spacing,
} from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import DialogTrigger, { DialogTriggerProps } from '@components/Generic/Dialog/DialogTrigger'
import Container from '@components/layout/Container'
import RenderIf from '@/utils/renderIf'
import { notEmpty } from '@/utils/checking'
import Typography from '@/components/Typography'

import * as stylex from '@stylexjs/stylex'

export type TextBlockProps = {
  theme?: ThemeColors;
  children: React.ReactNode;
  dialogTriggerProps?: DialogTriggerProps
}

const styles = stylex.create({
  container: {
    margin: `${spacing.sm} 0`,
    padding: `${spacing.sm} 16px`,
    borderRadius: globalTokens.borderRadiusSmall,
    width: '100%',
  }
})

const TextBlock = ({
  theme = 'cream',
  dialogTriggerProps = {
    id: '',
    text: '',
    variant: 'underlined',
    theme,
    size: 'small',
  },
  children
}: TextBlockProps) => (
  <Container as="div" theme={theme} styleProp={styles.container}>
    { children }
    <RenderIf condition={notEmpty(dialogTriggerProps.text)}>
      <Typography as="span">
        {' '}|{' '}
      </Typography>
      <DialogTrigger {...dialogTriggerProps} />
    </RenderIf>
  </Container>
)

export default TextBlock
