import {
  globalTokens,
  spacing,
} from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'
import MediaContainer from '@/components/Content/MediaContainer'
import AtomicLevelTooltip from '@/components/Content/AtomicLevelTooltip'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  container: {
    maxWidth: '794px',
    marginBottom: {
      default: spacing.lg,
      [DESKTOP]: spacing.xxl,
    },
  },
  subheader: {
    maxWidth: {
      default: '100%',
      [DESKTOP]: '500px'
    },
    margin: `${spacing.md} 0 0`,
  },
  imgContainer: {
    overflow: 'hidden',
    borderRadius: globalTokens.borderRadius,
    position: 'relative',
    objectFit: 'cover',
    width: {
      default: '100%',
      [DESKTOP]: '50%'
    },
    maxWidth: {
      default: '100%',
      [DESKTOP]: '383px'
    },
    height: '282px',
  },
  contentContainer: {
    display: {
      default: 'block',
      [DESKTOP]: 'flex'
    },
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bodyContainer: {
    flex: '1',
    maxWidth: {
      default: '100%',
      [DESKTOP]: '330px'
    },
    placeSelf: {
      default: 'start',
      [DESKTOP]: 'center',
    },
    marginTop: {
      default: `calc(${spacing.md} + ${spacing.xs})`,
      [DESKTOP]: 0
    }
  },
  mediaContainer: {
    height: '100%',
    maxHeight: '100%',
  },
  layout1Button: {
    width: {
      default: '100%',
      [DESKTOP]: 'fit-content',
    },
  }
})

const variantStyles = stylex.create({
  default: {
    order: {
      default: 0,
      [DESKTOP]: '0',
    }
  },
  reversed: {
    order: {
      default: 0,
      [DESKTOP]: '1',
    }
  }
})

type Variant = keyof typeof variantStyles;

export type ZpatternPropsL4 = {
  theme?: ThemeColors
  header?: string
  subheader?: string
  media?: any
  variant?: Variant
  tooltip?: any
};

const ZPattern = ({
  theme = 'cream',
  header,
  subheader,
  media,
  variant,
  tooltip
}: ZpatternPropsL4) => {
  const safeVariant = variant || 'default'

  const gridContentStyles = [styles.contentContainer]

  return (
    <Container
      as="section"
      theme={theme}
      styleProp={styles.container}
    >
      <Container
        as="div"
        styleProp={gridContentStyles}
      >
        <Container
          as="div"
          styleProp={{
            ...styles.imgContainer,
            ...variantStyles[safeVariant],
          }}
        >
          {tooltip && (<AtomicLevelTooltip header={tooltip.title} content={tooltip.description} theme={tooltip.theme} />)}
          <MediaContainer
            header={header}
            objectFit="cover"
            asset={{ url: media }}
            imageHeight="282px"
            autoPlay
            styleProps={
              styles.mediaContainer
            }
          />

        </Container>
        <Container as="div" styleProp={styles.bodyContainer}>
          <Typography textLeft as="h6" typographyTheme="h6Primary" fontBold>
            {header}
          </Typography>
          <Container as="div" styleProp={styles.subheader}>
            {subheader}
          </Container>
        </Container>
      </Container>
    </Container>
  )
}

export default ZPattern
