import CallToAction from '@components/Generic/CallToAction'
import {
  colors,
  spacing,
} from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { condition, equals } from '@utils/checking'
import MediaContainer from '@/components/Content/MediaContainer'
import AtomicLevelTooltip from '@/components/Content/AtomicLevelTooltip'
import { useDialogContext } from '@providers/config/DialogContext'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  subheader: {
    maxWidth: '500px',
    margin: `${spacing.md} 0`,
  },
  imgContainer: {
    position: 'relative',
    objectFit: 'cover',
    width: {
      default: '100%',
      [DESKTOP]: '50%'
    },
    height: {
      default: '335px',
      [DESKTOP]: '450px'
    },
    boxSizing: 'border-box',
  },
  imgContainerCentered: {
    maxWidth: {
      default: '100%',
      [DESKTOP]: '450px'
    },
  },
  contentContainerCentered: {
    minWidth: 'initial',
    maxWidth: {
      default: '100%',
      [DESKTOP]: '430px'
    },
    padding: {
      default: `${spacing.lg} ${spacing.md} 0`,
      [DESKTOP]: 0
    },
  },
  contentContainer: {
    display: {
      default: 'block',
      [DESKTOP]: 'flex'
    },
  },
  centeredGrid: {
    justifyContent: 'center',
    maxWidth: '960px',
    margin: {
      default: '0',
      [DESKTOP]: `0 auto ${spacing.xl}`,
    },
  },
  video: {
    width: '100%',
    height: '100%',
    maxHeight: '450px',
    objectFit: 'cover'
  },
  bodyContainer: {
    flex: '1',
    minWidth: {
      default: '100%',
      [DESKTOP]: '460px'
    },
    placeSelf: {
      default: 'start',
      [DESKTOP]: 'center',
    },

    padding: {
      default: `${spacing.lg} ${spacing.md}`,
      [DESKTOP]: `0 ${spacing.lg}`,
    },
  },
  mediaContainer: {
    height: '100%',
    maxHeight: '100%',
  },
  layout1Button: {
    width: {
      default: '100%',
      [DESKTOP]: 'fit-content',
    },
  },
})

const layout3 = stylex.create({
  mainWrapperReverse: {
    display: 'flex',
    flexDirection: {
      default: 'column-reverse',
      [DESKTOP]: 'row',
    },
    gap: {
      default: spacing.xs,
      [DESKTOP]: '50px',
    },
  },
  subheader: {
    margin: `${spacing.xs} 0`,
  },
  mediaContainerWrapper: {
    padding: {
      default: `${spacing.xs} ${spacing.md}`,
      [DESKTOP]: 0,
    },
    maxHeight: {
      default: '176px',
      [DESKTOP]: '100%'
    },
    maxWidth: {
      default: '500px',
      [DESKTOP]: '100%'
    },
  },
  mediaContainer: {
    borderRadius: spacing.sm,
    overflow: 'hidden',
  },
  ctaWrapper: {
    borderTopWidth: '1px',
    borderTopStyle: 'solid',
    borderColor: colors.gray300,
    marginTop: {
      default: spacing.xs,
      [DESKTOP]: spacing.lg,
    },
  },
  cta: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingInline: '0',
    paddingBlock: spacing.md,
    borderBottomWidth: '1px',
    borderBottomStyle: 'solid',
    borderColor: colors.gray300,
    borderRadius: '0',
    fontSize: {
      default: '1.125rem',
      [DESKTOP]: '1.25rem',
    },
    fontWeight: 'bold',
  },
})

const variantStyles = stylex.create({
  default: {
    order: {
      default: 0,
      [DESKTOP]: '0',
    },
  },
  reversed: {
    order: {
      default: 0,
      [DESKTOP]: '1',
    }
  }
})

type Layout = 'layout1' | 'layout2' | 'layout3' | undefined;
type Variant = keyof typeof variantStyles;

type ZpatternProps = {
  theme?: ThemeColors
  header?: string
  subheader?: string
  content?: string
  layout?: Layout
  media?: any
  variant?: Variant
  button?: any
  globalLayout?: string
  tooltip?: any
};

const ZPattern = ({
  theme = 'cream',
  header,
  subheader,
  content,
  media,
  button,
  variant,
  layout,
  tooltip
}: ZpatternProps) => {
  const { triggerDialog } = useDialogContext()
  const safeVariant = variant || 'default'
  const isCentered = equals(layout, 'layout2')
  const useLayout3 = equals(layout, 'layout3')

  const gridContentStyles = condition<{}>(
    isCentered || useLayout3,
    [styles.contentContainer, styles.centeredGrid],
    [styles.contentContainer]
  )

  const renderMediaContainer = () => (
    <Container
      as="div"
      styleProp={{
        ...styles.imgContainer,
        ...variantStyles[safeVariant],
        ...isCentered && styles.imgContainerCentered,
        ...useLayout3 && layout3.mediaContainerWrapper
      }}
    >
      {tooltip && (<AtomicLevelTooltip header={tooltip.title} content={tooltip.description} theme={tooltip.theme} />)}
      <MediaContainer
        header={header}
        objectFit="cover"
        asset={{ url: media }}
        imageHeight="432px"
        autoPlay
        styleProps={[
          styles.mediaContainer,
          useLayout3 && layout3.mediaContainer
        ]}
      />
    </Container>
  )

  const renderBodyContainer = () => (
    <Container
      as="div"
      styleProp={[
        styles.bodyContainer,
        useLayout3 && styles.contentContainerCentered
      ]}
    >
      <Container
        as="div"
        styleProp={[
          styles.subheader,
          useLayout3 && layout3.subheader
        ]}
      >
        <Typography as="p" typographyTheme="bodySmall">
          {content}
        </Typography>
      </Container>
      {useLayout3 ? (
        <Typography textLeft as="h3" typographyTheme="h4Secondary" fontBold>
          {header}
        </Typography>
      ) : (
        <Typography textLeft as="h2" typographyTheme="h4Secondary" fontBold>
          {header}
        </Typography>
      )}
      <Container as="div" styleProp={styles.subheader}>
        {subheader}
      </Container>
      {button && button.length > 0 && (
        <Container
          as="div"
          grid
          styleProp={[
            useLayout3 && layout3.ctaWrapper
          ]}
        >
          {button.map((btn: any) => (
            <CallToAction
              onClick={() => triggerDialog(btn.id)}
              styleProp={[
                styles.layout1Button,
                useLayout3 && layout3.cta
              ]}
              useArrow={!useLayout3}
              useLongArrow={useLayout3}
              {...btn}
            />
          ))}
        </Container>
      )}
    </Container>
  )

  return (
    <Container
      as="section"
      theme={theme}
    >
      <Container
        as="div"
        styleProp={[
          gridContentStyles,
          useLayout3 && layout3.mainWrapperReverse
        ]}
      >
        {renderMediaContainer()}
        {renderBodyContainer()}
      </Container>
    </Container>
  )
}

export default ZPattern
