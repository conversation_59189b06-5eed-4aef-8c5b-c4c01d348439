import CallToAction from '@components/Generic/CallToAction'
import {
  spacing,
} from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import Container from '@components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { condition, equals } from '@utils/checking'
import MediaContainer from '@/components/Content/MediaContainer'
import AtomicLevelTooltip from '@/components/Content/AtomicLevelTooltip'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  subheader: {
    maxWidth: '500px',
    margin: `${spacing.md} 0`,
  },
  imgContainer: {
    position: 'relative',
    objectFit: 'cover',
    width: {
      default: '100%',
      [DESKTOP]: '50%'
    },
    height: {
      default: '335px',
      [DESKTOP]: '450px'
    },
    boxSizing: 'border-box',
  },
  imgContainerCentered: {
    maxWidth: {
      default: '100%',
      [DESKTOP]: '450px'
    },
  },
  contentContainer: {
    display: {
      default: 'block',
      [DESKTOP]: 'flex'
    },
  },
  centeredGrid: {
    justifyContent: 'center',
    maxWidth: '960px',
    margin: {
      default: '0',
      [DESKTOP]: `0 auto ${spacing.xl}`,
    },
  },
  video: {
    width: '100%',
    height: '100%',
    maxHeight: '450px',
    objectFit: 'cover'
  },
  bodyContainer: {
    flex: '1',
    minWidth: {
      default: '100%',
      [DESKTOP]: '460px'
    },
    placeSelf: {
      default: 'start',
      [DESKTOP]: 'center',
    },

    padding: {
      default: `${spacing.lg} ${spacing.md}`,
      [DESKTOP]: `0 ${spacing.lg}`,
    },
  },
  mediaContainer: {
    height: '100%',
    maxHeight: '100%',
  },
  layout1Button: {
    width: {
      default: '100%',
      [DESKTOP]: 'fit-content',
    },
  }
})

const variantStyles = stylex.create({
  default: {
    order: {
      default: 0,
      [DESKTOP]: '0',
    }
  },
  reversed: {
    order: {
      default: 0,
      [DESKTOP]: '1',
    }
  }
})

export type LayoutL1L2 = 'layout1' | 'layout2' | undefined;
type Variant = keyof typeof variantStyles;

export type ZpatternPropsL1L2 = {
  theme?: ThemeColors
  header?: string
  content?: string
  subheader?: string
  layout?: LayoutL1L2
  media?: any
  variant?: Variant
  button?: any
  globalLayout?: string
  tooltip?: any
};

const ZPattern = ({
  theme = 'cream',
  header,
  subheader,
  media,
  button,
  variant,
  layout,
  tooltip
}: ZpatternPropsL1L2) => {
  const safeVariant = variant || 'default'
  const isCentered = equals(layout, 'layout2')

  const gridContentStyles = condition<{}>(
    isCentered,
    [styles.contentContainer, styles.centeredGrid],
    [styles.contentContainer]
  )

  return (
    <Container
      as="section"
      theme={theme}
    >
      <Container
        as="div"
        styleProp={gridContentStyles}
      >
        <Container
          as="div"
          styleProp={{
            ...styles.imgContainer,
            ...variantStyles[safeVariant],
            ...isCentered && styles.imgContainerCentered
          }}
        >
          {tooltip && (<AtomicLevelTooltip header={tooltip.title} content={tooltip.description} theme={tooltip.theme} />)}
          <MediaContainer
            header={header}
            objectFit="cover"
            asset={{ url: media }}
            imageHeight="432px"
            autoPlay
            styleProps={
              styles.mediaContainer
            }
          />

        </Container>
        <Container as="div" styleProp={styles.bodyContainer}>
          <Typography textLeft as="h2" typographyTheme="h4Secondary" fontBold>
            {header}
          </Typography>
          <Container as="div" styleProp={styles.subheader}>
            {subheader}
          </Container>
          {button && (
          <CallToAction
            styleProp={styles.layout1Button}
            useArrow
            {...button}
          />
          )}
        </Container>
      </Container>
    </Container>
  )
}

export default ZPattern
