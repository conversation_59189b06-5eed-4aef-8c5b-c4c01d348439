import {
  colors,
  defaultTheme as $T
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

// TODO: Add focus states with outline

const styles = stylex.create({
  base: {
    fontSize: 14,
    lineHeight: 1.5,
    display: 'inline-block',
    borderRadius: 40,
    textAlign: 'center',
    fontWeight: 600,
    textTransform: 'capitalize',
    padding: '12px 24px',
    backgroundColor: {
      default: $T.primarySurface,
      ':disabled': colors.gray,
    },
    cursor: {
      default: 'pointer',
      ':disabled': 'not-allowed',
    },
    color: {
      default: $T.primaryText,
      ':disabled': colors.gray,
    },
  },
  primary: {
    backgroundColor: {
      default: $T.primaryCTASurface,
      ':hover': {
        default: null,
        '@media (hover: hover)': $T.primaryCTASurfaceHover,
      },
    },
    color: {
      default: $T.primaryCTAText,
      ':hover': {
        default: null,
        '@media (hover: hover)': $T.primaryCTAText,
      },
    },
  },
  secondary: {
    backgroundColor: {
      default: $T.secondaryCTASurface,
      ':hover': {
        default: null,
        '@media (hover: hover)': $T.secondaryCTASurfaceHover,
      },
    },
    color: {
      default: $T.secondaryCTAText,
      ':hover': {
        default: null,
        '@media (hover: hover)': $T.secondaryCTAText,
      },
    },
  },
  tertiary: {
    backgroundColor: {
      default: $T.tertiaryCTASurface,
      ':hover': {
        default: null,
        '@media (hover: hover)': $T.tertiaryCTASurfaceHover,
      },
    },
    color: {
      default: $T.tertiaryCTAText,
      ':hover': {
        default: null,
        '@media (hover: hover)': $T.tertiaryCTAText,
      },
    },
  },
  transparent: {
    backgroundColor: {
      default: 'transparent',
      ':hover': 'transparent'
    }
  },
  transparentBorder: {
    backgroundColor: {
      default: 'transparent',
      ':hover': $T.primaryCTASurface,
    },
    borderStyle: 'solid',
    borderWidth: 2,
    borderColor: $T.primaryCTASurface,
    color: {
      default: $T.primaryCTASurface,
      ':hover': $T.primaryCTAText,
    }
  },
  disabled: {
    backgroundColor: colors.gray,
    borderWidth: null,
    cursor: 'not-allowed',
    color: colors.white,
  },
  withIcon: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    textDecoration: 'none',
    gap: '6px',
    fontWeight: 400,
  },
  underlined: {
    textDecoration: 'underline',
    backgroundColor: 'transparent',
    color: $T.primaryCTASurface,
    padding: 0,
  },
  small: {
    padding: '8px 16px',
  },
  round: {
    padding: '6px',
  },
  inline: {
    padding: 0,
    lineHeight: 'inherit',
  },
  fullWidth: {
    width: '100%',
  },
  fullWidthMobile: {
    width: {
      default: '100%',
      '@media (min-width: 1024px)': 'auto'
    }
  },
  larger: {
    minWidth: '250px',
  }
})

export default styles
