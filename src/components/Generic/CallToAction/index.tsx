'use client'

import styles from './tokens.stylex'
import { CallToActionVariants } from './types'

import themes, { ThemeColors } from '@/app/themeThemes.stylex'
import { navigationClicked } from '@/redux/features/events/eventsSlice'
import { useAppDispatch } from '@redux/hooks'
import Icon from '@components/Generic/Icon'
import { defaultTheme as $T } from '@/app/themeTokens.stylex'

import Link from 'next/link'
import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'
import { ReactNode } from 'react'

// TODO: connect this to the full icon library via props to avoid all these extra props

export type CTAProps = {
  children?: ReactNode;
  size?: 'small' | 'round' | 'inline';
  submit?: boolean;
  onClick?: () => void;
  onMouseOver?: () => void;
  onMouseOut?: () => void;
  theme?: ThemeColors;
  variant?: CallToActionVariants;
  href?: string;
  custom?: false | ((children: ReactNode, styles: stylex.StyleXStyles, props: CTAProps) => ReactNode)
  disabled?: boolean;
  useArrow?: boolean;
  useLongArrow?: boolean;
  id?: string;
  fullWidth?: boolean;
  fullWidthMobile?: boolean;
  larger?: boolean;
  icon?: {
    url: string
  };
  styleProp?: stylex.StyleXStyles;
  anchor?: string;
}

export type ButtonProps = Omit<CTAProps, 'href'>

export type AnchorProps = Omit<CTAProps, 'onClick'>

// eslint-disable-next-line complexity
const Button = ({
  children,
  onClick,
  onMouseOver,
  onMouseOut,
  submit,
  disabled = false,
  useArrow = false,
  useLongArrow = false,
  icon,
  styleProp,
  ...props
}: ButtonProps) => (
  <button
    type={submit ? 'submit' : 'button'}
    onClick={onClick}
    disabled={disabled}
    onMouseOver={onMouseOver}
    onFocus={onMouseOver}
    onMouseOut={onMouseOut}
    onBlur={onMouseOut}
    {...stylex.props(styleProp)}
    {...props}
  >
    {icon && (
      <Image
        src={icon.url}
        alt="Icon"
        width={24}
        height={24}
        loading="lazy"
        style={{ objectFit: 'contain' }}
      />
    )}
    {children} {useArrow && <span>→</span>}
    {useLongArrow && <Icon name="BigRightLongArrow" fill={$T.primaryCTASurface} />}
  </button>
)

const Anchor = ({
  children,
  href,
  disabled = false,
  useArrow = false,
  useLongArrow = false,
  icon,
  styleProp,
  ...props
}: AnchorProps) => {
  const dispatch = useAppDispatch()
  return (
    <Link
      href={disabled ? '#' : (href as string)}
      onClick={() => dispatch(navigationClicked({
        navigationUrl: href as string,
        // TODO: Handle cases when children is not a string
        // navigationTitle: children as string,
        // label: children as string
      }))}
      {...stylex.props(styleProp)}
      {...props}
    >
      {icon && (
        <Image
          src={icon.url}
          alt="Icon"
          width={24}
          height={24}
          loading="lazy"
          style={{ objectFit: 'contain' }}
        />
      )}
      {children} {useArrow && <span>→</span>}
      {useLongArrow && <Icon name="BigRightLongArrow" fill={$T.primaryCTASurface} />}
    </Link>
  )
}

// eslint-disable-next-line complexity
const CallToAction = (props: CTAProps) => {
  const {
    children,
    href,
    size,
    theme,
    variant,
    icon,
    styleProp,
    fullWidth = false,
    fullWidthMobile = false,
    disabled = false,
    larger = false,
    custom = false,
  } = props

  const ctaStyles = [
    styles.base,
    size && styles[size],
    theme && themes[theme],
    fullWidth && styles.fullWidth,
    variant && styles[variant],
    fullWidthMobile && styles.fullWidthMobile,
    disabled && styles.disabled,
    !!icon && styles.withIcon,
    larger && styles.larger,
    styleProp,
  ] as stylex.StyleXStyles

  if (custom) {
    return custom(children, ctaStyles, props)
  }

  if (href === '' || typeof href === 'undefined') {
    return (
      <Button {...props} styleProp={ctaStyles}>
        {children}
      </Button>
    )
  }

  return (
    <Anchor {...props} styleProp={ctaStyles}>
      {children}
    </Anchor>
  )
}

export default CallToAction
