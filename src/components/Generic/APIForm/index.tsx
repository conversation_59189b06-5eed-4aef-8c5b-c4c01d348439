'use client'

import Typography from '@components/Typography'
import formatPhoneNumber from '@utils/phoneNumberConversion'
import { colors } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import { subscribeToPostScript } from '@/app/forms/utils/serverFunctions'

import React, { ChangeEvent, useState } from 'react'
import { useForm } from 'react-hook-form'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  form: {
    zIndex: '1',
  },
  header: {
    color: colors.white,
    maxWidth: '450px',
  },
  body: {
    maxWidth: '425px',
    color: colors.white,
  },
  phoneInput: {
    width: '100%',
    maxWidth: '400px',
    height: '50px',
    paddingLeft: '1rem',

  },
  submitButton: {
    height: '50px',
    color: colors.navy,
    fontWeight: 'bold',
    borderRadius: '9999px',
    backgroundColor: colors.marigold,
    width: '100%',
    maxWidth: '400px',
    borderStyle: 'none',
  },
  successMessage: {
    color: colors.marigold,
  },
  disclaimer: {
    maxWidth: '800px',
    color: colors.white,
    position: 'absolute',
    bottom: '20px'
  },
  fontWhite: {
    color: colors.white
  },
})

type FormDetailsObjectType = {
  header: string,
  body: string,
  ctaCopy: string,
  disclaimer?: string,
  successMessage: string,
  keyword: string,
}

type Props = {
  formDetailsObject: FormDetailsObjectType,
}

type FormData = {
  phoneNumber: string,
  email?: string
}

// eslint-disable-next-line complexity
const APIForm = ({ formDetailsObject }: Props) => {
  const {
    header,
    body,
    ctaCopy,
    keyword,
  } = formDetailsObject

  const [successState, setSuccessState] = useState<boolean>(false)
  const [apiErrorState, setApiErrorState] = useState<string>('')

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    resetField,
  } = useForm<FormData>()

  const onFormSubmit = async (data: FormData) => {
    const cleanedPhoneNumber = data.phoneNumber.replace(/\D/g, '')

    const payload = {
      number: cleanedPhoneNumber,
      email: '',
      keyword
    }

    const res = await subscribeToPostScript(payload)

    if ('phone_number' in res) {
      setSuccessState(true)
      setApiErrorState('')
      resetField('phoneNumber')
    }

    if ('errors' in res) {
      setApiErrorState(res.errors[0].type)
      setSuccessState(false)
      resetField('phoneNumber')
    }
  }

  const handlePhoneChange = (e: ChangeEvent<HTMLInputElement>) => {
    const inputEvent = e.nativeEvent as InputEvent

    const formattedPhoneNumber = formatPhoneNumber(
      e.target.value,
      inputEvent.inputType === 'deleteContentBackward'
    )

    setValue('phoneNumber', formattedPhoneNumber)
  }

  return (
    <Container as="form" flex flexCentered alignCentered gap="3" paddingInline="md" onSubmit={handleSubmit(onFormSubmit)} styleProp={[styles.form]}>
      <Typography fontSecondary fontBold textCentered as="h3" styleProp={styles.header}>{header}</Typography>

      <Typography textCentered as="p" size="sm" styleProp={styles.body}>{body}</Typography>

      {!successState && (
        <input
          {...register('phoneNumber', {
            required: 'Phone number is required',
            minLength: 10,
            onChange: handlePhoneChange,
          })}
          placeholder="Phone Number"
          {...stylex.props(styles.phoneInput)}
        />
      )}

      {!successState && (
        <input value={ctaCopy} type="submit" {...stylex.props(styles.submitButton)} />
      )}

      {successState && (
        <Typography as="p" size="xxs" textCentered fontBold styleProp={styles.successMessage}>
          You&apos;re in! Stay tuned for exclusive updates and early access.
        </Typography>
      )}

      {errors?.phoneNumber?.type === 'minLength' && (
        <Typography as="p" size="xs" textCentered styleProp={styles.fontWhite}>
          Phone number must be at least 10 digits
        </Typography>
      )}

      {apiErrorState === 'v2.entity_conflict' && (
        <Typography as="p" size="xs" textCentered styleProp={styles.fontWhite}>
          You&apos;re already subscribed! Don&apos;t worry, you&apos;ll still be first to know when it launches.
        </Typography>
      )}

      {apiErrorState === 'value_error' && (
        <Typography as="p" size="xs" textCentered styleProp={styles.fontWhite}>
          Could not validate phone number.
        </Typography>
      )}

      <Typography as="p" size="xxs" textCentered styleProp={styles.disclaimer}>
        {/* eslint-disable-next-line max-len */}
        *By providing your phone number or submitting this form, you agree to receive recurring automated marketing text messages (e.g. cart reminders) from this shop and third parties acting on its behalf at the phone number provided. Consent is not a condition to obtain goods or services. Msg & data rates may apply. Msg frequency varies. Reply HELP for help and STOP to cancel. You also agree to the Terms of Service and Privacy Policy.
      </Typography>
    </Container>
  )
}

export default APIForm
