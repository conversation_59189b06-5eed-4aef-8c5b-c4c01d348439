'use client'

import { CallToActionVariants } from '../CallToAction/types'

import Typography from '@components/Typography'
import CallToAction from '@components/Generic/CallToAction'
import Container from '@components/layout/Container'
import { spacing } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'

import { ChangeEvent, useRef, useState } from 'react'
// eslint-disable-next-line import/no-extraneous-dependencies
import { HTMLImgComparisonSliderElement, ImgComparisonSlider } from '@img-comparison-slider/react'
import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

type ImageProps = {
  src: string
  alt: string
  width: number
  height: number
}

export type BeforeAfterSliderProps = {
  beforeLabel?: string,
  beforeImage: ImageProps,
  afterLabel?: string,
  afterImage: ImageProps,
  styleProp?: {},
  button?: {
    text: string;
    variant?: CallToActionVariants;
    onClick?: () => void;
    theme?: ThemeColors;
    href?: string;
    disabled?: boolean;
    useArrow?: boolean;
    id?: string;
    visibleOnDesktop?: boolean;
  }
}

const DEKSTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
    gap: `calc(3 * ${spacing.xs})`,
    paddingInline: {
      default: spacing.md,
      [DEKSTOP]: 0,
    },
  },
  beforeAfter: {
    width: '100%',
    height: 'auto',
    marginBlockStart: spacing.xs,
    marginBlockEnd: {
      default: spacing.md,
      [DEKSTOP]: 0,
    },
  },
  img: {
    width: '100%',
    height: 'auto',
  },
  mobileOnly: {
    display: {
      default: 'flex',
      [DEKSTOP]: 'none',
    },
    flexDirection: 'column',
    width: '100%',
  }
})

const BeforeAfterSlider = ({
  beforeLabel = 'before',
  beforeImage,
  afterLabel = 'after',
  afterImage,
  button
}: BeforeAfterSliderProps) => {
  const ref = useRef<HTMLImgComparisonSliderElement>(null)
  const [isAftter, setIsAfter] = useState(false)
  const MAX_VALUE = 100
  const COMPARE_VALUE = 50

  const handleSlide = (e: ChangeEvent<HTMLImgComparisonSliderElement>) => setIsAfter(e.target.value > COMPARE_VALUE)

  const handleClick = (value: number) => () => {
    if (ref.current) {
      ref.current.value = value
      setIsAfter(value > COMPARE_VALUE)
    }
  }

  return (
    <Container as="div" {...stylex.props(styles.wrapper)}>
      <Container as="div">
        <CallToAction variant="underlined" onClick={handleClick(0)}>
          <Typography
            as="span"
            size="sm"
            underline={!isAftter}
            fontBold={!isAftter}
          >
            {beforeLabel}
          </Typography>
        </CallToAction>
        <Typography as="span" size="sm"> / </Typography>
        <CallToAction variant="underlined" onClick={handleClick(MAX_VALUE)}>
          <Typography
            as="span"
            size="sm"
            underline={isAftter}
            fontBold={isAftter}
          >
            {afterLabel}
          </Typography>
        </CallToAction>
      </Container>
      <ImgComparisonSlider ref={ref} onSlide={handleSlide} {...stylex.props(styles.beforeAfter)}>
        <Image
          slot="first"
          src={beforeImage.src}
          alt={beforeImage.alt}
          width={beforeImage.width}
          height={beforeImage.height}
          sizes="100vw"
          {...stylex.props(styles.img)}
        />
        <Image
          slot="second"
          src={afterImage.src}
          alt={afterImage.alt}
          width={afterImage.width}
          height={afterImage.height}
          sizes="100vw"
          {...stylex.props(styles.img)}
        />
      </ImgComparisonSlider>
      {button && (
        <Container as="div" styleProp={!button.visibleOnDesktop && styles.mobileOnly}>
          <CallToAction {...button}>
            {button.text}
          </CallToAction>
        </Container>
      )}
    </Container>
  )
}

export default BeforeAfterSlider
