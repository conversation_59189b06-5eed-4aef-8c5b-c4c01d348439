import CallToAction from '@components/Generic/CallToAction'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'

import * as stylex from '@stylexjs/stylex'

import type { CallToActionVariants } from '@components/Generic/CallToAction/types'

type PillProps = {
  text: string;
  href: string;
  variant?: CallToActionVariants;
};

type PillCollectionProps = {
  pill: PillProps;
  styleProp?: {};
  onClick?: () => void;
}

const styles = stylex.create({
  pillWrapper: {
    boxSizing: 'border-box',
    margin: 0,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '10px',
    borderRadius: '25px',
    borderWidth: '1px',
    borderStyle: 'solid',
    cursor: 'pointer',
    transition: '0.2s ease-in-out all',
    maxWidth: '300px'
  },
  anchor: {
    padding: '10px 24px'
  },
  text: {
    whiteSpace: 'nowrap',
  },
})

const PillCollection = ({
  pill,
  styleProp,
  onClick
}: PillCollectionProps) => (
  <CallToAction
    href={pill.href}
    size="small"
    variant={pill.variant}
    styleProp={[
      styles.pillWrapper,
      styles.anchor,
      styleProp
    ]}
    onClick={onClick}
  >
    <Container
      as="div"
      flex
      flexRow
      noWrap
    >
      <Typography as="span" typographyTheme="bodySmall" styleProp={styles.text}>
        {pill.text}
      </Typography>
    </Container>
  </CallToAction>
)

export default PillCollection
