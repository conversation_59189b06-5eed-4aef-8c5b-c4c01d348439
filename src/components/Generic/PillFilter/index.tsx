'use client'

import ArrowLightDown from '@components/Generic/Icon/lib/ArrowLightDown'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import CallToAction from '@components/Generic/CallToAction'
import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import { useState } from 'react'

type PillProps = {
  text: string
  href: string
};

const moveDown = stylex.keyframes({
  '0%': { transform: 'translateY(0px) scale(1)' },
  '15%': { transform: 'translateY(0px) scale(1)' },
  '30%': { transform: 'translateY(-3px)' },
  '65%': { transform: 'translateY(2.4px) scale(1.1)' },
  '80%': { transform: 'translateY(2.4px) scale(1.1)' },
  '85%': { transform: 'translateY(-2px) scale(0.9)' },
  '90%': { transform: 'translateY(1px) scale(1.05)' },
  '100%': { transform: 'translateY(0px) scale(1)' },
})

const styles = stylex.create({
  pillWrapper: {
    boxSizing: 'border-box',
    margin: 0,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '10px',
    borderRadius: '25px',
    backgroundColor: {
      default: 'transparent',
      ':hover': colors.cream,
    },
    borderColor: {
      default: colors.white,
      ':hover': colors.cream,
    },
    borderWidth: '1px',
    borderStyle: 'solid',
    cursor: 'pointer',
    transition: '0.2s ease-in-out all',
    // eslint-disable-next-line @stylexjs/valid-styles
    ':hover .ArrowLightDown': {
      animationName: {
        default: 'none',
        '@media (min-width: 1024px)': moveDown,
      },
      animationIterationCount: 'infinite',
      animationTimingFunction: 'ease-in-out',
      animationDuration: '1500ms',
    },
  },
  pillWrapperCTA: {
    color: {
      default: colors.white,
      ':hover': colors.navy,
    },
    paddingInline: '24px',
    paddingBlock: '7px',
  },
  text: {
    whiteSpace: 'nowrap',
    marginRight: '10px',
    fontSize: '14px',
  },
})

const PillFilter = ({ theme, pill }: {
  theme: ThemeColors
  pill: PillProps
}) => {
  const [svgColor, setSvgColor] = useState(false)
  return (
    <Container
      as="div"
      flex
      noWrap
      flexRow
      styleProp={styles.pillWrapper}
      onMouseEnter={() => setSvgColor(true)}
      onMouseLeave={() => setSvgColor(false)}
    >
      <CallToAction
        href={pill.href}
        size="small"
        theme={theme}
        variant="transparent"
        styleProp={styles.pillWrapperCTA}
      >
        <Container
          as="div"
          flex
          flexRow
          noWrap
          alignCentered
        >
          <Typography as="span" typographyTheme="bodySmall" styleProp={styles.text}>
            {pill.text}
          </Typography>

          <ArrowLightDown color={svgColor ? colors.navy : colors.white} />
        </Container>
      </CallToAction>
    </Container>
  )
}

export default PillFilter
