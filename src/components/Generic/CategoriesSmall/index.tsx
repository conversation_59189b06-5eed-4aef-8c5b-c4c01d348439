'use client'

import Container, { Size } from '@/components/layout/Container'
import Typography from '@/components/Typography'
import Slider, { Slide } from '@components/Generic/Slider'
import { NextButton, PrevButton } from '@components/Generic/Slider/SliderButtons'
import Card from '@components/Generic/Card'
import { cardProps } from '@components/Generic/Card/types'
import { spacing, colors } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'

import * as stylex from '@stylexjs/stylex'
import React, { useCallback, useEffect, useState } from 'react'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  categoryHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBlockEnd: `calc(4 * ${spacing.xs})`,
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: spacing.xl
    }
  },
  categorySlider: {
    paddingInlineStart: {
      default: spacing.md,
      [DESKTOP]: spacing.xl
    }
  },
  categorySlide: {
    textAlign: 'center',
    marginInlineEnd: `calc(4 * ${spacing.xs})`
  },
  carouselItems: {
    flex: {
      default: '0 0 40%',
      [DESKTOP]: '0 0 16%'
    },
    textAlign: 'center',
    marginInlineEnd: `calc(4 * ${spacing.xs})`
  },
  basicItems: {
    flex: {
      default: '0 0 80%',
      [DESKTOP]: `0 0 calc(16% - 4 * ${spacing.xs})`
    },
    marginBottom: spacing.lg
  },
  moreItems: {
    flex: {
      default: '0 0 80%',
      [DESKTOP]: `0 0 calc(20% - 4 * ${spacing.xs})`
    },
    marginBlockEnd: spacing.lg
  },
  arrows: {
    display: {
      default: 'none',
      [DESKTOP]: 'flex',
    }
  },
  arrowsLayout2: {
    position: 'absolute',
    right: '4rem',
    top: '4rem',
  },
  textCentered: {
    marginLeft: 'auto',
    marginRight: 'auto'
  },
  container: {
    position: 'relative',
  }
})

type categoriesSmallProps = {
  header?: string,
  paddingBlock?: Size,
  hideArrows?: boolean,
  cards?: cardProps[],
  sliderOptions?: object,
  textCentered?: boolean,
  gridView?: boolean,
  children?: React.ReactNode,
  theme?: ThemeColors,
  layout?: string,
}

// eslint-disable-next-line complexity
const CategoriesSmall = ({
  header,
  paddingBlock,
  cards,
  sliderOptions = { align: 'start' },
  hideArrows,
  textCentered,
  gridView,
  children,
  theme = 'white',
  layout
}: categoriesSmallProps) => {
  if (layout === 'layout2') {
    textCentered = true
    paddingBlock = '7'
  }
  const [emblaApi, setEmblaApi] = useState<any[]>([])
  const [isStart, setIsStart] = useState<boolean>(true)
  const [isEnd, setIsEnd] = useState<boolean>(false)

  const scrollPrev = useCallback(
    () => (emblaApi.length > 0 ? emblaApi[0].scrollPrev() : null),
    [emblaApi]
  )
  const scrollNext = useCallback(
    () => (emblaApi.length > 0 ? emblaApi[0].scrollNext() : null),
    [emblaApi]
  )

  useEffect(() => {
    if (!emblaApi.length) return

    const checkButtons = () => {
      setIsStart(!emblaApi[0].canScrollPrev())
      setIsEnd(!emblaApi[0].canScrollNext())
    }

    emblaApi[0].on('select', checkButtons)
    emblaApi[0].on('reInit', checkButtons)

    checkButtons()
  }, [emblaApi, isStart, isEnd])

  const MORE_ITEMS = 6
  const gridItemClass = cards && cards.length > MORE_ITEMS ? styles.moreItems : styles.basicItems

  return (
    <Container as="section" theme={theme} paddingBlock={paddingBlock} styleProp={styles.container}>
      <Container as="div" styleProp={styles.categoryHeader}>
        {header && (
          <Typography as="h3" size="h4" fontSecondary styleProp={textCentered && styles.textCentered}>
            {header}
          </Typography>
        )}
        {!hideArrows && (
          <Container as="div" flex flexRow gap="7" styleProp={[styles.arrows, layout === 'layout2' && styles.arrowsLayout2]}>
            <PrevButton onClick={scrollPrev} enabled fill={isStart ? colors.gray : colors.navy} />
            <NextButton onClick={scrollNext} enabled fill={isEnd ? colors.gray : colors.navy} />
          </Container>
        )}
      </Container>
      <Slider
        options={sliderOptions}
        styleProp={styles.categorySlider}
        setEmblaList={setEmblaApi}
        flexWrap={gridView}
      >
        {cards && cards.map((card) => (
          <Slide styleProp={{ ...styles.categorySlide, ...gridView ? gridItemClass : styles.carouselItems }} key={`categ-${card.id}`}>
            <Card {...card} />
          </Slide>
        ))}
        {children}
      </Slider>
    </Container>
  )
}

export default CategoriesSmall
