import {
  CalloutProps,
  CalloutsProps
} from './types'

import RenderIf from '@utils/renderIf'
import { notEmpty } from '@utils/checking'
import { toCamelCase } from '@utils/regex'
import Callout from '@components/Generic/Callout'
import Container from '@components/layout/Container'
import { ThemeColors } from '@/app/themeThemes.stylex'
import {
  globalTokens as $G,
  defaultTheme as $T
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  borderBase: {
    borderColor: $T.primarySurface,
    borderWidth: 1,
    borderStyle: 'solid',
    borderRadius: $G.borderRadiusSmall,
  },
  borderTransparent: {
    borderColor: $T.primaryText,
    backgroundColor: 'transparent',
  }
})

const Callouts = ({
  callouts,
  size,
  styleProp
}: CalloutsProps) => (
  <RenderIf condition={notEmpty(callouts)}>
    <Container as="div" flex flexRow gap="1">
      {callouts.map((callout: CalloutProps) => (
        <Callout
          key={callout.sys.id}
          title={callout.title}
          theme={callout.settings?.theme && (toCamelCase(callout.settings.theme) as ThemeColors)}
          size={size}
          styleProp={[
            styleProp,
            styles.borderBase,
            callout.settings?.layout && toCamelCase(callout.settings.layout || '') === 'layout2' && styles.borderTransparent
          ]}
        />
      ))}
    </Container>
  </RenderIf>
)

export default Callouts
