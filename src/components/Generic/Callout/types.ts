import { ThemeColors } from '@/app/themeThemes.stylex'
import { ContentfulImage } from '@/lib/contentful/types/generic'

export type CalloutProps = {
  title: string,
  badge?: ContentfulImage,
  placement?: string[],
  settings?: {
    theme?: ThemeColors,
    layout?: string,
  }
  sys: {
    id: string
  }
}

export type CalloutsProps = {
  callouts: CalloutProps[]
  size?: 'small' | 'medium'
  styleProp?: {}
}
