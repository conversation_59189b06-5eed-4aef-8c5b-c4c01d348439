import { colors } from '@/app/themeTokens.stylex'
import Typography from '@/components/Typography'

type SimpleCalloutProps = {
  title: string;
  theme: keyof typeof colors
}

const SimpleCallout = ({ title, theme }: SimpleCalloutProps) => (
  <Typography
    as="span"
    typographyTheme="captionLarge"
    typographyThemeMobile="captionSmall"
    styleProp={{
      color: colors[theme]
    }}
    uppercase
    fontBold
  >
    {title}
  </Typography>
)

export default SimpleCallout
