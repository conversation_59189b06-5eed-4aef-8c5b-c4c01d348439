'use client'

import Callouts from '@components/Generic/Callout/Callouts'
import { CalloutProps } from '@components/Generic/Callout/types'
import useDynamicPricing from '@hooks/useDynamicPricing'

import { useEffect, useState } from 'react'

type DynamicCalloutsProps = {
  calloutItems: CalloutProps[]
  price: number,
  compareAtPrice?: number,
  showDiscountValue?: boolean
  slug?: string
}

const DISALLOW_LIST = ['gift-card']

const DynamicCallouts = ({
  calloutItems,
  price,
  compareAtPrice,
  showDiscountValue,
  slug = ''
}: DynamicCalloutsProps) => {
  const [callouts, setCallouts] = useState<CalloutProps[]>(calloutItems)
  const { callouts: dynamicCallouts } = useDynamicPricing({ price, compareAtPrice, showDiscountValue })
  const isSlugDisallowed = DISALLOW_LIST.includes(slug)

  useEffect(() => {
    const staticCallouts = calloutItems.filter((callout) => !dynamicCallouts.some((dynCallout) => dynCallout.sys.id === callout.sys.id))
    const highestTierCallout = dynamicCallouts.length > 0 && !isSlugDisallowed ? [dynamicCallouts[0]] : []
    setCallouts([...highestTierCallout, ...staticCallouts])
  }, [calloutItems, dynamicCallouts, price, compareAtPrice, slug, isSlugDisallowed])

  return <Callouts callouts={callouts} />
}

export default DynamicCallouts
