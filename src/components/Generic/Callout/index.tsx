import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import { ThemeColors } from '@/app/themeThemes.stylex'
import {
  spacing,
  globalTokens as $
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

// TODO: add callout styles and layouts
type CalloutProps = {
  title: string,
  theme?: ThemeColors,
  size?: 'small' | 'medium'
  styleProp?: {}
}

const styles = stylex.create({
  root: {
    borderRadius: $.borderRadiusSmall,
  },
  small: {
    paddingInline: `calc(${spacing.xs} / 2)`,
    paddingBlock: `calc(${spacing.xxs} / 1.75)`
  },
  medium: {
    paddingInline: `calc(${spacing.xs} / 1.5)`,
    paddingBlock: `calc(${spacing.xxs} / 1.25)`
  }
})

const Callout = ({
  title,
  theme = 'mist',
  size = 'medium',
  styleProp
}: CalloutProps) => (
  <Container
    as="div"
    theme={theme}
    styleProp={[styles.root, styleProp]}
  >
    <Typography
      as="p"
      typographyTheme={size === 'small' ? 'captionSmall' : 'captionLarge'}
      typographyThemeMobile="captionSmall"
      styleProp={styles[size]}
      uppercase
      fontBold
    >
      {title}
    </Typography>
  </Container>
)

export default Callout
