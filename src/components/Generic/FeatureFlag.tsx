'use client'

import RenderIf from './RenderIf'

import { findUrlExperimentById } from '@/providers/GrowthBook'

import { ReactNode } from 'react'
import { useFeatureIsOn, useFeatureValue } from '@growthbook/growthbook-react'

type FeatureFlagProps = {
  name: string
  defaultValue: string
  children: ({ isOn, value }: { isOn: boolean; value: string }) => ReactNode
}

const FeatureFlag = ({
  name = '',
  defaultValue = '',
  children = () => null,
}: FeatureFlagProps) => {
  const isOn = useFeatureIsOn(name)
  const value = useFeatureValue(name, defaultValue)
  const forcedVariant = findUrlExperimentById(name)

  return (
    <RenderIf condition={isOn}>
      {children({
        isOn,
        value: forcedVariant?.variationId || value
      })}
    </RenderIf>
  )
}

export default FeatureFlag
