import { colors } from '@/app/themeTokens.stylex'
import Icon from '@components/Generic/Icon'
import { IconsSize } from '@components/Generic/Icon/types'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'

export type StarsProps = {
  size?: IconsSize;
  starLength?: number;
  iconColor?: keyof typeof colors;
  onStarClick?: Function;
}

const styles = stylex.create({
  container: {
    cursor: 'pointer',
  }
})

const MAX_RATING = 5

const Stars = ({
  starLength = 1,
  iconColor = 'navy',
  size,
  onStarClick = () => {},
}: StarsProps) => (
  <Container as="div" flex flexRow styleProp={styles.container}>
    {
      Array.from({ length: starLength }, (_, i) => i).map((key) => (
        <Icon
          key={key}
          name="Star"
          size={size}
          fill={colors[iconColor] as string}
          onClick={() => onStarClick(key)}
        />
      ))
    }
    {
      Array.from({ length: MAX_RATING - starLength }, (_, i) => i).map((key) => (
        <Icon
          key={key}
          name="Star"
          size={size}
          fill={colors.gray300}
          onClick={() => onStarClick(key)}
        />
      ))
    }
  </Container>
)

export default Stars
