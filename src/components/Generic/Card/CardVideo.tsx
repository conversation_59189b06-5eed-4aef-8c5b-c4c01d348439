'use client'

import { cardVideoProps } from './types'
import styles from './tokens.stylex'

import useIsMobile from '@/hooks/isMobile'

import { useRef, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import * as stylex from '@stylexjs/stylex'

const CardVideo = ({
  isPlaying,
  link,
  loop = true,
  muted = true,
  playOnHover,
  playsInline = true,
  ratio,
  styleProp = {},
  video,
  posterImage,
  hover
}: cardVideoProps) => {
  const aspectRatio = ratio || '1'
  const videoRef = useRef<HTMLVideoElement>(null)
  const { isMobile } = useIsMobile()

  const handlePlay = () => {
    if (!videoRef.current) return
    if (isPlaying) {
      videoRef.current.play()
      if (posterImage) {
        videoRef.current.currentTime = 0
      }
      return
    }
    videoRef.current.pause()
  }

  useEffect(() => {
    if (!videoRef.current) return
    if (isMobile) {
      videoRef.current.play()
    } else {
      handlePlay()
    }
  }, [isPlaying, videoRef, isMobile])

  const VideoComponent = (
    <>
      <video
        ref={videoRef}
        playsInline={playsInline}
        autoPlay={video.autoPlay || !playOnHover}
        muted={muted}
        loop={loop}
        poster={video.poster || ''}
        {...stylex.props(styles.video)}
      >
        {video.sources.map((source) => (
          <source key={source.id ?? source.src} src={source.src} type={source.type} />
        ))}
      </video>
      {posterImage?.url && !isMobile && (
        <Image
          src={posterImage.url}
          alt={posterImage.title}
          fill
          {...stylex.props([styles.hoverImage, hover && styles.hover])}
        />
      )}
    </>
  )

  if (link) {
    return (
      <Link href={link} {...stylex.props(styleProp)} style={{ aspectRatio }}>
        {VideoComponent}
      </Link>
    )
  }

  return (
    <div {...stylex.props(styleProp)} style={{ aspectRatio }}>
      {VideoComponent}
    </div>
  )
}

export default CardVideo
