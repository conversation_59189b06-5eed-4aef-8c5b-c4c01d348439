'use client'

import { cardContentProps, cardImageProps, cardProps } from './types'
import CardVideo from './CardVideo'
import styles from './tokens.stylex'

import Callouts from '@components/Generic/Callout/Callouts'
import { CalloutProps } from '@components/Generic/Callout/types'
import Typography from '@/components/Typography'

import { useState } from 'react'
import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import Link from 'next/link'

export const CardImage = ({
  image,
  hoverImage,
  link,
  callouts,
  ratio,
  rounded,
  styleProp = {}
}: cardImageProps) => {
  const aspectRatio = ratio || `${image.width}/${image.height}`
  const imageProps = rounded ? { ...styles.image, ...styles.rounded } : styles.image
  const hoverImageProps = rounded ? { ...styles.hoverImage, ...styles.rounded } : styles.hoverImage

  const ImageComponent = (
    <>
      {callouts && (
        <div {...stylex.props(styles.callouts)}>
          <Callouts callouts={callouts as CalloutProps[]} />
        </div>
      )}
      <Image
        src={image.url}
        alt={image.title}
        fill
        {...stylex.props(imageProps, styleProp)}
      />
      {hoverImage?.url && (
        <Image
          src={hoverImage.url}
          alt={hoverImage.title}
          fill
          {...stylex.props(hoverImageProps)}
        />
      )}
    </>
  )

  if (link) {
    return (
      <Link href={link} {...stylex.props(styles.imageContainer)} style={{ aspectRatio }}>
        {ImageComponent}
      </Link>
    )
  }

  return (
    <div {...stylex.props(styles.imageContainer)} style={{ aspectRatio }}>
      {ImageComponent}
    </div>
  )
}

export const CardContent = ({
  title,
  link,
  withIcon,
  description
}: cardContentProps) => (
  <div {...stylex.props(styles.cardContent)}>
    {link && title ? (
      <Link
        href={link}
        title={title}
        {...stylex.props(styles.link)}
      >
        <Typography as="span" typographyTheme="bodySmall">
          {title} {withIcon && <span>→</span>}
        </Typography>
      </Link>
    ) : (
      <Typography as="span" size="h5">
        {title}
      </Typography>
    )}
    {description && <Typography as="span" size="bodySmall">{description}</Typography>}
  </div>
)

const Card = ({
  image,
  setIsHovered,
  hover,
  posterImage,
  title,
  description,
  callouts,
  link,
  children,
  rounded,
  ratio,
  withIcon,
  video,
  theme,
  styleProp = {},
  styleImageProp = {},
  styleContentProp = {},
  styleVideoProp = {},
}: cardProps) => {
  const [isPlaying, setIsPlaying] = useState(false)

  const handleMouseEnter = () => {
    if (video && video.playOnHover) {
      setIsPlaying(true)
    }
    if (setIsHovered) {
      setIsHovered(true)
    }
  }

  const handleMouseLeave = () => {
    if (video && video.playOnHover) {
      setIsPlaying(false)
    }
    if (setIsHovered) {
      setIsHovered(false)
    }
  }

  const renderImage = () => {
    if (!image) return null
    return (
      <CardImage
        callouts={callouts}
        hoverImage={posterImage}
        image={image}
        link={link}
        ratio={ratio || image.ratio}
        rounded={rounded}
        styleProp={styleImageProp}
      />
    )
  }

  const renderVideo = () => {
    if (!video) return null
    return (
      <CardVideo
        link={link}
        ratio={ratio || video.ratio}
        styleProp={styleVideoProp}
        video={video}
        posterImage={posterImage}
        hover={hover}
        playOnHover={video.playOnHover}
        isPlaying={video.playOnHover ? isPlaying : true}
      />
    )
  }

  return (
    <div
      {...stylex.props(styles.card, styleProp)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {renderImage()}
      {renderVideo()}
      {(title || description) && (
        <CardContent
          description={description}
          link={link}
          styleProp={styleContentProp}
          theme={theme}
          title={title}
          withIcon={withIcon}
        />
      )}
      {children && children}
    </div>
  )
}

export default Card
