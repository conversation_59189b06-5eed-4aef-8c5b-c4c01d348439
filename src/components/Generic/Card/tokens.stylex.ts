import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  card: {
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
  },
  imageContainer: {
    width: '100%',
    height: 'auto',
    padding: spacing.xs,
    position: 'relative',
  },
  callouts: {
    position: 'relative',
    zIndex: 1,
  },
  image: {
    objectFit: 'cover',
    transition: 'opacity 0.3s',
    opacity: {
      ':has(> img):hover': 1,
    },
  },
  hoverImage: {
    objectFit: 'cover',
    transition: 'opacity 0.3s',
    opacity: 1
  },
  hover: {
    opacity: 0,
    transition: 'opacity 0.3s',
  },
  rounded: {
    borderRadius: '50%',
    overflow: 'hidden',
  },
  cardContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: spacing.xs,
    marginTop: spacing.sm,
  },
  link: {
    textDecoration: {
      default: 'none',
      ':hover': 'underline',
    }
  },
  video: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
})

export default styles
