import { ThemeColors } from '@/app/themeThemes.stylex'

export type imageProps = {
  height: number;
  ratio?: string;
  title: string;
  url: string;
  width: number;
};

export type videoProps = {
  autoPlay?: boolean;
  loop?: boolean;
  muted?: boolean;
  playsInline?: boolean;
  poster?: string;
  ratio?: string;
  playOnHover?: boolean;
  sources: {
    id: string;
    src: string;
    type: string;
  }[];
};

export type CalloutsProps = {
  title: string;
  settings: {
    theme: ThemeColors;
  };
};

export type cardImageProps = {
  callouts?: CalloutsProps[];
  hoverImage?: imageProps;
  image: imageProps;
  link?: string;
  ratio?: string;
  rounded?: boolean;
  styleProp?: {};
};

export type cardVideoProps = {
  isPlaying?: boolean;
  link?: string;
  loop?: boolean;
  muted?: boolean;
  name?: string;
  playOnHover?: boolean;
  playsInline?: boolean;
  ratio?: string;
  styleProp?: {};
  video: videoProps;
  posterImage?: imageProps;
  hover?: boolean
};

export type cardProps = {
  setIsHovered?: (isHovered: boolean) => void;
  hover?: boolean;
  callouts?: CalloutsProps[];
  children?: React.ReactNode;
  description?: string;
  posterImage?: imageProps;
  id?: string;
  image?: imageProps;
  link?: string;
  ratio?: string;
  rounded?: boolean;
  styleContentProp?: {};
  styleImageProp?: {};
  styleProp?: {};
  styleVideoProp?: {};
  theme?: ThemeColors;
  title?: string;
  video?: videoProps;
  withIcon?: boolean;
};

export type cardContentProps = {
  title?: string;
  link?: string;
  withIcon?: boolean;
  description?: string;
  theme?: ThemeColors;
  styleProp?: {};
};
