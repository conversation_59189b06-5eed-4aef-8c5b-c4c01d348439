'use client'

import { useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

export interface AnchorRedirectMap {
  [key: string]: string;
}

const DEFAULT_ANCHOR_REDIRECTS: AnchorRedirectMap = {
  '#cookware': '/collections/cookware',
  '#stainless-steel-cookware': '/collections/stainless-steel',
  '#cast-iron': '/collections/cast-iron',
  '#bakeware': '/collections/bakeware',
  '#food-storage': '/collections/food-storage',
  '#pantry-storage': '/collections/food-storage#pantry-storage',
  '#prepware': '/collections/prepware',
  '#bundles': '/collections/bundles',
  '#linens': '/linens-glass-lids-steamers',
  '#accessories': '/collections/accessories'
}

export default function AnchorRedirectHandler({ anchorRedirects = DEFAULT_ANCHOR_REDIRECTS }: { anchorRedirects?: AnchorRedirectMap }) {
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const { hash } = window.location

    if (!hash) return

    if (!anchorRedirects || Object.keys(anchorRedirects).length === 0) return

    const anchor = hash.slice(1)
    const redirectPath = anchorRedirects[`#${anchor}`]

    if (!redirectPath) return

    const currentSearchParams = searchParams.toString()

    const [basePath, hashFragment] = redirectPath.split('#')

    let destination = basePath

    if (currentSearchParams) {
      destination += `?${currentSearchParams}`
    }

    if (hashFragment) {
      destination += `#${hashFragment}`
    }

    router.push(destination)
  }, [router, searchParams, anchorRedirects])

  return null
}
