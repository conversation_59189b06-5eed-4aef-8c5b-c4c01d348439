import CallToAction, { AnchorProps } from '@/components/Generic/CallToAction'
import DialogTrigger from '@/components/Generic/Dialog/DialogTrigger'

const RenderCTAS = (
  {
    buttons = [],
    mergeProps = {},
    styleProp = {}
  }:{
    buttons: AnchorProps[],
    mergeProps?: AnchorProps,
    styleProp?: any
  }
) => buttons.map((button) => {
  const {
    children,
    href,
    id,
    variant = 'primary',
    theme,
    ...rest
  } = button
  return href ? (
    <CallToAction
      key={id}
      variant={variant}
      href={href}
      {...rest}
      {...mergeProps}
      theme={theme}
      styleProp={styleProp}
    >
      {children}
    </CallToAction>
  ) : (
    <DialogTrigger
      variant={variant}
      key={id}
      text={String(children)}
      id={id || ''}
      styleProp={styleProp}
      {...rest}
    />
  )
})

export default RenderCTAS
