'use client'

import Icon from '../Icon'

import { colors } from '@/app/themeTokens.stylex'
import Slider, { Slide } from '@components/Generic/Slider'
import Container from '@components/layout/Container'
import Wrapper from '@components/layout/Wrapper'
import Typography from '@components/Typography'
import MediaContainer from '@/components/Content/MediaContainer'
import { ContentfulTokenSettings } from '@/lib/contentful/types/generic'

import * as stylex from '@stylexjs/stylex'
import { FC, useEffect, useState } from 'react'

const LG = '@media (min-width: 992px)'
const MD = '@media (max-width: 768px)'
const XS = '@media (max-width: 375px)'

export type TimelineSliderProps = {
  slides: {
    id: string,
    media: string,
    mobileMedia: string,
    header: string,
    description: string
  }[]
  settings?: ContentfulTokenSettings
}

const styles = stylex.create({
  root: {
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: colors.offWhite,
    paddingBlock: {
      [LG]: 80,
      default: 40,
    },
    paddingInline: {
      default: 20,
      [LG]: 100,
    }
  },
  slide: {
    display: {
      default: 'block',
      [LG]: 'flex'
    },
    flexDirection: {
      default: 'column',
      [LG]: 'row-reverse',
    },
    width: 'min(100% - 2rem, 1200px)',
    marginInline: 'auto',
    columnGap: {
      default: 0,
      [LG]: '5%'
    }
  },
  slidesReversed: {
    flexDirection: {
      [LG]: 'row'
    }
  },
  left: {
    flex: {
      default: '2 2 auto',
      [LG]: '2 2 50%',
    },
    overflowY: {
      default: 'auto',
      [XS]: 'scroll'
    },
    display: {
      default: 'block',
      [LG]: 'flex'
    },
    justifyContent: {
      default: 'normal',
      [LG]: 'center'
    },
    flexDirection: {
      default: 'initial',
      [LG]: 'column'
    },
    marginBlockStart: {
      [LG]: 0,
      default: '32px'
    }
  },
  right: {
    display: 'block',
    flex: {
      default: '1 1.5 auto',
      [LG]: '1 1 40%'
    },
    flexDirection: 'column',
  },
  imageContainer: {
    position: 'relative',
    aspectRatio: '1',
    minHeight: {
      '@media (max-width: 1024px)': {
        default: '350px',
      },
    },
    width: {
      '@media (max-width: 1024px)': {
        default: '100%',
      },
    },
  },
  imageContainerContent: {
    height: '100%',
    width: '100%',
    maxHeight: '100%',
  },
  header: {
    marginBottom: 16
  },
  media: {
    objectFit: 'cover',
  },
  dot: {
    height: {
      default: 16,
      [MD]: 12,
    },
    width: {
      default: 16,
      [MD]: 12,
    },
    backgroundColor: colors.gray300,
    borderRadius: '50%',
  },
  isSelected: {
    backgroundColor: colors.sage
  },
  progress: {
    display: 'flex',
    flex: '1',
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  sliderContainer: {
    display: 'flex',
    justifyContent: 'space-evenly',
    alignContent: 'center',
    marginTop: {
      [LG]: 40,
      [MD]: 50,
      default: 20,
    },
    flexDirection: 'row',
    width: {
      [MD]: '100%',
      default: 685,
    },
    justifySelf: 'center',
    alignSelf: 'center',
  },
  progressContainer: {
    position: 'relative',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    marginInline: 25
  },
  progressLeft: {
    width: {
      default: '15% !important',
      [MD]: '50px !important',
    },
    height: 2,
    backgroundColor: colors.sage
  },
  progressRight: {
    width: {
      default: '15% !important',
      [MD]: '50px !important',
    },
    height: 2,
    backgroundColor: colors.gray300
  },
  progressRightFilled: {
    backgroundColor: colors.sage
  },
  progressMiddle: {
    flex: '1',
    height: 2,
    position: 'relative',
    backgroundColor: colors.gray300
  },
  progressFill: {
    position: 'absolute',
    height: 2,
    backgroundColor: colors.sage,
  },
  dotsContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 1,
    alignItems: 'center',
    width: '100%',
    paddingInline: {
      default: '15%',
      [MD]: 50,
    }
  },
  button: { color: colors.black },
  disabled: { color: colors.gray300 }
})

const TimelineSlider:FC<TimelineSliderProps> = ({ slides = [], settings }) => {
  const [emblaList, setEmblaList] = useState<any>([])
  const [currentSlide, setCurrentSlide] = useState(0)
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])
  const reversed = settings?.layout === 'Layout 2'

  const handleNext = () => {
    emblaList.forEach((embla: any) => embla.scrollNext())
    setCurrentSlide(emblaList[0].selectedScrollSnap())
  }

  const handlePrev = () => {
    emblaList.forEach((embla: any) => embla.scrollPrev())
    setCurrentSlide(emblaList[0].selectedScrollSnap())
  }

  const handleScroll = (index: number) => () => {
    emblaList.forEach((embla: any) => embla.scrollTo(index))
    setCurrentSlide(index)
  }

  useEffect(() => {
    if (!emblaList.length) return
    setScrollSnaps(emblaList[0].scrollSnapList())
    emblaList.forEach((embla: any) => {
      embla.on('select', () => {
        setCurrentSlide(embla.selectedScrollSnap())
      })
    })
  }, [emblaList])

  useEffect(() => {
    if (!emblaList.length) return
    emblaList.forEach((embla: any) => {
      embla.scrollTo(currentSlide)
    })
  }, [emblaList, currentSlide])

  return (
    <Wrapper styleProp={styles.root}>

      <Slider
        options={{ loop: false }}
        setEmblaList={setEmblaList}
        justifyStart
      >
        {slides.map((slide, index) => {
          const asset = { url: slide.mobileMedia ?? slide.media }
          return (
            <Slide
              key={`slide.title-${slide.id}`}
              styleProp={[
                styles.slide,
                reversed && styles.slidesReversed
              ]}
            >
              <Container as="div" styleProp={styles.right}>
                <Container as="div" styleProp={styles.imageContainer}>
                  <MediaContainer
                    asset={asset}
                    autoPlay={currentSlide === index}
                    soundIcon
                    imageHeight="100%"
                    fixedIcon
                    styleProps={styles.imageContainerContent}
                  />
                </Container>
              </Container>
              <Container as="div" styleProp={styles.left}>
                <Typography
                  as="h2"
                  typographyTheme="h2Secondary"
                  fontBold
                  styleProp={styles.header}
                >
                  {slide.header}
                </Typography>
                {slide.description}
              </Container>
            </Slide>
          )
        })}
      </Slider>
      <div {...stylex.props(styles.sliderContainer)}>
        <button
          type="button"
          onClick={handlePrev}
          disabled={currentSlide === 0}
          aria-label="Previous Slide"
        >
          <Icon name="LeftArrow" size="medium" {...stylex.props(styles.button, currentSlide === 0 && styles.disabled)} />
        </button>

        <div {...stylex.props(styles.progressContainer)}>
          <div {...stylex.props(styles.progress)}>
            <div {...stylex.props(styles.progressLeft)} />
            <div {...stylex.props(styles.progressMiddle)}>
              <div
                {...stylex.props(styles.progressFill)}
                style={{
                  // eslint-disable-next-line no-magic-numbers
                  width: `${(currentSlide / (scrollSnaps.length - 1)) * 100}%`,
                }}
              />
            </div>
            <div
              {...stylex.props({
                ...styles.progressRight,
                ...currentSlide === scrollSnaps.length - 1 && styles.progressRightFilled
              })}
            />
          </div>

          {/* Dots */}
          <div {...stylex.props(styles.dotsContainer)}>
            {Array(scrollSnaps.length).fill(0).map((_, index) => (
              <button
                // eslint-disable-next-line react/no-array-index-key
                key={`scroll-snap-${index}`}
                {...stylex.props({
                  ...styles.dot,
                  ...index <= currentSlide && styles.isSelected
                })}
                type="button"
                onClick={handleScroll(index)}
              />
            ))}
          </div>
        </div>
        <button
          type="button"
          onClick={handleNext}
          disabled={currentSlide === scrollSnaps.length - 1}
          aria-label="Next Slide"
        >
          <Icon name="RightArrow" size="medium" {...stylex.props(styles.button, currentSlide === scrollSnaps.length - 1 && styles.disabled)} />
        </button>
      </div>
    </Wrapper>
  )
}

export default TimelineSlider
