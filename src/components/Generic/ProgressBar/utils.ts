import { CalloutProps } from '@components/Generic/Callout/types'

type Tier = {
  threshold: number
  callouts: CalloutProps[]
  discount: number
}

const tierList: Tier[] = [
  {
    threshold: 675,
    discount: 0.1,
    callouts: [
      {
        title: 'Caraway & MATE Sweatshirt',
        settings: { theme: 'sage' },
        sys: { id: '10off' }
      }
    ]
  }
]

const MAX_PROGRESS = 100

const TWO = 2

const ANIMATION_TIMEOUT = 400

const getSegmentedProgress = (currentAmount: number) => {
  const segmentCount = tierList.length

  let progress = 0

  for (let i = 0; i < segmentCount; i += 1) {
    if (currentAmount >= tierList[i].threshold) {
      progress += MAX_PROGRESS / segmentCount
    } else {
      const previousThreshold = i === 0 ? 0 : tierList[i - 1].threshold
      const segmentSize = tierList[i].threshold - previousThreshold
      const segmentProgress = ((currentAmount - previousThreshold) / segmentSize) * (MAX_PROGRESS / segmentCount)
      progress += segmentProgress
      break
    }
  }

  return Math.min(progress, MAX_PROGRESS)
}

const DECIMAL_PLACES = 2

const formatNumber = (num: number) => (num % 1 === 0 ? num.toString() : num.toFixed(DECIMAL_PLACES))

const cartFormatter = (cartStore: any, currentThresholdIdx: number, amount: number, currentThreshold: any) => {
  const formattedProducts = cartStore?.products.map((product: any) => ({
    image_url: product?.imageUrl,
    line_item_id: product?.lineItemId,
    name: product?.name,
    option_values: product?.options,
    price: product?.amount,
    product_id: product?.productId,
    quantity: product?.quantity,
    sku: '',
    slug: product?.slug,
    variant: product?.variant,
  }))

  const cartObject = {
    cart_id: cartStore?.cartId,
    currency: cartStore?.currency,
    products: formattedProducts,
    value: cartStore?.value,
    tieredDiscounts: {
      currentTier: currentThresholdIdx + 1,
      totalTiers: tierList.length,
      discountedValue: amount * (currentThreshold?.discount || 0)
    }
  }

  return cartObject || {}
}

export {
  tierList,
  MAX_PROGRESS,
  TWO,
  ANIMATION_TIMEOUT,
  getSegmentedProgress,
  formatNumber,
  cartFormatter
}
