/* eslint-disable no-magic-numbers */

'use client'

import {
  ANIMATION_TIMEOUT, cartFormatter, getSegmentedProgress, tierList
} from '../utils'

import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { useDialogContext } from '@/providers/config/DialogContext'
import { colors } from '@/app/themeTokens.stylex'
import { useAppDispatch, useAppSelector } from '@/redux/hooks'
import { elementInteraction } from '@/redux/features/events/eventsSlice'

import React, { useState } from 'react'
import * as stylex from '@stylexjs/stylex'
import { useCart } from '@shopify/hydrogen-react'
import Image from 'next/image'

const moveDown = stylex.keyframes({
  '0%': { transform: 'translateX(0px)' },
  '25%': { transform: 'translateX(3px)' },
  '50%': { transform: 'translateX(-3px)' },
  '75%': { transform: 'translateX(3px)' },
  '100%': { transform: 'translateX(0px)' },
})

const styles = stylex.create({
  containerStyles: {
    display: {
      default: 'flex',
      '@media (max-width: 992px)': 'none'
    },
    borderRadius: '12px',
    height: '100px',
    margin: 'auto',
    position: 'fixed',
    bottom: '20px',
    right: '50%',
    transform: 'translateX(50%)',
    transition: 'width 0.3s ease, border-radius 0.3s ease, right 0.3s ease, bottom 0.3s ease',
    transformOrigin: 'left',
    zIndex: '40',
    backgroundColor: '#F4C5B9'
  },
  containerBecomesButton: {
    borderRadius: '50%',
    width: '52px',
    height: '52px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: colors.white,
  },
  moveCircularContainer: {
    right: '17px',
    bottom: '85px',
    transform: 'unset',
  },
  iconInfoHolder: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: '2rem',
    cursor: 'pointer',
    // eslint-disable-next-line @stylexjs/valid-styles
    ':hover .svg': {
      fill: colors.navy,
      transition: 'fill 0.5s ease',
    },
    // eslint-disable-next-line @stylexjs/valid-styles
    ':hover .circle': {
      stroke: '#F4C5B9'
    },
    // eslint-disable-next-line @stylexjs/valid-styles
    ':hover .path': {
      fill: '#F4C5B9'
    },
  },
  textArea: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    width: '50%',
    gap: '0.6rem',
  },
  progressBarArea: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    width: '100%',
    paddingTop: '0.5rem',
    position: 'relative'
  },
  progressBar: {
    position: 'relative',
    height: '30px',
    width: '100%',
    borderRadius: '20px',
    overflow: 'hidden',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor: 'black',
    marginTop: '0.6rem',
    backgroundColor: '#FCFCFA'
  },
  cartAmountBox: {
    position: 'absolute',
    top: '5px',
    width: '38px',
    height: '20px',
    backgroundColor: colors.white,
    borderRadius: '4px',
    textAlign: 'center',
    lineHeight: '25px',
    fontSize: '10px',
    transform: 'translateX(-50%)',
    transition: 'left 0.3s ease',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  caret: {
    position: 'absolute',
    bottom: '-3px',
    width: '7px',
    height: '7px',
    backgroundColor: colors.white,
    transform: 'rotate(45deg)',
  },
  progressBarFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    backgroundColor: colors.perracotta300,
    transition: 'width 0.3s ease',
    zIndex: 1,
  },
  segments: {
    display: 'flex',
    position: 'relative',
    height: '100%',
    zIndex: 2,
  },
  segment: {
    borderRightWidth: '1px',
    borderRightStyle: 'solid',
    borderRightColor: colors.black,
    height: '100%',
    width: '25%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  lastSegment: {
    height: '100%',
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRightWidth: 'none',
    borderRightStyle: 'none',
    borderRightColor: 'none',
  },
  segmentLabels: {
    zIndex: 3,
    display: 'flex',
    position: 'relative',
  },
  segmentLabel: {
    width: '100%',
    textAlign: 'center',
    marginTop: '5px',
  },
  segmentLabelCopy: {
    fontSize: '10px',
  },
  iconExitHolder: {
    display: 'flex',
    alignItems: 'center',
    marginRight: '2rem',
    cursor: 'pointer',
    transition: '0.2s ease-in-out all',
    // eslint-disable-next-line @stylexjs/valid-styles
    ':hover .exit-icon': {
      animationName: {
        default: 'none',
        '@media (min-width: 1024px)': moveDown,
      },
      animationIterationCount: 'infinite',
      animationTimingFunction: 'ease-in-out',
      animationDuration: '1500ms',
    },
  },
  checkmarkIcon: {
    marginRight: '4px',
  },
  closedButtonText: {
    width: '100%',
    marginTop: '9px',
  },
  closedProgressBarCopy: {
    cursor: 'pointer',
  }
})

/* eslint-disable complexity */
const ProgressBar = () => {
  const [isOpen, setIsOpen] = useState(true)
  const [move, setMove] = useState(false)

  const { lines } = useCart()

  const dispatch = useAppDispatch()

  const cartStore = useAppSelector((state) => state.cart.cart)

  let amount = 0

  lines?.forEach((line) => {
    const price = Number(line?.merchandise?.price?.amount) || 0
    const quantity = line?.quantity || 0
    amount += price * quantity
  })

  const currentThresholdIdx = tierList.slice().reverse().findIndex((tier) => amount >= tier.threshold)

  const currentThreshold = tierList[currentThresholdIdx] || {}

  const reachedSuccess = tierList[tierList.length - 1].threshold <= amount

  const barProgress = getSegmentedProgress(amount)

  const priceNeeded = tierList[tierList.length - 1].threshold - amount

  const handleExitIconClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsOpen(false)

    dispatch(elementInteraction({
      properties: {
        category: 'Progress Bar',
        label: "Mother's Day 2025",
        interaction_type: 'click',
        interaction_value: 'close',
        placement: 'Global',
        cart: cartFormatter(cartStore, currentThresholdIdx, amount, currentThreshold)
      }
    }))

    setTimeout(() => {
      setMove(true)
    }, ANIMATION_TIMEOUT)
  }

  const handleContainerClickOpen = () => {
    if (!isOpen) {
      setIsOpen(true)
      setMove(false)

      dispatch(elementInteraction({
        properties: {
          category: 'Progress Bar',
          label: "Mother's Day 2025",
          interaction_type: 'click',
          interaction_value: 'open',
          placement: 'Global',
          cart: cartFormatter(cartStore, currentThresholdIdx, amount, currentThreshold)
        }
      }))
    }
  }

  const { triggerDialog } = useDialogContext()

  const handleDialogClick = (event: React.MouseEvent) => {
    event?.stopPropagation()
    triggerDialog('5VZ9074rDtPx8nuDvSlHnn')

    dispatch(elementInteraction({
      properties: {
        category: 'Dialog',
        label: "Mother's Day 2025",
        interaction_type: 'click',
        interaction_value: 'open',
        placement: 'Global',
      }
    }))
  }

  return (
    <Container
      as="div"
      flex
      flexRow
      noWrap
      size="3"
      gap="2"
      theme="mist"
      styleProp={[
        styles.containerStyles,
        !isOpen && styles.containerBecomesButton,
        move && styles.moveCircularContainer,
      ]}
      onClick={handleContainerClickOpen}
    >
      {isOpen && (
        <>
          <div
            {...stylex.props(styles.iconInfoHolder)}
            onClick={(e) => handleDialogClick(e)}
          >
            <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="svg">
              <circle cx="12" cy="12" r="11.4" stroke="#1F3438" strokeWidth="1.2" className="circle" />
              {/* eslint-disable-next-line max-len */}
              <path d="M12.1546 9.54757C11.6362 9.54757 11.2186 9.12997 11.2186 8.62597C11.2186 8.10757 11.6362 7.70437 12.1546 7.70437C12.6586 7.70437 13.0762 8.10757 13.0762 8.62597C13.0762 9.12997 12.6586 9.54757 12.1546 9.54757ZM12.7738 10.8004V18.0004H11.5066V10.8004H12.7738Z" fill="#1F3438" className="path" />
            </svg>
          </div>
          <div {...stylex.props(styles.textArea)}>
            {!reachedSuccess && amount === 0 && (
              <Typography as="p" size="captionLarge">
                Add to cart to unlock a <b>free gift!</b>
              </Typography>
            )}
            {!reachedSuccess && amount >= 1 && amount <= 375 && (
              <Typography as="p" size="captionLarge">
                Unlock a <b>free gift</b> at $675!
              </Typography>
            )}
            {!reachedSuccess && amount >= 376 && amount <= 575 && (
              <Typography as="p" size="captionLarge">
                <b>Don&apos;t miss out!</b> Free gift at $675!
              </Typography>
            )}
            {!reachedSuccess && amount >= 576 && amount <= 674 && (
              <Typography as="p" size="captionLarge">
                <b>Almost there!</b> Add ${priceNeeded} more to unlock
              </Typography>
            )}
            {reachedSuccess && (
              <Typography as="p" size="captionLarge">
                Congrats! You have a <b>free gift in cart!</b>
              </Typography>
            )}
          </div>

          <div {...stylex.props(styles.progressBarArea)}>
            <div
              {...stylex.props(styles.cartAmountBox)}
              style={{ left: `calc(${barProgress}%)` }}
            >
              ${amount}
              <div {...stylex.props(styles.caret)} />
            </div>
            <div {...stylex.props(styles.progressBar)}>
              <div
                {...stylex.props(styles.progressBarFill)}
                style={{ width: `${barProgress}%` }}
              />

              <div {...stylex.props(styles.segments)}>
                {tierList.map((tier, index) => (
                  <div
                    key={tier.threshold}
                    {...stylex.props(
                      index === tierList.length - 1
                        ? styles.lastSegment
                        : styles.segment
                    )}
                  >
                    <Typography as="p" size="xxs">
                      {tier.callouts[0].title}
                    </Typography>
                  </div>
                ))}
              </div>
            </div>
            <div {...stylex.props(styles.segmentLabels)}>
              {tierList.map((tier) => (
                <div key={tier.threshold} {...stylex.props(styles.segmentLabel)}>
                  <Typography as="p" styleProp={styles.segmentLabelCopy}>Spend ${tier.threshold}</Typography>
                </div>
              ))}
            </div>
          </div>
          <div
            {...stylex.props(styles.iconExitHolder)}
            onClick={handleExitIconClick}
          >
            <Image
              style={{ objectPosition: 'center' }}
              loading="lazy"
              src="/assets/Exit-Icon.svg"
              alt="Desktop Savings Progress Bar Exit Icon"
              width={30}
              height={30}
              className="exit-icon"
            />
          </div>
        </>
      )}
      {!isOpen && move && (
        <Container as="div" flex {...stylex.props(styles.closedButtonText)}>
          <Typography as="p" size="xxs" textCentered fontBold styleProp={styles.closedProgressBarCopy}>Free Gift</Typography>
        </Container>
      )}
    </Container>
  )
}

export default ProgressBar
