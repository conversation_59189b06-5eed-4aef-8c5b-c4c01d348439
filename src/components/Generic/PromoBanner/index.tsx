import Typography from '@components/Typography'
import { spacing } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Container, { Size } from '@components/layout/Container'
import Wrapper from '@components/layout/Wrapper'

import Link from 'next/link'
import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'

type PromoBannerProps = {
  subheader?: string;
  link?: string;
  header?: string;
  slides: {
    assetsCollection: {
      items: {
        fileName: any
        url: any
      }[]
    }
  }[];
  theme?: ThemeColors;
  size?: Size
};

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  wrapper: {
    gap: spacing.md,
  },
  img: {
    width: '100%',
    height: 'auto',
  },
  grid: {
    display: 'grid',
    justifyItems: 'center',
    marginInline: 'auto',
    gap: {
      default: spacing.md,
      [DESKTOP]: spacing.lg,
    },
  },
  gridEven: {
    gridTemplateColumns: {
      default: 'repeat(2, 1fr)',
      [DESKTOP]: 'repeat(4, 1fr)',
    },
  },
  gridOdd: {
    gridTemplateColumns: {
      default: 'repeat(2, 1fr)',
      [DESKTOP]: 'repeat(3, 1fr)',
    },
  },
  gridItemOddLastChild: {
    gridColumn: {
      default: 'span 2',
      [DESKTOP]: 'span 1',
    },
  },
})

const even = 2
const disclaimer = (link: string, subheader: string) => (link ? (
  <Link href={link}>
    <Typography as="span" size="captionLarge">{subheader}</Typography>
  </Link>
) : subheader && <Typography as="span" size="captionLarge">{subheader}</Typography>)

const PromoBanner = ({
  subheader = '',
  link = '',
  slides,
  header,
  theme = 'slate',
  size = '5'
}: PromoBannerProps) => {
  const { items } = slides?.length > 0 ? slides[0].assetsCollection : { items: [] }
  const isEven = (items?.length ?? 0) % even === 0

  return (
    <Wrapper as="section" theme={theme} styleProp={styles.wrapper}>
      <Container
        as="div"
        size={size}
        flex
        flexCentered
        align="center"
        gap="4"
        paddingBlock="5"
      >
        {header && <Typography as="span" size="md">{header}</Typography>}
        {items && items?.length > 0 && (
          <Container
            as="div"
            styleProp={[styles.grid, isEven ? styles.gridEven : styles.gridOdd]}
          >
            {items?.map((item, index) => (
              <Container
                as="div"
                styleProp={index === items.length - 1 && !isEven ? styles.gridItemOddLastChild : undefined}
              >
                <Image
                  {...stylex.props(styles.img)}
                  alt={item.fileName}
                  src={item.url}
                  width={900}
                  height={506}
                />
              </Container>
            ))}
          </Container>
        )}
        {disclaimer(link, subheader)}
      </Container>
    </Wrapper>
  )
}

export default PromoBanner
