'use client'

import Typography from '@/components/Typography'
import { TypographyThemes } from '@/app/typographyThemes.stylex'
import Container from '@components/layout/Container'
import { colors, spacing } from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { toCamelCase } from '@/utils/regex'
import useKlaviyoSubscribe from '@/hooks/useKlaviyoSubscribe'

import * as stylex from '@stylexjs/stylex'
import { useForm } from 'react-hook-form'

type StyleProp = {
  styleProp?: stylex.StyleXStyles
  theme: ThemeColors
  formId: string
  headerTypography?: TypographyThemes
  bodyTypography?: TypographyThemes
}

type FormFields = {
  email: string
}

const styles = stylex.create({
  formContainer: {
    containerType: 'inline-size',
    paddingInline: spacing.md,
    paddingBlock: spacing.xxl,
  },
  contentWrapper: {
    display: 'grid',
    gridTemplateColumns: {
      default: '1fr',
      '@container (min-width: 750px)': '1fr 1.75fr',
    },
    alignSelf: 'center',
    gap: spacing.md,
  },
  formContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: spacing.md,
  },
  header: {
    color: colors.cream,
  },
  description: {
    color: colors.cream,
    marginBottom: spacing.md,
  },
  inputContainer: {
    borderBottomWidth: 2,
    borderBottomStyle: 'solid',
  },
  input: {
    padding: spacing.sm,
    paddingInlineStart: 0,
    flex: '4',
    borderWidth: 0,
    backgroundColor: 'transparent',
    color: 'inherit',
    outline: {
      default: 'none',
      ':focus': 'none'
    },
    '::placeholder': {
      color: 'inherit',
      opacity: 1,
    },
    touchAction: 'manipulation',
    fontSize: 16,
  },
  submitButton: {
    display: 'inline-flex',
    flex: '1',
    paddingBlock: spacing.sm,
    justifyContent: 'flex-end',
    color: 'inherit',
    borderWidth: 0,
    cursor: {
      default: 'pointer',
      ':disabled': 'not-allowed'
    },
    fontSize: 20,
    opacity: {
      default: 1,
      ':hover': 0.7,
      ':disabled': 0.5
    },
    transitionProperty: 'opacity',
    transitionDuration: '200ms',
    transitionTimingFunction: 'ease'
  },
  thankYou: {
    color: 'inherit',
    marginBottom: spacing.lg,
  }
})

const KlaviyoEmailCaptureForm = ({
  styleProp,
  theme,
  formId,
  headerTypography = 'h4Secondary',
  bodyTypography = 'bodyLarge'
}: StyleProp) => {
  const {
    register,
    handleSubmit,
    formState: {
      errors,
      isSubmitSuccessful,
      isSubmitting
    },
    setError,
  } = useForm<FormFields>()

  const {
    subscribe,
  } = useKlaviyoSubscribe({
    formId,
    setError
  })

  const themeSettings = toCamelCase(theme) as ThemeColors || 'navy'

  return (
    <Container flex gap="1" styleProp={[styles.formContainer, styleProp]} theme={themeSettings}>
      <Container size="4" styleProp={styles.contentWrapper}>
        <Typography as="h2" typographyTheme={headerTypography} {...stylex.props(styles.header)}>
          One step closer to cleaner cooking
        </Typography>
        <Container styleProp={styles.formContent}>
          <Typography as="p" typographyTheme="bodyLarge" {...stylex.props(styles.description)}>
            Unlock free shipping on your next order $90+ and be the first to learn about new collections when you sign up for emails from Caraway.
          </Typography>
          {isSubmitSuccessful ? (
            <Typography as="p" typographyTheme={bodyTypography} fontBold {...stylex.props(styles.thankYou)}>
              Thank you for subscribing!
            </Typography>
          ) : (
            <form onSubmit={handleSubmit(subscribe)}>
              <Container flex flexRow styleProp={styles.inputContainer}>
                <input
                  type="email"
                  {...register('email', {
                    required: 'Please enter your email',
                  })}
                  placeholder="Your email"
                  {...stylex.props(styles.input)}
                />
                <button type="submit" disabled={isSubmitting} {...stylex.props(styles.submitButton)}>
                  {isSubmitting ? '...' : '→'}
                </button>
              </Container>
              {errors.email && (
                <Typography as="p" typographyTheme="bodySmall" {...stylex.props(styles.thankYou)}>
                  {errors.email.message}
                </Typography>
              )}
            </form>
          )}
        </Container>
      </Container>
    </Container>
  )
}

export default KlaviyoEmailCaptureForm
