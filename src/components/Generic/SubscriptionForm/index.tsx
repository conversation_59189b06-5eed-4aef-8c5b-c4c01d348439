'use client'

import KlaviyoEmailCaptureForm from './KlaviyoEmailCaptureForm'

import { ThemeColors } from '@/app/themeThemes.stylex'
import { TypographyThemes } from '@/app/typographyThemes.stylex'

import * as stylex from '@stylexjs/stylex'

type SubscriptionFormProps = {
  theme: ThemeColors
  styleProp?: stylex.StyleXStyles
  formId: string
  id?: string
  headerTypography?: TypographyThemes
  bodyTypography?: TypographyThemes
}

const SubscriptionForm = ({
  theme,
  styleProp,
  formId,
  id,
  headerTypography,
  bodyTypography
}: SubscriptionFormProps) => {
  const listID = formId || id

  if (!listID) return null

  return (
    <KlaviyoEmailCaptureForm
      styleProp={styleProp}
      theme={theme}
      formId={listID}
      headerTypography={headerTypography}
      bodyTypography={bodyTypography}
    />
  )
}

export default SubscriptionForm
