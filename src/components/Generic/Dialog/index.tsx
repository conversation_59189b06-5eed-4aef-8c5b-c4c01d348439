'use client'

import DialogLayout1 from './Layout1'
import DialogLayout5 from './Layout5'
import ContentSectionDialog from './ContentSectionDialog'
import DropAHintDialog from './DropAHintDialog'
import { DialogProps } from './types'
import DialogRoot from './DialogRoot'
import WeightandDimensionsDialog from './WeightAndDimensionsDialog'
import NotifyMeDialog from './NotifyMe'
import SizeGuide from './SizeGuide'
import VideoDialog from './Video'
import DialogLayout7 from './Layout7'
import ProductSelectionDialog from './ProductSelectionDialog'

import { useDialogContext } from '@providers/config/DialogContext'

import { useEffect, useRef } from 'react'

const layouts = {
  'Layout 1': DialogLayout1,
  'Layout 2': DialogLayout1,
  'Layout 3': WeightandDimensionsDialog,
  'Layout 4': SizeGuide,
  'Layout 5': DialogLayout5,
  NotifyMe: NotifyMeDialog,
  DropAHint: DropAHintDialog,
  'Layout 11': ContentSectionDialog,
  'Layout 6': VideoDialog,
  'Layout 7': DialogLayout7,
  ProductSelectionDialog
}

const Dialog = (props: DialogProps) => {
  const { id, layout } = props
  const dialogRef = useRef<HTMLDialogElement>(null)
  const {
    registerDialog,
    closeModal,
    openModals
  } = useDialogContext()

  useEffect(() => {
    if (dialogRef.current) {
      registerDialog(id, dialogRef)
    }
  }, [id, registerDialog])

  if (!layout) return null

  if (layout in layouts === false) return null

  const DialogLayout = layouts[layout as keyof typeof layouts]

  return (
    <DialogRoot ref={dialogRef} onClick={() => closeModal(id)}>
      <DialogLayout openModals={openModals} {...(props as any)} />
    </DialogRoot>
  )
}

export default Dialog
