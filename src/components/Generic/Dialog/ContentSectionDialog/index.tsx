import RenderIf from '../../RenderIf'
import { CloseDialogButton } from '../Layout1/DialogWrapper'

import { ThemeColors } from '@/app/themeThemes.stylex'
import {
  defaultTheme as $T,
  colors,
  spacing
} from '@/app/themeTokens.stylex'
import ZPattern, { ZPatternProps } from '@/components/Content/ZPattern'
import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { notEmpty } from '@/utils/checking'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  container: {
    borderRadius: spacing.md,
    padding: {
      default: `${spacing.lg} ${spacing.md}`,
      [DESKTOP]: `${spacing.xl} ${spacing.xl} 0`,
    },
    width: {
      default: '90vw',
      [DESKTOP]: '915px',
    },
    height: '90vh',
  },
  overflow: {
    overflow: 'scroll',
    height: '100%',
  },
  heading: {
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomStyle: 'solid',
    borderBottomColor: colors.gray300,
    marginBottom: {
      default: spacing.lg,
      [DESKTOP]: spacing.lg,
    },
  },
  title: {
    marginBottom: spacing.sm,
  },
  subheader: {
    marginBottom: spacing.md,
  },
  headingWrapper: {
    maxWidth: '100%',
  },
  close: {
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: $T.primaryText,
    borderRadius: '50%',
    top: spacing.md,
    right: spacing.md,
    backgroundColor: colors.white,
    zIndex: 999,
    width: '28px',
    height: '28px',
  }
})

type SectionZpattern = ZPatternProps & {
  sys: {
    id: string
  }
}

type DialogProps = {
  theme?: ThemeColors
  header?: string,
  content?: string,
  subheader?: string
  sections: {
    items: SectionZpattern[]
  }
}

const ContentSectionDialog = ({
  theme,
  header,
  subheader,
  content,
  sections = {
    items: []
  }
}: DialogProps) => (
  <Container theme={theme} styleProp={styles.container}>
    <CloseDialogButton styleProp={styles.close} size="small" />
    <Container styleProp={styles.overflow}>
      <Container styleProp={styles.heading}>
        <Container styleProp={styles.headingWrapper}>
          <Typography as="h3" typographyTheme="h3Secondary" styleProp={styles.title}>{header}</Typography>
          <RenderIf condition={notEmpty(subheader)}>
            <Typography as="p" typographyTheme="bodyLarge">{subheader}</Typography>
          </RenderIf>
          <RenderIf condition={notEmpty(content)}>
            <Container>
              { content }
            </Container>
          </RenderIf>
        </Container>
      </Container>
      {sections.items.map((section) => (
        <ZPattern
          theme={section.theme}
          key={section.sys.id}
          slides={section.slides}
          layout={section.layout}
          header={section.header}
          settings={section?.settings}
        />
      ))}
    </Container>
  </Container>
)

export default ContentSectionDialog
