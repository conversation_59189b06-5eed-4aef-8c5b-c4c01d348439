import { CloseDialogButton } from '../Layout1/DialogWrapper'
import { DialogProps } from '../types'

import Container from '@/components/layout/Container'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  outer: {
    width: '100%',
    paddingInline: '15px',
  },
  container: {
    position: 'relative',
    display: 'flex',
    width: {
      default: '100%',
      '@media (min-width: 910px)': '900px'
    },
    padding: '15px 20px',
    flexDirection: {
      default: 'column',
      '@media (min-width: 768px)': 'row'
    }
  },
})

const NotifyMeDialog = (props: DialogProps) => {
  const { content, mobileContent } = props
  return (
    <Container
      flex
      flexCentered
      styleProp={styles.outer}
    >
      <Container
        styleProp={styles.container}
        theme="offWhite"
      >
        <CloseDialogButton />
        {content}
        {mobileContent}
      </Container>
    </Container>
  )
}

export default NotifyMeDialog
