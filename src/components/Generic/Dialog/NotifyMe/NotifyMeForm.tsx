'use client'

import CallToAction from '@components/Generic/CallToAction'
import { colors, globalTokens, spacing } from '@/app/themeTokens.stylex'
import { chord } from '@lib/chord/events'
import { VariantProps } from '@/components/Product/types'
import Typography from '@/components/Typography'
import Container from '@/components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import { useState } from 'react'
import { UseFormReturn } from 'react-hook-form'

const styles = stylex.create({
  form: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    gap: {
      default: spacing.sm,
      '@media (min-width: 375px)': '36px'
    },
    marginBlockStart: {
      default: spacing.md,
      '@media (min-width: 375px)': spacing.lg
    },
    marginBlockEnd: {
      default: spacing.sm,
      '@media (min-width: 375px)': '30px'
    }
  },
  input: {
    background: colors.offWhite,
    borderTopWidth: '0',
    borderLeftWidth: '0',
    borderRightWidth: '0',
    borderBottomWidth: '1px',
    textAlign: 'left',
    padding: '0 0 6px 0',
    outline: 'none',
    '::placeholder': {
      fontFamily: globalTokens.primaryFontFamily,
    }
  },
  inputDesktop: {
    '::placeholder': {
      fontSize: '16px'
    }
  },
  error: {
    color: colors.burgundy
  }
})

type FormFields = {
  email: string
}

type NotifyMeFormProps = {
  desktop?: boolean
  variant: VariantProps
  form: UseFormReturn<FormFields>
  title: string
}

const NotifyMeForm = ({
  desktop = false,
  variant,
  form,
  title
}: NotifyMeFormProps) => {
  const [analyticsMissing, setAnalyticsMissing] = useState(false)
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = form

  const onSubmit = (data: FormFields) => {
    try {
      setAnalyticsMissing(false)

      if (!chord) {
        setAnalyticsMissing(true)
        return false
      }
      chord.track('Email Captured', {
        email: data.email,
        placement_component: 'notify-modal',
        placement_page: title,
        variant_id: variant.variantId
      })

      return true
    } catch (err) {
      throw new Error('Email submission failed')
    }

    return null
  }

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      {...stylex.props(styles.form)}
    >
      <Container flex gap="1">
        <input
          type="email"
          placeholder="Email"
          {...register('email', {
            required: 'Please enter a valid email!'
          })}
          {...stylex.props([styles.input, desktop && styles.inputDesktop])}
        />
        {errors && errors.email && (
          <Typography
            as="p"
            typographyTheme="captionLarge"
            styleProp={styles.error}
          >
            {errors.email.type === 'required' && 'Email is required!'}
            {errors.email.type === 'pattern' && 'Invalid Email Address!'}
          </Typography>
        )}
      </Container>
      <CallToAction
        theme="navy"
        fullWidth
        submit
      >
        Notify Me When Available
      </CallToAction>
      {analyticsMissing && (
        <p>Please turn off your ad blocker</p>
      )}
    </form>
  )
}

export default NotifyMeForm
