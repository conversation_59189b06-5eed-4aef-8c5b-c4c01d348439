'use client'

import NotifyMeForm from './NotifyMeForm'

import Container from '@/components/layout/Container'
import { VariantProps } from '@/components/Product/types'
import SwatchInfo from '@components/Generic/DropAHint/Components/SwatchInfo'
import { spacing } from '@/app/themeTokens.stylex'
import Typography from '@/components/Typography'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import { useForm } from 'react-hook-form'

const styles = stylex.create({
  mobileOnly: {
    display: {
      default: 'flex',
      '@media (min-width: 650px)': 'none',
    },
    flexDirection: 'column',
  },
  productImage: {
    maxWidth: '100%',
    height: 'auto'
  },
  info: {
    alignItems: 'flex-start',
    gap: {
      default: spacing.xs,
      '@media (min-width: 375px)': spacing.sm
    },
    marginBottom: {
      default: spacing.sm,
      '@media (min-width: 375px)': spacing.lg
    },
    maxWidth: '300px'
  },
  success: {
    marginBottom: spacing.lg,
    marginTop: {
      default: spacing.sm,
      '@media (min-width: 375px)': 0
    }
  }
})

type NotifyMeMobileProps = {
  title: string
  variant: VariantProps
}

type FormFields = {
  email: string
}

const NotifyMeMobile = ({ variant, title }: NotifyMeMobileProps) => {
  const { swatch, gallery } = variant
  const displayImage = gallery?.items?.[0]
  const form = useForm<FormFields>()
  const { isSubmitSuccessful } = form.formState

  return (
    <Container
      styleProp={styles.mobileOnly}
      flexCentered
    >
      <Image
        src={displayImage?.url}
        alt={displayImage?.title}
        width={296}
        height={223}
        {...stylex.props(styles.productImage)}
      />
      <Container
        flex
        gap="sm"
        align="left"
        styleProp={styles.info}
      >
        <Typography
          as="h6"
          typographyTheme="h6Primary"
        >
          {title}
        </Typography>
        <SwatchInfo
          swatch={swatch}
        />
      </Container>
      {!isSubmitSuccessful && (
        <>
          <Typography
            as="p"
            typographyTheme="bodySmall"
            textLeft
          >
            Drop your email address below to be the first to know when this item is available.
          </Typography>
          <NotifyMeForm
            variant={variant}
            form={form}
            title={title}
          />
        </>
      )}
      {isSubmitSuccessful && (
        <Container
          flex
          flexCentered
          styleProp={styles.success}
        >
          <Typography
            as="p"
            typographyTheme="bodySmall"
          >
            Thank you! We will notify you when this item is back in stock.
          </Typography>
        </Container>
      )}
    </Container>
  )
}

export default NotifyMeMobile
