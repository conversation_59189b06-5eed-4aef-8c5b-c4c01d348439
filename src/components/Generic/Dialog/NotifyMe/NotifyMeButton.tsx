'use client'

import NotifyMeDesktop from './NotifyMeDesktop'
import NotifyMeMobile from './NotifyMeMobile'

import Container from '@/components/layout/Container'
import { VariantProps } from '@/components/Product/types'
import { useDialogContext } from '@/providers/config/DialogContext'
import CallToAction from '@components/Generic/CallToAction'
import Dialog from '@components/Generic/Dialog'

type NotifyMeButtonProps = {
  variant: VariantProps
  title: string
}

const NotifyMeButton = ({ variant, title }: NotifyMeButtonProps) => {
  const { triggerDialog } = useDialogContext()

  return (
    <Container flex gap="sm">
      <Dialog
        layout="NotifyMe"
        id="NotifyMe"
        header=""
        subheader=""
        content={(
          <NotifyMeDesktop
            title={title}
            variant={variant}
          />
        )}
        mobileContent={(
          <NotifyMeMobile
            title={title}
            variant={variant}
          />
        )}
      />
      <CallToAction
        fullWidth
        variant="primary"
        onClick={() => triggerDialog('NotifyMe')}
      >
        Notify Me
      </CallToAction>
    </Container>
  )
}

export default NotifyMeButton
