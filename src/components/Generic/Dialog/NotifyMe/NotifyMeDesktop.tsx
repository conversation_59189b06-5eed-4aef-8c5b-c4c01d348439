'use client'

import NotifyMeForm from './NotifyMeForm'

import SwatchInfo from '@components/Generic/DropAHint/Components/SwatchInfo'
import Container from '@/components/layout/Container'
import { VariantProps } from '@/components/Product/types'
import Typography from '@/components/Typography'

import Image from 'next/image'
import * as stylex from '@stylexjs/stylex'
import { useForm } from 'react-hook-form'

const styles = stylex.create({
  desktopOnly: {
    display: {
      default: 'none',
      '@media (min-width: 650px)': 'flex'
    },
    paddingBlock: '95px'
  },
  product: {
    width: '350px'
  },
  productImage: {
    maxWidth: '100%',
    height: 'auto'
  },
  infoContainer: {
    paddingInline: '90px'
  },
  info: {
    maxWidth: '370px'
  }
})

type NotifyMeDesktopProps = {
  title: string
  variant: VariantProps
}

type FormFields = {
  email: string
}

const NotifyMeDesktop = ({ variant, title }: NotifyMeDesktopProps) => {
  const { swatch, gallery } = variant
  const displayImage = gallery?.items?.[0]
  const form = useForm<FormFields>()
  const { isSubmitSuccessful } = form.formState

  return (
    <Container
      flex
      flexRow
      styleProp={styles.desktopOnly}
      noWrap
      flexCentered
    >
      <Container
        flex
        gap="md"
        flexCentered
        styleProp={styles.product}
      >
        <Image
          src={displayImage?.url}
          alt={displayImage?.title}
          width={270}
          height={200}
          {...stylex.props(styles.productImage)}
        />
        <Typography
          as="h6"
          typographyTheme="h6Primary"
        >
          {title}
        </Typography>
        <SwatchInfo
          swatch={swatch}
        />
      </Container>
      <Container flex styleProp={styles.infoContainer}>
        <Container
          flex
          styleProp={styles.info}
        >
          {!isSubmitSuccessful && (
            <>
              <Typography
                as="p"
                typographyTheme="bodyLarge"
                textCentered
              >
                Drop your email address below to be the first to know when this item is available.
              </Typography>
              <NotifyMeForm
                variant={variant}
                form={form}
                title={title}
              />
            </>
          )}
          {isSubmitSuccessful && (
            <Typography
              as="p"
              typographyTheme="bodyLarge"
              textCentered
            >
              Thank you! We will notify you when this item is back in stock.
            </Typography>
          )}
        </Container>
      </Container>
    </Container>
  )
}

export default NotifyMeDesktop
