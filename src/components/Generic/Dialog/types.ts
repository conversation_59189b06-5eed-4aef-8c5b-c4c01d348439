import { ThemeColors } from '@/app/themeThemes.stylex'
import { AnchorProps } from '@components/Generic/CallToAction'

import { ReactNode } from 'react'

export type LayoutProps =
| 'Layout 1'
| 'Layout 2'
| 'Layout 3'
| 'Layout 4'
| 'Layout 5'
| 'WeightAndDimensions'
| 'NotifyMe'
| 'DropAHint'
| 'ProductSelectionDialog'

export type ExtendedAnchorProps = AnchorProps & { text: string }

export type PairedWith = {
  content: string
  title: string
  asset: {
    description: string
    url: string
  },
  mobileAsset: {
    description: string
    url: string
  }
}[]

export type ElementsToCompare = {
  items: {
    name: string
    description: string
    selectedAsset: {
      description: string
      url: string
    }
    pairedWith: PairedWith
    callToAction?: {
      text: string
      page: {
        __typename: string
        slug: string
      }
    }
  }[]
}

export type DialogProps = {
  id: string
  header: string
  subheader: string
  asset?: string
  content: string | ReactNode
  mobileContent: string | ReactNode
  buttons?: ExtendedAnchorProps[]
  footerContent?: string
  theme?: ThemeColors
  layout?: LayoutProps
  text?: string
  dialogToggles?: {
    title?: string
    content: string | ReactNode
    mobileContent: string | ReactNode
    asset?: {
      items: {
        fileName: string
        url: string
      }[]
    }
  }[],
  compareChart?: {
    header: string
    elementsToCompare: ElementsToCompare
  },
  sections?: {}[]
  openModals?: Record<string, boolean>
}
