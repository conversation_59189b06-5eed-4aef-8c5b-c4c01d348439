import { DialogProps } from '@/components/Generic/Dialog/types'
import ToggleSwitcher from '@/components/Generic/Toggle'
import { CloseDialogButton } from '@/components/Generic/Dialog/Layout1/DialogWrapper'
import WeightAndDimTable from '@/components/Generic/WeightAndDimensions/WeightAndDimTable'
import Container from '@/components/layout/Container'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import { useState } from 'react'

const DESKTOP = '@media (min-width: 520px)'

const styles = stylex.create({
  close: {
    zIndex: 1,
  },
  container: {
    height: {
      default: '95%',
      [DESKTOP]: '650px'
    },
  },
  dialogContainer: {
    minHeight: {
      default: '100%',
      [DESKTOP]: '650px'
    },
    flexWrap: 'nowrap',
    padding: {
      default: '0',
      '@media(min-width: 520px) and (max-width: 820px)': '60px 0px 40px 0px',
      '@media (min-width: 821px)': '60px 40px 40px 40px'
    },
  },
  scrollContainer: {
    flexWrap: 'nowrap',
    paddingBlockEnd: {
      default: spacing.md,
      [DESKTOP]: 0
    },
    overflow: 'hidden',
    '::-webkit-scrollbar': {
      display: 'none',
    },
  },
  toggleContainer: {
    marginBlockStart: {
      default: '60px',
      [DESKTOP]: spacing.xxs
    },
    marginBlockEnd: {
      default: spacing.md,
      [DESKTOP]: spacing.xxs
    },
    width: {
      default: '95%',
      [DESKTOP]: '315px'
    }
  }
})

const WeightandDimensionsDialog = (props: DialogProps) => {
  const {
    content,
    mobileContent,
    header,
    dialogToggles = []
  } = props
  const [activeOption, setActiveOption] = useState({ title: header, id: header })

  const hasToggles = dialogToggles.length > 0

  const options = [
    { title: header, id: header },
    ...dialogToggles.map((toggle, index) => ({
      title: toggle.title || `Option ${index + 1}`,
      id: toggle.title || `option-${index + 1}`
    }))
  ]

  const tableData: Record<string, NonNullable<DialogProps['dialogToggles']>[0]> = {
    [header]: {
      content,
      mobileContent
    }
  }

  if (hasToggles) {
    const toggleData = dialogToggles[0]
    tableData[toggleData.title || 'Option 1'] = {
      content: toggleData.content,
      mobileContent: toggleData.mobileContent,
      asset: toggleData.asset
    }
  }

  const handleSwitchOption = (index: number) => {
    if (options[index]) {
      setActiveOption(options[index])
    } else {
      setActiveOption(options[0])
    }
  }

  return (
    <Container
      flex
      flexCentered
      styleProp={styles.container}
    >
      <Container
        flex
        theme="offWhite"
        styleProp={styles.dialogContainer}
      >
        <Container styleProp={styles.close}>
          <CloseDialogButton />
        </Container>
        {hasToggles && (
          <ToggleSwitcher
            optionsList={options}
            handleChange={handleSwitchOption}
            activeOption={activeOption.title}
            theme="gray200"
            outline
            secondary
            fontBold
            styleProp={styles.toggleContainer}
          />
        )}
        <Container flex styleProp={styles.scrollContainer}>
          <WeightAndDimTable {...tableData[activeOption.title]} />
        </Container>
      </Container>
    </Container>
  )
}
export default WeightandDimensionsDialog
