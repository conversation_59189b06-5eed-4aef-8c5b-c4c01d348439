import { DialogProps } from '../types'
import { CloseDialogButton } from '../Layout1/DialogWrapper'

import Container from '@components/layout/Container'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const DESKTOP_MEDIA_QUERY = '@media (min-width: 768px)'

const styles = stylex.create({
  container: {
    position: 'relative',
    marginBlock: 'auto',
    maxHeight: {
      default: '90vh',
      [DESKTOP_MEDIA_QUERY]: '100%'
    },
    borderRadius: '20px',
    maxWidth: {
      default: '90vw',
      [DESKTOP_MEDIA_QUERY]: '65vh'
    },
    paddingInline: {
      default: spacing.sm,
      [DESKTOP_MEDIA_QUERY]: spacing.md
    }
  },
  content: {
    maxHeight: {
      default: '90vh',
      [DESKTOP_MEDIA_QUERY]: '100%'
    },
    paddingInline: {
      default: spacing.sm,
      [DESKTOP_MEDIA_QUERY]: spacing.lg
    },
    paddingBlock: spacing.lg,
    overflowY: 'auto',
    height: '100%',
    width: '100%'
  }
})

function DialogLayout7({ theme = 'cream', content }: DialogProps) {
  return (
    <Container as="div" size="3" theme={theme} styleProp={styles.container}>
      <CloseDialogButton />
      <Container as="div" styleProp={styles.content}>
        {content}
      </Container>
    </Container>
  )
}

export default DialogLayout7
