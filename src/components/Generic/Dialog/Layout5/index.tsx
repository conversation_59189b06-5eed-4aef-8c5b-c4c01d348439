import { DialogProps } from '../types'
import { CloseDialogButton } from '../Layout1/DialogWrapper'

import { DesktopOnly, MobileOnly } from '@utils/responsiveDisplay'
import Container from '@components/layout/Container'
import Typography from '@components/Typography'
import RenderIf from '@utils/renderIf'
import { condition, notEmpty } from '@utils/checking'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'

const styles = stylex.create({
  container: {
    position: 'relative',
    gap: spacing.lg,
    display: {
      default: 'block',
      '@media (min-width: 1024px)': 'flex'
    },
    width: {
      default: '90%',
      '@media (min-width: 1024px)': '800px'
    },
    padding: {
      default: `${spacing.xl} ${spacing.md} ${spacing.md}`,
      '@media (min-width: 1024px)': ` ${spacing.xl} ${spacing.lg} ${spacing.lg}`
    },
    alignItems: {
      default: 'initial',
      '@media (min-width: 1024px)': 'center'
    },
    maxHeight: '85vh',
    overflowY: 'auto',
  },
  textContainer: {
    textAlign: {
      default: 'center',
      '@media (min-width: 1024px)': 'left',
    },
    flex: {
      default: 'initial',
      '@media (min-width: 1024px)': '1'
    }
  },
  imageWrapper: {
    textAlign: 'center',
    marginTop: {
      default: '28px',
      '@media (min-width: 1024px)': '0'
    },
    height: {
      default: 'inherit'
    }
  },
  image: {
    height: {
      default: 'inherit',
      '@media (min-width: 1024px)': '357px'
    }
  }
})

const DialogLayout5 = ({
  theme = 'cream',
  header,
  content,
  mobileContent,
  asset
}: DialogProps) => (
  <Container as="div" theme={theme} styleProp={styles.container}>
    <CloseDialogButton />
    <Container as="div" styleProp={styles.textContainer}>
      {header && (
        <Typography
          as="h3"
          fontSecondary
          typographyTheme="h3Secondary"
          typographyThemeMobile="h4Secondary"
          marginBottom="md"
        >
          {header}
        </Typography>
      )}
      {mobileContent ? (
        <>
          <MobileOnly>{mobileContent}</MobileOnly>
          <DesktopOnly>{content}</DesktopOnly>
        </>
      ) : content}
    </Container>
    <Container as="div" styleProp={styles.imageWrapper}>
      <RenderIf condition={notEmpty(asset)}>
        <Image
          src={condition<string>(notEmpty(asset), asset, '')}
          width={291}
          height={357}
          style={{ objectFit: 'cover' }}
          alt={header}
          {...stylex.props(styles.image)}
        />
      </RenderIf>
    </Container>
  </Container>
)

export default DialogLayout5
