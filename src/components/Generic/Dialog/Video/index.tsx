import { DialogProps } from '../types'
import CallToAction from '../../CallToAction'

import Close from '@components/Generic/Icon/lib/Close'
import Container from '@components/layout/Container'
import { spacing } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import { useEffect, useRef } from 'react'

const DESKTOP_MEDIA_QUERY = '@media (min-width: 768px)'

const styles = stylex.create({
  container: {
    position: 'relative',
  },
  video: {
    width: '100%',
    height: 'auto',
    maxHeight: '90vh'
  },
  close: {
    display: {
      default: 'none',
      [DESKTOP_MEDIA_QUERY]: 'block',
    },
    position: 'absolute',
    top: spacing.xs,
    right: spacing.md,
    cursor: 'pointer',
    borderStyle: 'none',
    zIndex: 1,
  },
})

export const CloseDialogButton = ({ onClick }: { onClick: () => void }) => (
  <form method="dialog" {...stylex.props(styles.close)}>
    <CallToAction submit size="round" variant="transparent" onClick={onClick}>
      <Close size="medium" />
    </CallToAction>
  </form>
)

function VideoDialog({
  asset,
  id,
  openModals
}: DialogProps) {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    const video = videoRef.current

    if (!video || !openModals) {
      return
    }

    if (openModals[id]) {
      video.play()
    } else {
      video.pause()
    }
  }, [openModals])

  if (!asset) return null

  return (
    <Container paddingInline="2" styleProp={styles.container}>
      <CloseDialogButton
        onClick={() => {
          videoRef.current?.pause()
        }}
      />
      <video
        ref={videoRef}
        {...stylex.props(styles.video)}
        src={asset}
        autoPlay
        controls
        loop
        playsInline
      >
        Your browser does not support the video tag.
      </video>
    </Container>
  )
}

export default VideoDialog
