import { DialogProps } from '../types'
import { CloseDialogButton } from '../Layout1/DialogWrapper'

import { colors, spacing } from '@/app/themeTokens.stylex'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  container: {
    maxHeight: '96dvh',
    maxWidth: '96vw',
    backgroundColor: colors.offWhite,
    borderRadius: spacing.xxs,
    overflow: 'auto',
  },
})

const ProductSelectionDialog = ({ content }: DialogProps) => (
  <Container styleProp={styles.container}>
    <Container theme="offWhite">
      <CloseDialogButton />
    </Container>
    {content}
  </Container>
)
export default ProductSelectionDialog
