import Typography from '@components/Typography'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  header: {
    margin: 0,
    width: '100%',
    position: 'relative'
  },
})

const DialogHeader = ({ header, subheader }: { header: string, subheader: string }) => (
  <header {...stylex.props(styles.header)}>
    <Typography as="h3" size="xs" textCentered>{subheader}</Typography>
    <Typography as="h2" size="h4" textCentered>{header}</Typography>
  </header>
)

export default DialogHeader
