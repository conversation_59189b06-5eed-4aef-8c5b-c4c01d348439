import {
  globalTokens as $,
  spacing,
} from '@/app/themeTokens.stylex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import Close from '@/components/Generic/Icon/lib/Close'
import CallToAction from '@components/Generic/CallToAction'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'

const TABLET = '@media (min-width: 768px)'

const styles = stylex.create({
  content: {
    padding: '20px',
    borderRadius: $.borderRadius,
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
    width: {
      default: 'calc(100% - 40px)',
      [TABLET]: '400px'
    }
  },
  close: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    cursor: 'pointer',
    borderStyle: 'none',
  },
})

type CloseDialogProps = {
  styleProp?: stylex.StyleXStyles
  size?: 'extraSmall' | 'medium' | 'small'
}

export const CloseDialogButton = ({ styleProp, size = 'medium' }: CloseDialogProps) => (
  <form method="dialog" {...stylex.props(styles.close, styleProp)}>
    <CallToAction submit size="round" variant="transparent">
      <Close size={size} />
    </CallToAction>
  </form>
)

const DialogWrapper = ({ theme, children }: { theme: ThemeColors, children: React.ReactNode }) => (
  <Container
    as="div"
    size="1"
    theme={theme}
    flexCentered
    gap="2"
    paddingBlock="3"
    styleProp={styles.content}
  >
    <CloseDialogButton />
    {children}
  </Container>
)

export default DialogWrapper
