'use client'

import DialogMedia from './DialogMedia'
import DialogHeader from './DialogHeader'
import DialogMain from './DialogMain'
import DialogFooter from './DialogFooter'
import DialogWrapper from './DialogWrapper'

import { DialogProps } from '../types'

const Dialog = ({
  theme = 'cream',
  header,
  subheader,
  asset,
  content,
  mobileContent,
  buttons = [],
  footerContent,
}: DialogProps) => (
  <DialogWrapper theme={theme}>
    {asset && <DialogMedia asset={asset} />}
    <DialogHeader header={header} subheader={subheader} />
    <DialogMain content={content} mobileContent={mobileContent} buttons={buttons} />
    <DialogFooter footerContent={footerContent} />
  </DialogWrapper>
)

export default Dialog
