import CallToAction, { AnchorProps } from '@components/Generic/CallToAction'
import Container from '@components/layout/Container'
import { DesktopOnly, MobileOnly } from '@utils/responsiveDisplay'

import { ReactNode } from 'react'

type ExtendedAnchorProps = AnchorProps & { text: string };

const DialogMain = ({
  content,
  mobileContent,
  buttons
}: { content: string | ReactNode, mobileContent: string | ReactNode, buttons: ExtendedAnchorProps[] }) => (
  <Container as="main" flexCentered gap="2">
    {mobileContent ? (
      <>
        <MobileOnly>{mobileContent}</MobileOnly>
        <DesktopOnly>{content}</DesktopOnly>
      </>
    ) : content}
    {buttons.length > 0 && (
      <Container as="div" gap="2" flexCentered>
        {buttons.map((button) => {
          const {
            text,
            href,
            id,
            variant
          } = button
          return (
            <CallToAction
              key={id}
              useArrow
              variant={variant}
              href={href}
            >
              {text}
            </CallToAction>
          )
        })}
      </Container>
    )}
  </Container>
)

export default DialogMain
