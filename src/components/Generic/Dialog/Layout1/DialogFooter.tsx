import RenderIf from '@utils/renderIf'
import { notEmpty } from '@utils/checking'
import Typography from '@components/Typography'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  footer: {
    display: 'flex',
    justifyContent: 'flex-end',
  }
})

const DialogFooter = ({ footerContent }: { footerContent?: string }) => (
  <footer {...stylex.props(styles.footer)}>
    <RenderIf condition={notEmpty(footerContent)}>
      <Typography as="span" size="xs">{footerContent}</Typography>
    </RenderIf>
  </footer>
)

export default DialogFooter
