'use client'

import { CallToActionVariants } from '../CallToAction/types'

import { ThemeColors } from '@/app/themeThemes.stylex'
import CallToAction from '@components/Generic/CallToAction'
import { useDialogContext } from '@providers/config/DialogContext'

import * as stylex from '@stylexjs/stylex'

export type DialogTriggerProps = {
  id: string
  children?: React.ReactNode
  text: string
  variant?: CallToActionVariants
  theme?: ThemeColors
  size?: 'small' | 'round' | 'inline'
  styleProp?: stylex.StyleXStyles
};

const DialogTrigger = ({
  id,
  text,
  variant,
  theme,
  size = 'small',
  styleProp,
  children
}: DialogTriggerProps) => {
  const { triggerDialog } = useDialogContext()

  return (
    <CallToAction
      onClick={() => triggerDialog(id)}
      variant={variant}
      theme={theme}
      size={size}
      styleProp={styleProp}
    >
      {children || text}
    </CallToAction>
  )
}

export default DialogTrigger
