import { DialogProps } from '../types'

import { colors, spacing } from '@/app/themeTokens.stylex'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 650px)'

const styles = stylex.create({
  container: {
    display: 'flex',
    flexDirection: {
      default: 'column',
      [DESKTOP]: 'row',
    },
    backgroundColor: {
      default: 'none',
      [DESKTOP]: colors.gray200,
    },
    height: {
      default: '100%',
      [DESKTOP]: 'auto',
    },
    minHeight: {
      default: '100%',
      [DESKTOP]: '450px',
    },
    borderRadius: spacing.xxs,
    overflow: 'hidden',
  },
})

const DropAHintDialog = (props: DialogProps) => {
  const { content, mobileContent } = props

  return (
    <Container styleProp={styles.container}>
      {content}
      {mobileContent}
    </Container>
  )
}
export default DropAHintDialog
