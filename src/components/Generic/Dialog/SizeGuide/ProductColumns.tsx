import { ElementsToCompare, PairedWith } from '../types'

import Container from '@/components/layout/Container'
import Typography from '@/components/Typography'
import { colors, spacing } from '@/app/themeTokens.stylex'
import { sanitize } from '@/utils/regex'

import * as stylex from '@stylexjs/stylex'
import Image from 'next/image'
import React from 'react'

const styles = stylex.create({
  productColumnContainer: {
    width: '100%',
    display: 'grid',
    gridAutoFlow: {
      default: 'row',
      '@media (min-width: 844px)': 'column'
    },
    columnGap: '64px',
    rowGap: '0'
  },
  smallGap: {
    columnGap: '24px'
  },
  productImageContainer: {
    height: {
      default: '80px',
      '@media (min-width: 844px)': '100px'
    },
    width: {
      default: '106px',
      '@media (min-width: 844px)': '130px'
    }
  },
  contentContainer: {
    display: {
      default: 'flex',
      '@media (min-width: 844px)': 'grid'
    },
    flexDirection: 'row',
    gridRow: 'span 6',
    rowGap: '0',
    gridTemplateRows: 'subgrid',
    paddingInline: {
      default: spacing.sm,
      '@media (min-width: 844px)': '0'
    },
  },
  subGrid1: {
    display: 'grid',
    maxWidth: {
      default: '116px',
      '@media (min-width: 844px)': 'unset'
    },
    gridTemplateRows: 'subgrid',
    gridRow: 'span 3',
    gap: '5px',
    alignSelf: 'start'
  },
  optionalContainer: {
    display: {
      default: 'flex',
      '@media (min-width: 844px)': 'grid'
    },
    flexDirection: 'column',
    gridTemplateRows: 'subgrid',
    gridRow: 'span 5',
  },
  subGrid2: {
    display: 'grid',
    flex: '127',
    gridTemplateRows: 'subgrid',
    gridRow: 'span 2',
    gap: '4px',
    paddingBottom: {
      default: '28px',
      '@media (min-width: 844px)': '35px'
    }
  },
  noPadding: {
    paddingBottom: '0'
  },
  subGrid3: {
    display: 'grid',
    flex: '127',
    gridTemplateRows: 'subgrid',
    gridRow: 'span 3',
    gap: '8px'
  },
  align: {
    textAlign: {
      default: 'left',
      '@media (min-width: 844px)': 'center'
    }
  },
  title: {
    maxWidth: {
      default: 'none',
      '@media (min-width: 844px)': '120px'
    }
  },
  description: {
    marginBottom: {
      default: '0',
      '@media (min-width: 844px)': '30px'
    }
  },
  pairedWithImage: {
    height: {
      default: '60px',
      '@media (min-width: 844px)': '80px'
    },
    width: {
      default: '106px',
      '@media (min-width: 844px)': '130px'
    }
  },
  mobile: {
    display: {
      default: 'block',
      '@media (min-width: 844px)': 'none'
    }
  },
  desktop: {
    display: {
      default: 'none',
      '@media (min-width: 844px)': 'block'
    }
  },
  content: {
    maxWidth: {
      default: 'none',
      '@media (min-width: 844px)': '120px'
    }
  },
  hr: {
    display: {
      default: 'block',
      '@media (min-width: 844px)': 'none'
    },
    marginTop: '24px',
    marginBottom: '24px',
    color: colors.gray300
  }
})

const FOUR = 4

type ProductColumnsProps = {
  compareElements: ElementsToCompare
};

const generatePairs = (pairedWith: PairedWith[0], thirdRow: boolean) => (
  <Container
    styleProp={[
      styles.subGrid2,
      !thirdRow && styles.noPadding
    ]}
  >
    <Container
      theme="offWhite"
      styleProp={[
        styles.pairedWithImage,
        styles.mobile
      ]}
    >
      <Image
        src={pairedWith.mobileAsset.url}
        alt={pairedWith.mobileAsset.description}
        layout="fill"
      />
    </Container>
    <Container
      theme="offWhite"
      styleProp={[
        styles.pairedWithImage,
        styles.desktop,
      ]}
    >
      <Image
        src={pairedWith.asset.url}
        alt={pairedWith.asset.description}
        layout="fill"
      />
    </Container>
    <Typography
      as="p"
      typographyTheme="bodyLarge"
      typographyThemeMobile="bodySmall"
      textCentered
      styleProp={[
        styles.content,
        styles.align,
      ]}
    >
      {pairedWith.title}
    </Typography>
  </Container>
)

const ProductColumns = ({ compareElements }: ProductColumnsProps) => {
  const { length } = compareElements.items

  return (
    <Container
      gap="3"
      flexCentered
      styleProp={[
        styles.productColumnContainer,
        length > FOUR && styles.smallGap
      ]}
    >
      {compareElements.items.map((item, index) => {
        const [secondRow, thirdRow] = item.pairedWith
        const lastSubGrid = thirdRow || secondRow

        return (
          <React.Fragment key={`${sanitize(item.name)}-${String(index)}`}>
            <Container
              flexCentered
              alignCentered
              gap="3"
              styleProp={styles.contentContainer}
              style={{ gridRow: item.pairedWith.length > 1 && 'span 8' }}
            >
              <Container styleProp={styles.subGrid1}>
                <Container
                  theme="offWhite"
                  styleProp={styles.productImageContainer}
                >
                  <Image
                    src={item.selectedAsset.url}
                    alt={item.description}
                    layout="fill"
                  />
                </Container>
                <Typography
                  as="p"
                  typographyTheme="bodyLarge"
                  fontBold
                  textCentered
                  styleProp={styles.title}
                >
                  {item.name}
                </Typography>
                <Typography
                  as="p"
                  typographyTheme="bodyLarge"
                  textCentered
                  styleProp={styles.description}
                >
                  {item.description}
                </Typography>
              </Container>
              <Container styleProp={styles.optionalContainer}>
                {secondRow && generatePairs(secondRow, !!thirdRow)}
                {thirdRow && (
                  <Container styleProp={styles.subGrid3}>
                    <Typography
                      as="p"
                      typographyTheme="captionLarge"
                      fontBold
                      styleProp={styles.align}
                    >
                      {lastSubGrid.title}
                    </Typography>
                    <Container
                      theme="offWhite"
                      styleProp={[
                        styles.pairedWithImage,
                        styles.mobile
                      ]}
                    >
                      <Image
                        src={lastSubGrid.mobileAsset.url}
                        alt={lastSubGrid.mobileAsset.description}
                        layout="fill"
                      />
                    </Container>
                    <Container
                      theme="offWhite"
                      styleProp={[
                        styles.pairedWithImage,
                        styles.desktop
                      ]}
                    >
                      <Image
                        src={lastSubGrid.asset.url}
                        alt={lastSubGrid.asset.description}
                        layout="fill"
                      />
                    </Container>
                    <Typography
                      as="p"
                      typographyTheme="bodyLarge"
                      typographyThemeMobile="bodySmall"
                      textCentered
                      styleProp={[
                        styles.content,
                        styles.align,
                      ]}
                    >
                      {lastSubGrid.content}
                    </Typography>
                  </Container>
                )}
              </Container>
            </Container>
            {index + 1 !== length && <hr {...stylex.props(styles.hr)} />}
          </React.Fragment>
        )
      })}
    </Container>
  )
}

export default ProductColumns
