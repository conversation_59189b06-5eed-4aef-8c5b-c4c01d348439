import ProductColumns from './ProductColumns'

import { DialogProps } from '../types'

import { colors, spacing } from '@/app/themeTokens.stylex'
import Container from '@/components/layout/Container'
import { CloseDialogButton } from '@/components/Generic/Dialog/Layout1/DialogWrapper'
import Typography from '@/components/Typography'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 844px)'

const styles = stylex.create({
  dialogContainer: {
    position: 'relative',
    height: {
      default: '100%',
      [DESKTOP]: '700px'
    },
    maxWidth: '100%'
  },
  closeDialog: {
    zIndex: 2,
    position: 'absolute',
    top: {
      default: 30,
      [DESKTOP]: 15
    },
    right: {
      default: 0,
      [DESKTOP]: 5
    }
  },
  container: {
    borderRadius: spacing.xxs,
    minHeight: {
      default: 'unset',
      [DESKTOP]: '0'
    },
    height: {
      default: '95%',
      [DESKTOP]: '100%'
    },
    maxHeight: {
      default: 'fit-content',
      [DESKTOP]: 'none'
    },
    maxWidth: '100%',
    width: {
      default: '360px',
      '@media(min-width: 844px) and (max-width: 902px)': '842px',
      '@media(min-width: 903px)': '900px'
    },
    backgroundColor: colors.offWhite,
    overflowY: 'scroll',
    paddingBottom: {
      default: spacing.lg,
      [DESKTOP]: '0'
    }
  },
  content: {
    maxWidth: '100%',
    gap: {
      default: '16px',
      [DESKTOP]: '28px'
    },
    padding: {
      default: '60px 28px',
      [DESKTOP]: '64px clamp(30px, 80%, 64px)'
    },
  },
  header: {
    maxWidth: '372px',
  }
})

const SizeGuide = (props: DialogProps) => {
  const { compareChart } = props || {}

  if (!compareChart) return null

  const { elementsToCompare, header } = compareChart

  return (
    <Container
      flex
      flexCentered
      styleProp={styles.dialogContainer}
    >
      <Container styleProp={styles.closeDialog} theme="offWhite">
        <CloseDialogButton />
      </Container>
      <Container
        flex
        theme="offWhite"
        styleProp={styles.container}
      >
        <Container
          flex
          flexCentered
          styleProp={styles.content}
        >
          <Typography
            as="h6"
            typographyTheme="h6Primary"
            typographyThemeMobile="h5Primary"
            textCentered
            fontBold
            styleProp={styles.header}
          >
            {header}
          </Typography>
          <ProductColumns compareElements={elementsToCompare} />
        </Container>
      </Container>
    </Container>
  )
}

export default SizeGuide
