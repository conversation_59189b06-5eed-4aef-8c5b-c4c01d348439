'use client'

import { ThemeColors } from '@/app/themeThemes.stylex'
import { colors } from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'
import { useState } from 'react'

const styles = stylex.create({
  button: {
    background: 'none',
    borderWidth: '0',
    borderStyle: 'none',
    borderColor: 'transparent',
    cursor: 'pointer',
    padding: '0',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  svg: {
    aspectRatio: '1',
  },
  svgWidth: (width) => ({
    width,
  }),
})

type SoundOnOffButtonProps = {
  width?: string | number | undefined | null
  play_on?: boolean
  iconColor?: ThemeColors
  styleProp?: {}
  onClick?: () => void
}

const SoundOnOffButton = ({
  width = '30px',
  play_on = false,
  iconColor = colors.white as ThemeColors,
  styleProp,
  onClick,
}: SoundOnOffButtonProps) => {
  const [isPlaying, setIsPlaying] = useState(play_on)

  const handleClick = () => {
    setIsPlaying(!isPlaying)
    if (onClick) { onClick() }
  }

  return (
    <button
      type="button"
      onClick={() => { handleClick() }}
      {...stylex.props(styles.button, styleProp)}
    >
      <svg
        viewBox="0 0 35 35"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        {...stylex.props(styles.svg, styles.svgWidth(width))}
      >
        {!isPlaying ? (
          <>
            <path
              d="M3.3667 20.2649V14.7582C3.3667 13.9718 3.64066 13.2176 4.12832 12.6614C4.61599 12.1053 5.2774 11.7929 5.96705 11.7929H9.73757C9.99178 11.7928
            10.2404 11.7078 10.4527 11.5483L18.2537 5.68287C18.4497 5.53567 18.677 5.45172 18.9115 5.43992C19.1459 5.42812 19.3788 5.4889 19.5855 5.61583C19.7921
            5.74276 19.9647 5.9311 20.0851 6.16087C20.2054 6.39063 20.269 6.65326 20.269 6.92089V28.1022C20.269 28.3698 20.2054 28.6325 20.0851 28.8622C19.9647
            29.092 19.7921 29.2803 19.5855 29.4073C19.3788 29.5342 19.1459 29.595 18.9115 29.5832C18.677 29.5714 18.4497 29.4874 18.2537 29.3402L10.4527 23.4748C10.2404
            23.3153 9.99178 23.2302 9.73757 23.2302H5.96705C5.2774 23.2302 4.61599 22.9178 4.12832 22.3617C3.64066 21.8055 3.3667 21.0513 3.3667 20.2649Z"
              stroke={iconColor}
              strokeWidth="2.78391"
            />
            <path
              d="M24.7471 15.0259C24.7471 15.0259 26.2384 15.7716 26.2384 17.6358C26.2384 19.5 24.7471 20.2457 24.7471 20.2457M26.2384 10.5518C29.2212 12.0431 30.7126
            14.2802 30.7126 17.6358C30.7126 20.9914 29.2212 23.2285 26.2384 24.7199"
              stroke={iconColor}
              strokeWidth="2.23707"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </>
        ) : (
          <>
            <path
              d="M3.13684 20.2649V14.7582C3.13684 13.9718 3.41081 13.2176 3.89847 12.6614C4.38613 12.1053 5.04754 11.7929 5.73719 11.7929H9.50771C9.76192 11.7928
                10.0105 11.7078 10.2228 11.5483L18.0239 5.68287C18.2199 5.53567 18.4472 5.45172 18.6816 5.43992C18.9161 5.42812 19.149 5.4889 19.3556 5.61583C19.5622
                5.74276 19.7349 5.9311 19.8552 6.16087C19.9755 6.39063 20.0391 6.65326 20.0391 6.92089V28.1022C20.0391 28.3698 19.9755 28.6325 19.8552 28.8622C19.7349
                29.092 19.5622 29.2803 19.3556 29.4073C19.149 29.5342 18.9161 29.595 18.6816 29.5832C18.4472 29.5714 18.2199 29.4874 18.0239 29.3402L10.2228
                23.4748C10.0105 23.3153 9.76192 23.2302 9.50771 23.2302H5.73719C5.04754 23.2302 4.38613 22.9178 3.89847 22.3617C3.41081 21.8055 3.13684 21.0513
                3.13684 20.2649Z"
              stroke={iconColor}
              strokeWidth="2.78391"
            />
            <g clipPath="url(#clip0_2821_32808)">
              <path d="M30.3032 20.8652L25.0005 15.5626" stroke={iconColor} strokeWidth="1.88651" strokeLinecap="round" />
              <path d="M25.0006 20.8652L30.3033 15.5626" stroke={iconColor} strokeWidth="1.88651" strokeLinecap="round" />
            </g>
            <defs>
              <clipPath id="clip0_2821_32808">
                <rect width="7.95402" height="7.95402" fill={iconColor} transform="translate(23.6749 14.2373)" />
              </clipPath>
            </defs>
          </>
        )}
      </svg>
    </button>
  )
}

export default SoundOnOffButton
