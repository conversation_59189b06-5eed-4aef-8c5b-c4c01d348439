'use client'

import React, { useEffect } from 'react'
import { EmblaOptionsType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'

const styles = stylex.create({
  slider: {
    overflow: 'hidden',
  },
  container: {
    display: 'flex',
  },
  center: {
    justifyContent: {
      default: 'center',
      [DESKTOP]: 'center',
    }
  },
  startThenCenter: {
    justifyContent: {
      default: 'start',
      [DESKTOP]: 'center',
    }
  },
  start: {
    justifyContent: 'start',
  },
  flexWrap: {
    flexWrap: {
      default: 'nowrap',
      [DESKTOP]: 'wrap',
    },
    justifyContent: {
      default: 'start',
      [DESKTOP]: 'center',
    }
  },
  slide: {
    flex: '0 0 100%',
    minWidth: 0
  },
  slideImage: {
    width: '100%',
    height: 'auto',
    objectFit: 'cover',
  }
})

export type SliderProps = {
  children: React.ReactNode
  options?: EmblaOptionsType,
  center?: boolean,
  startThenCenter?: boolean,
  setEmblaList?: (embla: any) => void;
  flexWrap?: boolean;
  styleProp?: {};
  styleContainerProp?: {};
  justifyStart?: boolean;
}

type SlideProps = {
  styleProp?: {};
  children: React.ReactNode;
}

export const Slide = ({ styleProp, children }: SlideProps) => (
  <div {...stylex.props([styles.slide, styleProp && styleProp as any])}>
    {children}
  </div>
)

const Slider = ({
  styleProp = false,
  styleContainerProp = false,
  options,
  center,
  startThenCenter,
  flexWrap,
  setEmblaList,
  justifyStart,
  children
}: SliderProps) => {
  const [emblaRef, embla] = useEmblaCarousel({ ...options, skipSnaps: true })

  useEffect(() => {
    if (!emblaRef || !embla) return
    if (setEmblaList) setEmblaList((prev: any) => [...prev, embla])
  }, [emblaRef, embla, setEmblaList])

  return (
    <div
      ref={emblaRef}
      {...stylex.props(
        styles.slider,
        styleProp && styleProp,
      )}
    >
      <div {...stylex.props(
        styles.container,
        flexWrap && styles.flexWrap,
        styleContainerProp && styleContainerProp,
        center && styles.center,
        startThenCenter && styles.startThenCenter,
        justifyStart && styles.start
      )}
      >
        {children}
      </div>
    </div>
  )
}

export default Slider
