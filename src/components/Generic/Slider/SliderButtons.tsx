import {
  defaultTheme as $T,
  colors
} from '@/app/themeTokens.stylex'
import Icon from '@components/Generic/Icon'
import Container from '@components/layout/Container'

import * as stylex from '@stylexjs/stylex'
import React, { MouseEvent } from 'react'

const PULSING = stylex.keyframes({
  '0%': {
    boxShadow: '0 0 0 0 white',
  },
  '70%': {
    boxShadow: '0 0 0 10px rgba(0, 0, 0, 0)',
  },
  '100%': {
    boxShadow: '0 0 0 0 rgba(0, 0, 0, 0)',
  },
})

const styles = stylex.create({
  button: {
    display: 'inline-block',
    padding: '0',
    margin: '0',
    borderWidth: '0',
    borderStyle: 'none',
    borderColor: 'transparent',
    background: 'transparent',
    cursor: 'pointer',
  },
  dot: {
    width: '8px',
    height: '8px',
    borderRadius: '50%',
    margin: '0',
    background: $T.primaryCTAText,
    transition: 'background 0.3s',
  },
  isSelected: {
    background: $T.primaryCTASurface,
  },
  detailDot: {
    margin: '10px',
    height: '25px',
    width: '25px',
    transform: 'scale(1)',
    cursor: 'pointer',
    display: 'block',
    backgroundColor: colors.navy,
    borderStyle: 'solid',
    borderWidth: '3px',
    borderColor: colors.white,
    borderRadius: '50%',
    outlineWidth: '10px',
    outlineStyle: 'solid',
    outlineColor: 'rgba(255,255,255,0.65)',
  },
  DetailActiveDot: {
    backgroundColor: '#fcfcfa',
    borderColor: '#1f3438',
    animationName: `${PULSING}`,
    animationDuration: '2s',
    animationIterationCount: 'infinite',
    borderRadius: '50%',
    margin: '10px',
    height: '25px',
    width: '25px',
    cursor: 'pointer',
  },
  activeDot: {
    backgroundColor: colors.navy,
  },
  inactiveDot: {
    backgroundColor: colors.navy400,
  },
})

type DotButtonProps = {
  selected: boolean,
  onClick: (event: MouseEvent<HTMLButtonElement>) => void,
  styleProp?: {},
  selectedStyleProp?: {},
}

export const DotButton = ({
  selected,
  onClick,
  styleProp,
  selectedStyleProp,
}: DotButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    {...stylex.props({
      ...styles.dot,
      ...styleProp,
      ...selected && (selectedStyleProp || styles.isSelected),
    })}
  />
)

type PrevButtonProps = {
  enabled: boolean,
  size?: 'small' | 'medium' | 'large',
  fill?: string,
  styleProp?: {}
  onClick: (event: MouseEvent<HTMLButtonElement>) => void,
}

export const PrevButton = ({
  enabled,
  size = 'medium',
  fill = $T.primaryText,
  styleProp,
  onClick
}: PrevButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    disabled={!enabled}
    aria-label="Previous Slide"
    {...stylex.props(styleProp)}
  >
    <Icon name="LeftArrow" size={size} fill={fill} />
  </button>
)

type NextButtonProps = {
  enabled: boolean,
  size?: 'small' | 'medium' | 'large',
  fill?: string,
  styleProp?: {}
  onClick: (event: MouseEvent<HTMLButtonElement>) => void,
}

export const NextButton = ({
  enabled,
  styleProp,
  size = 'medium',
  fill = $T.primaryText,
  onClick
}: NextButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    disabled={!enabled}
    aria-label="Next Slide"
    {...stylex.props(styleProp)}
  >
    <Icon name="RightArrow" size={size} fill={fill} />
  </button>
)

type DotButtonsProps = {
  scrollSnaps: Array<number>,
  handleScroll: (index: number) => (event: MouseEvent<HTMLButtonElement>) => void,
  styleProp: object | undefined,
  dotsStyleProp?: object | undefined,
  dotsSelectedStyleProp?: object | undefined,
  currentSlide: number,
}

export const DotButtons = ({
  scrollSnaps,
  handleScroll,
  styleProp,
  dotsStyleProp,
  dotsSelectedStyleProp,
  currentSlide,
}: DotButtonsProps) => (
  <Container as="div" styleProp={styleProp}>
    {scrollSnaps.map((snap, index) => (
      <DotButton
        key={snap}
        onClick={handleScroll(index)}
        selected={index === currentSlide}
        styleProp={dotsStyleProp}
        selectedStyleProp={dotsSelectedStyleProp}
      />
    ))}
  </Container>
)

type CircleButtonProps = {
  enabled: boolean,
  fill: string,
  styleProp?: {},
  onClick: (event: MouseEvent<HTMLButtonElement>) => void,
}

export const NextCircleButton = ({
  enabled,
  fill,
  styleProp = {},
  onClick
}: CircleButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    disabled={!enabled}
    aria-label="Next Slide"
    {...stylex.props(styleProp)}
  >
    <Icon name="RightArrowCircle" size="xxlarge" fill={fill} />
  </button>
)

export const PrevCircleButton = ({
  enabled,
  fill,
  styleProp = {},
  onClick
}: CircleButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    disabled={!enabled}
    aria-label="Prev Slide"
    {...stylex.props(styleProp)}
  >
    <Icon name="LeftArrowCircle" size="xxlarge" fill={fill} />
  </button>
)

type SlideNavigationButtonsProps = {
  totalSlides: number;
  handleScroll: (index: number) => (event: React.MouseEvent<HTMLButtonElement>) => void;
  containerStyleProp?: {};
  styleProps?: Array<{}>;
  currentSlide: number;
};

export const SlideNavigationButtons = ({
  totalSlides,
  handleScroll,
  containerStyleProp,
  styleProps = [],
  currentSlide,
}: SlideNavigationButtonsProps) => (
  <Container styleProp={containerStyleProp}>
    {Array.from({ length: totalSlides }, (_, index) => (
      <button
        key={index}
        type="button"
        onClick={handleScroll(index)}
        aria-label={`Go to slide ${index + 1}`}
        {...stylex.props(
          styles.detailDot,
          index === currentSlide ? styles.activeDot : styles.inactiveDot,
          styleProps[index] || {}
        )}
      />
    ))}
  </Container>
)
