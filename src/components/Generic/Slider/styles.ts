import {
  defaultTheme as $T,
  spacing
} from '@/app/themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  viewport: {
    overflow: 'hidden',
  },
  container: {
    display: 'flex',
    touchAction: 'pan-y pinch-zoom',
  },
  slide: (slideWidth: number) => ({
    transform: 'translate3d(0, 0, 0)',
    flex: `0 0 ${slideWidth}%`,
    minWidth: '0',
  }),
  buttons: {
    position: 'relative',
    display: 'grid',
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: '0.6rem',
    alignItems: 'center',
  },
  dotsContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dotsContainerCenter: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dotsGrid: {
    gap: '0.5rem',
    display: 'flex',
    flexWrap: 'wrap',
  },
  dotSelected: {
    backgroundColor: $T.indicatorActive,
    borderColor: $T.indicatorActive,
  },
  dots: {
    display: 'inline-block',
    width: spacing.sm,
    height: spacing.sm,
    borderRadius: '50%',
    borderStyle: 'solid',
    borderWidth: '2px',
    borderColor: $T.indicatorActive,
    backgroundColor: {
      default: 'transparent',
      ':hover': $T.indicatorActive,
    },
    textIndent: '-9999px',
    cursor: 'pointer',

  }
})

export default styles
