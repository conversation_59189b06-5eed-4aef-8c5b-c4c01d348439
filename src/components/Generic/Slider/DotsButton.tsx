import styles from './styles'

import { condition, notEmpty } from '@/utils/checking'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

type PropType = {
  isSelected: boolean,
  styleProp?: {},
  selectedDotsStyles?: {},
  children: React.ReactNode,
  onClick?: () => void,
  title?: string,
}

const DotButton = ({
  styleProp,
  selectedDotsStyles,
  children,
  isSelected,
  onClick = () => {},
  title = '',
}: PropType) => {
  const style = styleProp || styles.dots
  const selectedStyle = isSelected
    ? condition<{}>(notEmpty(selectedDotsStyles), selectedDotsStyles, styles.dotSelected)
    : {}
  return (
    <button
      title={title}
      type="button"
      onClick={onClick}
      {...stylex.props(style, isSelected && selectedStyle)}
    >
      {children}
    </button>
  )
}

export default DotButton
