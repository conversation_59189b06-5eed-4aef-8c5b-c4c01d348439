'use client'

import Typography from '@components/Typography'

import React, { useState, useEffect, useCallback } from 'react'

type CountdownProps = {
  endDate: string;
  onComplete?: () => void;
};

const MILLISECONDS_IN_A_SECOND = 1000
const SECONDS_IN_A_MINUTE = 60
const MINUTES_IN_AN_HOUR = 60
const HOURS_IN_A_DAY = 24
const THRESHOLD_DAYS = 3

const MILLISECONDS_IN_A_MINUTE = MILLISECONDS_IN_A_SECOND * SECONDS_IN_A_MINUTE
const MILLISECONDS_IN_AN_HOUR = MILLISECONDS_IN_A_MINUTE * MINUTES_IN_AN_HOUR
const MILLISECONDS_IN_A_DAY = MILLISECONDS_IN_AN_HOUR * HOURS_IN_A_DAY

const Countdown: React.FC<CountdownProps> = ({ endDate, onComplete }) => {
  const calculateTimeLeft = useCallback((): number => {
    const endDateTime = new Date(new Date(endDate).toLocaleString('en-US', { timeZone: 'America/New_York' })).getTime()
    const currentTime = new Date(new Date().toLocaleString('en-US', { timeZone: 'America/New_York' })).getTime()
    const timeDiff = endDateTime - currentTime
    return timeDiff > 0 ? timeDiff : 0
  }, [endDate])

  const [timeLeft, setTimeLeft] = useState<number>(calculateTimeLeft)

  useEffect(() => {
    const intervalId = setInterval(() => {
      const timeDiff = calculateTimeLeft()
      setTimeLeft(timeDiff)

      if (timeDiff <= 0) {
        clearInterval(intervalId)
        if (onComplete) onComplete()
      }
    }, MILLISECONDS_IN_A_SECOND)

    return () => clearInterval(intervalId)
  }, [calculateTimeLeft, endDate, onComplete])

  if (timeLeft <= 0) {
    return (
      <>
        <Typography as="span" typographyTheme="bodySmall">00:00:00 Sale Ends Now!</Typography>
        <span> | </span>
      </>
    )
  }

  const days = Math.floor(timeLeft / MILLISECONDS_IN_A_DAY)
  const hours = Math.floor(timeLeft / MILLISECONDS_IN_AN_HOUR)
  const minutes = Math.floor((timeLeft % MILLISECONDS_IN_AN_HOUR) / MILLISECONDS_IN_A_MINUTE)
  const seconds = Math.floor((timeLeft % MILLISECONDS_IN_A_MINUTE) / MILLISECONDS_IN_A_SECOND)

  const formatTime = () => {
    if (days > THRESHOLD_DAYS) {
      return `${days} days left`
    }
    return `${hours}h ${minutes}m ${seconds}s`
  }

  return (
    <>
      <Typography as="span" typographyTheme="bodySmall" suppressHydrationWarning>{formatTime()}</Typography>
      <span> | </span>
    </>
  )
}

export default Countdown
