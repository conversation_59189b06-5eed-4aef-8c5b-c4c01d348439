import PageSections from '@components/PageSections'
import {
  getPageBySlug,
  getPageSections,
} from '@lib/contentful/fetchPages'
import { generateSEOMetaTags } from '@/utils/metadata'

export async function generateMetadata() {
  const { seoMetadata }:any = await getPageBySlug('home')
  return generateSEOMetaTags(seoMetadata)
}

const Home = async () => {
  const page = await getPageBySlug('home')
  const sections = await getPageSections(page)

  return <PageSections sections={sections} />
}

export default Home
