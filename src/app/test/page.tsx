import WhySlider from '@/components/Content/WhySlider'
import HowWeCompare from '@/components/Content/HowWeCompare'
import Hero from '@components/Content/Hero'
import InstructionsListCircle from '@components/Content/InstructionsListCircle'
import PageCTA from '@components/Content/PageCTA'
import Tabs from '@components/Content/Tabs'
import BeforeAfter from '@components/Generic/BeforeAfter'
import BlogHeroHeader from '@components/Article/BlogHeroHeader'
import Rating from '@components/Generic/Rating'
import TimelineSlider from '@components/Generic/TimelineSlider'
import Feature from '@components/layout/Feature'
import { baseProps } from '@components/layout/Feature/types'
import Wrapper from '@components/layout/Wrapper'

// TODO: New Modules to be created on /src/app/test2/page.tsx
// http://localhost:3000/test2

const blogHeroImage = {
  src: '/assets/blog-header-image.png',
  title: 'test',
}

const slideData: baseProps[] = [
  {
    id: 'data-1',
    subheader: 'Lorem Ipsum Dolor',
    header: 'Lorem Ipsum Dolor',
    body: 'Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna.',
    button: {
      children: 'Lorem Ipsum Dolor',
      href: '/demo-page',
      variant: 'primary',
      id: 'cta-1',
      useArrow: true
    },
    image: '/assets/kitchen-gadgets-hero.jpg'
  },
  {
    id: 'data-2',
    subheader: 'Lorem Ipsum Dolor 2',
    header: 'Lorem Ipsum Dolor 2',
    body: 'Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna.',
    button: {
      children: 'Lorem Ipsum Dolor 2',
      href: '/demo-page',
      variant: 'primary',
      id: 'cta-1',
      useArrow: true
    },
    image: '/assets/kitchen-gadgets-hero.jpg',
    smallHeadline: true
  }
]

const timelideSlideData: {
  id: string;
  media: string;
  mobileMedia: string;
  header: string;
  description: string;
}[] = [
  {
    id: 'slide-1',
    media: '/assets/timeline-slider/slide1.jpg',
    mobileMedia: '/assets/timeline-slider/slide1-mobile.jpg',
    header: 'The Origin Story',
    description: `A few years back I accidentally overheated
    a non-stick PTFE (aka Teflon®) coated pan and got sick with Teflon® Flu (let me tell you…not fun).
    After looking into the problem, I found that it only took 2.5 minutes for Teflon® coated pans to heat to 500ºF
    and begin releasing toxic chemicals into the air.`
  },
  {
    id: 'slide-2',
    media: '/assets/timeline-slider/slide2.jpg',
    mobileMedia: '/assets/timeline-slider/slide2-mobile.jpg',
    header: 'Lorem Ipsum Dolor 2',
    description: `A few years back I accidentally overheated
    a non-stick PTFE (aka Teflon®) coated pan and got sick with Teflon® Flu (let me tell you…not fun).
    After looking into the problem, I found that it only took 2.5 minutes for Teflon® coated pans to heat to 500ºF
    and begin releasing toxic chemicals into the air.`
  },
  {
    id: 'slide-3',
    media: '/assets/timeline-slider/slide3.jpg',
    mobileMedia: '/assets/timeline-slider/slide3-mobile.jpg',
    header: 'Lorem Ipsum Dolor 2',
    description: `A few years back I accidentally overheated
    a non-stick PTFE (aka Teflon®) coated pan and got sick with Teflon® Flu (let me tell you…not fun).
    After looking into the problem, I found that it only took 2.5 minutes for Teflon® coated pans to heat to 500ºF
    and begin releasing toxic chemicals into the air.`
  },
  {
    id: 'slide-4',
    media: '/assets/timeline-slider/slide4.jpg',
    mobileMedia: '/assets/timeline-slider/slide4-mobile.jpg',
    header: 'Lorem Ipsum Dolor 2',
    description: `A few years back I accidentally overheated
    a non-stick PTFE (aka Teflon®) coated pan and got sick with Teflon® Flu (let me tell you…not fun).`
  }
]

const Test = () => (
  <>
    <BlogHeroHeader
      category={
        {
          name: 'Cooking Tips'
        }
      }
      title="How To Host An Iconic Summer Soirée"
      theme="cream"
      image={blogHeroImage}
    />
    <Hero
      props={[
        {
          id: 'first',
          media: {
            items: [
              {
                url: 'https://videos.ctfassets.net/vitsw3itv0qj/4tEwOmL5aVRiB0tI85e7RS/c57e3de51216ab04217e5d0cf4a6bacc/How_to_Cook_with_Caraway.mp4'
              }
            ]
          },
          contentAlignment: 'left',
          subHeadline: 'Lorem Ipsum Dolor SUB',
          header: 'Lorem Ipsum Dolor  Headline',
          text: 'Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna. Pellentesque sit amet sapien.',
          buttons: [
            {
              children: 'Lorem Ipsum Dolor',
              href: '/demo-page',
              variant: 'primary',
              id: 'cta-1'
            },
            {
              children: 'Lorem Ipsum Dolor',
              href: '/demo-page',
              variant: 'secondary',
              id: 'cta-2'
            }
          ],
        },
      ]}
    />
    <Feature slides={[]} content={slideData[0]} theme="black" type="base" />
    <Feature slides={[]} content={slideData[1]} theme="white" type="base" reverse />
    <Feature slides={slideData} theme="cream" type="slider" />
    <Feature slides={slideData} theme="slate" type="slider" reverse />
    <BeforeAfter
      theme="cream"
      header={{
        header: 'Dress Up Your Kitchen',
        body: `Ditch the cabinet clutter and discover how our non-toxic
        Cookware Set can transform your kitchen in an instant. Slide to see for yourself below.`,
      }}
      beforeAfterItems={[
        {
          beforeImage: {
            src: '/assets/Bw_Before_50.jpg',
            alt: 'Before',
            width: 900,
            height: 506
          },
          afterImage: {
            src: '/assets/BW_After_50.jpg',
            alt: 'After',
            width: 900,
            height: 506
          }
        },
        {
          beforeImage: {
            src: '/assets/Bw_Before_50.jpg',
            alt: 'Before',
            width: 900,
            height: 506
          },
          afterImage: {
            src: '/assets/BW_After_50.jpg',
            alt: 'After',
            width: 900,
            height: 506
          },
          button: {
            useArrow: false,
            text: 'Show Now',
            href: '/demo-page-2',
            theme: 'babyBlue300',
            variant: 'primary',
            id: 'cta-2',
          },
        }
      ]}
    />
    <Rating
      isAggregated
      withoutReviews
      textContentOn="right"
      rating={5}
      reviewCount={10000}
      iconColor="sage"
      size="small"
    />
    <InstructionsListCircle
      header="Lorem Ipsum Dolor"
      slides={[
        {
          title: 'Lorem Ipsum Dolor',
          text: 'Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna.',
          id: 'slide-1'
        },
        {
          title: 'Lorem Ipsum Dolor 2',
          text: 'Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna.',
          id: 'slide-2'
        }
      ]}
      theme="navy"
    />
    <TimelineSlider slides={timelideSlideData} />
    <PageCTA
      image="/assets/kitchen-gadgets-hero.jpg"
      header="Discover Caraway"
      buttons={[
        {
          text: 'Lorem Ipsum Dolor',
          href: '/demo-page',
          variant: 'primary',
          id: 'cta-1'
        },
        {
          text: 'Lorem Ipsum Dolor',
          href: '/demo-page',
          variant: 'secondary',
          id: 'cta-2'
        }
      ]}
    />
    <PageCTA
      header="Discover Marigold"
      theme="marigold"
      buttons={[
        {
          text: 'Lorem Ipsum Dolor',
          href: '/demo-page',
          variant: 'primary',
          id: 'cta-1'
        }
      ]}
      contentAlignment="top"
    />

    <Wrapper size="sm">
      <Tabs
        slides={[
          {
            title: 'Lorem Ipsum Dolor',
            content: {
              json: '2 Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna.',
            },
            settings: {
              theme: 'navy'
            },
            id: 'slide-1'
          },
          {
            title: 'Lorem Ipsum Dolor 2',
            content: {
              json: '1 Lorem ipsum dolor sit amet consectetur adipiscing elit Ut et massa mi. Aliquam in hendrerit urna.',
            },
            settings: {
              theme: 'slate'
            },
            id: 'slide-2'
          }
        ]}
      />
    </Wrapper>

    <Wrapper as="div">
      <WhySlider
        theme="gray200"
        items={[
          {
            image: '/assets/CeramicCoated_Cream.webp',
            description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
          Sed non risus. Suspendisse lectus tortor, 
          dignissim sit amet, adipiscing nec, ultricies sed, dolor. Cras elementum ultrices diam. Maecenas ligula massa, varius a, semper congue, euismod non, mi.`,
            title: 'Non-Toxic Coating',
            text: 'Lorem ipsum dolor ',
            theme: 'gray200',
          },
          {
            image: '/assets/Stainless_Steel.webp',
            description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
          Sed non risus. Suspendisse lectus tortor, 
          dignissim sit amet, adipiscing nec, ultricies sed, dolor. Cras elementum ultrices diam. 
          Maecenas ligula massa, varius a, semper congue, euismod non, mi.`,
            title: 'Non-Stick',
            text: ' consectetur adipiscing elit. Sed non risus. Suspendisse',
            theme: 'marigold',
          },
          {
            image: '/assets/Bw_Before_50.jpg',
            description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
          Sed non risus. Suspendisse lectus tortor, 
          dignissim sit amet, adipiscing nec, ultricies sed, dolor. Cras elementum ultrices diam. 
          Maecenas ligula massa, varius a, semper congue, euismod non, mi.`,
            title: 'Eco-Friendly',
            text: 'Maecenas ligula massa',
            theme: 'babyBlue',
          },
        ]}
      />
    </Wrapper>

    <Wrapper as="div">
      <HowWeCompare
        header="How We Compare"
        content={{
          titles: ['Caraway® Non-Stick', 'PTFE Non-Stick (ex: Teflon®)', 'Glass, Stoneware or Porcelain'],
          rows: [
            {
              title: 'Non-Toxic',
              description: 'Are the materials used in the non-stick coating safe for my family and the environment?',
              checkMarks: ['orangeCheck', 'blueCheck', 'orangeCheck']
            },
            {
              title: 'Non-Stick',
              description: 'Does food easily slide around the cooking surface with a minimal amount of oil or butter?',
              checkMarks: ['orangeCheck', 'checkHalfFill', 'orangeCheck']
            },
            {
              title: 'Cleaning',
              description: 'Are the pots and pans easily washed and cared for?',
              checkMarks: ['orangeCheck', 'blueCheck', 'orangeCheck']
            },
            {
              title: 'Heat Conductivity',
              description: 'Does the surface heat evenly, avoiding cool spots and unevenly cooked food?',
              checkMarks: ['orangeCheck', 'checkHalfFill', 'orangeCheck']
            },
            {
              title: 'Versatility',
              description: 'Does the cookware perform well no matter what cooktop or environment?',
              checkMarks: ['orangeCheck', 'checkHalfFill', 'orangeCheck']
            }
          ]
        }}
        theme="white"
      />
    </Wrapper>
  </>
)

export default Test
