import * as stylex from '@stylexjs/stylex'

const DARK = '@media (prefers-color-scheme: dark)'

export const fontSizes = stylex.defineVars({
  xxs: '0.75rem',
  xs: '0.9rem',
  sm: '1rem',
  md: '1.25rem',
  lg: '1.75rem',
  xl: '2.5rem',
  xxl: '4rem',
  h1: {
    default: '3.5625rem',
    '@media (max-width: 1024px)': '2.5rem',
  },
  h2: {
    default: '3rem',
    '@media (max-width: 1024px)': '2rem',
  },
  h3: {
    default: '2.25rem',
    '@media (max-width: 1024px)': '1.75rem',
  },
  h4: {
    default: '2rem',
    '@media (max-width: 1024px)': '1.5rem',
  },
  h5: {
    default: '1.75rem',
    '@media (max-width: 1024px)': '1.375rem',
  },
  h6: {
    default: '1.5rem',
    '@media (max-width: 1024px)': '1.125rem',
  },
  subheading: {
    default: '1rem',
  },
  bodyLarge: {
    default: '1rem',
  },
  bodySmall: {
    default: '0.875rem',
  },
  captionLarge: {
    default: '0.75rem',
  },
  captionSmall: {
    default: '0.625rem',
  }
})

export const breakpoints = stylex.defineVars({
  xxs: '280px',
  xs: '320px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  xxl: '1540px',
})

export const spacing = stylex.defineVars({
  xxs: '4px',
  xs: '8px',
  sm: '12px',
  md: '20px',
  lg: '40px',
  xl: '60px',
  xxl: '80px',
})

export const colors = stylex.defineVars({
  babyBlue: {
    default: '#A7CAD4',
  },
  babyBlue300: {
    default: '#D3E5E9',
  },
  beige300: {
    default: '#f2f1ed',
  },
  black: {
    default: '#333333',
    [DARK]: '#333333',
  },
  black300: {
    default: '#999999',
  },
  burgundy: {
    default: '#6A2828',
  },
  burgundy300: {
    default: '#7A4949',
  },
  cream: {
    default: '#F7E6C1',
    [DARK]: '#F7E6C1',
  },
  cream300: {
    default: '#FBF3E0',
  },
  emerald: {
    default: '#1E3830',
  },
  gray: {
    default: '#737373'
  },
  gray100: {
    default: '#F7F7F7',
  },
  gray200: {
    default: '#EFEFEF',
  },
  gray300: {
    default: '#C7C7C7',
  },
  gray500: {
    default: '#9F9F9F',
  },
  lavender: {
    default: '#CFC7DC',
  },
  lavenderLight: {
    default: '#EDE9F2',
  },
  marigold: {
    default: '#F0B323',
    [DARK]: '#F0B323',
  },
  marigold300: {
    default: '#F7D991',
  },
  mist: {
    default: '#B5C9C0',
  },
  mist200: {
    default: '#E7EDE8',
  },
  mist300: {
    default: '#DAE4DF',
  },
  mist400: {
    default: '#D1DDD7',
  },
  mist600: {
    default: '#ACC2B8',
  },
  navy: {
    default: '#1F3438',
    [DARK]: '#1F3438',
  },
  navy300: {
    default: '#2C4D54',
  },
  navy400: {
    default: '#8F999B',
  },
  navy700: {
    default: '#1F3438',
  },
  offWhite: {
    default: '#FCFCFA',
    [DARK]: '#FCFCFA',
  },
  peach: {
    default: '#FBB88F',
  },
  perracotta: {
    default: '#BC522F',
    [DARK]: '#BC522F',
  },
  perracotta300: {
    default: '#DF9780',
  },
  perracotta500: {
    default: '#D47555',
  },
  red700: {
    default: '#C12F28',
  },
  rose: {
    default: '#f4c5B9',
  },
  rust: {
    default: '#B35E38',
  },
  rustDark: {
    default: '#975233',
  },
  sage: {
    default: '#5E6C51',
    [DARK]: '#5E6C51',
  },
  sage300: {
    default: '#90A088',
  },
  sage500: {
    default: '#6B8060',
  },
  skyBlue: {
    default: '#C8D8EB',
  },
  skyBlueDark: {
    default: '#B3C8E1',
  },
  slate: {
    default: '#57728B',
  },
  slate300: {
    default: '#ABB8C5',
  },
  violet: {
    default: '#BBABD7',
  },
  violetLight: {
    default: '#F8F5FC',
  },
  warmBeige: {
    default: '#F4EADE',
  },
  warmGray: {
    default: '#C9C6B7',
  },
  warmGray100: {
    default: '#F5F3EB',
  },
  warmGray300: {
    default: '#E4E3DB',
  },
  warmGrayExtraLight: {
    default: '#F2F1ED',
  },
  white: {
    default: '#FFFFFF',
    [DARK]: '#FFFFFF',
  },
})

export const surface = stylex.defineVars({
  primary: {
    default: colors.navy,
    [DARK]: colors.navy,
  },
  secondary: {
    // 1b. Base Color Token converted into a Semantic Token
    default: colors.perracotta500,
    [DARK]: colors.perracotta500,
  },
  tertiary: {
    default: colors.white,
    [DARK]: colors.white,
  }
})

export const text = stylex.defineVars({
  primary: {
    default: colors.white,
    [DARK]: colors.white,
  },
  secondary: {
    default: colors.navy,
    [DARK]: colors.navy,
  },
})

export const globalTokens = stylex.defineVars({
  pageGap: spacing.md,
  contentGap: spacing.md,
  headerH1: fontSizes.xxl,
  text: fontSizes.md,
  borderRadiusSmall: '4px',
  borderRadius: '8px',
  borderRadiusLarge: '16px',
  borderWidth: '2px',
  boxShadow: '0 4px 5px 2px rgba(0 0 0 / 5%)',
  boxShadowInvert: '0 -4px 10px 0 rgba(0 0 0 / 5%)',
  fontFamily: 'Roboto, sans-serif',
  primaryFontFamily: 'Moderat, sans-serif',
  secondaryFontFamily: 'Suisse Works, sans-serif',
  maxWidth: breakpoints.xl,
  fontSize: '16px',
  promoBarHeight: '38px',
  fontWeightBold: '700',
  lineHeight: '1.5',
  letterSpacing: '0.5px',
  transition: 'all 0.3s ease',
  timingFunction: 'cubic-bezier(0.3, 1, 0.75, 1)',
  transitionSmooth: 'all 0.3s cubic-bezier(0.3, 1, 0.75, 1)'
})

export const defaultTheme = stylex.defineVars({
  primarySurface: surface.primary,
  secondarySurface: surface.secondary,
  primaryText: text.primary,
  secondaryText: text.secondary,
  primaryCTASurface: surface.secondary,
  primaryCTASurfaceHover: surface.tertiary,
  primaryCTAText: text.secondary,
  secondaryCTASurface: surface.tertiary,
  secondaryCTASurfaceHover: surface.secondary,
  secondaryCTAText: colors.navy,
  tertiaryCTASurface: colors.marigold,
  tertiaryCTAText: colors.navy,
  tertiaryCTASurfaceHover: colors.marigold300,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray300,
  indicatorHover: colors.gray200,
})

export const typographyTheme = stylex.defineVars({
  fontFamily: globalTokens.primaryFontFamily,
  fontSize: globalTokens.fontSize,
  fontWeight: 400,
  lineHeight: globalTokens.lineHeight,
  letterSpacing: globalTokens.letterSpacing,
})

export const typographyThemeMobile = stylex.defineVars({
  fontFamily: globalTokens.primaryFontFamily,
  fontSize: globalTokens.fontSize,
  fontWeight: 400,
  lineHeight: globalTokens.lineHeight,
  letterSpacing: globalTokens.letterSpacing,
})
