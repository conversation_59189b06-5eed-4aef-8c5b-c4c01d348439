import fetchGraphQL from '@/lib/contentful/fetchGraphQL'
import { ContentfulProduct } from '@/lib/contentful/types/products'
import { ContentfulProductVariant } from '@/lib/contentful/types/variants'

export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const slugs = searchParams.get('slugs')?.split(',').map((slug) => slug).join('", "')

  const data = await fetchGraphQL(
    `
      query {
        recommendedProducts: productCollection(where: {slug_in: ["${slugs}"]}) {
          items {
            __typename
            slug
            title
            productId
            productMetadata: productMetadataCollection(limit: 5) {
              items {
                type
                description {
                  json
                }
              }
            }
            variants: variantsCollection(limit: 15) {
              items {
                gtin8: globalTradeItemNumber
                sku
                swatch {
                  __typename
                  slug
                  presentation
                  icon {
                    url
                  }
                  style
                }
                variantId
                estimatedShippingDate
                price
                compareAtPrice
                primaryImage {        
                  title
                  url(transform: {
                    quality: 75
                  })
                }
              }
            }
          }
        }
      }
    `
  )

  const recommendedItems = data?.data?.recommendedProducts?.items ?? []

  const products = recommendedItems.map((product: ContentfulProduct) => {
    const variants = product.variants.items.filter(
      (variant: ContentfulProductVariant) => !variant.estimatedShippingDate
    )

    return {
      ...product,
      variants: {
        items: variants,
      },
    }
  })

  return Response.json(products)
}
