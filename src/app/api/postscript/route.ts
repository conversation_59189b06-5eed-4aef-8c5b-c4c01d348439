import { NextResponse } from 'next/server'

/* eslint-disable import/prefer-default-export */
export async function POST(req: Request) {
  const requestBody = await req.json()
  const {
    phone_number,
    email,
    keyword
  } = requestBody

  try {
    const payload = JSON.stringify({
      phone_number,
      email: email || undefined,
      keyword,
    })

    const options = {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.POSTSCRIPT_BEARER_TOKEN}`
      },
      body: payload
    }
    const response = await fetch('https://api.postscript.io/api/v2/subscribers', options)
    const res = await response.json()

    return NextResponse.json(res)
  } catch (err) {
    return NextResponse.json({
      errors: [
        { message: err instanceof Error ? err.message : 'Unknown error' }
      ]
    }, { status: 400 })
  }
}
