import client from '@/lib/meilisearch'

import { NextResponse } from 'next/server'

/* eslint-disable import/prefer-default-export */
export async function GET() {
  try {
    const meilisearchIndex = client.index(process.env.NEXT_PUBLIC_MEILISEARCH_INDEX as string)
    const indexOperationResult = await meilisearchIndex.search('cook')

    return NextResponse.json({ indexOperationResult })
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error(error)
    return NextResponse.json({
      error: 'Something went wrong'
    })
  }
}
