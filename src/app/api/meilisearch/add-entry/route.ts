import { MEILISEARCH_INDEX_PRODUCT_FIELDS } from '../query'

import { transformQueryToIndex } from '@/utils/meilesearch'
import client from '@/lib/meilisearch'
import fetchGraphQL from '@/lib/contentful/fetchGraphQL'

import { NextResponse } from 'next/server'

// THE CMS WILL TRIGGER THIS FUNCTION EVERY NEW ENTRY
const getProductById = async (id: string) => {
  const { data } = await fetchGraphQL(
    `
       query {
        product(id: "${id}"){
        ${MEILISEARCH_INDEX_PRODUCT_FIELDS}
        }
      }
    `,
    ['product']
  )
  return transformQueryToIndex([data.product])
}

/* eslint-disable import/prefer-default-export */
export async function POST(request: Request) {
  try {
    const { sys } = await request.json()
    const { id } = sys

    const productsToTheIndex = await getProductById(id)
    const meilisearchIndex = client.index(process.env.NEXT_PUBLIC_MEILISEARCH_INDEX as string)
    const indexOperationResult = await meilisearchIndex.addDocuments(productsToTheIndex)

    return NextResponse.json({ indexOperationResult })
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error(error)
    return NextResponse.json({ error: 'Something went wrong' }, { status: 500 })
  }
}
