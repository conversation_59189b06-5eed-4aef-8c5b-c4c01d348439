import { getPageSectionsData, getPageSectionsStructure } from '@/lib/contentful/fetchPages'
import { PageSection } from '@/lib/contentful/types'

import { NextRequest, NextResponse } from 'next/server'

/* eslint-disable import/prefer-default-export */
export async function POST(req: NextRequest) {
  try {
    const { pageId, sectionIds } = await req.json()

    if (!pageId || !sectionIds) {
      return NextResponse.json({ error: 'Missing parameters: pageId and sectionIds are required' }, { status: 400 })
    }

    const structure = await getPageSectionsStructure(pageId)
    const sectionsToFetch = structure.filter((section: PageSection) => sectionIds.includes(section.sys.id))
    const sections = await getPageSectionsData(sectionsToFetch)

    return NextResponse.json({ sections })
  } catch (error) {
    return NextResponse.json({ error: (error as Error).message }, { status: 500 })
  }
}
