import { NextResponse } from 'next/server'

/* eslint-disable import/prefer-default-export */
export async function POST(req: Request) {
  const requestBody = await req.json()
  const { email, klaviyoListId } = requestBody

  try {
    const payload = JSON.stringify({
      data: {
        type: 'subscription',
        attributes: {
          profile: {
            data: {
              type: 'profile',
              attributes: {
                email
              }
            }
          }
        },
        relationships: {
          list: {
            data: {
              type: 'list',
              id: klaviyoListId
            }
          }
        }
      }
    })

    const options = {
      method: 'POST',
      headers: {
        revision: '2024-10-15.pre',
        Accept: 'application/json',
        'content-type': 'application/vnd.api+json',
        Authorization: process.env.NEXT_PUBLIC_KLAVIYO_API_KEY || ''
      },
      body: payload
    }

    const response = await fetch(
      `https://a.klaviyo.com/client/subscriptions?company_id=${process.env.NEXT_PUBLIC_KLAVIYO_API_KEY}`,
      options
    )

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`)
    }

    const SUCCESS_STATUS = 202

    const responseText = await response.text()

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`)
      console.error('Error response text:', responseText)
      throw new Error(responseText)
    }

    if (response.status === SUCCESS_STATUS) {
      return NextResponse.json({
        status: 'success'
      })
    }
    return NextResponse.json({
      status: 'error'
    })
  } catch (err) {
    return NextResponse.json({
      errors: [
        { message: err instanceof Error ? err.message : 'Unknown error' }
      ]
    })
  }
}
