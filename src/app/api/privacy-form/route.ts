import { type FormFields } from '@/components/Generic/PrivacyRequestForm'

import Airtable from 'airtable'
import { NextResponse } from 'next/server'

/* eslint-disable import/prefer-default-export */
export async function POST(request: Request) {
  try {
    const data: FormFields = await request.json()
    const airtable = new Airtable({
      apiKey: process.env.AIRTABLE_API_KEY_PRIVACY_POLICY
    }).base(process.env.AIRTABLE_PRIVACY_POLICY_BASE_ID_PRIVACY_REQUEST as string)

    const insertData = {
      'First Name': data.firstName,
      'Last Name': data.lastName,
      Email: data.email,
      'Street Address': data.streetAddress,
      City: data.city,
      State: data.selectedState,
      'Zip Code': data.zipcode,
      'Request Type': data.requestType,
      'Consent Confirmation': data.consentConfirmation ? 'Yes' : 'No',
      'Right to Appeal': data.rightToAppeal ? 'Yes' : 'No',
      'Appeal Details': data.appealDetails,
      'Verification Method': data.verificationMethod && `${data.verificationMethod} California Only`
    }

    await airtable('Privacy Policy').create([{ fields: insertData }])

    return NextResponse.json({ success: true })
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return NextResponse.json(
      { error: `Failed to submit form: ${errorMessage}` },
      { status: 500 }
    )
  }
}
