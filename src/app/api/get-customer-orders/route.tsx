import { NextRequest, NextResponse } from 'next/server'

const SHOPIFY_DOMAIN = process.env.NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN
const ADMIN_API_TOKEN = process.env.NEXT_PUBLIC_SHOPIFY_ADMIN_API_TOKEN

/* eslint-disable import/prefer-default-export */
export async function POST(req: NextRequest) {
  const { email } = await req.json()

  if (!email) {
    return NextResponse.json({ error: 'Email is required' }, { status: 400 })
  }

  try {
    const query = `
      query getCustomerWithOrders($email: String!) {
        customers(first: 1, query: $email) {
          edges {
            node {
              id
              email
              firstName
              lastName
              orders(first: 5) {
                edges {
                  node {
                    id
                    displayFulfillmentStatus
                    lineItems(first: 10) {
                      edges {
                        node {
                          title
                          id
                          variant {
                            product { 
                              id
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `

    const res = await fetch(`${SHOPIFY_DOMAIN}/admin/api/2025-04/graphql.json`, {
      method: 'POST',
      headers: {
        'X-Shopify-Access-Token': ADMIN_API_TOKEN || '',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables: {
          email: `email:${email}`
        }
      }),
    })

    const json = await res.json()

    const customerEdge = json.data.customers.edges[0]
    if (!customerEdge) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    const orders = customerEdge.node.orders.edges.map((edge: any) => edge.node).filter((order: any) => order.displayFulfillmentStatus === 'FULFILLED')
    const productMap = new Map<string, { id: string; title: string }>()

    // Using any in this file since the data is retrieved from Shopify and the structure is not strictly defined
    orders.forEach((order: any) => {
      order.lineItems.edges.forEach((lineItemEdge: any) => {
        const lineItem = lineItemEdge.node
        const productId = lineItem?.variant?.product?.id
        const productTitle = lineItem?.title

        if (productId && !productMap.has(productId) && !productTitle.includes('Caraway Package Protection')) {
          productMap.set(productId, {
            id: productId,
            title: lineItem.title,
          })
        }
      })
    })

    const uniqueProducts = Array.from(productMap.values()).reverse()

    if (uniqueProducts.length === 0) {
      return NextResponse.json({ error: 'No products found for this customer' }, { status: 404 })
    }

    return NextResponse.json({ products: uniqueProducts })
  } catch (err) {
    console.error('GraphQL error:', err)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
