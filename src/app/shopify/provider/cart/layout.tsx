'use client'

import { open } from '@redux/features/cart/cartSlice'
import { useAppDispatch } from '@redux/hooks'

import { CartProvider, useCart, CartCheckoutButton } from '@shopify/hydrogen-react'

function CartComponent() {
  const { linesAdd, status } = useCart()

  const merchandise = { merchandiseId: '{id-here}' }

  return (
    <div>
      Cart Status:
      {status}
      <button type="button" onClick={() => linesAdd([merchandise])}>Add Line</button>
    </div>
  )
}

export default function App({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const dispatch = useAppDispatch()

  return (
    <div>
      <CartProvider
        onLineAdd={() => {
          dispatch(open())
        }}
      >
        {children}
        <CartComponent />
        <CartCheckoutButton> Checkout </CartCheckoutButton>
      </CartProvider>
    </div>
  )
}
