import client from '@lib/shopify/storefront-client'

import React from 'react'

const GRAPHQL_QUERY = `
  query {
    shop {
      name
    }
  }
`

const GetData = async () => {
  const res = await fetch(client.getStorefrontApiUrl(), {
    body: JSON.stringify({
      query: GRAPHQL_QUERY,
    }),
    headers: client.getPublicTokenHeaders(),
    method: 'POST',
  })

  if (!res.ok) {
    throw new Error(res.statusText)
  }

  return res.json()
}

const Page = async () => {
  const { data } = await GetData()
  return (
    <div>{data.shop.name}</div>
  )
}

export default Page
