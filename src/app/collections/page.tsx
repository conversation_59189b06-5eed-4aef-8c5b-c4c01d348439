import ProductListViewedEvent from '@/components/Product/ProductListViewedEvent'
import InfiniteSectionsLoader from '@/components/InfiniteSectionsLoader'
import AnchorRedirectHandler from '@/components/Generic/AnchorRedirectHandler'
import {
  getPageIdBySlug,
  getPageSectionsData,
  getPageSectionsStructure
} from '@/lib/contentful/fetchPages'
import { PageSection } from '@/lib/contentful/types'

import { Suspense } from 'react'

function getSectionsToFetch(pageStructure: PageSection[]) {
  let foundFirstProduct = false
  return pageStructure.filter((section) => {
    if (section.sectionType !== 'PageSectionsProduct') {
      return true
    }

    if (!foundFirstProduct) {
      foundFirstProduct = true
      return true
    }

    return false
  })
}

export default async function CollectionsPage() {
  const pageId = await getPageIdBySlug('collections')
  const pageStructure = await getPageSectionsStructure(pageId)
  const sectionsToFetch = getSectionsToFetch(pageStructure)
  const sections = await getPageSectionsData(sectionsToFetch)

  const grids = sections.filter(
    (section: { subtype: string }) => section.subtype === 'Product Grid'
  )

  const products = grids.map((grid: { productsCollection: any }) => grid.productsCollection).flat()

  return (
    <>
      <Suspense>
        <AnchorRedirectHandler />
      </Suspense>
      <ProductListViewedEvent
        products={products}
        title={pageStructure.name}
        id={pageStructure.slug}
      />
      <InfiniteSectionsLoader
        pageId={pageId}
        initialSections={sections}
        pageStructure={pageStructure}
      />
    </>
  )
}
