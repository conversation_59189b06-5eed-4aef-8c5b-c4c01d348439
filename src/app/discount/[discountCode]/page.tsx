import DiscountCode from '@/components/Cart/DiscountCodeRedirect'
import PageCTA from '@/components/Content/PageCTA'
import { getCouponRedirectData } from '@/utils/coupons'

// redirects have to be dynamic to be reliable
export const dynamic = 'force-dynamic'

type DiscountProps = {
  params: {
    discountCode: string;
  };
  searchParams?: {
    redirect: string;
  };
};

const DiscountV2 = ({ params, searchParams }: DiscountProps) => {
  const { discountCode } = params
  const { redirect } = getCouponRedirectData(searchParams)

  return (
    <DiscountCode code={discountCode} redirect={redirect}>
      <PageCTA
        header={discountCode}
        subheader="Hang tight, we’re preheating the page for you!"
        theme="sage"
      />
    </DiscountCode>
  )
}

export default DiscountV2
