import { ThemeColors } from './themeThemes.stylex'

import RichTextRender from '@/utils/RichTextRenderer'
import { AssetType } from '@/components/Content/MediaContainer'
import fetchGlobalPage404 from '@/lib/contentful/fetchGlobalPage404'
import { condition, notEmpty } from '@utils/checking'
import { getTypeFace } from '@/utils/contentfulSettings'
import generateCallToActionProps, { CallToActionExtractProps } from '@/utils/generateCallToActionProps'
import { toCamelCase } from '@/utils/regex'
import Hero from '@/components/Content/Hero'
import { AnchorProps } from '@/components/Generic/CallToAction'

import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

const layoutProps: Record<string, {
  contentAlignment?: 'left' | 'right' | 'center' | undefined,
  smallSize?: boolean,
  evenContent?: boolean,
  hideMobileImage?: boolean,
  doNotSplitImage?: boolean,
  inverse?: boolean | undefined
}> = {
  'Layout 1': {
    contentAlignment: 'center',
  },
  'Layout 2': {
    contentAlignment: 'left',
  },
  'Layout 3': {
    contentAlignment: 'right',
  },
  'Layout 4': {
    contentAlignment: 'center',
    smallSize: true
  },
  'Layout 5': {
    contentAlignment: 'left',
    evenContent: true
  },
  'Layout 6': {
    contentAlignment: 'right',
    evenContent: true
  },
  'Layout 7': {
    contentAlignment: 'left',
    hideMobileImage: true
  },
  'Layout 8': {
    contentAlignment: 'left',
    doNotSplitImage: true
  },
  'Layout 9': {
    inverse: false
  },
  'Layout 10': {
    inverse: true
  },
}

type HeroSliderProps = {
  id: string;
  media?: {
    items: AssetType[];
  } | undefined;
  mobileMedia?: {
    items: AssetType[];
  } | undefined;
  smallSize?: boolean;
  evenContent?: boolean;
  doNotSplitImage?: boolean;
  logo?: AssetType[];
  contentAlignment?: 'left' | 'right' | 'center';
  hideMobileImage?: boolean
  theme?: ThemeColors;
  width?: string
  subHeadline?: string;
  header: string;
  text?: string;
  mediaPlaying?: boolean;
  buttons: AnchorProps[];
  typeFace?: 'primaryFontFamily' | 'secondaryFontFamily';
  onPlayerChangeState?: (playing: boolean) => void;
  videoIconSize?: number;
  // eslint-disable-next-line
    layout?: string;
}

type LayoutValue = keyof typeof layoutProps
function getSliderProps(
  block: any,
  globalTheme: string,
  globalLayout: LayoutValue
): HeroSliderProps | undefined {
  let id
  let text
  let header
  let subHeadline
  let width
  let theme
  let layout: LayoutValue
  let logo
  let media
  let mobileMedia

  const typeFace = getTypeFace(block.settings?.fontFamily)

  if (block.__typename === 'BlockContent') {
    id = block.sys.id
    text = block.content && RichTextRender({
      content: block.content?.json
    })
    header = block.title
    media = block.assetsCollection
    mobileMedia = block.mobileAssetsCollection

    theme = condition<string>(
      notEmpty(block.settings?.theme),
      block.settings?.theme ? toCamelCase(block.settings.theme) : undefined,
      globalTheme
    )

    width = block.settings?.width

    layout = condition<LayoutValue>(
      notEmpty(block.settings?.layout),
      block.settings?.layout,
      globalLayout
    )

    const buttons: CallToActionExtractProps[] = []

    if (notEmpty(block.referencesCollection?.items)) {
      const { items } = block.referencesCollection
      items.forEach((referBlock: any) => {
        if (referBlock.__typename === 'BlockContent') {
          logo = referBlock.assetsCollection?.items
          subHeadline = referBlock.content && documentToPlainTextString(
            referBlock.content?.json
          )
        }

        if (referBlock.__typename === 'BlockCallToAction') {
          buttons.push(
            generateCallToActionProps(referBlock)
          )
        }
      })
    }

    return {
      id,
      text,
      header,
      subHeadline,
      width,
      buttons,
      media,
      mobileMedia,
      logo,
      theme,
      typeFace,
      layout: toCamelCase(layout),
      ...layoutProps[layout],
    } as HeroSliderProps
  }
  return undefined
}

export default async function Custom404() {
  const globalPage404 = await fetchGlobalPage404()

  const props: HeroSliderProps[] = []
  const blocks = globalPage404.blocksCollection.items

  if (notEmpty(blocks)) {
    blocks.forEach((block: any) => {
      const sliderProps = getSliderProps(block, '', '')
      if (sliderProps) {
        // eslint-disable-next-line react/prop-types
        props.push(sliderProps)
      }
    })
  }

  return (
    <Hero props={props} />
  )
}
