import Postscript from '@scripts/postscript'
import DigitalGenius from '@scripts/digitalGenius'
import Okendo from '@scripts/okendo'
import MicrosoftClarity from '@scripts/microsoftClarity'
import RosterApp from '@scripts/roster'
import RevRoll from '@scripts/revRoll'
import Amped from '@scripts/amped'
import Tatari from '@scripts/tatari'
import Axon from '@scripts/axon'
import Neon from '@scripts/neontv'
import Corso from '@scripts/corso'
import Alia from '@scripts/alia'
import KeepCart from '@scripts/keepcart'

import { SpeedInsights } from '@vercel/speed-insights/next'
import { Analytics } from '@vercel/analytics/react'
import React from 'react'

const Scripts = () => (
  <>
    <Postscript />
    <DigitalGenius />
    <Okendo />
    <MicrosoftClarity />
    <RosterApp />
    <Amped />
    <Tatari />
    <RevRoll />
    <SpeedInsights />
    <Analytics />
    <Axon />
    <Neon />
    <Corso />
    <Alia />
    <KeepCart />
  </>
)

export default Scripts
