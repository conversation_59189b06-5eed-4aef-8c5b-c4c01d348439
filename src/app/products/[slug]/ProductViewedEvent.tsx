'use client'

import { productViewed } from '@redux/features/events/eventsSlice'
import { useAppDispatch, useAppSelector } from '@redux/hooks'
import { chord } from '@lib/chord/events'
import getCookie from '@utils/getCookie'
import { generateTtkTrackingId } from '@/utils/trackingId'
import getCollections from '@utils/productData'

import { useEffect, useState } from 'react'

import type { RootState } from '@redux/store'

// TODO: refactor and move into redux to maintain the patterns established

const ProductViewedEvent = ({ product, variant }: {
  product: any
  variant: any
}) => {
  const dispatch = useAppDispatch()
  const cart = useAppSelector((state: RootState) => state.cart.cart)
  const [isProductViewed, setIsProductViewed] = useState(false)
  const eventID = generateTtkTrackingId()

  const collections = getCollections(product)
  const firstCollection = collections.length > 0 ? collections[0] : null

  useEffect(() => {
    if (isProductViewed) return

    dispatch(productViewed({
      product, variant, cart, quantity: 1
    }))

    // the following code handles non standard product viewed and viewed content events, this is temporary and should be deprecated
    const viewedProductData = {
      Name: product?.title,
      Brand: 'Caraway',
      Currency: 'USD',
      // Categories: product?.collection[0]?.slug,
      Price: variant?.price,
      CompareAtPrice: variant?.compareAtPrice,
      URL: window.location.href,
      ProductId: product?.productId,
      ImageURL: variant.primaryImage.url,
      Id: variant?.variantId,
      // Category: product?.collection[0]?.slug,
      Value: variant?.price,
      fbp: getCookie('_fbp'),
      fbc: getCookie('_fbc'),
      event_id: eventID
    }

    const ttqData = {
      contents: [
        {
          content_id: product.productId?.toString(),
          content_name: product?.title,
          // content_category: product?.collection[0]?.slug,
          brand: 'Caraway',
          quantity: 1,
          price: variant?.price
        }
      ],
      value: variant?.price,
      content_type: 'product',
      currency: 'USD',
    }

    window?.postscript?.event('page_view', {
      shop_id: '4845',
      url: window.location.href,
      page_type: 'product',
      resource: {
        // category: product.collection[0]?.slug,
        name: product.title,
        // eslint-disable-next-line no-magic-numbers
        price_in_cents: variant.price * 100,
        resource_id: product.productId,
        resource_type: 'product',
        sku: variant.sku,
        variant_id: variant.variantId,
        vendor: 'Caraway'
      }
    })

    window?.dataLayer?.push({
      event: 'rr_view_item',
      rr_cart_id: cart.cartId,
      ecommerce: {
        currencyCode: 'USD',
        detail: {
          products: [{
            name: product.title,
            id: product.productId.toString(),
            sku: variant.sku,
            variant_id: variant.variantId,
            price: variant.price,
            brand: 'Caraway',
            category: firstCollection,
            image: variant?.primaryImage?.url,
            quantity: 1
          }]
        }
      },
    })

    window?.ttq?.track('ViewContent', ttqData, { event_id: eventID })
    chord.track('Viewed Product', viewedProductData)

    setIsProductViewed(true)
  }, [cart, dispatch, isProductViewed, product, variant, eventID])

  return null
}

export default ProductViewedEvent
