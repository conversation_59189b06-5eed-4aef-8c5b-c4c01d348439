/* eslint-disable */

import ProductViewedEvent from './ProductViewedEvent'

import {
  getAllProductSlugs,
  getProductBySlug,
  extractProductDetails
} from '@lib/contentful/fetchProducts'
import { getProductReviewsAggregate, getProductLatestReviews } from '@lib/okendo'
import {
  getPageSections,
} from '@lib/contentful/fetchPages'
import Container from '@components/layout/Container'
import ProductDetails from '@components/Product/ProductDetails'
import OkendoReviewsWidget from '@components/Okendo/ReviewsWidget'
import PageSections from '@components/PageSections'
import Spacer from '@components/layout/Spacer'
import { generateProductRichSnippet } from '@/utils/jsonLd'
import JsonLd from '@components/Generic/JsonLd'
import { PageObjectWithSeoMetadata } from '@/app/[...slug]/page'
import fetchGlobalMetadata from '@lib/contentful/fetchGlobalMetadata'
// import DiscountCodeRedirect from '@components/Cart/DiscountCodeRedirect'
// import { CouponRedirectSearchParams, getCouponRedirectData } from '@/utils/coupons'
import Dialog from '@components/Generic/Dialog'
import { MIN_RATING } from '@components/Generic/Rating'
import { generateMetaTagsWithFallBack } from '@/utils/metadata'
import { isAvailable } from '@components/Product/ProductCard/utils'
import { toCamelCase } from '@/utils/regex'
import { ThemeColors } from '@/app/themeThemes.stylex'
import { extractProductDialogs } from '@lib/contentful/fetchGlobalDialogs'

import { notFound } from 'next/navigation'
import variantAttribute from '@/utils/variants'

export async function generateStaticParams() {
  const products = await getAllProductSlugs()
  return products
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const { slug } = params
  const [
    product,
    globalMetadata
  ] = await Promise.all<PageObjectWithSeoMetadata>([
    getProductBySlug(slug),
    fetchGlobalMetadata()
  ])

  if (!product) {
    return {
      title: 'Product not found',
      description: 'Product not found',
    }
  }

  return generateMetaTagsWithFallBack(product, globalMetadata)
}

type ProductPageProps = {
  params: {
    slug: string
  }
}

// eslint-disable-next-line complexity
const ProductPage = async ({ params }: ProductPageProps) => {
  const { slug } = params
  const product: any = await getProductBySlug(slug)

  if (!product) {
    notFound()
  }

  const defaultSwatch = product.productMetadata?.items.find(
    (item: { type: string; references?: { items?: Array<{ slug?: string }> } }) => item.type === 'Card Default Swatch'
  )?.references?.items?.[0]


  const defaultSwatchVariant = product.variants.items.find(
    (item: { swatch: { slug: string } }) => item.swatch.slug === defaultSwatch?.slug
  )

  const variant = defaultSwatchVariant || product.variants.items[0]

  const {swatchType, slug: swatchSlug} = variant.swatch

  variantAttribute.set(`${swatchType}=${swatchSlug}`)

  const group = 'default'

  const dialogs = await extractProductDialogs(product)
  const reviewsAggregate = await getProductReviewsAggregate(product.productId)
  const { reviews: reviewsLatest, aggregated } = await getProductLatestReviews(
    product.productId
  )
  const reviews = {
    aggregate: reviewsAggregate,
    latest: reviewsLatest,
    aggregated
  }

  const showReviews = reviews.aggregate.count > 0 && reviews.aggregate.rating >= MIN_RATING

  const details = await extractProductDetails(product)
  const sections = await getPageSections(product)

  // Remove separator section block when reviews are not shown
  if (sections.length > 0 && !showReviews) {
    const lastSection = sections[sections.length - 1]

    if (lastSection.subtype === 'Separator') {
      sections.pop()
    }
  }

  // TODO: Temp solution for anatomy module can sync the variant with the product
  sections.forEach((section: { variant?: any }) => {
    section.variant = variant
  })

  // const { discountCode, redirect: discountRedirect } = getCouponRedirectData(searchParams)

  return (
    <>
      <Container as="section" theme="offWhite" size="6" align="center">
        <ProductViewedEvent product={product} variant={variant} />

        <Spacer size="md" />
        <ProductDetails
          variant={variant}
          product={product as any}
          group={group}
          details={details}
          reviews={reviews}
        />
      </Container>
      <Spacer size="lg" />
      <PageSections sections={sections} />
      <Spacer size="lg" />
      {showReviews && (
        <Container
          as="section"
          theme="offWhite"
          size="5"
          align="center"
          id="okendo_reviews"
          styleProp={{ overflow: 'hidden' }}
        >
          <OkendoReviewsWidget productId={product.productId} />
        </Container>
      )}
      <JsonLd data={generateProductRichSnippet(product, variant, reviews)} />
      {/* <DiscountCodeRedirect code={discountCode} redirect={discountRedirect} /> */}
      {dialogs && dialogs.map((dialog: any) => (
        <Dialog
          layout={dialog?.settings?.layout ?? 'Layout 1'}
          theme={toCamelCase(dialog?.settings?.theme) as ThemeColors}
          id={dialog.sys.id}
          key={dialog.sys.id}
          header={dialog.header}
          content={dialog.content}
          mobileContent={dialog.mobileContent}
          subheader={dialog.subheader}
          dialogToggles={dialog.togglesAndReferences?.toggles}
          compareChart={dialog?.togglesAndReferences?.compareChart}
          sections={dialog.sections}
        />
      ))}
    </>
  )
}

export default ProductPage
