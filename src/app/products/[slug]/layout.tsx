import ProductPage from '@providers/shopify/ProductProvider'
import {
  getProductID,
} from '@lib/contentful/fetchProducts'
import getShopifyData from '@/lib/shopify/getShopifyData'

import { flattenConnection } from '@shopify/hydrogen-react'
import { notFound } from 'next/navigation'

import type { Product } from '@shopify/hydrogen-react/storefront-api-types'

export const dynamicParams = false

const ProductLayout = async ({ params, children }: Readonly<{
  params: { slug: string };
  children: React.ReactNode;
}>) => {
  const { slug } = params
  const productSlug = await getProductID(slug)

  if (!productSlug) {
    notFound()
  }

  const { productId: id } = productSlug

  const { data } = await getShopifyData(id)
  const product = data?.product ? flattenConnection(data.product) : {}

  return (
    <ProductPage data={product as unknown as Product}>
      {children}
    </ProductPage>
  )
}

export default ProductLayout
