/* eslint-disable react/prop-types */

// TODO: Make a Add TO Cart Component
// TODO move this out to a separate file to avoide use client

'use client'

import { colors } from '@/app/themeTokens.stylex';
import en from '@/locales/en-US.json';

import { useProduct, AddToCartButton } from '@shopify/hydrogen-react';
import stylex from '@stylexjs/stylex';

const styles = stylex.create({
  button: {
    padding: '12px 24px',
    lineHeight: 1.5,
    backgroundColor: colors.marigold,
  }
});

function ProductAddToCartButton({ product }) {
  const variantId = product?.variants?.nodes[0]?.id;

  if (!variantId) {
    return null;
  }

  return (
    <AddToCartButton
      {...stylex.props(styles.button)}
      variantId={variantId}
    >
      { en.product.add_to_cart }
    </AddToCartButton>
  )
}

export default function Page() {
  const { product } = useProduct();
  return (
    <div style={
      {
        textAlign: 'center', display: 'block', width: '100%', paddingTop: '1rem'
      }
   }>
      <div>Home // Products // Circle Pan PDP</div>
      <h1>{product?.title}</h1>
      <ProductAddToCartButton product={product?.product} />
    </div>
  )
}
