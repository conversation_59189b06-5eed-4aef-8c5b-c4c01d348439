'use client'

import CallToAction from '@/components/Generic/CallToAction'
import Input from '@/components/ui/Forms/Input'
import {
  fontSizes,
  spacing,
  colors
} from '@/app/themeTokens.stylex'
import Typography from '@/components/Typography'
import useFormFields from '@/hooks/useFormFields'

import * as stylex from '@stylexjs/stylex'
import { useCallback, useState } from 'react'
import Link from 'next/link'
import { parseGid } from '@shopify/hydrogen-react'

type Product = {
  id: string
  title: string
}

const styles = stylex.create({
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: spacing.md,
    paddingRight: spacing.md,
    minHeight: '650px',
    backgroundColor: `${colors.beige300}`,
  },
  list: {
    listStyleType: 'disc',
    paddingLeft: '20px',
    maxWidth: '400px',
    textAlign: 'left',
    margin: '0 auto',
  },
  link: {
    marginBottom: spacing.xxs,
    display: 'block',
    color: 'blue',
    padding: spacing.sm,
    fontSize: fontSizes.sm,
    cursor: 'pointer',
    textDecoration: 'underline'
  },
  heading: {
    fontSize: fontSizes.lg,
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    backgroundColor: colors.white,
    padding: '24px',
    borderRadius: '12px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    width: '100%',
    maxWidth: '600px',
    textAlign: 'center',
  },
  label: {
    fontSize: '16px',
    fontWeight: 500,
    color: '#333',
    marginBottom: '20px',
  },
  errorText: {
    color: colors.red700,
    fontSize: fontSizes.sm,
    marginTop: '8px',
  },
  underline: {
    textDecoration: 'underline',
  },
})

const OKENDO_SUBSCRIBER_ID = process.env.NEXT_PUBLIC_OKENDO_SUBSCRIBER_ID

const OrderList = ({
  products,
  email,
  firstName,
  lastName
}: {
  products: Product[]
  email: string
  firstName: string
  lastName: string
}) => (
  <div {...stylex.props(styles.form)}>
    <Typography as="h2" typographyTheme="h3Secondary" {...stylex.props(styles.heading)}>
      Almost There!
    </Typography>
    <Typography as="p">
      <span {...stylex.props(styles.underline)}>Step two:</span> Please select the product you ordered below and leave a review to complete your warranty registration.
    </Typography>
    <Typography as="p">
      Once your review is submitted, your warranty is activated! Thank you for being part of the Caraway family - your clean journey starts here!
    </Typography>
    <ul {...stylex.props(styles.list)}>
      {products.map((product) => {
        const { id } = parseGid(product.id)
        const fullName = `${firstName} ${lastName}`.trim()
        const url = `https://okendo.reviews/?subscriberId=${OKENDO_SUBSCRIBER_ID}&productId=shopify-${id}&email=${encodeURIComponent(email)}&name=${encodeURIComponent(fullName)}`
        return (
          <li key={product.id}>
            <Link href={url} target="_blank" {...stylex.props(styles.link)}>
              {product.title}
            </Link>
          </li>
        )
      })}
    </ul>
  </div>
)

const EmailForm = ({
  setProductList,
  error,
  setError,
  user,
  handleChange,
}: {
  setProductList: (products: Product[]) => void
  error: string
  setError: (msg: string) => void
  user: {
    firstName: string
    lastName: string
    email: string
    phoneNumber: string
  }
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void
}) => {
  const handleSubmit = useCallback(async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError('')

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(user.email)) {
      setError('Please enter a valid email address.')
      return
    }

    try {
      const res = await fetch('/api/get-customer-orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: user.email }),
      })

      const data = await res.json()

      if (!res.ok) {
        setError(data.error || 'An unexpected error occurred.')
        return
      }

      setProductList(data.products)
    } catch (err) {
      setError('Sorry, there were no orders found for this user.')
      console.error(err)
    }
  }, [user.email, setProductList, setError])

  return (
    <form {...stylex.props(styles.form)} onSubmit={handleSubmit}>
      <Typography as="h1" typographyTheme="h3Secondary" {...stylex.props(styles.heading)}>
        Register Your Warranty.
      </Typography>
      {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
      <label htmlFor="email" {...stylex.props(styles.label)}>
        <span {...stylex.props(styles.underline)}>Step one:</span> Please enter your information to get started.
      </label>

      <Input id="firstName" type="text" placeholder="First Name" value={user.firstName} event={handleChange} required />
      <Input id="lastName" type="text" placeholder="Last Name" value={user.lastName} event={handleChange} required />
      <Input id="email" type="email" placeholder="Email" value={user.email} event={handleChange} required />
      <Input id="phoneNumber" type="text" placeholder="Phone Number" value={user.phoneNumber} event={handleChange} />

      {error && (
        <div {...stylex.props(styles.errorText)} role="alert">
          {error}
        </div>
      )}

      <CallToAction submit>Submit</CallToAction>
    </form>
  )
}

const LeaveReviewForm = () => {
  const [productList, setProductList] = useState<Product[]>([])
  const [error, setError] = useState('')

  const [user, handleChange] = useFormFields({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
  })

  return (
    <div>
      <div {...stylex.props(styles.container)}>
        {productList.length > 0 ? (
          <OrderList products={productList} email={user.email} firstName={user.firstName} lastName={user.lastName} />
        ) : (
          <EmailForm
            setProductList={setProductList}
            user={user}
            handleChange={handleChange}
            error={error}
            setError={setError}
          />
        )}
      </div>
    </div>
  )
}

export default LeaveReviewForm
