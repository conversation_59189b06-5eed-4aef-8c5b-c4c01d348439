import {
  fontSizes,
  globalTokens as $,
  typographyThemeMobile as $MT,
} from './themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const h1Primary = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: 'clamp(2.25rem, 1.5938rem + 2.625vw, 3.5625rem)',
  lineHeight: 'clamp(3rem, 2.375rem + 2.5vw, 4.25rem)',
  letterSpacing: '1',
})

const h1Secondary = stylex.createTheme($MT, {
  fontFamily: $.secondaryFontFamily,
  fontSize: 'clamp(2rem, 1.2857rem + 2.2857vw, 3rem)',
  lineHeight: 'clamp(2.75rem, 1.8571rem + 2.8571vw, 4rem)',
  letterSpacing: '1',
  fontWeight: 700,
})

const h2Primary = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: 'clamp(1.75rem, 1.3929rem + 1.1429vw, 2.25rem)',
  lineHeight: 'clamp(2.5rem, 2.1429rem + 1.1429vw, 3rem)',
  letterSpacing: '1',
})

const h2Secondary = stylex.createTheme($MT, {
  fontFamily: $.secondaryFontFamily,
  fontSize: 'clamp(1.75rem, 1.3929rem + 1.1429vw, 2.25rem)',
  lineHeight: 'clamp(2.5rem, 2.1429rem + 1.1429vw, 3rem)',
  letterSpacing: '1',
  fontWeight: 700,
})

const h3Primary = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: 'clamp(1.5rem, 1.1429rem + 1.1429vw, 2rem)',
  lineHeight: 'clamp(2.25rem, 1.5357rem + 2.2857vw, 3.25rem)',
  letterSpacing: '1',
})

const h3Secondary = stylex.createTheme($MT, {
  fontFamily: $.secondaryFontFamily,
  fontSize: 'clamp(1.5rem, 1.1429rem + 1.1429vw, 2rem)',
  lineHeight: 'clamp(2.25rem, 1.5357rem + 2.2857vw, 3.25rem)',
  letterSpacing: '1',
  fontWeight: 700,
})

const h4Primary = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: 'clamp(1.375rem, 1.1071rem + 0.8571vw, 1.75rem)',
  lineHeight: 'clamp(2.125rem, 1.8571rem + 0.8571vw, 2.5rem)',
  letterSpacing: '1',
})

const h4Secondary = stylex.createTheme($MT, {
  fontFamily: $.secondaryFontFamily,
  fontSize: 'clamp(1.375rem, 1.1071rem + 0.8571vw, 1.75rem)',
  lineHeight: 'clamp(2.125rem, 1.8571rem + 0.8571vw, 2.5rem)',
  letterSpacing: '1',
  fontWeight: 700,
})

const h5Primary = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: 'clamp(1.25rem, 1.0714rem + 0.5714vw, 1.5rem)',
  lineHeight: 'clamp(1.75rem, 1.5714rem + 0.5714vw, 2rem)',
  letterSpacing: '1',
})

const h5Secondary = stylex.createTheme($MT, {
  fontFamily: $.secondaryFontFamily,
  fontSize: 'clamp(1.25rem, 1.0714rem + 0.5714vw, 1.5rem)',
  lineHeight: 'clamp(1.75rem, 1.3929rem + 1.1429vw, 2.25rem)',
  letterSpacing: '1',
  fontWeight: 700,
})

const h6Primary = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: 'clamp(1.125rem, 1.0357rem + 0.2857vw, 1.25rem)',
  lineHeight: 'clamp(1.625rem, 1.5357rem + 0.2857vw, 1.75rem)',
  letterSpacing: '1',
})

const h6Secondary = stylex.createTheme($MT, {
  fontFamily: $.secondaryFontFamily,
  fontSize: 'clamp(1.125rem, 1.0357rem + 0.2857vw, 1.25rem)',
  lineHeight: 'clamp(1.625rem, 1.3571rem + 0.8571vw, 2rem)',
  letterSpacing: '1',
  fontWeight: 700,
})

const subheading = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: fontSizes.sm,
  lineHeight: '1.5',
  letterSpacing: '1.4px',
})

const bodyLarge = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: fontSizes.sm,
  lineHeight: '1.5rem',
  letterSpacing: '1',
})

const bodySmall = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: fontSizes.xs,
  lineHeight: '1.375rem',
  letterSpacing: '1.57',
})

const bodyXSmall = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: fontSizes.xxs,
  lineHeight: '1.375rem',
  letterSpacing: '1.57',
})

const captionLarge = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: fontSizes.xxs,
  lineHeight: fontSizes.sm,
  letterSpacing: '1',
})

const captionSmall = stylex.createTheme($MT, {
  fontFamily: $.primaryFontFamily,
  fontSize: '0.625rem',
  lineHeight: '1.4',
  letterSpacing: '1',
})

const typographyThemesMobile = {
  h1Primary,
  h1Secondary,
  h2Primary,
  h2Secondary,
  h3Primary,
  h3Secondary,
  h4Primary,
  h4Secondary,
  h5Primary,
  h5Secondary,
  h6Primary,
  h6Secondary,
  subheading,
  bodyLarge,
  bodySmall,
  bodyXSmall,
  captionLarge,
  captionSmall,
}

export default typographyThemesMobile
