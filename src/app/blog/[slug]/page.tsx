import { PageObjectWithSeoMetadata } from '@/app/[...slug]/page'
import ArticleSections from '@components/ArticleSections'
import fetchGlobalMetadata from '@lib/contentful/fetchGlobalMetadata'
import JsonLd from '@components/Generic/JsonLd'
import { ArticleType, generateArticleJsonLd } from '@/utils/articleJsonLd'
import {
  getBlogPageBySlug,
  getArticleSections,
  FetchBlogResponseProps
} from '@lib/contentful/fetchBlogs'
import fetchGraphQL from '@lib/contentful/fetchGraphQL'
import Container from '@/components/layout/Container'
import BlogHeroHeader from '@components/Article/BlogHeroHeader'
import { generateMetaTagsWithFallBack } from '@/utils/metadata'

import { notFound } from 'next/navigation'

export const dynamicParams = false

const limit = 1

const getAllBlogArticles = `
  query {
    blogArticleCollection(limit: ${limit}) {
      items {
        slug
      }
    }
  }
`

const extractEntries = (fetchResponse: FetchBlogResponseProps) => fetchResponse.data.blogArticleCollection.items.map((page) => ({
  slug: page.slug,
  sections: page.articleSectionsCollection
}))

export async function generateStaticParams() {
  const data = await fetchGraphQL(getAllBlogArticles, ['blogArticles'])
  return extractEntries(data)
}

type ArtlcleWithSeo = PageObjectWithSeoMetadata & {
  authorCollection?: {
    items: {
      name: string
    }[]
  }
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const { slug } = params
  const [
    article,
    globalMetadata
  ] = await Promise.all<ArtlcleWithSeo>([
    getBlogPageBySlug(slug),
    fetchGlobalMetadata()
  ])

  if (!article) {
    return {
      title: 'Article not found',
      description: 'Article not found',
    }
  }

  return generateMetaTagsWithFallBack(article, globalMetadata)
}

const blogHeroImage = {
  url: '/assets/blog-header-image.png',
}

const BlogPage = async ({ params }: { params: { slug: string } }) => {
  const { slug } = params
  const article = await getBlogPageBySlug(slug)

  if (!article) {
    notFound()
  }

  const sections = getArticleSections(article)
  const { name, image } = article
  const banner = image?.url || article?.seoMetadata?.image?.url || blogHeroImage.url
  const articleInfo = {
    authors: article.authorCollection,
    datePublished: article.datePublished,
    path: `https://carawayhome.com/blog/${slug}`
  }

  return (
    <Container as="section">
      <BlogHeroHeader
        category={{
          name: 'Home & Kitchen with Caraway'
        }}
        title={name}
        theme="cream"
        image={{ src: banner, title: name }}
      />
      <Container pageGap>
        <ArticleSections sections={sections} articleInfo={articleInfo} />
      </Container>
      <JsonLd data={generateArticleJsonLd(article as unknown as ArticleType, sections)} />
    </Container>
  )
}

export default BlogPage
