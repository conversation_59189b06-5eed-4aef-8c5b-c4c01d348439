'use client'

import Container from '@/components/layout/Container'
import APIForm from '@/components/Generic/APIForm'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  containerStyle: {
    minHeight: '700px',
    height: '65vh',
    backgroundImage: 'url(/assets/sms-holiday-desktop.jpg)',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
  },
})

type FormDetailsObjectType = {
  header: string,
  body: string,
  ctaCopy: string,
  disclaimer?: string
  successMessage: string,
  keyword: string
}

const CarawaySMSProgramForm = () => {
  const formDetailsObject: FormDetailsObjectType = {
    header: 'Unlock Weekly Flash Deals',
    body: 'Join our SMS program to be first to learn about new launches, get free shipping, exclusive offers, and more.',
    ctaCopy: 'Subscribe →',
    // eslint-disable-next-line max-len
    disclaimer: '*By providing your phone number, you agree to receive recurring automated marketing text messages (e.g. cart reminders) from this shop and third parties acting on its behalf. Consent is not a condition to obtain goods or services. Msg & data rates may apply. Msg frequency varies. Reply HELP for help and STOP to cancel. You also agree to the Terms of Service and Privacy Policy.',
    successMessage: 'Thanks for signing up!',
    keyword: 'CARAWAYSMS',
  }

  return (
    <Container
      as="div"
      flexCentered
      styleProp={styles.containerStyle}
    >
      <APIForm
        formDetailsObject={formDetailsObject}
      />
    </Container>
  )
}

export default CarawaySMSProgramForm
