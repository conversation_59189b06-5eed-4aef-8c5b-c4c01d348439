type PostScriptResponseType = {
  created_at: string
  email?: string
  id: string
  phone_number: string
  resource: string
  shopify_customer_id?: string
} | {
  errors: {
    message: string,
    type: string
  }[]
}

type KlaviyoResponseType = {
  status: 'success' | 'error'
} | {
  errors: {
    message: string,
  }[]
}

type onSubmitType = {
  number: string,
  email: string,
  keyword?: string,
  klaviyoListId?: string
}

export async function subscribeToPostScript(data: onSubmitType): Promise<PostScriptResponseType> {
  const {
    number,
    email,
    keyword
  } = data

  const httpResponse = await fetch('/api/postscript', {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      phone_number: number,
      email: email || undefined,
      keyword,
    })
  })

  const response = await httpResponse.json()
  return response
}

export async function subscribeToKlaviyo(data: onSubmitType): Promise<KlaviyoResponseType> {
  const { email, klaviyoListId } = data

  const httpResponse = await fetch('/api/klaviyo', {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'content-type': 'application/vnd.api+json',
    },
    body: JSON.stringify({
      email,
      klaviyoListId
    })
  })

  const response = await httpResponse.json()
  return response
}
