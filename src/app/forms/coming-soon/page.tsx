import Container from '@/components/layout/Container'
import APIForm from '@/components/Generic/APIForm'
import { getPageBySlug, getPageSections } from '@/lib/contentful/fetchPages'
import PageSections from '@/components/PageSections'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  containerStyle: {
    height: {
      default: '90vh',
      '@media (min-width: 992px)': '90vh',
    },
    aspectRatio: '16 / 11',
    width: '100%',
    position: 'relative',
    justifyContent: {
      default: 'start',
      '@media (min-width: 992px)': 'center',
    },
    paddingTop: {
      default: '30%',
      '@media (min-width: 992px)': '0%',
    },
    zIndex: 1,
  },
  desktopBgImage: {
    display: {
      default: 'none',
      '@media (min-width: 992px)': 'block',
    },
    objectFit: 'fill',
  },
  mobileBgImage: {
    display: {
      default: 'block',
      '@media (min-width: 992px)': 'none',
    },
    objectFit: 'cover',
  },
  desktopVideo: {
    display: {
      default: 'none',
      '@media (min-width: 992px)': 'block',
    },
    position: 'absolute',
    zIndex: 0,
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    top: '0',
    left: '0',
  },
  mobileVideo: {
    display: {
      default: 'block',
      '@media (min-width: 992px)': 'none',
    },
    position: 'absolute',
    zIndex: 0,
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    top: '0',
    left: '0',
  },
})

type FormDetailsObjectType = {
  header: string,
  body: string,
  ctaCopy: string,
  disclaimer?: string,
  successMessage: string,
  keyword: string,
}

const ComingSoonForm = async () => {
  const formDetailsObject: FormDetailsObjectType = {
    header: 'Unlock SMS Exclusive Offers',
    body: 'Join our SMS program to be first to learn about new launches, get free shipping, exclusive flash deals, and more.',
    ctaCopy: "Let's Text",
    successMessage: 'Thanks for signing up!',
    keyword: 'FATHERSDAY',
  }

  const page = await getPageBySlug('caraway-5-years')
  const results = await getPageSections(page)
  const sections = results[results.length - 1]

  return (
    <div>
      <Container
        as="div"
        flexCentered
        styleProp={styles.containerStyle}
      >
        <video autoPlay loop muted playsInline {...stylex.props(styles.desktopVideo)}>
          <source src="/assets/airtight-early-access-desktop.mp4" type="video/mp4" />
        </video>
        <video autoPlay loop muted playsInline {...stylex.props(styles.mobileVideo)}>
          <source src="/assets/airtight-early-access-mobile.mp4" type="video/mp4" />
        </video>
        <APIForm
          formDetailsObject={formDetailsObject}
        />
      </Container>
      <PageSections sections={[sections]} />
    </div>
  )
}

export default ComingSoonForm
