import ProductGrid from '@components/Product/ProductGrid'
import Container from '@components/layout/Container'
import PageSections from '@components/PageSections'
import {
  getCollectionBySlug,
  getPageSections,
} from '@lib/contentful/fetchPages'
import {
  FetchCollectionResponseProps,
} from '@lib/contentful/types'
import fetchGraphQL from '@lib/contentful/fetchGraphQL'
import Wrapper from '@components/layout/Wrapper'
import Spacer from '@components/layout/Spacer'
import Feature from '@components/layout/Feature'
import ProductListViewedEvent from '@/components/Product/ProductListViewedEvent'

import { notFound } from 'next/navigation'

export const dynamicParams = false

const limit = 1

const getAllCollections = `
  query {
    collectionCollection(limit: ${limit}, where: { hidden: false }) {
      items {
        slug
      }
    }
  }
`
const extractEntries = (fetchResponse: FetchCollectionResponseProps) => fetchResponse.data.collectionCollection.items.map((collection) => ({
  slug: collection.slug,
}))

export async function generateStaticParams() {
  const data = await fetchGraphQL(getAllCollections, ['shop'])
  return extractEntries(data)
}

const Page = async ({ params }: { params: { slug: string } }) => {
  const { slug } = params
  const collection = await getCollectionBySlug(slug)

  if (!collection) {
    notFound()
  }

  const {
    products,
    name,
    seo
  } = collection
  const { description, image } = seo || {}
  const sections = await getPageSections(collection)

  const banner = {
    header: name,
    body: description || '',
    image: image?.url || '',
  }

  return (
    <>
      <ProductListViewedEvent products={products} title={collection.name} id={slug} />
      <Wrapper size="5">
        <Feature slides={[]} content={banner} theme="offWhite" type="base" reverse />
      </Wrapper>
      <ProductGrid products={products} />
      <Spacer size="lg" />
      <Container as="section">
        <PageSections sections={sections} />
      </Container>
    </>

  )
}

export default Page
