import Container from '@/components/layout/Container'
import Spacer from '@/components/layout/Spacer'
import Wrapper from '@/components/layout/Wrapper'
import ProductGrid from '@components/Product/ProductGrid'
import { getAllProducts } from '@lib/contentful/fetchProducts'

import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  root: {
    width: '100%',
    maxWidth: 1196,
    paddingBlock: '20px',
    margin: '0 auto',
  },
  products: {
    display: 'grid',
    gridTemplateColumns: 'repeat(4, 1fr)',
    gap: '72px 19px',
    gridAutoFlow: 'dense',
  }
})

// TODO need to update the data return to reduce payload
const CollectionsPage = async () => {
  const products = await getAllProducts()
  return (
    <Wrapper as="section" styleProp={styles.root}>
      <h1 style={{ marginBottom: 40 }}>All Products</h1>
      <Spacer size="md" />
      <Container
        as="div"
        grid
        gap="2"
        styleProp={[styles.products]}
      >
        <ProductGrid products={products} />
      </Container>
    </Wrapper>
  )
}

export default CollectionsPage
