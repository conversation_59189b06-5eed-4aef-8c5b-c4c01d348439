import Scripts from './scripts'
import { colors } from './themeTokens.stylex'

import Provider from '@providers/StoreProvider'
import Header from '@components/layout/Header'
import Footer from '@components/layout/Footer'
import Cart from '@components/Cart/Cart'
import Segment from '@components/Segment/Segment'
import Dialog from '@components/Generic/Dialog'
import fetchAboutNavigation from '@lib/contentful/fetchAboutNavigation'
import fetchNavigation from '@lib/contentful/fetchNavigation'
import fetchMobileNavigation from '@lib/contentful/fetchMobileNavigation'
import fetchMobileQuickNavigation from '@lib/contentful/fetchMobileQuickNavigation'
import fetchPromoBar from '@lib/contentful/fetchPromoBar'
import Promobar from '@/components/layout/Header/Promobar'
import fetchFooter from '@lib/contentful/fetchFooter'
import fetchGlobalMetadata from '@lib/contentful/fetchGlobalMetadata'
import fetchGlobalDialogs from '@lib/contentful/fetchGlobalDialogs'
import JsonLd from '@components/Generic/JsonLd'
import { generatePageLdJson } from '@utils/pageJsonLd'
import { getAllProductsWithMetadata, getAllProductsWithVariantCustomMessage } from '@/lib/contentful/fetchProducts'
import fetchSearchMenu from '@/lib/contentful/fetchSearchMenu'

import Link from 'next/link'
import stylex from '@stylexjs/stylex'
import './globals.css'
import { Suspense } from 'react'

const styles = stylex.create({
  skipToContent: {
    position: 'fixed',
    zIndex: 1000,
    background: colors.white,
    left: {
      default: '-1000%',
      ':focus': 0,
      ':focus-visible': 0,
      ':target': 0,
    },
  }
})

export async function generateMetadata() {
  const globalMetadata = await fetchGlobalMetadata()
  const title = globalMetadata?.seoMetadata?.name
  const description = globalMetadata?.seoMetadata?.description
  const image = globalMetadata?.seoMetadata?.image?.url
  const keywords = globalMetadata?.seoMetadata?.keywords || undefined
  const blockSearchIndexing = globalMetadata?.seoMetadata?.blockSearchIndexing

  return {
    title,
    description,
    openGraph: {
      images: [
        image
      ]
    },
    other: {
      'oke:subscriber_id': process.env.NEXT_PUBLIC_OKENDO_SUBSCRIBER_ID || '',
      keywords,
      robots: blockSearchIndexing ? 'noindex' : undefined,
    },
  }
}

export default async function RootLayout({ children, params }: Readonly<{
  children: React.ReactNode;
  params: {
    slug: string
  }
}>) {
  const aboutNavigationData = await fetchAboutNavigation()
  const navigationData = await fetchNavigation()
  const promoBarData = await fetchPromoBar()
  const footerData = await fetchFooter()
  const globalMetadata = await fetchGlobalMetadata()
  const mobileNavigationData = await fetchMobileNavigation()
  const mobileQuickNavigationData = await fetchMobileQuickNavigation()
  const globalDialogs = await fetchGlobalDialogs()
  const productsWithAnIncludedItem = await getAllProductsWithMetadata()
  const productsCartItemsDetails = await getAllProductsWithVariantCustomMessage()
  const searchMenuData = await fetchSearchMenu()

  return (
    <html lang="en">
      <body>
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="48x48" href="/favicon-48x48.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="manifest" href="/site.webmanifest" />
        <link rel="mask-icon" href="/favicon.svg" color="#5E6C51" />
        <meta name="apple-mobile-web-app-title" content="Caraway" />
        <meta name="msapplication-TileColor" content="#5E6C51" />
        <meta name="theme-color" content="#2C4D54" />
        <nav aria-label="Accessibility Links">
          <Link
            href="/accessibility"
            tabIndex={0}
            {...stylex.props(styles.skipToContent)}
          >
            Click to view our Accessibility Statement or contact us with
            accessibility-related questions.
          </Link>
          <Link
            href="#main"
            tabIndex={0}
            {...stylex.props(styles.skipToContent)}
          >
            Skip to content
          </Link>
        </nav>
        <Provider>
          <Header
            aboutNavigationData={aboutNavigationData}
            navigationData={navigationData}
            promoBarData={promoBarData}
            mobileNavigationData={mobileNavigationData}
            mobileQuickNavigationData={mobileQuickNavigationData}
            promoBar={<Promobar slug={params.slug} />}
            searchMenuData={searchMenuData}
          />
          {children}
          <Footer items={footerData as any} />
          <Suspense>
            <Cart productsCartItemsDetails={productsCartItemsDetails} productsWithAnIncludedItem={productsWithAnIncludedItem} />
          </Suspense>
          <Segment />
          {globalDialogs?.map((dialog : any) => (
            <Dialog
              key={dialog.id}
              header={dialog.header}
              subheader={dialog.subheader}
              content={dialog.content}
              mobileContent={dialog.mobileContent}
              footerContent={dialog.footerContent}
              asset={dialog.asset}
              theme={dialog.theme}
              layout={dialog.layout}
              id={dialog.id}
              compareChart={dialog?.togglesAndReferences?.compareChart}
              sections={dialog.sections}
            />
          ))}
          <JsonLd data={generatePageLdJson(globalMetadata)} />
        </Provider>
        <div id="popover-container" />
        <Scripts />
      </body>
    </html>
  )
}
