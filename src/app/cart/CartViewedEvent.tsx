'use client'

import { cartViewed } from '@redux/features/events/eventsSlice'
import { useAppDispatch, useAppSelector } from '@redux/hooks'

import { useEffect, useState } from 'react'

import type { RootState } from '@redux/store'

const CartViewedEvent = ({
  isCartPage
}: {
  isCartPage?: boolean
}) => {
  const dispatch = useAppDispatch()
  const cart = useAppSelector((state: RootState) => state.cart.cart)
  const openStatus = useAppSelector((state: RootState) => state.cart.open)
  const [isCartViewed, setIsCartViewed] = useState(false)

  useEffect(() => {
    // cart page
    if (isCartPage && !isCartViewed) {
      dispatch(cartViewed({ cart }))
      setIsCartViewed(true)
    }
  }, [cart, dispatch, isCartPage, isCartViewed])

  useEffect(() => {
    // cart flyout
    if (!isCartPage) {
      if (openStatus && !isCartViewed) {
        // TODO: products are outdated when this fires before addToCart()
        dispatch(cartViewed({ cart }))
        setIsCartViewed(true)
      }
      if (!openStatus && isCartViewed) {
        setIsCartViewed(false)
      }
    }
  }, [cart, dispatch, isCartPage, isCartViewed, openStatus])

  return null
}

export default CartViewedEvent
