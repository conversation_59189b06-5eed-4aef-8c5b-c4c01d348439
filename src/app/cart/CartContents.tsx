'use client'

import { LineItemProp } from '@components/Cart/types'
import CartMain from '@components/Cart/CartMain'
import CartFooter from '@components/Cart/CartFooter'

import { useCart } from '@shopify/hydrogen-react'

const compareAtPriceTotals = (lines: LineItemProp[]) => lines.reduce((acc, line) => acc + (line.merchandise.compareAtPrice?.amount || Number(line.cost.totalAmount.amount)) * line.quantity, 0)

const CartContents = () => {
  const {
    cost,
    totalQuantity,
    lines,
    status,
    discountCodes
  } = useCart()

  const amount = Number(cost?.totalAmount?.amount) || 0
  const compareAtPriceTotal = compareAtPriceTotals(lines as LineItemProp[])

  return (
    <>
      <CartMain
        lines={lines as LineItemProp[]}
        status={status}
      />
      <CartFooter
        totalQuantity={totalQuantity ?? 0}
        status={status}
        amount={amount}
        discountCodes={discountCodes as any}
        compareAtPriceTotal={compareAtPriceTotal}
      />
    </>
  )
}

export default CartContents
