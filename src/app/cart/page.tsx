import CartViewedEvent from './CartViewedEvent'

import {
  globalTokens as $,
  spacing,
} from '@/app/themeTokens.stylex'
import Container from '@components/layout/Container'
import OrderSummary from '@components/Cart/OrderSummary'
import Accordion from '@components/Generic/Accordion'
import LineItemSummary from '@components/Cart/LineItemSummary'
import CartHeaderSummary from '@components/Cart/CartHeaderSummary'
import { getAllProductsWithMetadata } from '@lib/contentful/fetchProducts'
import fetchGlobalCartFAQs from '@/lib/contentful/fetchGlobalCartFAQs'
import CartPerks from '@/components/Cart/CartPerks'

import React from 'react'
import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 1024px)'
const styles = stylex.create({
  wrapper: {
    width: '100%',
    padding: {
      default: 'initial',
      [DESKTOP]: spacing.lg,
    },
    paddingBottom: {
      default: spacing.xxl,
      [DESKTOP]: 'none',
    },
  },
  wrapperMiddle: {
    margin: '0 auto',
    maxWidth: $.maxWidth,
    flexWrap: 'nowrap',
    alignItems: {
      default: 'center',
      [DESKTOP]: 'flex-start',
    },
    flexDirection: {
      default: 'column',
      [DESKTOP]: 'row',
    },
    justifyContent: 'space-between',
    // TODO: Fix this @0-cool
    // eslint-disable-next-line @stylexjs/valid-styles
    ':only-child': {
      justifyContent: 'center',
    },
    gap: {
      default: spacing.md,
      [DESKTOP]: spacing.xl,
    }
  },
  wrapperMiddleNoItems: {
    justifyContent: 'center',
    paddingBlock: spacing.md,
  },
  wrapperItems: {
    width: '100%',
    maxWidth: '550px',
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: 'initial',
    },
  },
  mainSummary: {
    width: '100%',
    maxWidth: '550px',
    flexWrap: 'nowrap',
    backgroundColor: 'transparent',
  },
  mainAccordion: {
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: 'initial',
    },
  },
  wrapperEmptyCartState: {
    paddingInline: {
      default: spacing.md,
      [DESKTOP]: 'initial',
    },
    paddingTop: {
      default: spacing.sm,
      [DESKTOP]: spacing.md,
    },
    paddingBottom: {
      default: spacing.sm,
      [DESKTOP]: spacing.md,
    }
  },
  wrapperOrderSummary: {
    maxWidth: '550px',
    width: '100%',
  },
})

const Cart = async () => {
  const globalCartFAQs = await fetchGlobalCartFAQs()
  const productsWithAnIncludedItem = await getAllProductsWithMetadata()

  const renderQuestions = () => globalCartFAQs.map((question: any, index: number) => (
    <Accordion
      key={question.id}
      title={question.title}
      isLast={index === globalCartFAQs.length - 1}
    >
      {question.content}
    </Accordion>
  ))

  return (
    <Container
      as="section"
      styleProp={[
        styles.wrapper,
      ]}
      theme="warmGrayExtraLight"
    >
      <CartViewedEvent isCartPage />
      <CartHeaderSummary />
      <Container
        as="div"
        flex
        flexRow
        styleProp={[styles.wrapperMiddle]}
      >
        <LineItemSummary productsWithAnIncludedItem={productsWithAnIncludedItem} />
        <Container as="div" theme="beige300" flex gap="2" styleProp={[styles.mainSummary]}>
          <Container as="div" styleProp={[styles.wrapperOrderSummary]}>
            <OrderSummary />
          </Container>
          <Container as="div" styleProp={[styles.wrapperEmptyCartState]}>
            <CartPerks
              perks={[
                {
                  text: 'Free Shipping On Orders $90+',
                  media: 'free-shipping',
                },
                {
                  text: 'Free Returns',
                  media: 'returns',
                },
                {
                  text: '30-Day Trial',
                  media: 'trial',
                },
              ]}
              layout="1"
            />
          </Container>
          {globalCartFAQs && (
            <Container as="div" styleProp={[styles.mainAccordion]}>{renderQuestions()}</Container>
          )}
        </Container>
      </Container>
    </Container>
  )
}

export default Cart
