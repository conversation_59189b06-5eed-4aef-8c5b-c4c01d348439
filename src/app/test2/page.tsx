import ComparisonChartKitchenGadgets from '@/components/ReadyToUse/ComparisonChartKitchenGadgets'
import ComparisonChartKnifeSet from '@/components/ReadyToUse/ComparisonChartKnifeSet'
import ComparisonChartKnives from '@/components/ReadyToUse/ComparisonChartKnives'
import ComparisonChartStainlessSteel from '@/components/ReadyToUse/ComparisonChartStainlessSteel'
import ComparisonChartSteamer from '@/components/ReadyToUse/ComparisonChartSteamer'
import ComparisonChartTeaKettle from '@/components/ReadyToUse/ComparisonChartTeaKettle'
import ComparisonChartUtensils from '@/components/ReadyToUse/ComparisonChartUtensils'
import ComparisonChartUtensilSet from '@/components/ReadyToUse/ComparisonChartUtensilSet'
import AnchorNavigation from '@components/Content/AnchorNavigation'
import AnchorNavigationCollection from '@components/Content/AnchorNavigationCollection'
import MoreArticles from '@components/Content/MoreArticles'
import Wrapper from '@components/layout/Wrapper'
import ComparisonChartBakeware from '@components/ReadyToUse/ComparisonChartBakeware'
import ComparisonChartCookware from '@components/ReadyToUse/ComparisonChartCookware'
import ComparisonChartCuttingBoard from '@components/ReadyToUse/ComparisonChartCuttingBoard'
import ComparisonChartCuttingBoards from '@components/ReadyToUse/ComparisonChartCuttingBoards'
import ComparisonChartFoodStorage from '@components/ReadyToUse/ComparisonChartFoodStorage'
import HeroLayout9 from '@/components/Content/Hero/Layout9'
import Separator from '@/components/Content/Separator'
import Tooltips from '@/components/Product/Tooltips'
import AccordionSplit from '@/components/Product/AccordionSplit'

const accordionSplitMedia = {
  url: '/assets/Bw_Before_50.jpg',
  width: 300,
  height: 500,
}

const accordionSplitItem = {
  id: '1',
  title: 'Lorem Ipsum Dolor',
  content: 'Lorem ipsum dolor sit amet consectetur. Arcu sit ac velit ligula ultricies id tortor. Goalind hasaferd intreo desis. Lorem ipsum dolor.',
  media: accordionSplitMedia,
  mobileMedia: accordionSplitMedia,
}

const page = () => (
  <>
    <Tooltips
      image="/assets/test/bg-tooltips.jpg"
      mobileImage="/assets/test/bg-tooltips-mobile.jpg"
      hotspots={[
        {
          position: {
            desktop: {
              x: 18,
              y: 55
            },
            mobile: {
              x: 35,
              y: 25
            }
          },
          popup: {
            id: 'popup1',
            title: 'Enameled Cast Iron Braiser',
            description: 'Perfect for braising, roasting, and developing rich, flavorful dishes with even heat.',

          }
        },
        {
          position: {
            desktop: {
              x: 45,
              y: 40
            },
            mobile: {
              x: 64,
              y: 50
            }
          },
          popup: {
            id: 'popup2',
            title: 'Enameled Cast Iron Dutch Oven',
            description: 'Great for slow-cooked meals, baking bread, and simmering soups with consistent heat.',

          }
        },
        {
          position: {
            desktop: {
              x: 45,
              y: 65
            },
            mobile: {
              x: 65,
              y: 70
            }
          },
          popup: {
            id: 'popup3',
            title: 'Enameled Cast Iron Grill Pan',
            description: 'Grill indoors effortlessly, achieving perfect sear marks on meats, veggies, and more.',

          }
        },
        {
          position: {
            desktop: {
              x: 78,
              y: 65
            },
            mobile: {
              x: 65,
              y: 83
            }
          },
          popup: {
            id: 'popup4',
            title: 'Enameled Cast Iron Skillet',
            description: 'An essential for daily cooking, ideal for searing, frying, and baking with even heat.',

          }
        },
      ]}
    />
    <Separator />
    <AccordionSplit
      title="Lorem Ipsum Dolor Quantium Sequant"
      theme="gray100"
      items={[
        accordionSplitItem,
        accordionSplitItem,
        accordionSplitItem,
      ]}
    />
    <Wrapper as="div" size="xl">
      <HeroLayout9
        inverse
        subHeadline="Lorem ipsum dolor"
        header="The Best Cookware Sets"
        text="Implement a reversible layout that allows switching the position of content and image with Layout."
        typeFace="secondaryFontFamily"
        mobileMedia={{
          // items: [{ url: '/assets/timeline-slider/slide2.jpg' }]
          items: [{ url: '/assets/BW_After_50.jpg', width: 640, height: 280 }]
        }}
        media={{
          // items: [{ url: '/assets/timeline-slider/slide2.jpg', width: 640, height: 280 }]
          items: [{ url: '/assets/BW_After_50.jpg', width: 600, height: 500 }]
        }}
        buttons={[
          {
            children: 'Shop Now',
            href: '/shop',
            useArrow: true,
            variant: 'tertiary',
          },
          {
            children: 'Learn More',
            href: '/learn',
          },
        ]}
      />
      <Separator />
      <HeroLayout9
        theme="navy"
        subHeadline="Lorem ipsum dolor"
        header="The Best Cookware Sets"
        text="Implement a reversible layout that allows switching the position of content and image with Layout."
        typeFace="secondaryFontFamily"
        mobileMedia={{
          // items: [{ url: '/assets/timeline-slider/slide2.jpg' }]
          items: [{ url: '/assets/BW_After_50.jpg', width: 640, height: 280 }]
        }}
        media={{
          // items: [{ url: '/assets/timeline-slider/slide2.jpg', width: 640, height: 280 }]
          items: [{ url: '/assets/BW_After_50.jpg', width: 600, height: 500 }]
        }}
        buttons={[
          {
            children: 'Shop Now',
            href: '/shop',
            useArrow: true,
            variant: 'tertiary',
          },
          {
            children: 'Learn More',
            href: '/learn',
          },
        ]}
      />
    </Wrapper>
    <ComparisonChartBakeware />
    <ComparisonChartCuttingBoard />
    <ComparisonChartCookware />
    <ComparisonChartUtensils />
    <ComparisonChartKnives />
    <ComparisonChartCuttingBoards />
    <ComparisonChartFoodStorage />
    <ComparisonChartKitchenGadgets />
    <ComparisonChartKnifeSet />
    <ComparisonChartStainlessSteel />
    <ComparisonChartSteamer />
    <ComparisonChartTeaKettle />
    <ComparisonChartUtensilSet />
    <Wrapper as="div">
      <AnchorNavigationCollection
        slides={[
          {
            text: 'Caraway® Non-Stick',
            href: '#caraway-non-stick',
          },
          {
            text: 'PTFE Non-Stick (ex: Teflon®)',
            href: '#ptfe-non-stick-ex-teflon',

          },
        ]}
        theme="white"
      />
      <AnchorNavigation
        theme="perracotta500"
        slides={[
          {
            text: 'Caraway® Non-Stick',
            href: '#caraway-non-stick',
          },
          {
            text: 'PTFE Non-Stick (ex: Teflon®)',
            href: '#ptfe-non-stick-ex-teflon',

          },
          {
            text: 'Uncoated Stainless Steel',
            href: '#uncoated-stainless-steel',

          },
          {
            text: 'Glass, Stoneware or Porcelain',
            href: '#glass-stoneware-or-porcelain',
          },
        ]}
      />
    </Wrapper>
    <MoreArticles
      articles={[
        {
          id: '1',
          image: '/assets/slide1.jpg',
          category: 'Category 1',
          title: 'Title 1',
          url: '/blogs/title-1'
        },
        {
          id: '2',
          image: '/assets/slide2.jpg',
          category: 'Category 2',
          title: 'Title 2',
          url: '/blogs/title-2'
        },
        {
          id: '3',
          image: '/assets/slide3.jpg',
          category: 'Category 3',
          title: 'Title 3',
          url: '/blogs/title-3'
        },
      ]}
    />
  </>
)

export default page
