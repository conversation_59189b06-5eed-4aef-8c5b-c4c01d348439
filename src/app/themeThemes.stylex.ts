import {
  colors,
  defaultTheme as $T,
} from './themeTokens.stylex'

import * as stylex from '@stylexjs/stylex'

const babyBlue = stylex.createTheme($T, {
  primarySurface: colors.babyBlue,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
  secondaryCTASurface: colors.cream,
  secondaryCTAText: colors.navy,
  secondaryCTASurfaceHover: colors.cream300,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const babyBlue300 = stylex.createTheme($T, {
  primarySurface: colors.babyBlue300,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
  secondaryCTASurface: colors.marigold,
  secondaryCTAText: colors.navy,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const beige300 = stylex.createTheme($T, {
  primarySurface: colors.beige300,
  secondarySurface: colors.mist300,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
})

const black = stylex.createTheme($T, {
  primarySurface: colors.black,
  primaryText: colors.white,
  primaryCTASurface: colors.cream,
  primaryCTAText: colors.navy,
  indicatorActive: colors.white,
  indicatorInactive: colors.gray300,
})

const black300 = stylex.createTheme($T, {
  primarySurface: colors.black300,
  primaryText: colors.white,
  primaryCTASurface: colors.black,
  primaryCTAText: colors.white,
})

const burgundy = stylex.createTheme($T, {
  primarySurface: colors.burgundy,
  primaryText: colors.white,
  primaryCTASurface: colors.marigold,
  primaryCTAText: colors.navy,
  indicatorActive: colors.white,
  indicatorInactive: colors.gray300,
})

const burgundy300 = stylex.createTheme($T, {
  primarySurface: colors.burgundy300,
  primaryText: colors.cream,
  primaryCTASurface: colors.marigold,
  primaryCTAText: colors.navy,
})

const cream = stylex.createTheme($T, {
  primarySurface: colors.cream,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
  secondaryCTASurface: colors.perracotta,
  secondaryCTAText: colors.white,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const cream300 = stylex.createTheme($T, {
  primarySurface: colors.cream300,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
  secondaryCTASurface: colors.perracotta,
  secondaryCTAText: colors.white,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const emerald = stylex.createTheme($T, {
  primarySurface: colors.emerald,
  primaryText: colors.white,
  primaryCTASurface: colors.cream,
  primaryCTAText: colors.navy,
  indicatorActive: colors.white,
  indicatorInactive: colors.gray300,
})

const gray = stylex.createTheme($T, {
  primarySurface: colors.gray,
  primaryText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
})

const gray100 = stylex.createTheme($T, {
  primarySurface: colors.gray100,
  primaryText: colors.navy,
  primaryCTASurfaceHover: colors.navy300,
  secondaryText: colors.gray500,
  secondaryCTASurface: colors.white,
})

const gray200 = stylex.createTheme($T, {
  primarySurface: colors.gray200,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  secondaryText: colors.gray500,
})

const gray300 = stylex.createTheme($T, {
  primarySurface: colors.gray300,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
})

const gray500 = stylex.createTheme($T, {
  primarySurface: colors.gray500,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
})

const lavender = stylex.createTheme($T, {
  primarySurface: colors.lavender,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const lavenderLight = stylex.createTheme($T, {
  primarySurface: colors.lavenderLight,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const lavenderBurgundy = stylex.createTheme($T, {
  primarySurface: colors.lavender,
  primaryText: colors.burgundy,
  primaryCTASurface: colors.burgundy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.burgundy300,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const marigold = stylex.createTheme($T, {
  primarySurface: colors.marigold,
  primaryText: colors.black,
  primaryCTAText: colors.white,
  primaryCTASurface: colors.black,
  primaryCTASurfaceHover: colors.navy300,
})

const marigold300 = stylex.createTheme($T, {
  primarySurface: colors.marigold300,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
})

const mist = stylex.createTheme($T, {
  primarySurface: colors.mist,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const mist200 = stylex.createTheme($T, {
  primarySurface: colors.mist200,
  secondarySurface: colors.mist400,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
  primaryCTASurface: colors.navy,
  primaryCTASurfaceHover: colors.navy300,
  primaryCTAText: colors.white,
  secondaryCTASurface: colors.babyBlue,
  secondaryCTAText: colors.navy,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const mist300 = stylex.createTheme($T, {
  primarySurface: colors.mist300,
  secondarySurface: colors.mist,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
  primaryCTASurface: colors.navy,
  primaryCTASurfaceHover: colors.navy300,
  primaryCTAText: colors.white,
  secondaryCTASurface: colors.babyBlue,
  secondaryCTAText: colors.navy,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const mist600 = stylex.createTheme($T, {
  primarySurface: colors.mist600,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const navy = stylex.createTheme($T, {
  primarySurface: colors.navy,
  primaryText: colors.white,
  secondaryText: colors.cream,
  primaryCTASurface: colors.cream,
  primaryCTAText: colors.navy,
  primaryCTASurfaceHover: colors.babyBlue300,
  secondaryCTASurface: colors.navy,
  secondaryCTAText: colors.white,
  secondaryCTASurfaceHover: colors.navy300,
  secondarySurface: colors.cream,
  indicatorActive: colors.white,
  indicatorInactive: colors.gray300,
})

const navy300 = stylex.createTheme($T, {
  primarySurface: colors.navy300,
  primaryText: colors.white,
  secondaryText: colors.gray500,
  primaryCTAText: colors.white,
  primaryCTASurface: colors.navy,
  primaryCTASurfaceHover: colors.navy300,
})

const navySkyBlue = stylex.createTheme($T, {
  primarySurface: colors.navy,
  primaryText: colors.skyBlue,
  primaryCTASurface: colors.skyBlue,
  primaryCTAText: colors.navy,
  primaryCTASurfaceHover: colors.navy300,
})

const offWhite = stylex.createTheme($T, {
  primarySurface: colors.offWhite,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTASurfaceHover: colors.navy300,
  primaryCTAText: colors.offWhite,
  secondaryText: colors.gray500,
  secondaryCTASurface: colors.perracotta,
  secondaryCTAText: colors.offWhite,
})

const peach = stylex.createTheme($T, {
  primarySurface: colors.peach,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
})

const perracotta = stylex.createTheme($T, {
  primarySurface: colors.perracotta,
  primaryText: colors.white,
  secondaryText: colors.perracotta,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
})

const perracotta300 = stylex.createTheme($T, {
  primarySurface: colors.perracotta300,
  primaryText: colors.navy,
  secondaryText: colors.gray500,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
})

const perracotta500 = stylex.createTheme($T, {
  primarySurface: colors.perracotta500,
  primaryText: colors.navy,
  primaryCTASurface: colors.perracotta500,
  primaryCTASurfaceHover: colors.navy300,
})

const red700 = stylex.createTheme($T, {
  primarySurface: colors.red700,
  primaryText: colors.white,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  secondaryText: colors.red700,
})

const rust = stylex.createTheme($T, {
  primarySurface: colors.rust,
  primaryText: colors.white,
  primaryCTASurface: colors.cream,
  primaryCTAText: colors.navy,
  primaryCTASurfaceHover: colors.babyBlue300,
  secondaryCTAText: colors.white,
  secondaryCTASurface: colors.navy,
  secondaryCTASurfaceHover: colors.navy300,
})

const rustDark = stylex.createTheme($T, {
  primarySurface: colors.rustDark,
  secondarySurface: colors.cream,
  primaryText: colors.white,
  secondaryText: colors.rustDark,
  primaryCTASurface: colors.cream,
  primaryCTAText: colors.navy,
  secondaryCTASurface: colors.rustDark,
  secondaryCTAText: colors.navy,
})

const sage = stylex.createTheme($T, {
  primarySurface: colors.sage,
  primaryText: colors.cream,
  primaryCTASurface: colors.cream,
  primaryCTAText: colors.sage,
  primaryCTASurfaceHover: colors.cream300,
})

const sage300 = stylex.createTheme($T, {
  primarySurface: colors.sage300,
  primaryText: colors.white,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
})

const sage500 = stylex.createTheme($T, {
  primarySurface: colors.sage500,
  primaryText: colors.white,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
})

const skyBlue = stylex.createTheme($T, {
  primarySurface: colors.skyBlue,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
})

const skyBlueDark = stylex.createTheme($T, {
  primarySurface: colors.skyBlueDark,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
})

const slate = stylex.createTheme($T, {
  primarySurface: colors.slate,
  primaryText: colors.white,
  primaryCTASurface: colors.white,
  primaryCTAText: colors.navy,
  primaryCTASurfaceHover: colors.offWhite,
})

const slate300 = stylex.createTheme($T, {
  primarySurface: colors.slate300,
  primaryText: colors.navy,
  primaryCTASurface: colors.perracotta,
  primaryCTAText: colors.white,
})

const transparent = stylex.createTheme($T, {
  primarySurface: colors.navy,
  primaryCTASurface: colors.white
})

const violet = stylex.createTheme($T, {
  primarySurface: colors.violet,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const violetLight = stylex.createTheme($T, {
  primarySurface: colors.violetLight,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const warmBeige = stylex.createTheme($T, {
  primarySurface: colors.warmBeige,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const warmGray = stylex.createTheme($T, {
  primarySurface: colors.warmGray,
  primaryText: colors.navy,
  primaryCTASurface: colors.perracotta,
  primaryCTAText: colors.white,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const warmGray100 = stylex.createTheme($T, {
  primarySurface: colors.warmGray100,
  primaryText: colors.navy,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const warmGray300 = stylex.createTheme($T, {
  primarySurface: colors.warmGray300,
  primaryText: colors.navy,
  secondaryCTASurface: colors.navy,
  secondaryCTAText: colors.white,
  secondaryCTASurfaceHover: colors.navy300,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const warmGrayExtraLight = stylex.createTheme($T, {
  primarySurface: colors.warmGrayExtraLight,
  primaryText: colors.navy,
  indicatorActive: colors.navy,
  indicatorInactive: colors.gray200,
})

const white = stylex.createTheme($T, {
  primarySurface: colors.white,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTASurfaceHover: colors.navy300,
  primaryCTAText: colors.white,
  secondaryText: colors.gray500,
  secondaryCTASurface: colors.marigold300,
  secondaryCTASurfaceHover: colors.marigold,
  secondaryCTAText: colors.white,
})

const rose = stylex.createTheme($T, {
  primarySurface: colors.rose,
  primaryText: colors.navy,
  primaryCTASurface: colors.navy,
  primaryCTAText: colors.white,
  primaryCTASurfaceHover: colors.navy300,
})

const themes = {
  babyBlue,
  babyBlue300,
  beige300,
  black,
  black300,
  burgundy,
  burgundy300,
  cream,
  cream300,
  emerald,
  gray,
  gray100,
  gray200,
  gray300,
  gray500,
  lavender,
  lavenderLight,
  lavenderBurgundy,
  marigold,
  marigold300,
  mist,
  mist200,
  mist300,
  mist600,
  navy,
  navy300,
  navySkyBlue,
  offWhite,
  peach,
  perracotta,
  perracotta300,
  perracotta500,
  red700,
  rose,
  rust,
  rustDark,
  sage,
  sage300,
  sage500,
  skyBlue,
  skyBlueDark,
  slate,
  slate300,
  transparent,
  violet,
  violetLight,
  warmBeige,
  warmGray,
  warmGray100,
  warmGray300,
  warmGrayExtraLight,
  white
}

export type ThemeColors = keyof typeof themes;

export default themes
