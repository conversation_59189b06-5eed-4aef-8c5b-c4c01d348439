:root {
  --max-width: 1100px;
  --foreground-rgb: #FCFCF9;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: #FCFCF9;
  }
}

@font-face {
  font-family: 'Moderat';
  src: url('../assets/fonts/Moderat-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Moderat';
  src: url('../assets/fonts/Moderat-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Suisse Works';
  src: url('../assets/fonts/SuisseWorks-Medium-WebS.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Suisse Works';
  src: url('../assets/fonts/SuisseWorks-Bold-WebS.woff2') format('woff2');  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  letter-spacing: 0.025rem;
}

body:has(dialog[open]) {overflow: hidden}

html,
body {
  max-width: 100vw;
  background-color: var(--foreground-rgb);
  color: #1f3438;
  font-family: 'Moderat', Arial, Helvetica, sans-serif;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

body:has(nav#mobile-menu-open) {
  overflow: hidden;
}

html {
  scroll-behavior: smooth;
}

a {
  color: inherit;
  text-decoration: none;
}

ul {
  list-style: none;
}

button {
  background-color: transparent;
  border: none;
  margin: 0;
  padding: 0;
  text-align: inherit;
  font: inherit;
  border-radius: 0;
  cursor: pointer;
}

::-webkit-details-marker {
  display: none;
}

td:has(> p > br) {
  display: none;
}

div#dg-chat { z-index: 40 !important; }
@media screen and (max-width: 768px) {
  div#dg-chat-widget-launcher {
    bottom: 70px !important;
  }
}

#global-promo-bar:has(+ #page-promo-bar:not(:empty)) {
  display: none; 
}

#page-promo-bar > div:empty {
  display: none;
}

#global-promo-bar:has(+ #page-promo-bar > div:empty) {
  display: block;
}