import Custom404 from '../not-found'

import PageSections from '@components/PageSections'
import ProductListViewedEvent from '@components/Product/ProductListViewedEvent'
import {
  getPageBySlug,
  getPageSections,
} from '@lib/contentful/fetchPages'
import {
  FetchPageResponseProps,
} from '@lib/contentful/types'
import fetchGraphQL from '@lib/contentful/fetchGraphQL'
import Redirect, { PageData } from '@utils/redirect'
import fetchGlobalMetadata from '@lib/contentful/fetchGlobalMetadata'
import DiscountCodeRedirect from '@/components/Cart/DiscountCodeRedirect'
import { CouponRedirectSearchParams, getCouponRedirectData } from '@/utils/coupons'
import { toCamelCase } from '@/utils/regex'
import CareAndCleaning from '@/components/Content/CareAndCleaning'
import { generateMetaTagsWithFallBack } from '@/utils/metadata'
import Dialog from '@/components/Generic/Dialog'
import { extractDialogs, feedDialogsWithSection } from '@/lib/contentful/fetchGlobalDialogs'
import RenderIf from '@/utils/renderIf'
import { generateFAQSnippets } from '@/utils/jsonLd'
import JsonLd from '@/components/Generic/JsonLd'

export const dynamicParams = false

const limit = 1
const DELAY_MS = 150

const getAllPages = `
  query {
    pageCollection(limit: ${limit}, where: {slug_not: "collections"}) {
      items {
        slug
      }
    }
    redirectCollection(limit: ${limit}) {
      items {
        slug
      }
    }
  }
`

const extractEntries = (fetchResponse: FetchPageResponseProps) => {
  const pages = fetchResponse.data.pageCollection.items.map((page) => ({
    slug: page.slug.split('/'),
  }))
  const redirects = fetchResponse.data.redirectCollection.items.map((redirect) => ({
    slug: redirect.slug.split('/'),
  }))
  return [...pages, ...redirects]
}

export async function generateStaticParams() {
  const data = await fetchGraphQL(getAllPages, ['pages'])
  return extractEntries(data)
}

export type PageObjectWithSeoMetadata = {
  seoMetadata: {
    blockSearchIndexing?: boolean
    keywords?: string
    name: string
    description: string
    image: {
      url: string
    }
  }
}

export async function generateMetadata({ params }: { params: { slug: string[] } }) {
  const { slug } = params

  const [page, globalMetadata] = await Promise.all<PageObjectWithSeoMetadata>([
    getPageBySlug(slug.join('/')),
    fetchGlobalMetadata()
  ])

  return generateMetaTagsWithFallBack(page, globalMetadata)
}

type PageProps = {
  params: {
    slug: string[]
  },
  searchParams?: CouponRedirectSearchParams
}

const renderPage = (pageCategory: string, page: any, sections: any, products: any, discountCode: string, redirect: string, pageDialogs: any) => {
  const isFaqPage = () => ('slug' in page ? page.slug === 'faqs' : false)
  const richSnippets = isFaqPage() ? generateFAQSnippets(sections) : {}

  return (
    <>
      {pageCategory === 'Collection' && 'name' in page && 'slug' in page && (
        <ProductListViewedEvent
          products={products}
          title={page.name as any}
          id={page.slug}
        />
      )}
      <PageSections sections={sections} />
      <DiscountCodeRedirect code={discountCode} redirect={redirect} />
      <RenderIf condition={isFaqPage()}>
        <JsonLd data={richSnippets} />
      </RenderIf>
      {pageDialogs?.map((dialog : any) => (
        <Dialog
          key={dialog.id}
          header={dialog.header}
          subheader={dialog.subheader}
          content={dialog.content}
          mobileContent={dialog.mobileContent}
          footerContent={dialog.footerContent}
          asset={dialog.asset}
          theme={dialog.theme}
          layout={dialog.layout}
          sections={dialog.sections}
          id={dialog.id}
          compareChart={dialog?.togglesAndReferences?.compareChart}
        />
      ))}
    </>
  )
}

const getProducts = (pageCategory: string, sections: any) => {
  if (pageCategory === 'Collection') {
    const grids = sections.filter((section: { subtype: string }) => section.subtype === 'Product Grid')
    return grids.map((grid: { products: any }) => grid.products).flat()
  }
  return []
}

const handleRedirect = (page: any) => Redirect({ data: { ...page, type: 'redirect' } as PageData })

const Page = async ({ params, searchParams }: PageProps) => {
  const { slug } = params

  await new Promise((resolve) => { setTimeout(resolve, DELAY_MS) })
  const page: any = await getPageBySlug(slug.join('/'))

  const pageType = page.__typename
  const pageCategory = 'pageCategory' in page ? page.pageCategory : 'Default'

  if (pageType === 'RedirectCollection') {
    return handleRedirect(page)
  }

  if (pageType === 'NotFound') return <Custom404 />

  const sections = await getPageSections(page)
  if (toCamelCase(slug[0]) === 'careAndCleaning') {
    return <CareAndCleaning sections={sections} />
  }

  const products = getProducts(pageCategory as string, sections)
  const { discountCode, redirect } = getCouponRedirectData(searchParams, slug.join('/'))

  await feedDialogsWithSection(page.dialogs)
  const dialogs = extractDialogs((page as any).dialogs.items || [])
  return renderPage(pageCategory as string, page, sections, products, discountCode as string, redirect, dialogs)
}

export default Page
