'use client'

import { useEffect, useState } from 'react'
import { createPortal } from 'react-dom'

export default function PromoBarPortal({ promoBar }: { promoBar: React.ReactNode }) {
  const [target, setTarget] = useState<HTMLElement | null>(null)

  useEffect(() => {
    const promoBarTarget = document.getElementById('page-promo-bar')
    setTarget(promoBarTarget)
  }, [])

  if (!target) {
    return null
  }

  return createPortal(
    promoBar,
    target
  )
}
