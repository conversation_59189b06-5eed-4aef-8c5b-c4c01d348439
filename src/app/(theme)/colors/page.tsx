import ColorSwatch from './components/ColorSwatch'

import { breakpoints, colors } from '@/app/themeTokens.stylex'

import stylex from '@stylexjs/stylex'

const styles = stylex.create({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: 20,
    alignItems: 'center',
    maxWidth: breakpoints.xl,
    margin: '0 auto',
    padding: 20,
  },
  wrapper: {
    display: 'grid',
    width: '100%',
    gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
    gap: 20,
  }
})

const disallowList = [
  '__themeName__'
]

const Page = () => (
  <div
    {...stylex.props(
      styles.container
    )}
  >
    <h1>Colors</h1>
    <div
      {...stylex.props(
        styles.wrapper
      )}
    >
      {Object.keys(colors).filter((color) => !disallowList.includes(color)).map((color) => (
        <ColorSwatch key={color} color={color as keyof typeof colors} />
      ))}
    </div>
  </div>
)

export default Page
