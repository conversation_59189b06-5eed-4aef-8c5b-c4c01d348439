import { colors } from '@/app/themeTokens.stylex'

import stylex from '@stylexjs/stylex'

const ColorStyles = stylex.create({
  wrapper: {
    boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column'
  },
  base: (color: keyof typeof colors) => ({
    display: 'flex',
    flexWrap: 'wrap',
    width: '150px',
    height: '150px',
    backgroundColor: colors[color],
    borderWidth: 12,
    borderColor: 'white',
    borderStyle: 'solid',
  }),
  text: {
    fontWeight: 'bold',
    color: colors.black,
    display: 'block',
    paddingBlock: '0 15px',
    paddingInline: 25,
    alignSelf: 'flex-start',
  }
})

type ColorSwatchProps = {
  color: keyof typeof colors
}

const ColorSwatch = ({ color }: ColorSwatchProps) => (
  <div
    {...stylex.props(
      ColorStyles.wrapper
    )}
  >
    <div
      {...stylex.props(
        ColorStyles.base(color)
      )}
    />
    <span
      {...stylex.props(
        ColorStyles.text
      )}
    >
      {String(color)}
    </span>
  </div>
)

export default ColorSwatch
