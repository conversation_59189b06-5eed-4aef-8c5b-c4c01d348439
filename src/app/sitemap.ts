import fetchGraphQL from '@/lib/contentful/fetchGraphQL'

import { MetadataRoute } from 'next'

export const mainSiteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.carawayhome.com'

const limit = 250

const noIndexQuery = 'where:{ seoMetadata: { blockSearchIndexing_not: true }}'

const getAllPagesQuery = `
  query {
    pageCollection(limit: ${limit}, ${noIndexQuery}) {
      items {
        slug
      }
    }
  }
`

const getAllBlogArticles = `
  query {
    blogArticleCollection(limit: ${limit}, ${noIndexQuery}) {
      items {
        slug
      }
    }
  }
`

const getAllCollections = `
  query {
    collectionCollection(limit: ${limit}, ${noIndexQuery}) {
      items {
        slug
      }
    }
  }
`

const getAllProducts = `
  query {
    productCollection(limit: ${limit}, ${noIndexQuery}) {
      items {
        slug
      }
    }
  }
`

type PageType = {
  slug: string
}

function generateSitemapItems(
  items = [],
  buildUrl = (v: string): string => v,
  changeFrequency: 'weekly' | 'always' | 'hourly' | 'daily' | 'monthly' | 'yearly' | 'never' = 'daily',
) {
  return items.map(({ slug }: PageType) => ({
    url: buildUrl(slug),
    lastModified: new Date(),
    changeFrequency,
  }))
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const [pageData, blogData, collectionsData, productsData] = await Promise.all([
    fetchGraphQL(getAllPagesQuery, ['pages']),
    fetchGraphQL(getAllBlogArticles, ['blogArticles']),
    fetchGraphQL(getAllCollections, ['collections']),
    fetchGraphQL(getAllProducts, ['products']),
  ])

  const { data: { pageCollection: { items: pages } } } = pageData
  const { data: { blogArticleCollection: { items: blogPages } } } = blogData
  const { data: { collectionCollection: { items: collectionsPages } } } = collectionsData
  const { data: { productCollection: { items: productsPages } } } = productsData
  return [
    // Home
    {
      url: mainSiteUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
    },
    // Pages
    ...generateSitemapItems(pages, (slug) => `${mainSiteUrl}/${slug}`),
    // Blog
    ...generateSitemapItems(blogPages, (slug) => `${mainSiteUrl}/blog/${slug}`),
    // Collections
    ...generateSitemapItems(collectionsPages, (slug) => `${mainSiteUrl}/collections/${slug}`),
    ...generateSitemapItems(collectionsPages, (slug) => `${mainSiteUrl}/shop/${slug}`),
    // Products
    ...generateSitemapItems(productsPages, (slug) => `${mainSiteUrl}/products/${slug}`),
  ]
}
