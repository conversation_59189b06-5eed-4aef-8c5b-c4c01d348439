'use client'

import {
  storeDomain, publicStorefrontToken, storefrontApiVersion
} from '@/config/storefront'

import { ShopifyProvider } from '@shopify/hydrogen-react'

export default function Provider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ShopifyProvider
      storeDomain={storeDomain}
      storefrontToken={publicStorefrontToken}
      storefrontApiVersion={storefrontApiVersion}
      countryIsoCode="US"
      languageIsoCode="EN"
    >
      {children}
    </ShopifyProvider>
  )
}
