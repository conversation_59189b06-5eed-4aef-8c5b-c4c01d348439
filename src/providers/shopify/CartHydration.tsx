import { updateCart } from '@redux/features/cart/cartSlice'
import { useAppDispatch } from '@redux/hooks'

import { useEffect } from 'react'
import {
  useCart,
  parseGid
} from '@shopify/hydrogen-react'

import type {
  CartLine,
  CartCost,
} from '@shopify/hydrogen-react/storefront-api-types'

const hydrateCart = ({
  lines,
  cost,
  id,
  dispatch,
}: {
  lines: CartLine[] | any
  cost: CartCost | any
  id: string | undefined
  dispatch: ReturnType<typeof useAppDispatch>;
}) => {
  const value = Number(cost?.totalAmount?.amount) || 0
  const currency = cost?.totalAmount?.currencyCode
  const cartId = parseGid(id)?.id || ''
  const products = lines.map((line: CartLine) => {
    const {
      merchandise: {
        product: {
          title: productName,
          handle: productSlug,
          id: productId
        },
        title: variantName,
        id: variantId,
        image,
        selectedOptions
      },
      cost: {
        totalAmount: { amount, currencyCode }
      },
      quantity,
      id: lineItemId
    } = line

    return {
      amount: Number(amount),
      currency: currencyCode,
      imageUrl: image?.url,
      lineItemId: parseGid(lineItemId).id,
      name: productName,
      options: selectedOptions?.map(
        (option) => `${option.name} - ${option.value}`,
      ),
      productId: parseGid(productId).id,
      quantity,
      slug: productSlug,
      variant: variantName,
      variantId: parseGid(variantId).id
    }
  })

  dispatch(updateCart({
    cartId,
    currency,
    value,
    products
  }))
}

const CartHydration = () => {
  const dispatch = useAppDispatch()

  const {
    status,
    id,
    lines,
    cost
  } = useCart()

  useEffect(() => {
    const isCartInitialized = status !== 'uninitialized'
    if (isCartInitialized) {
      hydrateCart({
        lines,
        cost,
        id,
        dispatch
      })
    }
  }, [status, id, lines, cost, dispatch])

  return null
}

export default CartHydration
