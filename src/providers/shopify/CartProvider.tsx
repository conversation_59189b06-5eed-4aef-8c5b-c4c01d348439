'use client'

import { open } from '@redux/features/cart/cartSlice'
import CartHydration from '@providers/shopify/CartHydration'
import { useAppDispatch } from '@redux/hooks'

import { CartProvider } from '@shopify/hydrogen-react'

export default function App({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const dispatch = useAppDispatch()

  return (
    <CartProvider
      onLineAdd={() => {
        dispatch(open())
      }}
    >
      <CartHydration />
      {children}
    </CartProvider>
  )
}
