'use client'

import { empty } from '@/utils/checking'
import { addLocalStorageData, getLocalStorageData } from '@/utils/localStorage'
import { generateUserRandomId } from '@/utils/growthbook'
import { chord } from '@lib/chord/events'

import { useEffect } from 'react'
import { GrowthBook, GrowthBookProvider } from '@growthbook/growthbook-react'

const LOCAL_STORAGE_KEY = '_caraway_anonymous_id'

type VariantsToTrigger = {
  experimentId: string;
  variationId: string;
}

export function checkUrlForGbExperimentVariation(): VariantsToTrigger[] {
  if (typeof window === 'undefined') {
    return []
  }

  const urlParams = new URLSearchParams(window.location.search)

  return Array.from(urlParams.entries()).reduce(
    (acc, [key, value]) => {
      if (key.includes('gb')) {
        const experimentId = key.replace('gb-', '')
        const variationId = value
        acc.push({ experimentId, variationId })
      }
      return acc
    },
    [] as VariantsToTrigger[]
  )
}

export function findUrlExperimentById(experimentId: string): VariantsToTrigger | undefined {
  return checkUrlForGbExperimentVariation().find((experiment) => experiment.experimentId === experimentId)
}

// Create a GrowthBook instance
const gb = new GrowthBook({

  apiHost: process.env.NEXT_PUBLIC_GROWTHBOOK_API_HOST,
  clientKey: process.env.NEXT_PUBLIC_GROWTHBOOK_CLIENT_KEY,
  enableDevMode: process.env.NODE_ENV.toLocaleLowerCase() !== 'production',

  trackingCallback: (experiment, result) => {
    console.log('experiment', experiment)

    chord.track('Experiment Viewed', {
      experimentId: experiment.key,
      variationId: result.key
    })
  },
})
gb.init({ streaming: true })

const GBProvider = ({ children }:{ children: React.ReactNode }) => {
  useEffect(() => {
    // CHECK IF There is any anonymousId in the cookie
    const userID = getLocalStorageData(LOCAL_STORAGE_KEY)

    if (empty(userID)) {
      // generate randomString 32 chars
      const newUserRandomId = generateUserRandomId()

      addLocalStorageData(LOCAL_STORAGE_KEY, newUserRandomId)
      gb.setAttributes({ id: newUserRandomId })
    } else {
      gb.setAttributes({ id: userID })
    }
  }, [])

  return (
    <GrowthBookProvider growthbook={gb}>
      { children }
    </GrowthBookProvider>
  )
}

export default GBProvider
