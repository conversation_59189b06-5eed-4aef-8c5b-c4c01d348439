'use client'

import React, {
  createContext, useContext, useRef, useCallback, useMemo, ReactNode,
  useState
} from 'react'

type DialogContextType = {
  registerDialog: (id: string, dialogRef: React.RefObject<HTMLDialogElement>) => void
  triggerDialog: (id: string) => void
  closeModal: (id: string) => void
  openModals: Record<string, boolean>
};

const DialogContext = createContext<DialogContextType | undefined>(undefined)

export const useDialogContext = (): DialogContextType => {
  const context = useContext(DialogContext)
  if (!context) {
    throw new Error('useDialogContext must be used within a DialogProvider')
  }
  return context
}

export const DialogProvider = ({ children }: { children: ReactNode }) => {
  const dialogRefs = useRef<Record<string, React.RefObject<HTMLDialogElement>>>({})
  const [openModals, setOpenModals] = useState({})

  const markDialogAsOpen = (id: string) => {
    setOpenModals((prev) => ({ ...prev, [id]: true }))
  }

  const markDialogAsClosed = (id: string) => {
    setOpenModals((prev) => ({ ...prev, [id]: false }))
  }

  const registerDialog = useCallback((id: string, dialogRef: React.RefObject<HTMLDialogElement>) => {
    dialogRefs.current[id] = dialogRef
    markDialogAsClosed(id)
  }, [])

  const closeModal = useCallback((id: string) => {
    const dialog = dialogRefs.current[id]?.current
    if (dialog) {
      dialog.close()
      markDialogAsClosed(id)
    }
  }, [])

  const triggerDialog = useCallback((id: string) => {
    const dialog = dialogRefs.current[id]?.current
    if (!dialog) return

    if (dialog.hasAttribute('open')) {
      closeModal(id)
      markDialogAsClosed(id)
    } else {
      dialog.showModal()
      markDialogAsOpen(id)
    }
  }, [closeModal])

  return useMemo(
    () => (
      <DialogContext.Provider
        value={{
          registerDialog,
          triggerDialog,
          closeModal,
          openModals,
        }}
      >
        {children}
      </DialogContext.Provider>
    ),
    [
      registerDialog,
      triggerDialog,
      closeModal,
      children,
      openModals,
    ]
  )
}
