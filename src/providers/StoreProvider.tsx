import GBProvider from './GrowthBook'

import ShopifyProvider from '@providers/shopify/ShopifyProvider'
import CartProvider from '@providers/shopify/CartProvider'
import ReduxProvider from '@providers/redux/ReduxStoreProvider'
import { DialogProvider } from '@providers/config/DialogContext'
import ConfettiProvider from '@/components/Confetti'

export default function Providers({ children } : { children: React.ReactNode }) {
  return (
    <ShopifyProvider>
      <ReduxProvider>
        <CartProvider>
          <DialogProvider>
            <GBProvider>
              <ConfettiProvider>
                {children}
              </ConfettiProvider>
            </GBProvider>
          </DialogProvider>
        </CartProvider>
      </ReduxProvider>
    </ShopifyProvider>
  )
}
