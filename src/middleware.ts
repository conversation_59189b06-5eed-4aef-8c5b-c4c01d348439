import { CouponRedirectSearchParams, getCouponRedirectData } from './utils/coupons'

import { NextResponse } from 'next/server'

import type { NextRequest } from 'next/server'

// Skip these paths:
// 1. Static files (e.g. .png, .jpg, .js, .css)
// 2. Next.js internals
// 3. API routes
const isExternalRequest = (pathname: string) => {
  const isStaticFile = !!pathname.match(/\.(?:ico|jpg|jpeg|png|gif|webp|svg|css|js|json|txt|woff2?|ttf|eot|otf|map)$/i)
  const isNextInternal = pathname.startsWith('/_next') || pathname.startsWith('/_vercel') || pathname.startsWith('/_static')
  const isApiRoute = pathname.startsWith('/api')
  return isStaticFile || isNextInternal || isApiRoute
}

export default function middleware(request: NextRequest) {
  const MOVED_PERMANENTLY = 301
  const { nextUrl: url } = request
  const { pathname, search } = url
  const params = new URLSearchParams(search)
  const lowercasePath = pathname.toLowerCase()
  const couponParams: CouponRedirectSearchParams = {
    promo: params.get('promo')!,
    discount: params.get('discount')!,
    redirect: params.get('redirect') || '/',
  }

  const { discountCode, redirect } = getCouponRedirectData(couponParams)

  // For now this only handles home page redirects
  if (pathname === '/' && discountCode && redirect) {
    return NextResponse.redirect(
      new URL(`/discount/${discountCode}?redirect=${redirect}`, url)
    )
  }
  if (isExternalRequest(pathname)) {
    return NextResponse.next()
  }
  // Redirect to lowercase path if the pathname is not already lowercase
  // This is a workaround for the fact that Next.js does not handle lowercase paths
  if (pathname !== lowercasePath) {
    url.pathname = lowercasePath
    return NextResponse.redirect(url, MOVED_PERMANENTLY)
  }
  return null
}
