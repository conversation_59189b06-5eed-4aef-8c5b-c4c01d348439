import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'

export const transformQueryToIndex = (products: any[] = []) => products.map((product: any) => {
  const shortDescription = product.productMetadataCollection?.items?.find(
    (item: any) => item.type === 'Card Short Description',
  )

  const hoverImage = product.productMetadataCollection?.items?.find(
    (item: any) => item.type === 'Card Hover Image',
  )

  const callouts = product.calloutsCollection?.items?.map(({ title, settings }: any) => ({
    title,
    theme: settings.theme
  })) || []

  return {
    id: product.sys.id,
    slug: product.slug,
    title: product.title,
    price: product.variantsCollection.items[0].price,
    compareAtPrice: product.variantsCollection.items[0].compareAtPrice,
    regularPriceOverride: product.variantsCollection.items[0].regularPriceOverride ?? null,
    onSale: product.variantsCollection.items[0].onSale,
    primaryImage: product.variantsCollection.items[0].primaryImage,
    hoverImage: hoverImage?.mediaCollection?.items[0] || null,
    keywords: product.seoMetadata.keywords,
    description: product.seoMetadata.description,
    blockSearchIndexing: Boolean(product.seoMetadata.blockSearchIndexing),
    shortDescription: shortDescription?.description && documentToPlainTextString(
      shortDescription?.description?.json,
    ),
    callouts,
  }
})

export default transformQueryToIndex
