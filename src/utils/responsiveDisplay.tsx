import React from 'react'
import * as stylex from '@stylexjs/stylex'

const styles = stylex.create({
  mobileOnly: {
    display: {
      default: 'inline',
      '@media (min-width: 1024px)': 'none',
    },
  },
  desktopOnly: {
    display: {
      default: 'none',
      '@media (min-width: 1024px)': 'inline',
    },
  },
})

const MobileOnly = ({ children }: { children: React.ReactNode }) => <span {...stylex.props(styles.mobileOnly)}>{children}</span>

const DesktopOnly = ({ children }: { children: React.ReactNode }) => <span {...stylex.props(styles.desktopOnly)}>{children}</span>

export { MobileOnly, DesktopOnly }
