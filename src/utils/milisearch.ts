import getCookie from './getCookie'

// HTTPS wrapper function
const httpsRequest = async (
  url: string,
  method: string,
  headers: {},
  data: {}
) => {
  try {
    const response = await fetch(url, {
      method,
      headers,
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('Error in HTTPS request:', error)
    throw error
  }
}

type MLEvent = {
  eventType: string;
  eventName: string;
  indexUid: string;
  objectId: string;
  position: number;
}

// Wrapper to call the specific CURL
const sendMLEvent = async ({
  eventType,
  eventName,
  indexUid,
  objectId,
  position,
}: MLEvent) => {
  const authorization = process.env.NEXT_PUBLIC_MEILISEARCH_API_KEY

  if (!authorization) {
    throw new Error('MeiliSearch API key not found, make sure to set it in the environment variables to keep tracking events.')
  }

  const url = 'https://edge.meilisearch.com/events'
  const method = 'POST'
  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${authorization}`,
    'X-MS-USER-ID': getCookie('ajs_anonymous_id'),
  }

  const data = {
    eventType,
    eventName,
    indexUid,
    objectId,
    position,
  }

  try {
    return await httpsRequest(url, method, headers, data)
  } catch (error) {
    console.error('Failed to send event:', error)
    throw error
  }
}

type MlTrackPayload = {
  objectId: string;
  position: number;
}

export const trackMlClickEvent = ({ objectId, position }: MlTrackPayload) => {
  sendMLEvent({
    eventType: 'click',
    eventName: 'Search Result Clicked',
    indexUid: process.env.NEXT_PUBLIC_MEILISEARCH_INDEX || '',
    objectId,
    position,
  })
}

export const trackMlAddToCartEvent = ({ objectId, position }: MlTrackPayload) => {
  sendMLEvent({
    eventType: 'conversion',
    eventName: 'Product Added To Cart',
    indexUid: process.env.NEXT_PUBLIC_MEILISEARCH_INDEX || '',
    objectId,
    position,
  })
}

export default sendMLEvent
