const generateSlug = (slug: string, type: string) => {
  if (!slug) {
    return ''
  }
  if (type === 'Page') {
    return `/${slug}`
  }
  if (type === 'Product') {
    return `/products/${slug}`
  }
  if (type === 'Collection') {
    return `/collections/${slug}`
  }
  if (type === 'Blog Article' || type === 'BlogArticle') {
    return `/blog/${slug}`
  }
  return `/${slug}`
}

export default generateSlug
