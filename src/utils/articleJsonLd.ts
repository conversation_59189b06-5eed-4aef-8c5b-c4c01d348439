import { documentToPlainTextString } from '@contentful/rich-text-plain-text-renderer'
import { Block, Inline } from '@contentful/rich-text-types'

export type ArticleType = {
  seoMetadata: {
    name: string
    image: {
      url: string
    }
  }
  datePublished: string
  authorCollection: {
    items: {
      name: string
    }[]
  }
}

export function generateArticleJsonLd(article: ArticleType, sections: any) {
  const name = article.seoMetadata?.name
  const articleBody = sections.map((section: { content: { json: Block | Inline; }; }) => documentToPlainTextString(section.content.json)).join(' ')
  const image = article.seoMetadata?.image?.url
  const { datePublished } = article

  const authors = article.authorCollection.items.length > 0
    ? article.authorCollection.items.map((author) => ({
      '@type': 'Person',
      name: author.name
    }))
    : [{
      '@type': 'Organization',
      name: 'Caraway Home'
    }]

  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    name,
    headline: name,
    description: articleBody.seoMetadata?.description,
    image: [image],
    datePublished,
    dateCreated: datePublished,
    author: authors,
    articleBody,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': '/'
    },
    publisher: {
      '@type': 'Organization',
      name: 'Caraway Home',
      url: '/'
    },
  }
}
