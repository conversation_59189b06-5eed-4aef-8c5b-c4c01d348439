import { colors, spacing } from '@/app/themeTokens.stylex'
import Typography from '@/components/Typography'

import * as stylex from '@stylexjs/stylex'

const DESKTOP = '@media (min-width: 520px)'

const styles = stylex.create({
  table: {
    width: 'inherit',
    padding: {
      default: 0,
      [DESKTOP]: spacing.md
    },
    marginBlock: '8px',
    marginBlockEnd: {
      default: spacing.sm,
      [DESKTOP]: 0
    },
    borderCollapse: 'collapse',
  },
  row: {
    display: 'flex',
    flexWrap: 'wrap',
    width: {
      default: 'fit-content',
      [DESKTOP]: '100%'
    },
  },
  header: {
    padding: '16px 10px',
    borderBottomWidth: {
      default: '0',
      [DESKTOP]: '1px'
    },
    borderBottomStyle: {
      default: 'none',
      [DESKTOP]: 'solid'
    },
    borderBottomColor: {
      default: 'transparent',
      [DESKTOP]: colors.gray300
    },
  },
  cell: {
    flexWrap: 'nowrap',
    display: {
      default: 'block',
      '@media(min-width: 375px)': 'table-cell',
    },
    minWidth: {
      default: '145px',
      '@media(min-width: 520px) and (max-width: 719px)': 'unset',
      '@media(min-width: 720px)': '130px'
    },
    padding: {
      default: '0',
      [DESKTOP]: spacing.sm
    },
  },
  bodyCopy: {
    textAlign: {
      default: 'left',
      [DESKTOP]: 'center'
    },
    borderBottomWidth: {
      default: '0',
      [DESKTOP]: '1px'
    },
    borderBottomStyle: {
      default: 'none',
      [DESKTOP]: 'solid'
    },
    borderBottomColor: {
      default: 'transparent',
      [DESKTOP]: colors.gray300
    },
  },
  columnHeader: {
    borderBottomWidth: {
      default: '0',
      [DESKTOP]: '1px'
    },
    borderBottomStyle: {
      default: 'none',
      [DESKTOP]: 'solid'
    },
    borderBottomColor: {
      default: 'transparent',
      [DESKTOP]: colors.gray300
    },
  },
  data: {
    textDecoration: 'none',
  },
  storageCell: {
    display: {
      default: 'block',
      '@media(min-width: 375px)': 'table-cell',
    },
    paddingTop: {
      default: spacing.xs,
      [DESKTOP]: '20px'
    },
    paddingBottom: {
      default: '16px',
      [DESKTOP]: spacing.xs
    },
    minWidth: {
      default: 'unset',
      [DESKTOP]: '115px',
    },
  },
  storageHeader: {
    borderBottomWidth: 0,
    borderBottomStyle: 'none',
    borderBottomColor: 'transparent',
  }
})

export const Table = (settings: any, children: any) => {
  const id = settings?.id

  return (
    <table
      {...stylex.props([
        styles.table,
        id && id === 'storage' && styles.storageHeader
      ])}
    >
      <tbody>
        {children}
      </tbody>
    </table>
  )
}
export const TableHeader = (node: any, children: any) => {
  const [child] = children

  if (!child || !child?.props) {
    return null
  }

  return (
    <th {...stylex.props(styles.header)}>
      <Typography as="span" typographyTheme="bodyLarge">
        {child.props.children}
      </Typography>
    </th>
  )
}

/* eslint-disable complexity */
export const TableCell = (settings: any, children: any) => {
  const [child] = children

  if (!child || !child?.props) {
    return null
  }

  const id = settings?.id
  const headerCell = child.props.children[0]?.props?.as === 'strong'

  return (
    <td
      {...stylex.props([
        styles.cell,
        !headerCell && styles.bodyCopy,
        headerCell && styles.columnHeader,
        id && id === 'storage' && !headerCell && styles.storageCell,
        id && id === 'storage' && headerCell && styles.storageHeader,
      ])}
    >
      <Typography
        as="span"
        typographyTheme="bodyLarge"
        styleProp={styles.data}
      >
        {child.props.children}
      </Typography>
    </td>
  )
}
