import {
  Bold, Code, EntryLink, Heading, InlineLink, Italic, Li, Ol, Text, Ul, Media
} from './elements'
import { Table, TableCell, TableHeader } from './tableElements'

import { documentToReactComponents } from '@contentful/rich-text-react-renderer'
import {
  BLOCKS, Document, MARKS, INLINES
} from '@contentful/rich-text-types'

const RichTextRenderer = ({
  content,
  links,
  settings,
  config
}: {
  content: Document,
  links?: any,
  settings?: any,
  config?: {
    skip: string[]
  }
}) => {
  const options = {
    renderMark: {
      [MARKS.CODE]: (children: any) => Code(children),
      [MARKS.BOLD]: (children: any) => Bold(children),
      [MARKS.ITALIC]: (children: any) => Italic(children),
    },
    renderNode: {
      [BLOCKS.OL_LIST]: Ol,
      [BLOCKS.UL_LIST]: Ul,
      [BLOCKS.LIST_ITEM]: Li,
      [BLOCKS.PARAGRAPH]: (node: any, children: any) => Text(settings, children),
      [INLINES.HYPERLINK]: (data: any, children: any) => InlineLink(data, children, settings),
      [INLINES.ENTRY_HYPERLINK]: EntryLink,
      [BLOCKS.HEADING_1]: Heading('h1', settings),
      [BLOCKS.HEADING_2]: Heading('h2', settings),
      [BLOCKS.HEADING_3]: Heading('h3', settings),
      [BLOCKS.HEADING_4]: Heading('h4', settings),
      [BLOCKS.HEADING_5]: Heading('h5', settings),
      [BLOCKS.HEADING_6]: Heading('h6', settings),
      [BLOCKS.TABLE]: (node: any, children: any) => Table(settings, children),
      [BLOCKS.TABLE_HEADER_CELL]: (node: any, children: any) => TableHeader(settings, children),
      [BLOCKS.TABLE_CELL]: (node: any, children: any) => TableCell(settings, children),
      [BLOCKS.EMBEDDED_ASSET]: (node: any) => {
        const { data: { target: { sys: { id } } } } = node
        const media = links.assets.block.find((asset: any) => asset.sys.id === id)
        if (media) {
          return <Media title={media.title} url={media.url} />
        }
        return null
      }
    },
  }

  // Skip rendering of certain node types if specified
  if (config?.skip) {
    config.skip.forEach((type: string) => {
      if ((options.renderNode as any)[type]) {
        (options.renderNode as any)[type] = () => null
      }
    })
  }

  return documentToReactComponents(content, options)
}

export default RichTextRenderer
