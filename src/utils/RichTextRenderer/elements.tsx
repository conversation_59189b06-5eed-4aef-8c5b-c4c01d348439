import generateSlug from '@utils/generateSlug'
import { colors, fontSizes, spacing } from '@/app/themeTokens.stylex'
import Typography from '@components/Typography'
import CallToAction from '@components/Generic/CallToAction'
import getEntrySlugById from '@/lib/contentful/fetchEntry'
import { TypographyThemes } from '@/app/typographyThemes.stylex'
import DialogTrigger from '@components/Generic/Dialog/DialogTrigger'

import * as stylex from '@stylexjs/stylex'
import React from 'react'
import Link from 'next/link'
import Image from 'next/image'

const styles = stylex.create({
  ul: {
    listStyleType: 'disc',
    paddingLeft: spacing.md,
  },
  ol: {
    listStyleType: 'decimal',
    paddingLeft: spacing.md,
    marginBottom: spacing.md,
  },
  olLi: {
    marginBottom: spacing.xxs,
    paddingLeft: spacing.sm,
  },
  ulLi: {
    paddingLeft: spacing.sm,
    '::marker': {
      fontSize: '8px',
    },
  },
  link: {
    color: colors.perracotta500,
  },
  fontSizeBL: {
    fontSize: fontSizes.bodyLarge
  },
  text: {
    color: colors.black,
  },
})

type HeadingType = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

function isChildNewLine(children: string | Promise<any>): boolean {
  if (children instanceof Promise) {
    return false
  }
  return typeof children === 'string' && children.trim() === ''
}

function formatChildren(children: any, render: (child: any) => any) {
  if (!children) {
    return null
  }

  if (!Array.isArray(children)) {
    if (isChildNewLine(children)) {
      return <br />
    }

    return render(children)
  }

  return children.map((child: any, idx) => {
    if (isChildNewLine(child)) {
      return <br key={`br-newline-${String(idx)}`} />
    }

    if (typeof child === 'string' && child.includes('\n')) {
      return child.split('\n').flatMap((part, index, arr) => {
        if (index < arr.length - 1) {
          return [part, <br key={`br-${part}-${child}-${String(idx)}-${String(index)}`} />]
        }
        return part
      })
    }

    return render(child)
  })
}

// Marks
export const Bold = (children: any) => formatChildren(children, (child: any) => (
  <Typography as="strong">{child}</Typography>
))

export const Italic = (children: any) => formatChildren(children, (child: any) => (
  <Typography as="em">{child}</Typography>
))

export const Code = (children: any) => formatChildren(children, (child: any) => (
  <Typography as="code">{child}</Typography>
))

// Img Renderer
interface MediaProps {
  title: string;
  url: string;
}

export const Media = ({ title, url }: MediaProps) => (
  <Image
    src={url}
    alt={title || ''}
    width={800}
    height={400}
    layout="responsive"
    quality={80}
  />
)

// Nodes
export const Ul = (_: any, children: any) => (
  <ul {...stylex.props(styles.ul)}>
    {React.Children.map(children, (child: any) => (
      <li {...stylex.props(styles.ulLi)}>{child}</li>
    ))}
  </ul>
)

export const Ol = (_: any, children: any) => (
  <ol {...stylex.props(styles.ol)}>
    {React.Children.map(children, (child: any) => (
      <li {...stylex.props(styles.olLi)}>{child}</li>
    ))}
  </ol>
)

export const Li = (_: any, children: any) => children

export const Text = (_: any, children: any) => {
  const {
    fontFamily,
    typographyTheme,
    typographyThemeMobile,
    textCenter,
    styleProp
  } = _ || {}
  const fontSecondary = fontFamily === 'Secondary (Suisse Works, sans-serif)'
  const theme = typographyTheme || 'bodyLarge'
  const textCentered = textCenter || false

  return (
    <Typography
      as="p"
      typographyTheme={theme}
      typographyThemeMobile={typographyThemeMobile}
      fontSecondary={fontSecondary}
      textCentered={textCentered}
      styleProp={styleProp}
    >
      {formatChildren(children, (child: any) => child)}
    </Typography>
  )
}

type Sizes = Record<HeadingType, TypographyThemes>

export const Heading = (type: HeadingType, settings?: any) => (_: any, children: any) => {
  const { typographyTheme } = settings || {}

  const sizes: Sizes = {
    h1: 'h1Secondary',
    // ATTENTION: h2 is currently being used for blog article headers.
    h2: 'h6Secondary',
    h3: 'h3Secondary',
    h4: 'h4Secondary',
    h5: 'h5Secondary',
    h6: 'h6Secondary',
  }

  return (
    <Typography
      typographyTheme={typographyTheme || sizes[type]}
      as={type}
    >
      {children}
    </Typography>
  )
}

export const InlineLink = ({ data }: any, children: any, settings?: any) => {
  const { uri } = data

  const styleProp = settings || styles.fontSizeBL
  return (
    <CallToAction
      size="inline"
      variant="underlined"
      href={uri}
      styleProp={styleProp}
    >
      {formatChildren(children, (child: any) => child)}
    </CallToAction>
  )
}

export const EntryLink = async ({ data }: any, children: any) => {
  const info = await getEntrySlugById(data.target.sys.id)
  const { items } = info.data.entryCollection
  const uri = generateSlug(items[0].slug, items[0].__typename)

  if (items[0].__typename === 'BlockDialog') {
    return (
      <DialogTrigger
        id={data.target.sys.id}
        text={children}
        variant="underlined"
        size="inline"
      />
    )
  }

  return (
    <Link
      href={uri}
      style={{ textDecoration: 'underline' }}
    >
      {children}
    </Link>
  )
}
