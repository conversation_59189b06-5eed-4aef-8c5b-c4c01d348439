import { condition, notEmpty } from './checking'
import { oneDayInTimeStamp } from './date'
import { getProductUrl } from './urls'

const tierList: { threshold: number, discount: number }[] = [
  { threshold: 85, discount: 0.1 },
  { threshold: 425, discount: 0.15 },
  { threshold: 525, discount: 0.2 }
]

const useTiers = false
const DECIMAL_PLACES = 2

type ReviewAggregate = {
  rating: number,
  count: number,
} | null | undefined

type ReviewPayload = {
  latest: {
    name: string,
    body: string,
    rating: number,
    datePublished?: string
  }[]
  aggregate: ReviewAggregate
}

type VariantType = {
  sku: string,
  gtin8: string,
  price: string,
  compareAtPrice: string,
  swatch: {
    swatchType: string,
    slug: string
  }
}

type ProductPayload = {
  title: string,
  slug: string,
  seoMetadata: {
    description: string,
    image: {
      url: string
    }
  },
  variants: {
    items: VariantType[]
  }
}

function generateOffers({
  variants = [] as VariantType[],
  priceValidUntil = '',
  baseUrl = ''
}) {
  return variants.map((variant: VariantType) => {
    const {
      sku,
      gtin8,
      price,
      compareAtPrice,
      swatch,
    } = variant

    // Construct variant parameter with the swatch information
    const variantParams = { [swatch.swatchType]: swatch.slug }

    // Extract the slug from the baseUrl
    const slug = baseUrl.split('/').pop() || ''

    // Generate the new URL format with the variant in the path
    const url = getProductUrl(slug, variantParams)

    const displayPrice = (() => {
      const priceNumber = parseFloat(price)
      const tier = tierList.find((t) => priceNumber >= t.threshold)
      return useTiers && tier ? (priceNumber * (1 - tier.discount)).toFixed(DECIMAL_PLACES) : price
    })()

    const displayCompareAtPrice = (() => {
      if (compareAtPrice) {
        return compareAtPrice
      }
      return price
    })()

    return {
      '@type': 'Offer',
      availability: 'https://schema.org/InStock',
      itemCondition: 'https://schema.org/NewCondition',
      url,
      sku,
      gtin8,
      price: displayPrice,
      priceCurrency: 'USD',
      priceValidUntil,
      priceSpecification: {
        '@type': 'UnitPriceSpecification',
        price: displayCompareAtPrice,
        priceCurrency: 'USD',
        valueAddedTaxIncluded: false
      },
      shippingDetails: {
        '@type': 'OfferShippingDetails',
        shippingRate: {
          '@type': 'MonetaryAmount',
          currency: 'USD',
          value: 0
        },
        shippingDestination: {
          '@type': 'DefinedRegion',
          addressCountry: 'US'
        },
        deliveryTime: {
          '@type': 'ShippingDeliveryTime',
          handlingTime: {
            '@type': 'QuantitativeValue',
            minValue: 0,
            maxValue: 1,
            unitCode: 'DAY'
          },
          transitTime: {
            '@type': 'QuantitativeValue',
            minValue: 1,
            maxValue: 5,
            unitCode: 'DAY'
          }
        }
      },
      hasMerchantReturnPolicy: {
        '@type': 'MerchantReturnPolicy',
        applicableCountry: 'US',
        returnPolicyCategory: 'https://schema.org/MerchantReturnFiniteReturnWindow',
        merchantReturnDays: 30,
        returnMethod: 'https://schema.org/ReturnByMail',
        returnFees: 'https://schema.org/FreeReturn'
      }
    }
  })
}

function generateReview({ latest = [] }: ReviewPayload) {
  return latest.map(
    ({
      name,
      datePublished,
      body,
      rating
    }) => (
      {
        '@type': 'Review',
        reviewBody: body,
        datePublished,
        reviewRating: {
          '@type': 'Rating',
          ratingValue: rating,
          bestRating: 5
        },
        author: {
          '@type': 'Person',
          name
        }
      }
    )
  )
}

export function generateProductRichSnippet(
  product: ProductPayload,
  variant: VariantType,
  reviews: ReviewPayload
) {
  const base = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    brand: {
      '@type': 'Brand',
      name: 'Caraway'
    }
  }
  const url = getProductUrl(product.slug)
  const variants = product.variants.items
  const priceValidUntil = new Date(new Date().getTime() + oneDayInTimeStamp)

  const offers = generateOffers({
    variants,
    priceValidUntil: priceValidUntil.toISOString(),
    baseUrl: url
  })

  const RATING_DECIMALS = 2

  const review = generateReview(reviews)
  const image = condition<string>(notEmpty(product.seoMetadata.image?.url), [product.seoMetadata.image?.url ?? ''])
  const aggregateRating = condition<{}>(
    notEmpty(reviews.aggregate?.rating) &&
    notEmpty(reviews.aggregate?.count),
    {
      ratingValue: reviews.aggregate?.rating ? reviews.aggregate.rating.toFixed(RATING_DECIMALS) : undefined,
      reviewCount: reviews.aggregate?.count
    },
    null
  )
  const { sku, gtin8 } = variant

  const result = {
    ...base,
    image,
    name: product.title,
    description: product.seoMetadata.description,
    url,
    gtin8,
    sku,
    offers,
    review,
    aggregateRating
  }

  return result
}

type Section = {
  subtype: string;
  slides: FaqSnippet[];
}

type FaqSnippet = {
  id: string;
  question: string;
  answer: string;
  answerPlainText: string;
}

type JsonLdQuestion = {
  '@type': 'Question',
  name: string,
  acceptedAnswer: {
    '@type': 'Answer',
    text: string
  }
}

export function generateFAQSnippets(sections: Section[] = []) {
  const questions = sections.reduce((acc, section) => {
    const { subtype, slides } = section
    if (subtype === 'Faq') {
      const questionSnippets = slides?.map(({ question, answerPlainText }: FaqSnippet) => ({
        '@type': 'Question',
        name: question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: answerPlainText,
        }
      }))

      acc = [
        ...acc,
        ...(questionSnippets as JsonLdQuestion[])
      ]
    }

    return acc
  }, [] as JsonLdQuestion[])

  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: questions
  }
}

export default generateProductRichSnippet
