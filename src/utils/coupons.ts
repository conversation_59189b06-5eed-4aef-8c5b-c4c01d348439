export const SITEWIDE_DISCOUNT_CODE = 'holiday24'

export type CouponRedirectSearchParams = {
  promo?: string
  discount?: string
  redirect?: string
}

export function getCouponRedirectData(searchParams?: CouponRedirectSearchParams, pageSlug?: string) {
  const {
    promo,
    discount,
    redirect: rawRedirect = '/',
  } = searchParams || {}

  const discountCode = promo || discount

  let url = rawRedirect

  // If the user is coming from an influencer link, add the redirect to the search params
  if (discountCode && pageSlug) {
    url = `/${pageSlug}`
  }

  // Remove leading slashes from redirect
  const redirectPath = url.replace(/^\/+/, '')

  const params = new URLSearchParams(searchParams)
  params.delete('promo')
  params.delete('discount')
  params.delete('redirect')
  const redirectSearchParams = params.size > 0 ? `?${params}` : ''
  const redirect = `/${redirectPath}${redirectSearchParams}`

  return { discountCode, redirect }
}
