/* eslint-disable import/prefer-default-export */
/**
 * @description - Accepts product price and compareAtPrice and returns savings percentage and amount
 * @param {number} price - The current price of the product.
 * @param {number} compareAtPrice - The original price of the product.
 * @returns {Object} - An object containing the savings percentage and amount.
 * @property {number} savingsPercentage - The percentage of savings.
 * * */
export const calculateSavings = ({ price, compareAtPrice }:{ price: number, compareAtPrice: number }): {
  savingsPercentage: number;
  savingsAmount: number
} => {
  const r = 100
  const savings = compareAtPrice - price
  const savingsPercentage = Math.round((savings / compareAtPrice) * r)
  return { savingsPercentage, savingsAmount: savings }
}
