import { notEmpty } from './checking'

import { AssetType } from '@/components/Content/MediaContainer'

type AssetCollectionType = {
  items : AssetType[]
}

export function getFirstAsset(asset: AssetCollectionType): AssetType {
  if (notEmpty(asset?.items)) {
    return asset.items[0]
  }
  return { url: '' }
}

export function getLastAsset(asset: AssetCollectionType): AssetType {
  if (notEmpty(asset?.items)) {
    const { length } = asset.items
    return asset.items[length - 1]
  }
  return { url: '' }
}

export function evaluateAsset(asset: AssetType): AssetType | null {
  return notEmpty(asset.url) ? asset : null
}

export default getFirstAsset
