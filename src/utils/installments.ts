const FOUR_MONTHS = 4
const TWELVE_MONTHS = 12

type InstallmentsReturn = {
  price: number;
  paymentPeriods: number;
  periodLabel: string;
  periodicPrice: string;
}

type Variant = {
  price: number;
}

export function getPriceInstallments(variants: Variant[]): InstallmentsReturn | null {
  if (!variants || !variants.length) {
    return null
  }

  const DECIMALS_TO_FIX = 2
  const lowestPriceVariant = variants.reduce((lowest, item) => (item.price < lowest.price ? item : lowest), variants[0])
  const lowestPrice = lowestPriceVariant.price

  const threshold = 150
  const paymentPeriods = lowestPrice <= threshold ? FOUR_MONTHS : TWELVE_MONTHS
  const periodLabel = lowestPrice <= threshold ? 'bi-weekly, for 8 weeks.*' : 'mo. for 12 months.*'
  const periodicPrice = (lowestPrice / paymentPeriods).toFixed(DECIMALS_TO_FIX)

  return {
    price: lowestPrice,
    paymentPeriods,
    periodLabel,
    periodicPrice,
  }
}

export default getPriceInstallments
