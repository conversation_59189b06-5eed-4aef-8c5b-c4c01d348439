import type { Attribute, AttributeInput } from '@shopify/hydrogen-react/storefront-api-types'

type CombinedAttributes = {
  [keys:string]: Attribute
}

const combineCartAttributes = (
  a:Attribute[] = [],
  b:Attribute[] = []
) => {
  const prop = 'key'

  try {
    return Object.values(
      [...a, ...b].reduce((acc:CombinedAttributes, v) => {
        if (v[prop]) {
          acc[v[prop]] = acc[v[prop]] ? { ...acc[v[prop]], ...v } : { ...v }
        }
        return acc
      }, {}),
    ) as AttributeInput[]
  } catch (error) {
    return []
  }
}

export default combineCartAttributes
