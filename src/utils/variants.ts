const variantStore = new Map<string, string>()

const parseUrlSegment = (segment: string) => {
  const pairs = segment.split('&')
  const parsed = new Map<string, string>()

  pairs.forEach((pair) => {
    const [key, value] = pair.split('=')
    if (key && value) {
      parsed.set(key, value)
    }
  })

  return parsed
}

const variantAttribute = {
  get: (key: string) => variantStore.get(key),
  set: (segment: string) => {
    const parsed = parseUrlSegment(segment)
    parsed.forEach((value, key) => variantStore.set(key, value))
  },
}

export default variantAttribute
