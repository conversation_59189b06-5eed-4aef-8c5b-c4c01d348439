import { redirect } from 'next/navigation'

type Destination = {
  __typename: string
  slug: string
}

export type PageData = {
  type: string
  slug: string
  destination?: Destination
  destinationCustom?: string
  utmSource?: string
  utmMedium?: string
  utmCampaign?: string
  utmTerm?: string
  discountCode?: string
}
function getBaseUrl(type: string, slug: string): string {
  if (slug === 'home') {
    return '/'
  }
  switch (type) {
    case 'Product':
      return `/products/${slug}`
    case 'BlogArticle':
      return `/blog/${slug}`
    case 'Page':
      return `/${slug}`
    default:
      return `/${slug}`
  }
}

function getUTMParams(utmSource?: string, utmMedium?: string, utmCampaign?: string, utmTerm?: string): string {
  const params = new URLSearchParams()
  if (utmSource) params.append('utm_source', utmSource)
  if (utmMedium) params.append('utm_medium', utmMedium)
  if (utmCampaign) params.append('utm_campaign', utmCampaign)
  if (utmTerm) params.append('utm_term', utmTerm)
  return params.toString()
}

function getFullPath(baseUrl: string, utmParams: string, discountCode?: string): string {
  let fullPath = baseUrl
  if (utmParams) fullPath += `?${utmParams}`
  if (discountCode) {
    fullPath += `${fullPath.includes('?') ? '&' : '?'}discount=${discountCode}`
  }
  return fullPath
}

export default function Redirect({ data }: { data: PageData }) {
  const {
    type,
    slug,
    destination,
    destinationCustom,
    utmSource,
    utmMedium,
    utmCampaign,
    utmTerm,
    discountCode
  } = data

  const baseType = destination ? destination.__typename : type
  const baseSlug = destination ? destination.slug : slug
  const baseUrl = destinationCustom || getBaseUrl(baseType, baseSlug)
  const utmParams = getUTMParams(utmSource, utmMedium, utmCampaign, utmTerm)
  const redirectUrl = getFullPath(baseUrl, utmParams, discountCode)

  redirect(redirectUrl)
}
