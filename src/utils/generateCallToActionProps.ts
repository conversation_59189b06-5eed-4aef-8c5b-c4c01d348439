import { condition, empty } from './checking'

import { ThemeColors } from '@/app/themeThemes.stylex'
import generateSlug from '@utils/generateSlug'
import { toCamelCase } from '@utils/regex'

export type CallToActionExtractProps = {
  children: string,
  anchor: string,
  href: string,
  variant: string,
  theme: ThemeColors,
  id: string
}

const checkIfIsVariant = (theme: string) => ['primary', 'secondary', 'tertiary', 'underlined', 'transparent', 'transparentBorder'].includes(theme)

function getAnchorPart(block: any): string {
  return block?.anchor ? `#${block.anchor}` : ''
}

function generateHref(block: any, anchorPart: string): string {
  return generateSlug(block.page?.slug, block.page?.__typename) + anchorPart ||
    (block.customUrl ? block.customUrl + anchorPart : '') || block.anchor || ''
}

function generateCallToActionProps(
  block: any
): CallToActionExtractProps {
  const themeOrVariantField = block?.settings?.theme && toCamelCase(block.settings.theme)
  const anchorPart = getAnchorPart(block)
  const isVariant = checkIfIsVariant(themeOrVariantField)
  // variant and theme are mutually exclusive
  const theme = isVariant ? undefined : themeOrVariantField
  const href = generateHref(block, anchorPart)

  const variant = isVariant
    ? themeOrVariantField
    : condition(empty(theme), 'primary', undefined)

  const props = {
    children: block.text,
    anchor: block.anchor,
    href,
    theme,
    variant,
    id: block.page?.sys?.id || block.sys?.id
  }

  return props
}

export default generateCallToActionProps
