export function not(v: boolean): boolean {
  return !v
}
export function equals(a: any, b: any): boolean {
  return a === b
}

export function notEquals(a: any, b: any): boolean {
  return !equals(a, b)
}

export function condition<T>(expression: boolean, trueResult: any, falseResult: any = undefined): T {
  return expression ? trueResult : falseResult
}

export const isString = (value: any): boolean => typeof value === 'string'
export const isObject = (value: any): boolean => typeof value === 'object' && value !== null
export const isNumber = (value: any): boolean => typeof value === 'number'

export function empty(value: any): boolean {
  if (Array.isArray(value)) {
    return value.length === 0
  }

  if (isObject(value)) {
    return Object.keys(value).length === 0
  }

  if (isString(value)) {
    return value === ''
  }

  return value === null || value === undefined
}

export function notEmpty(value: any): boolean {
  return !empty(value)
}

export function isClient(): boolean {
  return typeof window !== 'undefined'
}
