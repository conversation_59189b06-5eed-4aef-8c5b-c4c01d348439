// URL utility functions for handling product URLs and variant parsing

import { slugify } from './regex'

/**
 * Supported product variant attributes
 */
export const SUPPORTED_ATTRIBUTES = ['color', 'size', 'value', 'group'] as const

/**
 * Type for product variant parameters
 */
export type ProductVariantParams = {
  [key in typeof SUPPORTED_ATTRIBUTES[number]]?: string;
} & {
  quantity?: string; // quantity is handled as search param, not URL segment
};

/**
 * Interface for objects that have a swatch
 */
interface HasSwatch {
  swatch?: {
    slug: string;
    swatchType?: string;
  };
}

/**
 * Interface for objects that have multiple swatches
 */
interface HasSwatches {
  swatches?: {
    items: HasSwatch[];
  };
}

/**
 * Strongly typed interface for product-like objects
 */
interface ProductLike {
  variants: {
    items: (HasSwatch & HasSwatches)[];
  };
  quantities?: {
    items: {
      toggles?: {
        items: { slug: string }[];
      };
    }[];
  };
  productMetadata?: {
    items: {
      type?: string;
      references?: {
        items: { slug?: string }[];
      };
    }[];
  };
  groups?: {
    items: {
      __typename?: string;
      slug?: string;
    }[];
  };
}

/**
 * Generate a basic product URL without variants
 */
export function productUrl(product: { slug: string }): string {
  return `/products/${product.slug}`
}

/**
 * Generates a product URL with variants in the path instead of query parameters
 */
export function getProductUrl(slug: string, variants?: ProductVariantParams | null) {
  if (!variants || Object.keys(variants).length === 0) {
    return `/products/${slug}/`
  }

  // Sort parameters in the same order as SUPPORTED_ATTRIBUTES to ensure consistency
  // Only include attributes that have values
  const variantString = SUPPORTED_ATTRIBUTES
    .filter((attr) => variants[attr])
    .map((attr) => `${attr}=${variants[attr]}`)
    .join('&')

  return `/products/${slug}/${variantString}/`
}

/**
 * Parses variant parameters from a URL path segment
 */
export function parseVariantParams(variantString: string | null | undefined): ProductVariantParams {
  if (!variantString) return {}

  // First decode the entire string as it may be URL encoded
  try {
    // This handles the case where the entire string is encoded: "color%3Dnavy"
    const decodedString = decodeURIComponent(variantString)

    // If the decoded string contains key=value pairs, process those
    if (decodedString.includes('=')) {
      const params: ProductVariantParams = {}

      decodedString.split('&').forEach((pairString) => {
        const [key, value] = pairString.split('=')
        if (key && value && SUPPORTED_ATTRIBUTES.includes(key as any)) {
          params[key as keyof ProductVariantParams] = value
        }
      })

      return params
    }
  } catch (e) {
    // If decoding fails, continue with original string
    console.warn('Failed to decode URL params:', e)
  }

  // Fallback: Try to parse the original string directly
  const params: ProductVariantParams = {}

  variantString.split('&').forEach((pair) => {
    if (!pair.includes('=')) return

    const [key, value] = pair.split('=')
    if (key && value && SUPPORTED_ATTRIBUTES.includes(key as any)) {
      try {
        params[key as keyof ProductVariantParams] = decodeURIComponent(value)
      } catch (e) {
        params[key as keyof ProductVariantParams] = value
      }
    }
  })

  return params
}

/**
 * Helper function to collect attribute values into a map
 * @param map The attribute map to add to
 * @param key The attribute key (e.g., 'color', 'size')
 * @param value The attribute value (e.g., 'navy', 'large')
 */
function collectAttribute(map: Map<string, Set<string>>, key?: string, value?: string): void {
  if (!key || !value || !SUPPORTED_ATTRIBUTES.includes(key as any)) return
  if (!map.has(key)) map.set(key, new Set())
  map.get(key)!.add(value)
}

/**
 * Generates all possible combinations of variants for a product
 * @param variants Array of product variants with swatch attributes
 * @param product Optional product object containing additional attributes like quantities
 * @returns Array of variant parameter objects representing all possible combinations
 */
export function generateVariantCombinations(
  variants: ProductLike['variants']['items'],
  product?: ProductLike
): ProductVariantParams[] {
  // Extract all unique attributes and their possible values
  const attributeMap = new Map<string, Set<string>>()

  // Collect all attribute types and values from variants
  variants.forEach((variant) => {
    // Handle primary swatch
    if (variant.swatch) {
      collectAttribute(attributeMap, variant.swatch.swatchType, variant.swatch.slug)
    }

    // Handle multiple swatches
    if (variant.swatches?.items?.length) {
      variant.swatches.items.forEach((swatch) => {
        collectAttribute(attributeMap, swatch.swatch?.swatchType, swatch.swatch?.slug)
      })
    }
  })

  // Add product-level attributes like quantity
  if (product?.quantities?.items?.length) {
    const quantityToggles = product.quantities.items[0]?.toggles?.items || []
    quantityToggles.forEach((toggle) => {
      collectAttribute(attributeMap, 'quantity', toggle.slug)
    })
  }

  // Add group attributes from product groups
  if (product?.groups?.items?.length) {
    product.groups.items.forEach((group) => {
      if (group.__typename === 'ProductsGroup' && group.slug) {
        collectAttribute(attributeMap, 'group', group.slug)
      }
    })
  }

  // Add other supported attributes from product metadata
  if (product?.productMetadata?.items) {
    product.productMetadata.items.forEach((metadata) => {
      if (metadata.type?.includes('Attribute') && metadata.references?.items) {
        const attributeType = metadata.type.toLowerCase().replace(' attribute', '')
        metadata.references.items.forEach((reference) => {
          collectAttribute(attributeMap, attributeType, reference.slug)
        })
      }
    })
  }

  // Convert to array format for easier processing
  const attributes = Array.from(attributeMap.entries()).map(([attrName, values]) => ({
    name: attrName,
    values: Array.from(values)
  }))

  // If no attributes found, return empty array
  if (attributes.length === 0) {
    return []
  }

  // Generate all combinations using cartesian product
  const combinations: ProductVariantParams[] = [{}]

  attributes.forEach((attribute) => {
    const { name, values } = attribute
    const newCombinations: ProductVariantParams[] = []

    // For each existing combination, create new combinations with each value of current attribute
    combinations.forEach((combo) => {
      values.forEach((value) => {
        newCombinations.push({
          ...combo,
          [name]: value
        })
      })
    })

    // Replace old combinations with new ones
    combinations.length = 0
    combinations.push(...newCombinations)
  })

  return combinations
}

/**
 * Find a variant that matches the given attribute
 */
function findVariantByAttribute(
  variants: ProductLike['variants']['items'],
  attribute?: string
): HasSwatch & HasSwatches | undefined {
  if (!attribute) return undefined
  return variants.find((v) => v.swatch?.slug === attribute)
}

/**
 * Find a variant based on multiple intrinsic attributes
 */
function findVariantByMultipleAttributes(
  variants: ProductLike['variants']['items'],
  intrinsicAttributes: string[]
): HasSwatch & HasSwatches | undefined {
  return variants.find((v) => v.swatches?.items?.every((swatch) => intrinsicAttributes.includes(swatch.swatch?.slug || '')))
}

/**
 * Find a variant based on available attributes with fallbacks
 */
function findVariantBySingleAttribute(
  variants: ProductLike['variants']['items'],
  variantParams: ProductVariantParams,
  defaultAttribute?: string
): HasSwatch & HasSwatches | null {
  // Try the default attribute first if provided
  if (defaultAttribute) {
    const found = findVariantByAttribute(variants, defaultAttribute)
    if (found) return found
  }

  // Try each supported attribute in priority order
  const foundAttribute = SUPPORTED_ATTRIBUTES.find((attr) => {
    const value = variantParams[attr]
    if (value) {
      return findVariantByAttribute(variants, value)
    }
    return false
  })

  if (foundAttribute) {
    const value = variantParams[foundAttribute]
    const found = findVariantByAttribute(variants, value!)
    if (found) return found
  }

  // Fallback to first variant if none found
  return variants.length > 0 ? variants[0] : null
}

/**
 * Extract intrinsic and extrinsic attributes from variant parameters
 */
function extractAttributes(variantParams: ProductVariantParams): {
  intrinsicAttributes: string[],
  attribute?: string,
  quantity?: string
} {
  // Only include variant-level attributes (exclude 'group' which is product-level)
  const variantLevelAttributes = SUPPORTED_ATTRIBUTES.filter((attr) => attr !== 'group')

  // Extract intrinsic attributes from variant-level attributes only
  const intrinsicAttributes = variantLevelAttributes
    .map((key) => variantParams[key])
    .filter(Boolean) as string[]

  // Extract extrinsic attributes
  const { quantity } = variantParams

  // The primary attribute for single-attribute selection - use first available from variant-level attributes
  const attribute = variantLevelAttributes.find((attr) => variantParams[attr]) ?? undefined

  return { intrinsicAttributes, attribute, quantity }
}

/**
 * Parse URL variant parameters
 */
function parseUrlVariantParams(params: { variants?: string }): ProductVariantParams {
  const { variants: variantString } = params

  return parseVariantParams(variantString)
}

/**
 * Try to find a variant using intrinsic attributes in priority order
 */
function findVariantByIntrinsicAttributes(
  productVariants: ProductLike['variants']['items'],
  intrinsicAttributes: string[]
): HasSwatch & HasSwatches | null {
  const foundAttr = intrinsicAttributes.find((attr) => findVariantByAttribute(productVariants, attr))
  return foundAttr ? findVariantByAttribute(productVariants, foundAttr) || null : null
}

/**
 * Try to find variant using multiple strategies
 */
function tryFindVariant(
  productVariants: ProductLike['variants']['items'],
  intrinsicAttributes: string[],
  attribute?: string
): HasSwatch & HasSwatches | null {
  // Try multi-attribute variants first
  if (intrinsicAttributes.length > 1) {
    const multiVariant = findVariantByMultipleAttributes(productVariants, intrinsicAttributes)
    if (multiVariant) return multiVariant
  }

  // Try single intrinsic attributes
  const intrinsicVariant = findVariantByIntrinsicAttributes(productVariants, intrinsicAttributes)
  if (intrinsicVariant) return intrinsicVariant

  // Try primary attribute if not already checked
  if (attribute && !intrinsicAttributes.includes(attribute)) {
    return findVariantByAttribute(productVariants, attribute) || null
  }

  return null
}

/**
 * Select a variant based on parsed parameters
 */
function selectVariant(
  product: ProductLike,
  variantParams: ProductVariantParams
): HasSwatch & HasSwatches | null {
  const { intrinsicAttributes, attribute } = extractAttributes(variantParams)
  const productVariants = product.variants.items || []

  // Try primary variant finding strategies
  const foundVariant = tryFindVariant(productVariants, intrinsicAttributes, attribute)
  if (foundVariant) return foundVariant

  // Fallback to specific attribute types or first variant
  return findVariantBySingleAttribute(productVariants, variantParams, attribute) || (productVariants.length > 0 ? productVariants[0] : null)
}

/**
 * Parse variant parameters from URL and select the matching variant from a product
 */
export function parseAndSelectVariant(
  params: { variants?: string },
  product: ProductLike
): { variantParams: ProductVariantParams, variant: HasSwatch & HasSwatches | null } {
  const variantParams = parseUrlVariantParams(params)

  // Select the appropriate variant
  const variant = selectVariant(product, variantParams)

  return { variantParams, variant }
}

/**
 * Get all variant slugs for all products
 * @param productSlugs Array of product slug objects
 * @param fetchProduct Function to fetch a product by slug
 * @returns Array of static parameters for all products and their variants
 */
export async function getAllVariantsSlugs(
  productSlugs: { slug: string }[],
  fetchProduct: (slug: string) => Promise<any>
): Promise<Array<{ slug: string, variants: string }>> {
  // Prepare promises for parallel processing
  const staticParamsPromises = productSlugs.map(async ({ slug }) => {
    const params: { slug: string, variants: string }[] = []

    // Then fetch the product with its variants
    const product = await fetchProduct(slug)

    if (product?.variants?.items?.length) {
      // First, generate single variant paths
      const singleVariantPaths = new Set<string>()

      // Add individual variant attributes
      product.variants.items.forEach((variant: {
        swatch?: { swatchType: string; slug: string };
        swatches?: { items: Array<{ swatchType?: string; slug: string }> };
      }) => {
        if (variant.swatch) {
          const { swatchType, slug: swatchSlug } = variant.swatch
          singleVariantPaths.add(`${swatchType}=${slugify(swatchSlug)}`)
        }

        // Handle multi-attribute variants
        if (variant.swatches?.items && variant.swatches.items.length > 0) {
          variant.swatches.items.forEach((swatch: { swatchType?: string; slug: string }) => {
            const swatchType = swatch.swatchType ?? swatch.slug
            const swatchValue = swatch.slug
            singleVariantPaths.add(`${swatchType}=${slugify(swatchValue)}`)
          })
        }
      })

      // Add individual quantity attributes if they exist
      if (product?.quantities?.items?.length > 0) {
        const quantityToggles = product.quantities.items[0]?.toggles?.items ?? []
        quantityToggles.forEach((toggle: { slug: string }) => {
          singleVariantPaths.add(`quantity=${slugify(toggle.slug)}`)
        })
      }

      // Add individual product metadata attributes
      if (product?.productMetadata?.items) {
        product.productMetadata.items.forEach((metadata: {
          type?: string;
          references?: { items: Array<{ slug?: string }> }
        }) => {
          if (metadata.type?.includes('Attribute') && metadata.references?.items) {
            metadata.references.items.forEach((reference: { slug?: string }) => {
              const attributeType = metadata.type!.toLowerCase().replace(' attribute', '')
              if (SUPPORTED_ATTRIBUTES.includes(attributeType as typeof SUPPORTED_ATTRIBUTES[number]) && reference.slug) {
                singleVariantPaths.add(`${attributeType}=${slugify(reference.slug)}`)
              }
            })
          }
        })
      }

      // Add single variant paths to params
      singleVariantPaths.forEach((variantPath) => {
        params.push({
          slug,
          variants: variantPath
        })
      })

      // Then, generate all variant combinations
      const variantCombinations = generateVariantCombinations(product.variants.items, product)

      // Create static paths for each variant combination (excluding single variants already added)
      variantCombinations.forEach((variantCombo) => {
        // Only combinations with multiple attributes
        if (Object.keys(variantCombo).length > 1) {
          // Sort parameters in the same order as SUPPORTED_ATTRIBUTES to ensure consistency
          const variantPath = SUPPORTED_ATTRIBUTES
            .filter((attr) => variantCombo[attr])
            .map((attr) => `${attr}=${slugify(variantCombo[attr]!)}`)
            .join('&')

          params.push({
            slug,
            variants: variantPath
          })
        }
      })
    }

    return params
  })

  // Resolve all promises and flatten the array
  const nestedParams = await Promise.all(staticParamsPromises)
  return nestedParams.flat()
}

/**
 * Validate unknown parameters
 */
function validateUnknownParams(variantParams: ProductVariantParams): string[] {
  const paramKeys = Object.keys(variantParams)
  const unknownKeys = paramKeys.filter((key) => !SUPPORTED_ATTRIBUTES.includes(key as any))

  return unknownKeys.length > 0
    ? [`Unknown parameters: ${unknownKeys.join(', ')}. Supported attributes: ${SUPPORTED_ATTRIBUTES.join(', ')}`]
    : []
}

/**
 * Validate intrinsic attributes against product variants
 */
function validateIntrinsicAttributes(
  intrinsicAttributes: string[],
  productVariants: ProductLike['variants']['items']
): string[] {
  if (intrinsicAttributes.length === 0) return []

  const hasMatchingVariant = productVariants.some((variant) => {
    if (variant.swatches?.items?.length) {
      // Multi-attribute variant
      return intrinsicAttributes.every((attr) => variant.swatches?.items?.some((swatch) => swatch.swatch?.slug === attr))
    }
    // Single attribute variant
    return intrinsicAttributes.includes(variant.swatch?.slug || '')
  })

  return hasMatchingVariant
    ? []
    : [`No variant found for attributes: ${intrinsicAttributes.join(', ')}`]
}

/**
 * Validate quantity parameter
 */
function validateQuantity(quantity: string | undefined, product: ProductLike): string[] {
  if (!quantity) return []

  const quantityToggles = product.quantities?.items?.[0]?.toggles?.items || []
  const validQuantities = quantityToggles.map((toggle) => toggle.slug)

  return validQuantities.length > 0 && !validQuantities.includes(quantity)
    ? [`Invalid quantity: ${quantity}. Valid options: ${validQuantities.join(', ')}`]
    : []
}

/**
 * Validate that variant parameters are valid for the given product
 */
export function validateVariantParams(
  variantParams: ProductVariantParams,
  product: ProductLike
): { isValid: boolean; errors: string[] } {
  const productVariants = product.variants.items || []
  const { intrinsicAttributes } = extractAttributes(variantParams)

  const errors = [
    ...validateUnknownParams(variantParams),
    ...validateIntrinsicAttributes(intrinsicAttributes, productVariants),
    ...validateQuantity(variantParams.quantity, product)
  ]

  return { isValid: errors.length === 0, errors }
}

export default productUrl
