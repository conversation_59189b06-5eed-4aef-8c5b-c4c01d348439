export const oneSecondInMs = 1000
export const oneMinuteInMs = 60
export const oneHourInSeconds = 60
export const oneDayInHours = 24
export const oneDayInTimeStamp = oneSecondInMs * oneMinuteInMs * oneHourInSeconds * oneDayInHours

const range = 10
const dateRange = range * oneDayInTimeStamp

export const formatDate = (
  dateString: string,
  format: 'full' | 'short' = 'full',
  checkDate: boolean = false
): string | null => {
  const date = new Date(dateString)

  if (Number.isNaN(date.getTime())) {
    return null
  }

  if (checkDate) {
    const currentDate = new Date()
    const timeDifference = date.getTime() - currentDate.getTime()

    if (timeDifference <= dateRange) {
      return null
    }
  }

  if (format === 'short') {
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      timeZone: 'UTC'
    })
  }

  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'UTC'
  })
}
