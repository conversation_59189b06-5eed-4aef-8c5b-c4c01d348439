const removeSpaces = (str: string): string => str?.replace(/\s/g, '')

const slugify = (str: string): string => {
  const trimmed = str.trim()
  return trimmed.replace(/\s/g, '-').toLowerCase()
}

const toLowerCase = (str: string): string => str.toLowerCase()

const toCamelCase = (str: string): string => {
  const matches = str?.match(/[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g)
  const s = matches ? matches
    .map((x) => x.slice(0, 1).toUpperCase() + x.slice(1).toLowerCase())
    .join('') : ''
  return s.slice(0, 1).toLowerCase() + s.slice(1)
}

const sanitize = (str: string): string => {
  const sanitized = str
    .normalize('NFKD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-z0-9]+/gi, '-')
    .replace(/^-+|-+$/g, '')
  return toLowerCase(sanitized)
}

export {
  removeSpaces,
  slugify,
  toLowerCase,
  toCamelCase,
  sanitize
}
