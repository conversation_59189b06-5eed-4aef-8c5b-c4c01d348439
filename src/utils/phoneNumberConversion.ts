const formatPhoneNumber = (value: string, isDeleting = false): string => {
  if (!value) return value

  const phoneNumber = value.replace(/\D/g, '')

  // This portion will format the phone number into the desired (xxx) xxx-xxxx format OR the +1 (xxx) xxx-xxxx format
  const phoneNumberLength = phoneNumber.length
  const fullDigits = 10
  const areaCodeLength = 3
  const prefixLength = 6
  const lineNumberLength = 10

  if (isDeleting) {
    return value
  }

  if (phoneNumberLength <= fullDigits) {
    return `(${phoneNumber.slice(0, areaCodeLength)}) ${phoneNumber.slice(areaCodeLength, prefixLength)}-${phoneNumber.slice(prefixLength, lineNumberLength)}`
  }

  return `+${phoneNumber[0]} (${phoneNumber.slice(1, areaCodeLength + 1)}) ${phoneNumber.slice(areaCodeLength + 1, prefixLength + 1)}-${phoneNumber.slice(prefixLength + 1, lineNumberLength + 1)}`
}

export default formatPhoneNumber
