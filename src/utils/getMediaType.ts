import { condition, isString } from './checking'

const getMediaType = (media: string) => {
  const extensionMap: { [key: string]: 'image' | 'video' } = {
    '.jpg': 'image',
    '.jpeg': 'image',
    '.webp': 'image',
    '.png': 'image',
    '.gif': 'image',
    '.mp4': 'video',
    '.ogg': 'video',
    '.webm': 'video',
  }

  const safeMedia = condition<string>(isString(media), media, '')
  const fileExtension = safeMedia.slice(safeMedia.lastIndexOf('.')).toLowerCase()

  return extensionMap[fileExtension] || 'unknown'
}

export default getMediaType
