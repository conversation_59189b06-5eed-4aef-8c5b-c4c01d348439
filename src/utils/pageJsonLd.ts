import { condition, notEmpty } from './checking'
import { SOCIAL } from './socialAndContacts'

type data = {
  seoMetadata: {
    name: string,
    description: string
  },
  logo: {
    url: string
  }
}

export function generatePageLdJson(data = {} as data) {
  const url = '/'
  const name = data?.seoMetadata.name
  const description = data?.seoMetadata.description
  const logo = condition<string>(
    notEmpty(data?.logo),
    data?.logo.url,
    null
  )

  return [
    {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      url,
      name,
    },
    {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      url,
      name,
      description,
      logo,
      email: '<EMAIL>',
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '6463890251',
        contactType: 'customer service',
      },
      sameAs: [...Object.values(SOCIAL)]
    },
    {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      url,
      name,
    }
  ]
}

export default generatePageLdJson
