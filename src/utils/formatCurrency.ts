const formatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 2,
})

const fraction = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 0,
})

const formatCurrency = (number: number) => {
  if (number % 1 === 0) return fraction.format(number)
  return formatter.format(number)
}

export default formatCurrency
