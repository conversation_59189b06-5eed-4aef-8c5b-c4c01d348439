import { usePathname, useSearchParams } from 'next/navigation'

// TODO: Seems obsolete, not used anywhere, consider removing
const useFullHref = (): string => {
  const pathname: string = usePathname()
  const searchParams: URLSearchParams = useSearchParams()

  const getFullUrl = (): string => {
    if (typeof window !== 'undefined') {
      const search: string = searchParams.toString()
      const fullPath: string = search ? `${pathname}?${search}` : pathname
      return `${window.location.origin}${fullPath}`
    }
    return ''
  }

  return getFullUrl()
}

export default useFullHref
